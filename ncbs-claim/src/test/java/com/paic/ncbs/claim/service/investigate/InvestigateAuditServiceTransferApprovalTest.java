package com.paic.ncbs.claim.service.investigate;

import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateAuditDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateVO;
import com.paic.ncbs.claim.service.investigate.impl.InvestigateAuditServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 测试移交审批功能
 */
@ExtendWith(MockitoExtension.class)
public class InvestigateAuditServiceTransferApprovalTest {

    @Mock
    private InvestigateMapper investigateDao;

    @Mock
    private TaskInfoMapper taskInfoMapper;

    @InjectMocks
    private InvestigateAuditServiceImpl investigateAuditService;

    private InvestigateAuditDTO investigateAuditDTO;
    private InvestigateVO investigateVO;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        investigateAuditDTO = new InvestigateAuditDTO();
        investigateAuditDTO.setIdAhcsInvestigate("TEST_INVESTIGATE_ID");
        investigateAuditDTO.setAuditOpinion("移交审批");

        investigateVO = new InvestigateVO();
        investigateVO.setIdAhcsInvestigate("TEST_INVESTIGATE_ID");
        investigateVO.setReportNo("TEST_REPORT_NO");
        investigateVO.setCaseTimes(1);
        investigateVO.setAuditorUm("TEST_AUDITOR_UM");
    }

    @Test
    void testHandleTransferApproval_Success() throws GlobalBusinessException {
        // 准备测试数据
        when(investigateDao.getInvestigateById("TEST_INVESTIGATE_ID")).thenReturn(investigateVO);

        // 执行测试
        investigateAuditService.handleTransferApproval(investigateAuditDTO, "TEST_USER_ID");

        // 验证结果
        verify(investigateDao, times(1)).getInvestigateById("TEST_INVESTIGATE_ID");
        verify(taskInfoMapper, times(1)).updateTaskAssigner(any(TaskInfoDTO.class));
    }

    @Test
    void testHandleTransferApproval_InvestigateNotFound() {
        // 准备测试数据
        when(investigateDao.getInvestigateById("TEST_INVESTIGATE_ID")).thenReturn(null);

        // 执行测试并验证异常
        GlobalBusinessException exception = assertThrows(GlobalBusinessException.class, () -> {
            investigateAuditService.handleTransferApproval(investigateAuditDTO, "TEST_USER_ID");
        });

        assertEquals("调查信息不存在", exception.getMessage());
        verify(taskInfoMapper, never()).updateTaskAssigner(any(TaskInfoDTO.class));
    }

    @Test
    void testHandleTransferApproval_AuditorUmEmpty() {
        // 准备测试数据
        investigateVO.setAuditorUm(null);
        when(investigateDao.getInvestigateById("TEST_INVESTIGATE_ID")).thenReturn(investigateVO);

        // 执行测试并验证异常
        GlobalBusinessException exception = assertThrows(GlobalBusinessException.class, () -> {
            investigateAuditService.handleTransferApproval(investigateAuditDTO, "TEST_USER_ID");
        });

        assertEquals("调查信息中审核人不能为空", exception.getMessage());
        verify(taskInfoMapper, never()).updateTaskAssigner(any(TaskInfoDTO.class));
    }

    @Test
    void testHandleTransferApproval_AuditorUmEmptyString() {
        // 准备测试数据
        investigateVO.setAuditorUm("");
        when(investigateDao.getInvestigateById("TEST_INVESTIGATE_ID")).thenReturn(investigateVO);

        // 执行测试并验证异常
        GlobalBusinessException exception = assertThrows(GlobalBusinessException.class, () -> {
            investigateAuditService.handleTransferApproval(investigateAuditDTO, "TEST_USER_ID");
        });

        assertEquals("调查信息中审核人不能为空", exception.getMessage());
        verify(taskInfoMapper, never()).updateTaskAssigner(any(TaskInfoDTO.class));
    }
}
