package com.paic.ncbs.claim.controller.investigate;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

public class test {

    public static void main(String[] args) {

 long data = System.currentTimeMillis();
        String s= data+"";
        // 测试环境
        String companyIdSit = "tpa-dev";
        String signatureSit = generateSign("2d6484b7667e49589d29807b499e0a2f", s, s).toLowerCase();
        // 生产环境
        String companyIdPro = "tpa-xiezhu";
        String signaturePro = generateSign("8d4ddb7563484943a2475550a0fc2c6a", s, s).toLowerCase();
        //String policyUnderwriting ="https://openapi-dev.samsunganycar.com/tpa-transplat/itfp/commonEntrance.do?timestamp={0}&nonce={1}&signature={2}&companyId="+companyId;
        String policyUnderwritingSit ="timestamp={0}&nonce={1}&signature={2}&companyId="+companyIdSit;
        policyUnderwritingSit = MessageFormat.format(policyUnderwritingSit,s,s,signatureSit);
        String policyUnderwritingPro ="timestamp={0}&nonce={1}&signature={2}&companyId="+companyIdPro;
        policyUnderwritingPro = MessageFormat.format(policyUnderwritingPro,s,s,signaturePro);
        //String a = Hashing.sha1().hashString(s + s + "2d6484b7667e49589d29807b499e0a2f", Charsets.UTF_8).toString().toUpperCase();
        System.out.println("中台测试请求签名：\n"+policyUnderwritingSit );
        System.out.println("中台生产请求签名：\n"+policyUnderwritingPro );
        String companyId = "ncbs-wesure-dev";
        String signature = generateSign("a0841058596a496f99642c5b2778f126", s, s).toLowerCase();
        String wesureFreightInsurance ="timestamp={0}&nonce={1}&signature={2}&companyId="+companyId;
        wesureFreightInsurance = MessageFormat.format(wesureFreightInsurance,s,s,signature);
        System.out.println("渠道测试请求签名：\n"+wesureFreightInsurance);

    }


    /**
     * 生成签名
     */
    public static String generateSign(String secretKey, String timestamp, String nonce) {
        String[] arr = new String[]{secretKey, timestamp, nonce};
        // 将secretKey、timestamp、nonce、三个参数进行字典序排序
        Arrays.sort(arr);
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < arr.length; i++) {
            content.append(arr[i]);
        }
        MessageDigest md = null;
        String signature = null;
        try {
            md = MessageDigest.getInstance("SHA");
            // 将三个参数字符串拼接成一个字符串进行sha1加密
            byte[] digest = md.digest(content.toString().getBytes());
            signature = byteToStr(digest);
        } catch (NoSuchAlgorithmException e) {
            // log.error(e.getMessage());
        }
        return signature;
    }
    /**
     * 将字节数组转换为十六进制字符串
     */
    private static String byteToStr(byte[] byteArray) {
        StringBuilder strDigest = new StringBuilder();
        for (byte b : byteArray) {
            strDigest.append(byteToHexStr(b));
        }
        return strDigest.toString();
    }
    private static String byteToHexStr(byte mByte) {
        char[] digit = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        char[] tempArr = new char[2];
        tempArr[0] = digit[(mByte >>> 4) & 0X0F];
        tempArr[1] = digit[mByte & 0X0F];
        return new String(tempArr);
    }
}
