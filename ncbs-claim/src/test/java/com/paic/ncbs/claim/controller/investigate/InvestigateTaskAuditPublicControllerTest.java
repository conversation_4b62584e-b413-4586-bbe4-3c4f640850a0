package com.paic.ncbs.claim.controller.investigate;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.controller.doc.DocAppFileUploadController;
import com.paic.ncbs.claim.controller.doc.DocumentController;
import com.paic.ncbs.claim.controller.doc.IOBSFileUploadController;
import com.paic.ncbs.claim.controller.report.QueryReportController;
import com.paic.ncbs.claim.controller.who.investigate.InvestigateTaskAuditPublicController;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.vo.fileupolad.FileInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskQueryVO;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskAuditPublicService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 测试 InvestigateTaskAuditPublicController 的功能
 */
@ExtendWith(MockitoExtension.class)
public class InvestigateTaskAuditPublicControllerTest {

    @Mock
    private InvestigateTaskAuditPublicService investigateTaskAuditPublicService;

    @Mock
    private QueryReportController queryReportController;

    @Mock
    private DocAppFileUploadController docAppFileUploadController;

    @Mock
    private IOBSFileUploadController iobsFileUploadController;

    @Mock
    private DocumentController documentController;

    @Mock
    private TaskListService taskListService;

    @Mock
    private ReportCustomerInfoService reportCustomerInfoService;

    @InjectMocks
    private InvestigateTaskAuditPublicController controller;

    private UserInfoDTO userInfoDTO;

    @BeforeEach
    void setUp() {
        userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserCode("TEST_USER");
    }

    @Test
    void testGetInvestigateTaskList_Success() throws Exception {
        // 准备测试数据
        WorkBenchTaskQueryVO queryVO = new WorkBenchTaskQueryVO();
        queryVO.setReportNo("TEST_REPORT_001");

        Map<String, List<com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO>> mockResult = new HashMap<>();
        mockResult.put("investigateApproval", new ArrayList<>());

        when(taskListService.getAllDepartmentCodesByCode(any())).thenReturn(Collections.singletonList("TEST_DEPT"));
        when(taskListService.getWorkBenchTaskList(any())).thenReturn(mockResult);

        try (MockedStatic<WebServletContext> mockedWebServletContext = mockStatic(WebServletContext.class)) {
            mockedWebServletContext.when(WebServletContext::getUser).thenReturn(userInfoDTO);
            mockedWebServletContext.when(WebServletContext::getDepartmentCode).thenReturn("TEST_DEPT");

            // 执行测试
            var result = controller.getInvestigateTaskList(queryVO);

            // 验证结果
            assertNotNull(result);
            assertEquals("000000", result.getCode());
            verify(taskListService, times(1)).getWorkBenchTaskList(any());
        }
    }

    @Test
    void testGetHistoryCaseListNew_Success() {
        // 准备测试数据
        String reportNo = "TEST_REPORT_001";
        Integer caseTimes = 1;
        Integer pageIndex = 1;
        Integer pageRows = 10;

        when(reportCustomerInfoService.getHistoryCaseListNew(any(), any(), any())).thenReturn(new ArrayList<>());

        // 执行测试
        var result = controller.getHistoryCaseListNew(reportNo, caseTimes, pageIndex, pageRows);

        // 验证结果
        assertNotNull(result);
        assertEquals("000000", result.getCode());
        verify(reportCustomerInfoService, times(1)).getHistoryCaseListNew(eq(reportNo), eq(caseTimes), any());
    }

    @Test
    void testGetDocumentList_Success() throws Exception {
        // 准备测试数据
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        fileInfoDTO.setReportNo("TEST_REPORT_001");
        fileInfoDTO.setCaseTimes(1);

        when(docAppFileUploadController.getDocumentList(any())).thenReturn(null);

        // 执行测试
        var result = controller.getDocumentList(fileInfoDTO);

        // 验证调用
        verify(docAppFileUploadController, times(1)).getDocumentList(fileInfoDTO);
    }

    @Test
    void testGetIntranetIOBSDownloadUrl_Success() {
        // 准备测试数据
        String fileId = "TEST_FILE_ID";
        String fileName = "test.pdf";

        when(iobsFileUploadController.getIntranetIOBSDownloadUrl(any(), any())).thenReturn(null);

        // 执行测试
        var result = controller.getIntranetIOBSDownloadUrl(fileId, fileName);

        // 验证调用
        verify(iobsFileUploadController, times(1)).getIntranetIOBSDownloadUrl(fileId, fileName);
    }

    @Test
    void testGetAllDocumentTypeList_Success() throws Exception {
        // 准备测试数据
        FileInfoVO fileInfoVO = new FileInfoVO();

        when(documentController.getAllDocumentTypeList(any())).thenReturn(null);

        // 执行测试
        var result = controller.getAllDocumentTypeList(fileInfoVO);

        // 验证调用
        verify(documentController, times(1)).getAllDocumentTypeList(fileInfoVO);
    }

    @Test
    void testGetInvestigateTaskList_ParametersSetCorrectly() throws Exception {
        // 准备测试数据
        WorkBenchTaskQueryVO queryVO = new WorkBenchTaskQueryVO();

        when(taskListService.getAllDepartmentCodesByCode(any())).thenReturn(Collections.singletonList("TEST_DEPT"));
        when(taskListService.getWorkBenchTaskList(any())).thenReturn(new HashMap<>());

        try (MockedStatic<WebServletContext> mockedWebServletContext = mockStatic(WebServletContext.class)) {
            mockedWebServletContext.when(WebServletContext::getUser).thenReturn(userInfoDTO);
            mockedWebServletContext.when(WebServletContext::getDepartmentCode).thenReturn("TEST_DEPT");

            // 执行测试
            controller.getInvestigateTaskList(queryVO);

            // 验证参数设置
            assertEquals("Y", queryVO.getIsIncludeSubordinates());
            assertEquals("N", queryVO.getIsMyCase());
            assertEquals("N", queryVO.getIsQuickPay());
            assertEquals("TEST_USER", queryVO.getUserCode());
            assertEquals("OC_INVESTIGATE_APPROVAL", queryVO.getTaskDefinitionBpmKey());
        }
    }

    @Test
    void testGetHistoryCaseListNew_DefaultPagination() {
        // 准备测试数据
        String reportNo = "TEST_REPORT_001";
        Integer caseTimes = 1;

        when(reportCustomerInfoService.getHistoryCaseListNew(any(), any(), any())).thenReturn(new ArrayList<>());

        // 执行测试（使用默认分页参数）
        var result = controller.getHistoryCaseListNew(reportNo, caseTimes, null, null);

        // 验证结果
        assertNotNull(result);
        verify(reportCustomerInfoService, times(1)).getHistoryCaseListNew(eq(reportNo), eq(caseTimes), any());
    }
}
