package com.paic.ncbs.claim.controller.common;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.service.customer.CustomerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "客户信息补充")
@RestController
@RequestMapping("/public/customer")
public class CustomerController {

    @Autowired
    private CustomerInfoService customerInfoService;

    @ApiOperation("定时任务，历史数据补加新客户号")
    @PostMapping(value = "/regenerateCustomerNo")
    public ResponseResult<Object> regenerateCustomerNo() throws GlobalBusinessException {
        customerInfoService.regenerateCustomerNo();
        return ResponseResult.success();
    }
}
