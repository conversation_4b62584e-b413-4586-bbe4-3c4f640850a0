package com.paic.ncbs.claim.service.prepay.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.prepay.ClmsPolicyPrepayDutyDetailEntity;
import com.paic.ncbs.claim.dao.mapper.prepay.ClmsPolicyPrepayDutyDetailMapper;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.prepayinfo.ClmsPolicyPrepayDutyDetailDTO;

import com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO;
import com.paic.ncbs.claim.model.vo.ahcs.PrePayCaseVO;
import com.paic.ncbs.claim.service.prepay.ClmsPolicyPrepayDutyDetailService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 预赔责任明细表(ClmsPolicyPrepayDutyDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-15 11:01:25
 */

@Slf4j
@Service("clmsPolicyPrepayDutyDetailService")
public class ClmsPolicyPrepayDutyDetailServiceImpl implements ClmsPolicyPrepayDutyDetailService {
    @Resource
    private ClmsPolicyPrepayDutyDetailMapper clmsPolicyPrepayDutyDetailMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ClmsPolicyPrepayDutyDetailDTO queryById(String id) {
        ClmsPolicyPrepayDutyDetailDTO dto = new ClmsPolicyPrepayDutyDetailDTO();
        ClmsPolicyPrepayDutyDetailEntity entity = this.clmsPolicyPrepayDutyDetailMapper.queryById(id);
        if(ObjectUtil.isEmpty(entity)){
           return dto;
        }
        BeanUtils.copyProperties(entity,dto);
        return dto;
    }


    /**
     * 新增数据
     *
     * @param dtoList   实例对象
     * @return 实例对象
     */
    @Override
    public void saveDutyDetail(List<DutyPrepayInfoDTO> dtoList) {
        log.info("saveDutyDetail责任明细数据保存入参={}", JsonUtils.toJsonString(dtoList));
        List<ClmsPolicyPrepayDutyDetailEntity>  entityList =
                checkAndSetData(dtoList);
        log.info("saveDutyDetail组装后的数据集合={}", JsonUtils.toJsonString(entityList));
        if(CollectionUtil.isEmpty(entityList)){
           return ;
        }
        this.clmsPolicyPrepayDutyDetailMapper.insertBatch(entityList);
    }

    /**
     * 入参校验
     * @param dtoList
     */
    private  List<ClmsPolicyPrepayDutyDetailEntity> checkAndSetData(List<DutyPrepayInfoDTO> dtoList) {
        List<ClmsPolicyPrepayDutyDetailDTO> dutyDtoList = new ArrayList<>();
        Date date = new Date();
        for (DutyPrepayInfoDTO dto : dtoList) {
            if(CollectionUtil.isEmpty(dto.getPolicyPrepayDutyDetailDTOList())){
                 throw new GlobalBusinessException("责任明细集合不能为空");
            }
            if(StringUtils.isEmptyStr(dto.getDutyCode())){
                throw new GlobalBusinessException("责任编码不能为空");
            }
            for (ClmsPolicyPrepayDutyDetailDTO detail : dto.getPolicyPrepayDutyDetailDTOList()) {
                if(StringUtils.isEmptyStr(detail.getDutyDetailCode())){
                    throw new GlobalBusinessException("责任明细编码不能为空");
                }
                detail.setDutyCode(dto.getDutyCode());
                detail.setReportNo(dto.getReportNo());
                detail.setId(UuidUtil.getUUID());
                detail.setCaseTimes(dto.getCaseTimes());
                detail.setCaseNo(dto.getCaseNo());
                detail.setSubTimes(dto.getSubTimes());
                detail.setCreatedBy(WebServletContext.getUserId());
                detail.setUpdatedBy(WebServletContext.getUserId());
                detail.setCreatedDate(date);
                detail.setUpdatedDate(date);
                detail.setPlanCode(dto.getPlanCode());
                detail.setPlanName(dto.getPlanName());
                dutyDtoList.add(detail);
            }

        }
        List<ClmsPolicyPrepayDutyDetailEntity> entityList = BeanUtil.copyToList(dutyDtoList,ClmsPolicyPrepayDutyDetailEntity.class);

        return entityList;
    }


    /**
     * 修改数据
     *
     * @param clmsPolicyPrepayDutyDetail 实例对象
     * @return 实例对象
     */
    @Override
    public ClmsPolicyPrepayDutyDetailDTO update(ClmsPolicyPrepayDutyDetailDTO clmsPolicyPrepayDutyDetail) {
        ClmsPolicyPrepayDutyDetailEntity entity = new  ClmsPolicyPrepayDutyDetailEntity();
        BeanUtils.copyProperties(clmsPolicyPrepayDutyDetail,entity);
        this.clmsPolicyPrepayDutyDetailMapper.update(entity);
        return this.queryById(clmsPolicyPrepayDutyDetail.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.clmsPolicyPrepayDutyDetailMapper.deleteById(id) > 0;
    }

    /**
     * 根据报案号，赔付次数，预赔次数查询责任明细信息
     * @param reportNo
     * @param caseTimes
     * @param subTimes
     */
    @Override
    public  List<ClmsPolicyPrepayDutyDetailDTO> getDutyDetailInfo(String reportNo, Integer caseTimes, Integer subTimes) {
         List<ClmsPolicyPrepayDutyDetailEntity>  entityList =  clmsPolicyPrepayDutyDetailMapper.getDutyDetailInfo(reportNo,caseTimes,subTimes);
         if(CollectionUtil.isEmpty(entityList)){
             return null;
         }

         return   BeanUtil.copyToList(entityList,ClmsPolicyPrepayDutyDetailDTO.class);

    }
}
