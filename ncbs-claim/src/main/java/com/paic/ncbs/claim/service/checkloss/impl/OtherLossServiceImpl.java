package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.model.dto.duty.OtherLossDTO;
import com.paic.ncbs.claim.dao.mapper.checkloss.OtherLossMapper;
import com.paic.ncbs.claim.service.checkloss.OtherLossService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class OtherLossServiceImpl implements OtherLossService {

	@Autowired
    OtherLossMapper otherLossDao;

	@Override
	@Transactional
	public void saveOtherLoss(OtherLossDTO otherLossDTO) {
		String reportNo = otherLossDTO.getReportNo();
		Integer caseTimes = otherLossDTO.getCaseTimes();
		String channelProcessId = otherLossDTO.getIdAhcsChannelProcess();
		String idAhcsOtherLoss = otherLossDTO.getIdAhcsOtherLossDTO();
		/*List<OtherLossDTO> otherLoss = otherLossDao.getOtherLoss(reportNo, caseTimes, otherLossDTO.getTaskCode(), channelProcessId);
		if(!CollectionUtils.isEmpty(otherLoss)){
			otherLoss.forEach(existNotPersonOtherDTO -> {
				otherLossDao.updateEffective(existNotPersonOtherDTO);
			});
		}*/
		otherLossDao.addOtherLoss(otherLossDTO);
	}

	@Override
	public void removeOtherLoss(String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
		otherLossDao.removeOtherLoss(reportNo, caseTimes, taskCode, channelProcessId);
	}

	@Override
	public void updateEffective(OtherLossDTO otherLossDTO) {
		otherLossDao.updateEffective(otherLossDTO);
	}
	public List<OtherLossDTO> getOtherLoss(String reportNo, Integer caseTimes) {
		return otherLossDao.getOtherLossListbyReportNo(reportNo, caseTimes);
	}

}
