package com.paic.ncbs.claim.replevy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 追偿建议表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Getter
@Setter
@TableName("clms_replevy_suggest")
public class ClmsReplevySuggest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 追偿案件号
     */
    @TableField("replevy_no")
    private String replevyNo;

    /**
     * 追偿次数
     */
    @TableField("replevy_times")
    private Integer replevyTimes;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 节点类型
     */
    @TableField("node_type")
    private String nodeType;

    /**
     * 操作人员代码
     */
    @TableField("operator_code")
    private String operatorCode;

    /**
     * 操作人员名称
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 案件处理机构
     */
    @TableField("make_com")
    private String makeCom;

    /**
     * 业务动作
     */
    @TableField("replevy_flag")
    private String replevyFlag;

    /**
     * 建议内容
     */
    @TableField("suggest_text")
    private String suggestText;

    /**
     * 消息类型
     */
    @TableField("message_type")
    private String messageType;

    /**
     * 有效标志
     */
    @TableField("valid_flag")
    private String validFlag;

    /**
     * 标志字段
     */
    @TableField("flag")
    private String flag;

    /**
     * 序号
     */
    @TableField("serial_no")
    private Integer serialNo;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;
}
