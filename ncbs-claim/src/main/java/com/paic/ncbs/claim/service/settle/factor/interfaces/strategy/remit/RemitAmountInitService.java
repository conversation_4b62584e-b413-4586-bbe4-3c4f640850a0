package com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.remit;

import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;

import java.util.List;
import java.util.Map;

/**
 * 免赔额初始化服务
 */
public interface RemitAmountInitService {
  public void initRemitData(String key,String ServiceName, DutyPayDTO duty, Map<String, CalculateAmountService> calServiceImplMap, List<MedicalBillInfoDTO> medicalBillInfoDTOList);
}
