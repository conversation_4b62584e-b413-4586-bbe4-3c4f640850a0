package com.paic.ncbs.claim.service.settle.factor.impl.common;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.settle.factor.EveryDayBillInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyBillLimitDto;
import com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo;
import com.paic.ncbs.claim.service.settle.factor.interfaces.common.AfterBillInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 每月赔付天数处理
 */
@Slf4j
@Order(1)
@Service
public class EveryMonthPayDaysServiceImpl implements AfterBillInfoService {
    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;
    @Autowired
    private DutyDetailPayMapper dutyDetailPayMapper;
    @Override
    public List<EveryDayBillInfoDTO> dealData(List<EveryDayBillInfoDTO> everyDayBillInfoDTOList,
                                              DutyDetailPayDTO detailPayDTO) {
        if (Objects.isNull(detailPayDTO.getConfigPayDays())) {
            return everyDayBillInfoDTOList;
        }
        if (CollectionUtils.isEmpty(everyDayBillInfoDTOList)) {
            return everyDayBillInfoDTOList;
        }
        //设置每一张发票日期所在的合同月
        setMonthValue(everyDayBillInfoDTOList, detailPayDTO);
        //按合同月分组：合同月内的所有发票 的合同月起始时间一致
        Map<Integer, List<EveryDayBillInfoDTO>> mothListMap = everyDayBillInfoDTOList.stream().sorted(Comparator.comparing(EveryDayBillInfoDTO::getMonth)).collect(Collectors.groupingBy(EveryDayBillInfoDTO::getMonth, LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<Integer, List<EveryDayBillInfoDTO>> entry : mothListMap.entrySet()) {
            List<EveryDayBillInfoDTO> dutyattList = entry.getValue();
            PolicyMonthDto policyMonthDto = dutyattList.get(0).getPolicyMonthDto();
            if (entry.getKey() == -1) {
                //不在保单有效期内的发票
                setEffectiveFlagFlag(dutyattList);
                continue;
            }
            //合同月内已赔付的所有发票日期记录
            List<DutyBillLimitDto> dutyBillLimitDtoLists = getDutyLimitMonth(policyMonthDto, detailPayDTO);
            if (CollectionUtils.isEmpty(dutyBillLimitDtoLists)) {
                //合同月内没有查询到赔付记录
                setBillValue(dutyattList, detailPayDTO);//给发票日期打标 是否能参与本次理算
            } else {
                //合同月内查询到了记录
                dealMothData(dutyBillLimitDtoLists, dutyattList, detailPayDTO);
            }

        }
        return everyDayBillInfoDTOList;
    }

    private void setEffectiveFlagFlag(List<EveryDayBillInfoDTO> dutyattList) {
        dutyattList.stream().forEach(everyDayBillInfoDTO -> everyDayBillInfoDTO.setEffectiveFlag("N"));
    }

    private void setMonthValue(List<EveryDayBillInfoDTO> dtoList, DutyDetailPayDTO detail) {
        for (EveryDayBillInfoDTO dto : dtoList) {
            PolicyMonthDto policyMonthDto =  getStartEndDate(dto.getBillDate(),detail.getMonthDtoList());
            if(Objects.isNull(policyMonthDto)){
                dto.setEffectiveFlag("N");
                dto.setMonth(-1);//不存在合同月、、不在保单有效期内
            }else {
                dto.setEffectiveFlag("Y");
                dto.setMonth(policyMonthDto.getMonth());
                dto.setPolicyMonthDto(policyMonthDto);
            }

        }

    }
    /**
     * Match匹配发票日期在哪一个合同月
     * @param billDate
     * @param monthDtoList
     */
    private PolicyMonthDto getStartEndDate(Date billDate, List<PolicyMonthDto> monthDtoList) {

        for (PolicyMonthDto monthDto : monthDtoList) {
            if(billDate.compareTo(monthDto.getStartDate())>=0 && billDate.compareTo(monthDto.getEndDate())<=0){
                return monthDto;
            }
        }
        return null;
    }
    private List<DutyBillLimitDto> getDutyLimitMonth(PolicyMonthDto policyMonthDto,DutyDetailPayDTO detail) {
        DutyLimitQueryVo dutyLimitQueryVo =new DutyLimitQueryVo();
        dutyLimitQueryVo.setPolicyNo(detail.getPolicyNo());
        dutyLimitQueryVo.setPlanCode(detail.getPlanCode());
        dutyLimitQueryVo.setDutyCode(detail.getDutyCode());
        dutyLimitQueryVo.setSatrtDate(policyMonthDto.getStartDate());
        dutyLimitQueryVo.setEndDate(policyMonthDto.getEndDate());
        LogUtil.info("报案号={}，责任查询参数={}",detail.getReportNo(), JsonUtils.toJsonString(dutyLimitQueryVo));
        List<DutyBillLimitDto> dtos= dutyBillLimitInfoMapper.getAllAlreadyPayTimes(dutyLimitQueryVo);
        if(CollectionUtils.isEmpty(dtos)){
            return dtos;
        }
        if(detail.getCaseTimes()>1){
            //重开案件去掉当前报案号对应的发票日期
            dtos= dtos.stream().filter(dutyBillLimitDto -> !Objects.equals(detail.getReportNo(),dutyBillLimitDto.getReportNo())).collect(Collectors.toList());
        }
        //去除金额为0的发票
        dtos= dtos.stream().filter(dutyBillLimitDto -> !(dutyBillLimitDto.getSettleClaimAmount().compareTo(BigDecimal.ZERO) == 0)).collect(Collectors.toList());
        List<DutyBillLimitDto> returnList=new ArrayList<>();
        Map<Date,List<DutyBillLimitDto>>  mothListMap=dtos.stream().sorted(Comparator.comparing(DutyBillLimitDto::getBillDate)).collect(Collectors.groupingBy(DutyBillLimitDto::getBillDate,LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<Date,List<DutyBillLimitDto>> entry : mothListMap.entrySet()){
            List<DutyBillLimitDto> evDayList = entry.getValue();
            List<String> caseNoList=getCaseNoList(evDayList);
            Integer count= dutyDetailPayMapper.getIndmenityInfo(caseNoList,detail.getDutyCode());
            if(count>=1){
                DutyBillLimitDto dto=new DutyBillLimitDto();
                dto.setBillDate(entry.getKey());
                returnList.add(dto);
            }
        }
        return returnList;
    }
    private void setBillValue(List<EveryDayBillInfoDTO> dutyattList, DutyDetailPayDTO detail) {
        //得到每月赔付天数
        Integer configPayDays = detail.getConfigPayDays();
//        Map<Date,List<EveryDayBillInfoDTO>>  dateListMap=dutyattList.stream().sorted(Comparator.comparing(EveryDayBillInfoDTO::getBillDate)).collect(Collectors.groupingBy(EveryDayBillInfoDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
        Map<Date, List<EveryDayBillInfoDTO>> dateListMap = dutyattList.stream()
                .collect(Collectors.groupingBy(
                        EveryDayBillInfoDTO::getBillDate,
                        LinkedHashMap::new,
                        Collectors.toList()
                ))
                .entrySet().stream()
                .sorted((e1, e2) -> {
                    BigDecimal sum2 = e2.getValue().stream()
                            .map(dto -> Optional.ofNullable(dto.getBillAmount()).orElse(BigDecimal.ZERO)
                                    .subtract(Optional.ofNullable(dto.getPrepaidAmount()).orElse(BigDecimal.ZERO))
                                    .subtract(Optional.ofNullable(dto.getImmoderateAmount()).orElse(BigDecimal.ZERO)))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal sum1 = e1.getValue().stream()
                            .map(dto -> Optional.ofNullable(dto.getBillAmount()).orElse(BigDecimal.ZERO)
                                    .subtract(Optional.ofNullable(dto.getPrepaidAmount()).orElse(BigDecimal.ZERO))
                                    .subtract(Optional.ofNullable(dto.getImmoderateAmount()).orElse(BigDecimal.ZERO)))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return sum2.compareTo(sum1);
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldVal, newVal) -> oldVal,
                        LinkedHashMap::new
                ));
        int count=0;
        for (Map.Entry<Date,List<EveryDayBillInfoDTO>> entry : dateListMap.entrySet()) {
            if(count<configPayDays){
                setFlagN(entry.getValue());//发票日的数据可以参与计算
                count++;
            }else{
                setFlagY(entry.getValue());
            }
        }
    }
    /**
     * 合同月内已有赔付记录的情况
     * 判段是否已超每月赔付天数
     * @param payBillLimitDtoLists
     * @param dutyattList
     * @param detail
     */
    private void dealMothData(List<DutyBillLimitDto> payBillLimitDtoLists, List<EveryDayBillInfoDTO> dutyattList, DutyDetailPayDTO detail) {
        //按发票日期排序分组
//        Map<Date,List<EveryDayBillInfoDTO>>  dateListMap=dutyattList.stream().sorted(Comparator.comparing(EveryDayBillInfoDTO::getBillDate)).collect(Collectors.groupingBy(EveryDayBillInfoDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
        Map<Date, List<EveryDayBillInfoDTO>> dateListMap = dutyattList.stream()
                .collect(Collectors.groupingBy(
                        EveryDayBillInfoDTO::getBillDate,
                        LinkedHashMap::new,
                        Collectors.toList()
                ))
                .entrySet().stream()
                .sorted((e1, e2) -> {
                    BigDecimal sum2 = e2.getValue().stream()
                            .map(dto -> Optional.ofNullable(dto.getBillAmount()).orElse(BigDecimal.ZERO)
                                    .subtract(Optional.ofNullable(dto.getPrepaidAmount()).orElse(BigDecimal.ZERO))
                                    .subtract(Optional.ofNullable(dto.getImmoderateAmount()).orElse(BigDecimal.ZERO)))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal sum1 = e1.getValue().stream()
                            .map(dto -> Optional.ofNullable(dto.getBillAmount()).orElse(BigDecimal.ZERO)
                                    .subtract(Optional.ofNullable(dto.getPrepaidAmount()).orElse(BigDecimal.ZERO))
                                    .subtract(Optional.ofNullable(dto.getImmoderateAmount()).orElse(BigDecimal.ZERO)))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return sum2.compareTo(sum1);
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldVal, newVal) -> oldVal,
                        LinkedHashMap::new
                ));
        int count =payBillLimitDtoLists.size();//已赔付的天数
        for (Map.Entry<Date,List<EveryDayBillInfoDTO>> entry : dateListMap.entrySet()) {
            List<DutyBillLimitDto> billLimitDtos =  payBillLimitDtoLists.stream().filter(dutyBillLimitDto -> !Objects.equals(entry.getKey(),dutyBillLimitDto.getBillDate())).collect(Collectors.toList());
            //如果不包含当前发票日期的条数都已经大于等于了每月赔付天数，那么当前发票日期的发票就不能参与计算了
            if(billLimitDtos.size()>=detail.getConfigPayDays()){
                setFlagY(entry.getValue());
            }else{
                //过滤看是否包含当前发票日期
                List<DutyBillLimitDto> currentDateDtos =  payBillLimitDtoLists.stream().filter(dutyBillLimitDto -> Objects.equals(entry.getKey(),dutyBillLimitDto.getBillDate())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(currentDateDtos)){
                    //包含当前发票日,当前发票日期可以参与计算
                    if(count<=detail.getConfigPayDays()){
                        setFlagN(entry.getValue());
                    }else{
                        setFlagY(entry.getValue());
                    }

                }else{
                    //不包含当前发票日期
                    if(count<detail.getConfigPayDays()){
                        setFlagN(entry.getValue());
                        count++;
                    }else{
                        setFlagY(entry.getValue());
                    }

                }


            }

        }

    }
    /**
     * 不参与计算的发票标记为Y Y-表示 超每月赔付天数
     * @param list
     */
    private void setFlagY(List<EveryDayBillInfoDTO> list) {
        for (EveryDayBillInfoDTO  dto :list) {
            dto.setExceedMothPayDays("Y");
        }
    }
    /**
     * 参与计算的发票 标记为N
     * @param list
     */
    private void setFlagN(List<EveryDayBillInfoDTO> list) {
        for (EveryDayBillInfoDTO  dto :list) {
            dto.setExceedMothPayDays("N");
        }
    }
    private List<String> getCaseNoList(List<DutyBillLimitDto> evDayList) {
        List<String> caseNolist=new ArrayList<>();
        for (DutyBillLimitDto dto : evDayList) {
            caseNolist.add(dto.getCaseNo());
        }
        return caseNolist;
    }

}
