package com.paic.ncbs.claim.replevy.vo;

import lombok.Data;

@Data
public class ReplevyReqApiVo {
    private String reportNo;
    private String caseTimes;
    private String replevyNo;
    private String replevyTimes;
    //追偿明细主键id
    private String replevyDetailId;
    //费用信息id
    private String replevyChargeId;
    /**
     * 操作标志 C-新增 D-删除 U-修改 V-查看
     */
    private String operateFlag;
    //页面初始化标志 1代表追偿主页面初始化，2代表直接理赔费用初始化，3代表追偿子页面
    //F代表费用审批，Z代表追偿审批
    private String initFlag;
    // 0-录入 1-只读
    private String opinionType;
}
