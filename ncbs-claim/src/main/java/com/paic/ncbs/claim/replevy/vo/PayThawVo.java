package com.paic.ncbs.claim.replevy.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PayThawVo {
    //流水单号，必填,银行流水接口的业务编码
    private String bankTransFlowNo;
    //冻结标记，F-冻结/R-释放
    private String freezeFlag;
    //冻结金额，CNY金额单位元
    private BigDecimal amount;
    //系统来源，理赔：C
    private String systemSource;
    //业务类型，3-理赔
    private String businessType;
    //流水单号+原业务单据号唯一
    private String originBusinessNo;

}
