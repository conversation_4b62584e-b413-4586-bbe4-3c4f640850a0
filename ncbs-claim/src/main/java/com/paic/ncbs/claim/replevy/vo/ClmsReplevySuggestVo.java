package com.paic.ncbs.claim.replevy.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，表clms_replevy_suggest的VO对象<br/>
 * 对应表名：clms_replevy_suggest,备注：追偿建议表
 *
 */
@Data
public class ClmsReplevySuggestVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	private Integer id;
	/** 对应字段：report_no,备注：报案号 */
	private String reportNo;
	/** 对应字段：replevy_no,备注：追偿案件号 */
	private String replevyNo;
	/** 对应字段：replevy_times,备注：追偿次数 */
	private Integer replevyTimes;
	/** 对应字段：case_times,备注：赔付次数 */
	private Integer caseTimes;
	/** 对应字段：node_type,备注：节点类型 */
	private String nodeType;
	/** 对应字段：operator_code,备注：操作人员代码 */
	private String operatorCode;
	/** 对应字段：operator_name,备注：操作人员名称 */
	private String operatorName;
	/** 对应字段：make_com,备注：案件处理机构 */
	private String makeCom;
	/** 对应字段：replevy_flag,备注：业务动作 */
	private String replevyFlag;
	/** 对应字段：suggest_text,备注：建议内容 */
	private String suggestText;
	/** 对应字段：message_type,备注：消息类型 */
	private String messageType;
	/** 对应字段：valid_flag,备注：有效标志 */
	private String validFlag;
	/** 对应字段：flag,备注：标志字段 */
	private String flag;
	/** 对应字段：serial_no,备注：序号 */
	private Integer serialNo;
	/** 对应字段：created_by,备注：创建人 */
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	private Date sysCtime;
	/** 对应字段：updated_by,备注：修改人员 */
	private String updatedBy;
	/** 对应字段：sys_utime,备注：修改时间 */
	private Date sysUtime;

}
