package com.paic.ncbs.claim.controller.ahcs;

import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.service.other.ConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "参数配置")
@RestController
@RequestMapping("/ahcs/do/app/paramterConfigAction")
public class ParamterConfigController extends BaseController {

	@Autowired
	ConfigService configService;


	@ApiOperation("根据参数编码获取配置")
	@RequestMapping(value = "/getValueByCode", method = RequestMethod.GET)
	public ResponseResult<String> getValueByCode(@RequestParam("code") String code) throws GlobalBusinessException {
	    if (StringUtils.isEmptyStr(code)) {
            throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "参数编码不能为空");
        }
	    if("NEED_REGISTER_AUDIT_AMOUNT".equals(code)){
	    	return ResponseResult.success("0");
		}

        return ResponseResult.success(configService.getParamterConfigValueByCode(code));
	}

}
