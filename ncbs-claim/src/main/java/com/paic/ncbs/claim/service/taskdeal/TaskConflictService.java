package com.paic.ncbs.claim.service.taskdeal;

import com.baomidou.mybatisplus.extension.service.IService;
import com.paic.ncbs.claim.model.dto.taskdeal.ClmsTaskConflictDTO;

import java.util.List;

/**
 * <p>
 * 流程任务控制规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
public interface TaskConflictService extends IService<ClmsTaskConflictDTO> {

    public List<ClmsTaskConflictDTO> findConflictByBpmKeyAndOpr(String bpmKey, String operation);

}
