package com.paic.ncbs.claim.service.settle.factor.impl.modelinit;


import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonBenefitMapper;
import com.paic.ncbs.claim.model.dto.duty.PersonBenefitDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.modelinit.ModelDataInitializeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 津贴信息初始查询
 */
@Slf4j
@Order(3)
@Service
public class ModelPersonBenefitInitializeServiceImpl implements ModelDataInitializeService {

    @Autowired
    private PersonBenefitMapper personBenefitMapper;

    @Override
    public ClaimCaseDTO initialize(ClaimCaseDTO claimCaseDTO) {
        List<PersonBenefitDTO> benefitList = personBenefitMapper.getPersonBenefit(claimCaseDTO.getReportNo(), claimCaseDTO.getCaseTimes(), BpmConstants.CHECK_DUTY,null);
        for (PersonBenefitDTO personBenefitDTO : benefitList) {
            personBenefitDTO.setBenefitTypeName(SettleConst.BENEFIT_MAPPING_MAP.get(personBenefitDTO.getBenefitType()));
        }
        claimCaseDTO.setPersonBenefitDTOList(benefitList);
        return claimCaseDTO;
    }


}
