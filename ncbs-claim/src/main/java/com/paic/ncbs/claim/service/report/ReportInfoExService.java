package com.paic.ncbs.claim.service.report;

import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.service.base.BaseService;

import java.util.List;

public interface ReportInfoExService extends BaseService<ReportInfoExEntity> {

    List<ReportInfoExEntity> getReportInfoEx(String reportNo);

    int updataReportInfoExByReportNo(ReportInfoExEntity reportInfoExEntity);

    List<ReportInfoExEntity> getReportInfoExByAcceptanceNo(String acceptanceNo);

    void updateCaseClassByReportNo(String reportNo, String lossClass,String loginUm);



}
