package com.paic.ncbs.claim.service.endcase.impl;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseSupervisionInfoMapper;
import com.paic.ncbs.claim.model.dto.endcase.CaseSupervisionInfoDTO;
import com.paic.ncbs.claim.service.endcase.CaseSupervisionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;


@Service(value = "caseSupervisionService")
public class CaseSupervisionServiceImpl implements CaseSupervisionService {

    @Autowired
    private CaseSupervisionInfoMapper caseSupervisionInfoMapper;

    @Override
    public void insertCaseSupervisionInfo(CaseSupervisionInfoDTO caseSupervisionInfoDTO) throws GlobalBusinessException {
        caseSupervisionInfoDTO.setCreatedBy(Optional.ofNullable(caseSupervisionInfoDTO.getCreatedBy()).orElseGet(() ->"SYSTEM"));
        caseSupervisionInfoDTO.setUpdatedBy(Optional.ofNullable(caseSupervisionInfoDTO.getUpdatedBy()).orElseGet(() ->"SYSTEM"));
        caseSupervisionInfoMapper.insert(caseSupervisionInfoDTO);
    }






}
