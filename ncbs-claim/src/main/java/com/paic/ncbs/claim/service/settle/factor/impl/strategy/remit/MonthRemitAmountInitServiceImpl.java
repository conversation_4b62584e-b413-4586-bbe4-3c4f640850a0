package com.paic.ncbs.claim.service.settle.factor.impl.strategy.remit;

import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.remit.RemitAmountInitService;

import java.util.List;
import java.util.Map;

/**
 * 暂无 此业务
 */
public class MonthRemitAmountInitServiceImpl implements RemitAmountInitService {
    @Override
    public void initRemitData(String key,String serviceName, DutyPayDTO duty, Map<String, CalculateAmountService> calServiceImplMap, List<MedicalBillInfoDTO> medicalBillInfoDTOList) {

    }
}
