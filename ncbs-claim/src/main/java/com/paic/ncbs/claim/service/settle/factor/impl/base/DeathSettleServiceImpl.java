package com.paic.ncbs.claim.service.settle.factor.impl.base;

import com.googlecode.aviator.Expression;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.DetailSettleReasonTemplateDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.base.BaseSettleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * 身故
 */
@Slf4j
@Service
public class DeathSettleServiceImpl implements BaseSettleService {
    @Override
    public void getSettleAmount(ClaimCaseDTO claimCaseDTO, DutyDetailPayDTO detailPayDTO, Expression expression) {
        BigDecimal autoSettleAmount =nvl(detailPayDTO.getMaxAmountPay(),0);
        detailPayDTO.setAutoSettleAmount(autoSettleAmount);
        log.info("责任明细分类为身故的理算报案号={}，理算金额={}",claimCaseDTO.getReportNo(),autoSettleAmount);
        DetailSettleReasonTemplateDTO detailSettleReasonTemplateDTO =new DetailSettleReasonTemplateDTO();
        detailSettleReasonTemplateDTO.setDutyDetailName(detailPayDTO.getDutyDetailName());
        detailSettleReasonTemplateDTO.setAutoSettleAmount(BigDecimalUtils.toString(autoSettleAmount));
        detailSettleReasonTemplateDTO.setCalReason(BigDecimalUtils.toString(autoSettleAmount));
        detailSettleReasonTemplateDTO.setCalFormulaData(SettleConst.SETTLE_REASON.get(detailPayDTO.getDutyDetailType()));
        detailPayDTO.setDetailSettleReasonTemplateDTO(detailSettleReasonTemplateDTO);
    }
}
