package com.paic.ncbs.claim.controller.duty;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.checkloss.DisabilityStandardVO;
import com.paic.ncbs.claim.service.checkloss.DisabilityStandardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "伤残标准")
@Controller
@RequestMapping("/duty/app/disabilityStandardAction")
public class DisabilityStandardController extends BaseController {
	
	@Resource(name="disabilityStandardService")
	private DisabilityStandardService disabilityStandardService;


	@ApiOperation("获取伤残条款/信息")
	@ResponseBody
	@RequestMapping(value="/getDisabilityStandardList", method=RequestMethod.GET)
	public ResponseResult<List<DisabilityStandardVO>> getDisabilityStandardList() throws GlobalBusinessException {
		LogUtil.audit("获取伤残条款/信息");

		List<DisabilityStandardVO> list =  disabilityStandardService.getDisabilityStandardList();
		return ResponseResult.success(list);
	}

}
