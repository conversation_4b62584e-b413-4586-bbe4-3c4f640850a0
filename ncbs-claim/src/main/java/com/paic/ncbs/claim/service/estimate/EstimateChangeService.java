package com.paic.ncbs.claim.service.estimate;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePolicyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.vo.estimate.EstimateChangeApplyVO;
import com.paic.ncbs.claim.model.vo.estimate.EstimateChangeVo;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface EstimateChangeService {

	/**
	 * 查询所有未决修正记录
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	List<EstimateChangeDTO> getAllEstimateChangeList(String reportNo, Integer caseTimes);

	/**
	 * 查询最新未决修正记录
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	List<EstimateChangeDTO> getLastEstimateChangeList(String reportNo, Integer caseTimes);

	/**
	 * 修正未决
	 * @param estimateChangeList
	 */
	void addEstimateChangeList(List<EstimateChangeDTO> estimateChangeList,List<EstimateDutyRecordDTO> dutyRecordDTOList);

	/**
	 * 失效未决修正记录
	 * @param estimateChangeDTO
	 */
	void deleteEstimateChange(EstimateChangeDTO estimateChangeDTO);

	/**
	 * 获取案件关联保单立案金额，select from clms_estimate_policy
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	List<EstimateChangeDTO> getPolicyRegisterAmount(String reportNo, Integer caseTimes);

	@Transactional
	String estimateChangeApply(EstimateChangePolicyFormDTO estimateChangePolicyForm) throws Exception;

	/**
	 * 未决修改责任层级
	 * @param estimateChangePolicyForm
	 * @return
	 */
	String estimateChange(EstimateChangePolicyFormDTO estimateChangePolicyForm);

	List<EstimateChangePolicyDTO> getApplyEstimatePolicyList(String reportNo, Integer caseTimes) throws Exception;

	EstimateChangeApplyVO getAuditEstimateChangeApplyVO(String reportNo, Integer caseTimes) throws Exception;

	List<EstimateChangeApplyVO> getEstimateChangeApplyVOList(String reportNo, Integer caseTimes) throws Exception;

	List<EstimateChangeApplyVO> getEstimateChangeApplyById(String id) throws Exception;

    List<String> sendAuditEstimateChangeApply(EstimateChangeApplyVO estimateChangeApplyVO) throws Exception;

    boolean checkEstimateChangePending(String reportNo, Integer caseTimes) throws GlobalBusinessException;

	EstimateChangeVo getEstimateChangeVo(String reportNo, int caseTimes, String idFlagHistoryChange, String isEstimate);
}
