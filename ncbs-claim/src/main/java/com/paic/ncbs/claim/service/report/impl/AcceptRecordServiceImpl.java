package com.paic.ncbs.claim.service.report.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.report.AcceptRecordDTO;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.report.AcceptRecordMapper;
import com.paic.ncbs.claim.service.report.AcceptRecordService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class AcceptRecordServiceImpl implements AcceptRecordService {

    @Autowired
    private AcceptRecordMapper acceptRecordDao;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private CaseProcessService caseProcessService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AcceptRecordDTO addAcceptRecord(AcceptRecordDTO acceptRecordDTO) throws GlobalBusinessException {

        String caseCurrStatus = wholeCaseBaseService.getWholeCaseStatus(acceptRecordDTO.getReportNo(), acceptRecordDTO.getCaseTimes());


        if (ConfigConstValues.WHOLE_CASE_STATUS_END.equals(caseCurrStatus)) {
            throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "案件已结案,不能发送收单");
        }

        LogUtil.audit("保存收单dto={}", JSON.toJSONString(acceptRecordDTO));
        String reportNo = acceptRecordDTO.getReportNo();
        Integer caseTimes = acceptRecordDTO.getCaseTimes();
        AcceptRecordDTO acceptRecord = acceptRecordDao.getAcceptRecord(reportNo, caseTimes);
        if (acceptRecord != null) {
            throw new GlobalBusinessException(ErrorCode.Record.EXSITES_ACCEPT_RECORD);
        }
        List<AcceptRecordDTO> acceptRecordList = this.getAcceptRecordByReportNo(reportNo, caseTimes);
        String userId = acceptRecordDTO.getReceiveVoucherUm();
        acceptRecordDTO.setUpdatedBy(userId);

        acceptRecordDTO.setDepartmentCode(WebServletContext.getDepartmentCode());
        boolean isNewProcess = caseProcessService.getIsNewProcess(acceptRecordDTO.getReportNo(), acceptRecordDTO.getCaseTimes());


        if (ListUtils.isEmptyList(acceptRecordList)) {
            acceptRecordDTO.setCreatedBy(userId);
            acceptRecordDTO.setIdAhcsAcceptRecord(UuidUtil.getUUID());
            Date date = null;
            if (isNewProcess) {
                date = wholeCaseBaseService.getReportDate(acceptRecordDTO.getReportNo());
            } else {

                date = new Date();
            }
            acceptRecordDTO.setClaimantApplyDate(date);
            acceptRecordDTO.setDisposableToldDate(date);
            acceptRecordDao.insertAcceptRecord(acceptRecordDTO);
        } else {
            Date date = new Date();
            if (acceptRecordList.get(0).getClaimantApplyDate() == null) {
                acceptRecordDTO.setClaimantApplyDate(date);
            }
            acceptRecordDTO.setReceiveVoucherDate(date);
            acceptRecordDTO.setClaimdocRedayDate(date);
            if (!isNewProcess) {
                acceptRecordDTO.setDisposableToldDate(date);
            }
            acceptRecordDao.modifyAcceptRecordSuffice(acceptRecordDTO);
        }
//        acceptRecordDTO = this.completeTask(acceptRecordDTO, departmentCode);
//        trackIntoService.removeTrackTaskProducer(reportNo, caseTimes, TacheNameConstants.COUNTERTOP_ACCEPT);
        return acceptRecordDTO;
    }

    @Override
    public AcceptRecordDTO getAcceptRecordByAsc(String reportNo, Integer caseTimes) {
        return acceptRecordDao.getAcceptRecordByAsc(reportNo, caseTimes);
    }

    @Override
    public AcceptRecordDTO getAcceptRecord(String reportNo, Integer caseTimes) {
        return acceptRecordDao.getAcceptRecord(reportNo, caseTimes);
    }

    @Override
    public List<AcceptRecordDTO> getAcceptRecordByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        return acceptRecordDao.getAcceptRecordByReportNo(reportNo, caseTimes);
    }

}
