package com.paic.ncbs.claim.controller.dynamic;


import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.dynamic.DynamicFieldResultVO;
import com.paic.ncbs.claim.service.dynamic.IDynamicFieldResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 动态字段服务
 */
@RestController
@RequestMapping("/app/dynamic")
public class DynamicFieldController {

    @Autowired
    private IDynamicFieldResultService dynamicFieldResultService;

    @RequestMapping(value = "/getDynamicFieldResult",produces = {"application/json"},method = RequestMethod.POST)
    public ResponseResult<List<DynamicFieldResultVO.FieldDetailVo>> getDynamicFieldResult(@RequestBody DynamicFieldResultVO dynamicFieldResultVO) throws Exception {
        List<DynamicFieldResultVO.FieldDetailVo> dynamicFieldResult = dynamicFieldResultService.getDynamicFieldResult(dynamicFieldResultVO);
        return ResponseResult.success(dynamicFieldResult);
    }
}
