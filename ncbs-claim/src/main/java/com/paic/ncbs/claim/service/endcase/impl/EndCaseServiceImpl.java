package com.paic.ncbs.claim.service.endcase.impl;

import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.EndCaseConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.IndemnityConclusionEnum;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.EndCaseMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.service.casezero.CaseZeroCancelService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.EndCaseService;
import com.paic.ncbs.claim.service.indicator.CaseIndicatorService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyPayService;
import com.paic.ncbs.claim.service.settle.MedicalBillService;
import com.paic.ncbs.claim.service.settle.WesureSettleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;



@Service("endCaseService")
public class EndCaseServiceImpl implements EndCaseService {


    @Autowired
    @Lazy
    MedicalBillService medicalBillService;

    @Autowired
    EndCaseMapper endCaseDao;
    @Autowired
    CaseBaseMapper caseBaseDao;
    @Autowired
    @Lazy
    CaseZeroCancelService caseZeroCancelService;
    @Autowired
    @Lazy
    CaseProcessService caseProcessService;

    @Resource(name = "wesureSettleService")
    private WesureSettleService wesureSettleService;
    @Resource(name = "riskPropertyPayService")
    private RiskPropertyPayService riskPropertyPayService;

    @Autowired
    private CaseIndicatorService caseIndicatorService;

    @Override
    public void endCase(WholeCaseBaseDTO wholeCaseBaseDTO){
        LogUtil.audit("#结案更新整案表跟保单赔案表数据reportNo={}",wholeCaseBaseDTO.getReportNo());
        wholeCaseBaseDTO.setWholeCaseStatus(EndCaseConstValues.WHOLE_CASE_END_STATUS);
        wholeCaseBaseDTO.setEndCaseDate(new Date());
        endCaseDao.modifyWholeCase(wholeCaseBaseDTO);
        caseBaseDao.batchModifyCaseBaseDTO(wholeCaseBaseDTO);

        generateWesureVerify(wholeCaseBaseDTO);

        // 计算结案时效
        this.caseIndicatorService.calCaseSettleIndicator(wholeCaseBaseDTO.getReportNo(),wholeCaseBaseDTO.getCaseTimes());

        /*try {
            riskPropertyPayService.saveRiskPropertyPay(wholeCaseBaseDTO);
        }catch (Exception e){
            LogUtil.info("结案保存标的赔付失败,不影响原有流程",e);
        }*/
    }

    @Override
    public void batchModifyCaseBaseDTO(WholeCaseBaseDTO wholeCaseBaseDTO) {
        caseBaseDao.batchModifyCaseBaseDTO(wholeCaseBaseDTO);
    }

    @Override
    public void endCaseZeroCancel(String reportNo, Integer caseTimes, String applyType) throws GlobalBusinessException {
        CaseZeroCancelDTO caseZeroCancelDTO = caseZeroCancelService.getLastZeroCancelInfo(reportNo, caseTimes);
        caseZeroCancelDTO.setApplyType(applyType);
        handleZeroCancelConclusion(reportNo, caseTimes, caseZeroCancelDTO);
        updateCaseProcessData(reportNo, caseTimes, caseZeroCancelDTO);
    }

    private void handleZeroCancelConclusion(String reportNo, Integer caseTimes, CaseZeroCancelDTO caseZeroCancelDTO)
            throws GlobalBusinessException{
        LogUtil.audit("#整案结案,入参 reportNo={},caseTimes={}#", reportNo, caseTimes);
        WholeCaseBaseDTO wholeCaseBaseDTO = endCaseDao.getWholeCaseBaseDTO(reportNo, caseTimes);
        String status = wholeCaseBaseDTO.getWholeCaseStatus();
        if(EndCaseConstValues.WHOLE_CASE_END_STATUS.equals(status)){
            String endDate = "";
            if(wholeCaseBaseDTO.getEndCaseDate() != null){
                Date date = new Date();
                endDate = new SimpleDateFormat(DateUtils.FULL_RULE_DATE_STR).format(date);
            }
            LogUtil.audit("#案件已经结案, reportNo={},caseTimes={}, 结案时间={}#", reportNo, caseTimes, endDate);
            throw new GlobalBusinessException(ErrorCode.EndCase.ERROR_CANCEL_CHECK_CASE_IS_END);
        }
        //增加报案注销
        String conclusion = EndCaseConstValues.APPLY_TYPE_CANCEL.equals(caseZeroCancelDTO.getApplyType())?
                (StringUtils.isNotEmpty(caseZeroCancelDTO.getCancelType()) ? caseZeroCancelDTO.getCancelType() : IndemnityConclusionEnum.INDEMNITY_CONCLUSION_ENUM_FIVE.getCode())
                :IndemnityConclusionEnum.INDEMNITY_CONCLUSION_ENUM_TWO.getCode();
        String caseFinisher = caseZeroCancelDTO.getVerifyUm() == null ? BaseConstant.SYSTEM : caseZeroCancelDTO.getVerifyUm();
        wholeCaseBaseDTO.setCaseFinisherUm(caseFinisher);
        wholeCaseBaseDTO.setUpdatedBy(caseFinisher);
        wholeCaseBaseDTO.setIndemnityConclusion(conclusion);
        wholeCaseBaseDTO.setIndemnityModel("");
        if(EndCaseConstValues.CASE_CANCEL.equals(conclusion)){
            wholeCaseBaseDTO.setCaseCancelReason(caseZeroCancelDTO.getVerifyRemark());
        }
        LogUtil.audit("#整案结案,更新案件结案状态,入参 reportNo={},caseTimes={}#", reportNo, caseTimes);
        this.endCase(wholeCaseBaseDTO);
    }

    private void updateCaseProcessData(String reportNo, Integer caseTimes, CaseZeroCancelDTO caseZeroCancelDTO) throws GlobalBusinessException{
        LogUtil.audit("#零注更新流程状态表信息#入参#reportNo={},caseTimes={}",reportNo,caseTimes);
        CaseProcessDTO caseProcessDTO = caseProcessService.getCaseCommissionInfo(reportNo, caseTimes);
        if(caseProcessDTO != null){
            String status = EndCaseConstValues.APPLY_TYPE_CANCEL.equals(caseZeroCancelDTO.getApplyType())?
                    CaseProcessStatus.CASE_CANCELLED.getCode() :CaseProcessStatus.CASE_CLOSED.getCode();
            caseProcessDTO.setProcessStatus(status);
            caseProcessService.updateCaseProcessByReportNo(caseProcessDTO);
        }
        LogUtil.audit("#零注更新流程状态表信息#结束#");

    }

    @Async("asyncPool")
    public void generateWesureVerify(WholeCaseBaseDTO wholeCaseBaseDTO){
        wesureSettleService.generateWesureVerify(wholeCaseBaseDTO);
    }

}
