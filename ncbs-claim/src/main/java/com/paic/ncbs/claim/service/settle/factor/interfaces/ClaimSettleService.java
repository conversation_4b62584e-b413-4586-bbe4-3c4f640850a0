package com.paic.ncbs.claim.service.settle.factor.interfaces;

import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.vo.settle.SettleSelectVO;
import org.springframework.transaction.annotation.Transactional;

/**
 * 理赔理算
 */
public interface ClaimSettleService {
    public ClaimCaseDTO settle(String reportNo, Integer caseTimes);

    @Transactional
    ClaimCaseDTO settleInit(String reportNo, Integer caseTimes);

    @Transactional
    ClaimCaseDTO reSettleInit(String reportNo, Integer caseTimes);

    /**
     * 重新理算
     * @param reportNo
     * @param caseTimes
     * @return
     */
    public ClaimCaseDTO reSettle(String reportNo, Integer caseTimes);

    /**
     * 单责任明细理算
     * @param settleSelectVO
     * @return
     */
    ClaimCaseDTO settleSelect(SettleSelectVO settleSelectVO);
}
