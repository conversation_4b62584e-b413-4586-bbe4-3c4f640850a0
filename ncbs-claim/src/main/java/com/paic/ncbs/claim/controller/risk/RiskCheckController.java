package com.paic.ncbs.claim.controller.risk;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.service.risk.RiskCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "风险检查控制类")
@RestController
@RequestMapping("/risk")
@Validated
public class RiskCheckController {

    @Autowired
    private RiskCheckService riskCheckService;

    @GetMapping("checkCustomerClaimRisk")
    @ApiOperation("查询客户理赔风险")
    public ResponseResult<Object> checkCustomerClaimRisk(@RequestParam("reportNo")String reportNo){
        return ResponseResult.success(riskCheckService.checkCustomerClaimRisk(reportNo));
    }
}
