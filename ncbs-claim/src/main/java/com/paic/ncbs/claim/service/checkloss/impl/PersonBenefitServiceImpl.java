package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.model.dto.duty.PersonBenefitDTO;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonBenefitMapper;
import com.paic.ncbs.claim.model.dto.endcase.CaseInfoParameterDTO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.checkloss.ChannelProcessService;
import com.paic.ncbs.claim.service.checkloss.PersonBenefitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
public class PersonBenefitServiceImpl implements PersonBenefitService {

	@Autowired
    PersonBenefitMapper personBenefitDao;

	@Autowired
	ChannelProcessService channelProcessService;

	@Autowired
	private AhcsCommonService ahcsCommonService;

	@Override
	@Transactional
	public void savePersonBenefit(List<List<PersonBenefitDTO>> personBenefitList, String reportNo, Integer caseTimes,
                                  String channelId, String loginUm, String taskId, String status) {
		List<PersonBenefitDTO> addPersonBenefitList = new ArrayList<>();
		if(!ListUtils.isEmptyList(personBenefitList)){
			Integer orderNo = 1;						for(List<PersonBenefitDTO> tmpPersonBenefitList : personBenefitList){
				for(PersonBenefitDTO tmpPersonBenefitDTO : tmpPersonBenefitList){
					tmpPersonBenefitDTO.setReportNo(reportNo);
					tmpPersonBenefitDTO.setCaseTimes(caseTimes);
					tmpPersonBenefitDTO.setIdAhcsChannelProcess(channelId);
					tmpPersonBenefitDTO.setCreatedBy(loginUm);
					tmpPersonBenefitDTO.setUpdatedBy(loginUm);
					tmpPersonBenefitDTO.setTaskId(taskId);
					tmpPersonBenefitDTO.setOrderNo(orderNo++);
					tmpPersonBenefitDTO.setStatus(status);
				}
				addPersonBenefitList.addAll(tmpPersonBenefitList);
			}
		}
		List<PersonBenefitDTO> existPersonBenefitList = personBenefitDao.getPersonBenefit(reportNo, caseTimes, taskId, channelId);
		if(ListUtils.isNotEmpty(existPersonBenefitList)){
			personBenefitDao.removePersonBenefit(reportNo, caseTimes, taskId, channelId);
		}
		if(ListUtils.isNotEmpty(addPersonBenefitList)){
			ahcsCommonService.batchHandlerTransactionalWithArgs(PersonBenefitMapper.class, addPersonBenefitList, ListUtils.GROUP_NUM, "addPersonBenefit");
		}
	}

	@Override
	public void removePersonBenefit(String reportNo, Integer caseTimes, String taskId, String channelId) {
		personBenefitDao.removePersonBenefit(reportNo, caseTimes, taskId, channelId);
	}

	@Override
	public Map<String, Object> getPersonBenefit(String reportNo, Integer caseTimes, String taskId, String channelProcessId) {
		Map<String,Object> retMap = new HashMap<>();
		List<List<PersonBenefitDTO>> rtnPersonBenefitList = new ArrayList<>();
		List<String> benefitTypes = new ArrayList<>();
		List<PersonBenefitDTO> personBenefitList = personBenefitDao.getPersonBenefit(reportNo, caseTimes, taskId, channelProcessId);
		Map<String,List<PersonBenefitDTO>> personBenefitMap = new LinkedHashMap<>();
		if(ListUtils.isNotEmpty(personBenefitList)){
			for(PersonBenefitDTO personBenefitDTO : personBenefitList){
				String benefitType = personBenefitDTO.getBenefitType();
				List<PersonBenefitDTO> innerList = personBenefitMap.get(benefitType);
				if(ListUtils.isEmptyList(innerList)){
					innerList = new ArrayList<>();
					innerList.add(personBenefitDTO);
					personBenefitMap.put(benefitType, innerList);
					benefitTypes.add(benefitType);
				}else{
					innerList.add(personBenefitDTO);
				}
			}
			for(List<PersonBenefitDTO> innerList : personBenefitMap.values()){
				rtnPersonBenefitList.add(innerList);
			}
		}
		retMap.put("benefitTypes", benefitTypes);
		retMap.put("personBenefitList", rtnPersonBenefitList);
		return retMap;
	}

	@Override
	public List<PersonBenefitDTO> getPersonBenefitByType(String reportNo, Integer caseTimes, String benefitType, String taskId) {
		return personBenefitDao.getPersonBenefitByType(reportNo, caseTimes, benefitType, taskId);
	}

	@Override
	public List<PersonBenefitDTO> getPersonBenefitByTypes(String reportNo, Integer caseTimes, List<String> benefitTypes, String taskId, String idAhcsChannelProcess) {
		return personBenefitDao.getPersonBenefitByTypes(reportNo, caseTimes, benefitTypes, taskId, idAhcsChannelProcess);
	}

	@Override
	@Transactional
	public void saveBenefitDays(BigDecimal hospitalDays, String reportNo, Integer caseTimes, String loginUm, String taskId, String status) {
		List<PersonBenefitDTO> personBenefitList = new ArrayList<>();
		PersonBenefitDTO personBenefitDTO = new PersonBenefitDTO ();
		personBenefitDTO.setReportNo(reportNo);
		personBenefitDTO.setCaseTimes(caseTimes);
		String channelId = this.getChannelProcessId(reportNo, caseTimes, loginUm);
		personBenefitDTO.setIdAhcsChannelProcess(channelId);
		personBenefitDTO.setCreatedBy(loginUm);
		personBenefitDTO.setUpdatedBy(loginUm);
		personBenefitDTO.setTaskId(taskId);
		personBenefitDTO.setOrderNo(1);
		personBenefitDTO.setBenefitType(SettleConst.BENEFIT_CODE_01);
		personBenefitDTO.setHospitalDays(hospitalDays);
		personBenefitDTO.setStatus(status);
		personBenefitList.add(personBenefitDTO);


		List<PersonBenefitDTO> existPersonBenefitList = personBenefitDao.getPersonBenefit(reportNo, caseTimes, taskId, channelId);
		if(ListUtils.isNotEmpty(existPersonBenefitList)){
			personBenefitDao.removePersonBenefit(reportNo, caseTimes, taskId, channelId);
		}
		if(ListUtils.isNotEmpty(personBenefitList)){
			ahcsCommonService.batchHandlerTransactionalWithArgs(PersonBenefitMapper.class, personBenefitList, ListUtils.GROUP_NUM, "addPersonBenefit");
		}
	}


	private String getChannelProcessId(String reportNo, Integer caseTimes, String userId) {
		CaseInfoParameterDTO caseInfoParameter = new CaseInfoParameterDTO();
		caseInfoParameter.setReportNo(reportNo);
		caseInfoParameter.setCaseTimes(caseTimes);
		caseInfoParameter.setChannelType(ChecklossConst.CASECLASS_PEOPLE_HURT);
		caseInfoParameter.setUserId(userId);
		return channelProcessService.getChannelProcessId(caseInfoParameter);
	}

}
