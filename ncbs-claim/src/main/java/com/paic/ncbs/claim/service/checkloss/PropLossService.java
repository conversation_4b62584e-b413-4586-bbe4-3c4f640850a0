package com.paic.ncbs.claim.service.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PropLossDTO;

import java.util.List;

public interface PropLossService{

    /**
     * 插入数据
     * @param propLossDTO
     */
    void insert(PropLossDTO propLossDTO);

    /**
     * 根据案件号查询财产损失信息
     * @param reportNo
     * @param caseTimes
     * @return
     */
    PropLossDTO queryByReportNo(String reportNo, Integer caseTimes);

    /**
     * 根据条件删除
     * @param reportNo
     * @param caseTimes
     */
    void deleteByCondition(String reportNo, int caseTimes);
}
