package com.paic.ncbs.claim.model.vo.communicate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "沟通明细表VO")
public class CommunicateDetailVO extends EntityDTO {

	private static final long serialVersionUID = 5988074294637550279L;

	@ApiModelProperty(value = "ahcs_communicate_detail表主键")
	private String idAhcsCommunicateDetail;

    @ApiModelProperty(value = "AHCS_COMMUNICATE_BASE表主键")
	private String idAhcsCommunicateBase;

    @ApiModelProperty(value = "发起人UM")
	private String initiatorUm;


	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	@ApiModelProperty(value = "发起时间")
	private Date initiatDate;

    @ApiModelProperty(value = "处理人UM")
	private String dealUm;


	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	@ApiModelProperty(value = "处理时间")
	private Date dealDate;

	@ApiModelProperty(value = "角色（0-沟通人，1-发起人）")
	private String role;

	@ApiModelProperty(value = "沟通内容")
	private String communicateContent;

	@ApiModelProperty(value = "任务状态(0-待处理，1-已处理)")
	private String taskStatus;

	public BigDecimal getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

	@ApiModelProperty(value = "沟通赔款金额")
	private BigDecimal payAmount;

	public String getIdAhcsCommunicateDetail() {
		return idAhcsCommunicateDetail;
	}

	public void setIdAhcsCommunicateDetail(String idAhcsCommunicateDetail) {
		this.idAhcsCommunicateDetail = idAhcsCommunicateDetail;
	}

	public String getIdAhcsCommunicateBase() {
		return idAhcsCommunicateBase;
	}

	public void setIdAhcsCommunicateBase(String idAhcsCommunicateBase) {
		this.idAhcsCommunicateBase = idAhcsCommunicateBase;
	}

	public String getInitiatorUm() {
		return initiatorUm;
	}

	public void setInitiatorUm(String initiatorUm) {
		this.initiatorUm = initiatorUm;
	}

	public String getDealUm() {
		return dealUm;
	}

	public void setDealUm(String dealUm) {
		this.dealUm = dealUm;
	}

	public Date getInitiatDate() {
		return initiatDate;
	}

	public void setInitiatDate(Date initiatDate) {
		this.initiatDate = initiatDate;
	}

	public Date getDealDate() {
		return dealDate;
	}

	public void setDealDate(Date dealDate) {
		this.dealDate = dealDate;
	}

	public String getRole() {
		return role;
	}

	public void setRole(String role) {
		this.role = role;
	}

	public String getCommunicateContent() {
		return communicateContent;
	}

	public void setCommunicateContent(String communicateContent) {
		this.communicateContent = communicateContent;
	}

	public String getTaskStatus() {
		return taskStatus;
	}

	public void setTaskStatus(String taskStatus) {
		this.taskStatus = taskStatus;
	}

}
