package com.paic.ncbs.claim.replevy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 追偿信息主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Getter
@Setter
@TableName("clms_replevy_main")
public class ClmsReplevyMain implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 立案号
     */
    @TableField("claim_no")
    private String claimNo;
    /**
     * 赔案号
     */
    @TableField("case_no")
    private String caseNo;

    /**
     * 保单号
     */
    @TableField("policy_no")
    private String policyNo;

    /**
     * 追偿案件号
     */
    @TableField("replevy_no")
    private String replevyNo;

    /**
     * 险种
     */
    @TableField("risk_code")
    private String riskCode;

    /**
     * 追偿次数
     */
    @TableField("replevy_times")
    private Integer replevyTimes;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 币别
     */
    @TableField("replevy_currency")
    private String replevyCurrency;

    /**
     * 计划追偿费用
     */
    @TableField("sum_plan_replevy")
    private BigDecimal sumPlanReplevy;

    /**
     * 追偿总收入
     */
    @TableField("sum_real_replevy")
    private BigDecimal sumRealReplevy;

    /**
     * 追偿总费用
     */
    @TableField("sum_replevy_fee")
    private BigDecimal sumReplevyFee;

    /**
     * 追偿意见
     */
    @TableField("replevy_opinion")
    private String replevyOpinion;

    /**
     * 意见说明
     */
    @TableField("opinion_text")
    private String opinionText;

    /**
     * 追偿描述
     */
    @TableField("replevy_text")
    private String replevyText;

    /**
     * 取消日期
     */
    @TableField("cancel_date")
    private LocalDateTime cancelDate;

    /**
     * 取消原因
     */
    @TableField("cancel_reason")
    private String cancelReason;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 归属机构
     */
    @TableField("make_com")
    private String makeCom;

    /**
     * 机构代码
     */
    @TableField("com_code")
    private String comCode;

    /**
     * 有效标志
     */
    @TableField("valid_flag")
    private String validFlag;

    /**
     * 标志字段 0-追偿处理中 1-追偿待审核 2-追偿已完成
     */
    @TableField("flag")
    private String flag;

    /**
     * 处理人代码
     */
    @TableField("handler_code")
    private String handlerCode;

    /**
     * 序号
     */
    @TableField("serial_no")
    private Integer serialNo;

    /**
     * 理算号
     */
    @TableField("compensate_no")
    private String compensateNo;

    /**
     * 审核状态1-已申请，2-待审核，3-审核通过，4-退回
     */
    @TableField("approve_flag")
    private String approveFlag;
    /**
     * 结束时间
     */
    @TableField("finish_date")
    private Date finishDate;
    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;
    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;
}
