package com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate;

import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.RemitAmountDTO;

/**
 * 计算实现接口
 */
public abstract class CalculateAmountService {
    /**
     * 初始化构建免赔额
     */
    public void initBuild(RemitAmountDTO remitAmountDTO){
        return ;
    }
    /**
     * 合理费用计算
     * @return
     */
    public abstract  void calculate(CalculateParamsDTO paramsDTO);

}
