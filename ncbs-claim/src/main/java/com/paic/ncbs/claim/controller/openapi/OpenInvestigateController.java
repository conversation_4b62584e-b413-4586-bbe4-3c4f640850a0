package com.paic.ncbs.claim.controller.openapi;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.investigate.OuterInvestigateResultDTO;
import com.paic.ncbs.claim.service.investigate.OuterInvestigateService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "调查对外")
@RestController
@RequestMapping("/public/investigate")
public class OpenInvestigateController {    
	
	@Autowired
	private OuterInvestigateService outerInvestigateService;

    @ApiOperation(value = "完成外部调查任务")
    @PostMapping(value = "/finishTask")
    public ResponseResult<Object> finishTask(@RequestBody OuterInvestigateResultDTO outerInvestigateResult) throws GlobalBusinessException {
    	LogUtil.audit("完成外部调查任务入参{}",JSON.toJSONString(outerInvestigateResult));
        return ResponseResult.success(outerInvestigateService.finishTask(outerInvestigateResult));
    }

}
