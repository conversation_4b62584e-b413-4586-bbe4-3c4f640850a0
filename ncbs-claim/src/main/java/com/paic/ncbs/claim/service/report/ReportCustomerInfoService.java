package com.paic.ncbs.claim.service.report;

import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.model.vo.report.OcasRealNameVo;
import com.paic.ncbs.claim.model.vo.report.ReportCustomerInfoVO;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimInfoToESVO;
import com.paic.ncbs.claim.service.base.BaseService;

import java.util.List;
import java.util.Map;

public interface ReportCustomerInfoService extends BaseService<ReportCustomerInfoEntity> {

	ReportCustomerInfoEntity getReportCustomerInfoByReportNo(String reportNo);

	ResponseResult<Map<String, Object>> getHistoryByPolicyNo(String policyNo, Pager pager,String certificateNo,String insuredName);

	void updateCustomerById(ReportCustomerInfoVO reportCustomerInfoVO) throws InterruptedException;

	void autoRealName(OcasRealNameVo ocasRealNameVo);

	List<ClaimInfoToESVO>  getHistoryCaseListNew(String reportNo, Integer caseTimes, Pager pager);

}
