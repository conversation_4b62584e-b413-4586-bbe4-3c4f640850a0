package com.paic.ncbs.claim.service.settle.factor.interfaces.reason;

import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EverySettleReasonParamsDTO;

import java.util.Date;
import java.util.List;

/**
 * 不在保单有效期，超年度赔付天数，超年度赔付天数等发票的理算依据构建
 */
public interface SettleReasonParamsBuildServie {
    EverySettleReasonParamsDTO  build(List<BIllSettleResultDTO> everyBIllResults, Date billDate);

}
