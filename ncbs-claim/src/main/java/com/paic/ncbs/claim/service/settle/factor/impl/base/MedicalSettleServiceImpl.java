package com.paic.ncbs.claim.service.settle.factor.impl.base;

import cn.hutool.core.collection.CollectionUtil;
import com.googlecode.aviator.Expression;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.*;
import com.paic.ncbs.claim.service.endcase.DutyBillLimitInfoService;
import com.paic.ncbs.claim.service.settle.factor.abstracts.calculate.AbstractCalculateAmountFactor;
import com.paic.ncbs.claim.service.settle.factor.interfaces.base.BaseSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.base.EveryBillTotalService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.bill.BIllSettleBuildService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.bill.MedicalBillInfoService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.common.AfterBillInfoService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.common.BeforeBillInfoService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.proportion.NoStandardProportionService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.reason.ReasonTemplateParamsBuildService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 医疗费用理算实现
 */
@Slf4j
@Service("medicalSettleServiceImpl")
public class MedicalSettleServiceImpl implements BaseSettleService {

    @Autowired
    private AbstractCalculateAmountFactor abstractCalculateAmountFactor;

    @Autowired
    private MedicalBillInfoService medicalBillInfoService;
    /**
     *增强扩展功能接口集合：针对发票得一些规则 实现CommonBillInfoService接口即可
     */
    @Autowired
    private List<BeforeBillInfoService> services;

    @Autowired
    private List<AfterBillInfoService> afterServiceImplList;

    @Autowired
    private DutyBillLimitInfoService dutyBillLimitInfoService;

    @Autowired
    private  BIllSettleBuildService bIllSettleBuildService;


    @Autowired
    private ReasonTemplateParamsBuildService reasonTemplateParamsBuildService;

    @Autowired
    private  EveryBillTotalService everyBillTotalService;
    @Autowired
    private NoStandardProportionService noStandardProportionService;

    @Override
    public void getSettleAmount(ClaimCaseDTO claimCaseDTO, DutyDetailPayDTO detailPayDTO,
                                Expression expression) {
        List<MedicalBillInfoDTO> medicalBillInfoDTOList = claimCaseDTO.getMedicalInfoDTO().getMedicalBillInfoDTOList();
        detailPayDTO.setAutoSettleAmount(BigDecimal.ZERO);
        if(Objects.equals("N",detailPayDTO.getIsSettleFlag())){
            return ;
        }
        if(CollectionUtil.isEmpty(medicalBillInfoDTOList)){
            return ;
        }
        List<MedicalBillInfoDTO> resultList = medicalBillInfoDTOList;
        if(CollectionUtil.isNotEmpty(services)){
            for (BeforeBillInfoService service : services) {
                List<MedicalBillInfoDTO> billInfoDTOList= service.expansion(resultList,detailPayDTO);
                log.info(JsonUtils.toJsonString(billInfoDTOList));
                resultList=billInfoDTOList;
            }
        }
        if(CollectionUtil.isEmpty(resultList)){
            return ;
        }
        //构建每日发票的数据，原始发票数据构建为计算需要的数据模型
        List<EveryDayBillInfoDTO> everyBillLists= medicalBillInfoService.getEveryDayBillInfoLists(resultList);
        //构建后的发票数据处理
        if(CollectionUtil.isNotEmpty(afterServiceImplList)){
//            everyBillLists = everyBillLists.stream()
//                    .sorted((o1, o2) -> {
//                        BigDecimal diff1 = Optional.ofNullable(o1.getBillAmount()).orElse(BigDecimal.ZERO)
//                                .subtract(Optional.ofNullable(o1.getPrepaidAmount()).orElse(BigDecimal.ZERO))
//                                .subtract(Optional.ofNullable(o1.getImmoderateAmount()).orElse(BigDecimal.ZERO));
//
//                        BigDecimal diff2 = Optional.ofNullable(o2.getBillAmount()).orElse(BigDecimal.ZERO)
//                                .subtract(Optional.ofNullable(o2.getPrepaidAmount()).orElse(BigDecimal.ZERO))
//                                .subtract(Optional.ofNullable(o2.getImmoderateAmount()).orElse(BigDecimal.ZERO));
//
//                        return diff2.compareTo(diff1);
//                    })
//                    .collect(Collectors.toList());
            for (AfterBillInfoService  afterBillInfoService : afterServiceImplList) {
                afterBillInfoService.dealData(everyBillLists,detailPayDTO);
            }
        }

        DutyDetailSettleReasonDTO dutyDetailSettleReasonDTO=new DutyDetailSettleReasonDTO();
        dutyDetailSettleReasonDTO.setDutyDetailCode(detailPayDTO.getDutyDetailCode());
        dutyDetailSettleReasonDTO.setDutyDetailName(detailPayDTO.getDutyDetailName());
        if(Objects.equals("2",detailPayDTO.getPayProportionType())){
            noStandardProportionService.noStardardSettle(detailPayDTO,everyBillLists);
            detailPayDTO.setDutyDetailSettleReasonDTO(dutyDetailSettleReasonDTO);
            return;
        }
        //按发票日期分组排序
        Map<Date,List<EveryDayBillInfoDTO>>  sortDateListMap=everyBillLists.stream().sorted(Comparator.comparing(EveryDayBillInfoDTO::getBillDate)).collect(Collectors.groupingBy(EveryDayBillInfoDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));

        //存每张发票计算要素和结果
        List<BIllSettleResultDTO> billSettleResultDTOList=new ArrayList<>();
        for (Map.Entry<Date, List<EveryDayBillInfoDTO>> entry: sortDateListMap.entrySet()){
            //按是否经医保结算排序分组
            Map<Integer,List<EveryDayBillInfoDTO>> serialMap=  entry.getValue().stream().sorted(Comparator.comparing(EveryDayBillInfoDTO::getSerialNo)).collect(Collectors.groupingBy(EveryDayBillInfoDTO::getSerialNo,LinkedHashMap::new,Collectors.toList()));
            for (Map.Entry<Integer,List<EveryDayBillInfoDTO>> serialEntry :serialMap.entrySet()) {
                List<EveryDayBillInfoDTO> isMediclList = serialEntry.getValue();

                for (EveryDayBillInfoDTO edto : isMediclList) {
                    SettleFactor factor=new SettleFactor();
                    CalculateParamsDTO paramsDTO =new CalculateParamsDTO();

                    //每张发票计算结果数据
                    BIllSettleResultDTO bIllSettleResultDTO =new BIllSettleResultDTO();
                    paramsDTO.setEveryDayBillInfoDTO(edto);
                    paramsDTO.setDutyDetailPayDTO(detailPayDTO);
                    paramsDTO.setSettleFactor(factor);
                    getBillSettleAmount(detailPayDTO.getCalServiceImplMap(),bIllSettleResultDTO,paramsDTO,expression);
                    //不在保单有效期，等待期内的发票，超每月赔付天数等发票的数据组装，理算金额都为0 ，
                    BIllSettleResultDTO bSRDTO= bIllSettleBuildService.dealBIllSettleResultDTO(edto,detailPayDTO,bIllSettleResultDTO);
                    if(Objects.equals("1",bSRDTO.getFlag())){
                        billSettleResultDTOList.add(bSRDTO);
                        continue;
                    }
                    setBIllSettleResultDTOValue(bIllSettleResultDTO,billSettleResultDTOList,edto,detailPayDTO);
                }

            }
        }
        log.info("报案号={}，发票理算信息={}",detailPayDTO.getReportNo(),JsonUtils.toJsonString(billSettleResultDTOList));
        detailPayDTO.setBillSettleResultDTOList(billSettleResultDTOList);
        //限额校验，赔付依据模板数据组装
        everyBillTotalService.everyBillTotalAmount(dutyDetailSettleReasonDTO,billSettleResultDTOList,detailPayDTO);

        //理算依据值设置
        setDetailReasonData(detailPayDTO);

    }

    private void setDetailReasonData(DutyDetailPayDTO detailPayDTO) {
        List<EverySettleReasonParamsDTO> setttleList = detailPayDTO.getDutyDetailSettleReasonDTO().getEverySettleReasonList();
        DetailSettleReasonTemplateDTO detailSettleReasonTemplateDTO =reasonTemplateParamsBuildService.buildParams(setttleList,detailPayDTO);
        detailPayDTO.setDetailSettleReasonTemplateDTO(detailSettleReasonTemplateDTO);
    }











    private BigDecimal getBillSettleAmount(Map<String, CalculateAmountService> calServiceImplMap, BIllSettleResultDTO bIllSettleResultDTO, CalculateParamsDTO paramsDTO, Expression expression) {
        if(Objects.isNull(paramsDTO.getEveryDayBillInfoDTO())){
            return BigDecimal.ZERO;
        }
        Map<String, Object> calparamsMap = new LinkedHashMap<>();
        BigDecimal settleAmount = BigDecimal.ZERO;
        for (Map.Entry<String, CalculateAmountService> en : calServiceImplMap.entrySet()) {
            CalculateAmountService service = en.getValue();
            String key = en.getKey();
//            if ((Objects.equals("N", paramsDTO.getEveryDayBillInfoDTO().getEffectiveFlag())
//                    || Objects.equals("Y", paramsDTO.getEveryDayBillInfoDTO().getExceedMothPayDays())
//                    || Objects.equals("Y", paramsDTO.getEveryDayBillInfoDTO().getExceedYearlyPayDays())
//                    || Objects.equals("Y", paramsDTO.getEveryDayBillInfoDTO().getWaitFlag()))
//                    && !Arrays.asList("ramount00", "ramount01", "ramount02", "payProportion").contains(key)) {
//                continue;
//            }
            if (Objects.isNull(service)) {
                throw new GlobalBusinessException("获取实现类为null名称" + service);
            }
            abstractCalculateAmountFactor.setCalculateReasonAmountInterface(service);
            abstractCalculateAmountFactor.getAmout(paramsDTO);
            calparamsMap.put(key, paramsDTO.getSettleFactor().getCalculateAmount());
        }

        BigDecimal result = (BigDecimal) expression.execute(calparamsMap);//理算金额
        if (result.compareTo(BigDecimal.ZERO) < 0) {
            settleAmount = BigDecimal.ZERO;
            bIllSettleResultDTO.setLessThanRemitAmountFlag("Y");
        }else{
            settleAmount= result.setScale(2, RoundingMode.HALF_UP);
            bIllSettleResultDTO.setLessThanRemitAmountFlag("N");
        }
        DutyDetailPayDTO dutyDetailPayDTO = paramsDTO.getDutyDetailPayDTO();
        bIllSettleResultDTO.setReasonableAmount(paramsDTO.getSettleFactor().getReasonableAmount());
        bIllSettleResultDTO.setPayProportion(paramsDTO.getSettleFactor().getPayProportion());
        bIllSettleResultDTO.setAutoSettleAmount(settleAmount);
        bIllSettleResultDTO.setFormulaAmount(settleAmount);
        if("2".equals(dutyDetailPayDTO.getRemitAmountType())){
            bIllSettleResultDTO.setExpendDayDeductible(paramsDTO.getSettleFactor().getExpendDayDeductible());
            bIllSettleResultDTO.setRemaindDayDeductible(paramsDTO.getEveryDayBillInfoDTO().getRemaindDayDeductible());
        }else {
            bIllSettleResultDTO.setRemitAmount(paramsDTO.getSettleFactor().getRemitAmount());
        }

        return settleAmount;
    }

    /**
     * 组装数据
     * @param bIllSettleResultDTO
     * @param bIllSettleResultDTOList
     * @param edto
     * @param detailPayDTO
     */
    private void setBIllSettleResultDTOValue(BIllSettleResultDTO bIllSettleResultDTO, List<BIllSettleResultDTO> bIllSettleResultDTOList, EveryDayBillInfoDTO edto, DutyDetailPayDTO detailPayDTO) {
        bIllSettleResultDTO.setReportNo(detailPayDTO.getReportNo());
        bIllSettleResultDTO.setPolicyNo(detailPayDTO.getPolicyNo());
        bIllSettleResultDTO.setCaseTimes(detailPayDTO.getCaseTimes());
        bIllSettleResultDTO.setProductCode(detailPayDTO.getProductCode());
        bIllSettleResultDTO.setPlanCode(detailPayDTO.getPlanCode());
        bIllSettleResultDTO.setDutyCode(detailPayDTO.getDutyCode());
        bIllSettleResultDTO.setDutyDetailCode(detailPayDTO.getDutyDetailCode());
        bIllSettleResultDTO.setMedicalSettleFlag(edto.getMedicalSettleFlag());
        bIllSettleResultDTO.setBillNo(edto.getBillNo());
        bIllSettleResultDTO.setBillDate(edto.getBillDate());
        bIllSettleResultDTO.setSettleType("0");
        bIllSettleResultDTO.setTimesLimit(Constants.FLAG_N);
        bIllSettleResultDTO.setDayLimit(Constants.FLAG_N);
        bIllSettleResultDTO.setMonthLimit(Constants.FLAG_N);
        bIllSettleResultDTO.setYearLimit(Constants.FLAG_N);
        bIllSettleResultDTO.setId(edto.getId());
        bIllSettleResultDTO.setTherapyType(edto.getTherapyType());
        bIllSettleResultDTO.setBillAmount(edto.getBillAmount());
        PolicyMonthDto policyMonthDto = getStartEndDate(edto.getBillDate(),detailPayDTO.getMonthDtoList());
        if(Objects.isNull(policyMonthDto)){
            bIllSettleResultDTO.setMonth(-1);
        }else{
            bIllSettleResultDTO.setMonth(policyMonthDto.getMonth());
        }
        //组装责任明细发票理算信息组装
        bIllSettleResultDTOList.add(bIllSettleResultDTO);
    }
    private PolicyMonthDto getStartEndDate(Date billDate, List<PolicyMonthDto> monthDtoList) {

        for (PolicyMonthDto monthDto : monthDtoList) {
            if(billDate.compareTo(monthDto.getStartDate())>=0 && billDate.compareTo(monthDto.getEndDate())<=0){
                return monthDto;
            }
        }
        return null;
    }

}
