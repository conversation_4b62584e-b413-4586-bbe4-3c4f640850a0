package com.paic.ncbs.claim.service.checkloss.impl;

import com.paic.ncbs.claim.dao.mapper.checkloss.DisabilityAppraisalMapper;
import com.paic.ncbs.claim.model.dto.checkloss.DisabilityAppraisalDTO;
import com.paic.ncbs.claim.service.checkloss.ChannelProcessService;
import com.paic.ncbs.claim.service.checkloss.DisabilityAppraisalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Component
@Service("disabilityAppraisalService")
public class DisabilityAppraisalServiceImpl implements DisabilityAppraisalService {

	@Autowired
	private DisabilityAppraisalMapper disabilityAppraisalDao;

	@Resource(name = "channelProcessService")
	private ChannelProcessService channelProcessService;


	@Override
	public void addDisabilityAppraisal(DisabilityAppraisalDTO disabilityAppraisalDTO) {
		disabilityAppraisalDao.addDisabilityAppraisal(disabilityAppraisalDTO);

	}


	@Override
	public void modifyDisabilityAppraisal(DisabilityAppraisalDTO disabilityAppraisalDTO) {
		disabilityAppraisalDao.modifyDisabilityAppraisal(disabilityAppraisalDTO);

	}


	@Override
	public DisabilityAppraisalDTO getDisabilityAppraisal(DisabilityAppraisalDTO disabilityAppraisalDTO) {
		return disabilityAppraisalDao.getDisabilityAppraisal(disabilityAppraisalDTO);
	}


	@Override
	public List<DisabilityAppraisalDTO> getIsAppraisal(String idAhcsChannelProcess) {
		return disabilityAppraisalDao.getIsAppraisal(idAhcsChannelProcess);
	}


	@Override
	public DisabilityAppraisalDTO getDisabilityAppraisalByIdAhcsChannelProcess(String idAhcsChannelProcess) {
		return disabilityAppraisalDao.getDisabilityAppraisalByIdAhcsChannelProcess(idAhcsChannelProcess);
	}


	@Override
	public void removeDisabilityAppraisalByIdAhcsChannelProcess(String idAhcsChannelProcess) {
		disabilityAppraisalDao.removeDisabilityAppraisalByIdAhcsChannelProcess(idAhcsChannelProcess);
	}

}
