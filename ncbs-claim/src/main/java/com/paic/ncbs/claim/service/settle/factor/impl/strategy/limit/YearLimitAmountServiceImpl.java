package com.paic.ncbs.claim.service.settle.factor.impl.strategy.limit;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EverySettleReasonParamsDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.LimitAmountService;
import org.springframework.stereotype.Service;

/**
 * 年限额
 */
@Service("yearLimitAmountServiceImpl")
public class YearLimitAmountServiceImpl implements LimitAmountService {
    @Override
    public void settleLimt(DutyDetailPayDTO detail, EverySettleReasonParamsDTO dto) {
        return;
    }
}
