package com.paic.ncbs.claim.service.taskdeal.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.service.taskdeal.ITaskInfoPlusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service
@Slf4j
public class TaskInfoPlusServiceImpl extends ServiceImpl<TaskInfoMapper, TaskInfoDTO> implements ITaskInfoPlusService {


}
