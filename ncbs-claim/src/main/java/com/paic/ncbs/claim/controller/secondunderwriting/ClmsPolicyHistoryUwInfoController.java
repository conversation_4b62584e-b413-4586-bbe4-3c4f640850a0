package com.paic.ncbs.claim.controller.secondunderwriting;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.secondunderwriting.ClmsPolicyHistoryUwInfoDTO;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsPolicyHistoryUwInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/second/underwriting")
public class ClmsPolicyHistoryUwInfoController {
    @Autowired
    private ClmsPolicyHistoryUwInfoService clmsPolicyHistoryUwInfoService;

    @GetMapping("/getClmsPolicyHistoryUwInfoList/{reportNo}")
    public ResponseResult<List<ClmsPolicyHistoryUwInfoDTO>> getClmsPolicyHistoryUwInfoList(@PathVariable("reportNo") String reportNo){
        List<ClmsPolicyHistoryUwInfoDTO>  result = clmsPolicyHistoryUwInfoService.getClmsPolicyHistoryUwInfoList(reportNo);
        return ResponseResult.success(result);
    }

}
