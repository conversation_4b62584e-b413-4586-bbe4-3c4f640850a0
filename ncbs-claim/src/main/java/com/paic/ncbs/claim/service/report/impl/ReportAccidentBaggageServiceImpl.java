package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentBaggageEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentBaggageMapper;
import com.paic.ncbs.claim.service.report.ReportAccidentBaggageService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportAccidentBaggageServiceImpl extends BaseServiceImpl<ReportAccidentBaggageEntity> implements ReportAccidentBaggageService {
	
	@Autowired
	private ReportAccidentBaggageMapper reportAccidentBaggageMapper;
	
	@Override
	public BaseDao<ReportAccidentBaggageEntity> getDao() {
		return reportAccidentBaggageMapper;
	}

	@Override
	public ReportAccidentBaggageEntity getReportAccidentBaggageByReportNo(String reportNo) {
		return reportAccidentBaggageMapper.getReportAccidentBaggageByReportNo(reportNo);
	}
}
