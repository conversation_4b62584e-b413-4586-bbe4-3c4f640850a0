package com.paic.ncbs.claim.service.settle.factor.impl.base;

import com.googlecode.aviator.Expression;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonOtherLossMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonOtherLossDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.DetailSettleReasonTemplateDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.base.BaseSettleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 其他
 */
@Service
public class OhtermedicalSettleServiceImpl implements BaseSettleService {
    @Autowired
    private PersonOtherLossMapper personOtherLossMapper;
    @Override
    public void getSettleAmount(ClaimCaseDTO claimCaseDTO, DutyDetailPayDTO detailPayDTO, Expression expression) {
        LogUtil.audit("--计算理算金额参数-- 报案号：{}，责任明细类型：{}",claimCaseDTO.getReportNo(),detailPayDTO.getDutyDetailName());
        BigDecimal autoSettleAmount =BigDecimal.ZERO;
        List<PersonOtherLossDTO> personOtherLossList = personOtherLossMapper.getPersonOtherLoss(claimCaseDTO.getReportNo(), claimCaseDTO.getCaseTimes(), ChecklossConst.STATUS_TMP_SUBMIT, BpmConstants.CHECK_DUTY, null);
        autoSettleAmount = personOtherLossList.stream().map(PersonOtherLossDTO::getLossAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        detailPayDTO.setAutoSettleAmount(autoSettleAmount);
        DetailSettleReasonTemplateDTO detailSettleReasonTemplateDTO =new DetailSettleReasonTemplateDTO();
        detailSettleReasonTemplateDTO.setDutyDetailName(detailPayDTO.getDutyDetailName());
        detailSettleReasonTemplateDTO.setAutoSettleAmount(BigDecimalUtils.toString(autoSettleAmount));
        detailPayDTO.setDetailSettleReasonTemplateDTO(detailSettleReasonTemplateDTO);
        LogUtil.audit("--99其他计算理算金额参数-- 报案号：{}，责任明细信息：{}",claimCaseDTO.getReportNo(), JsonUtils.toJsonString(detailPayDTO));
    }
}
