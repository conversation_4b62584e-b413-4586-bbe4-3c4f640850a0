package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.model.dto.duty.OtherLossPptDTO;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.dao.mapper.checkloss.OtherLossPptMapper;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.checkloss.OtherLossPptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OtherLossPptServiceImpl implements OtherLossPptService {

	@Autowired
    OtherLossPptMapper otherLossPptDao;

	@Autowired
	private AhcsCommonService ahcsCommonService;

	@Override
	public void removeOtherLossPptList(String reportNo, Integer caseTimes, String taskCode) {
		otherLossPptDao.removeOtherLossPptList(reportNo, caseTimes, taskCode);

	}

	@Override
	public List<OtherLossPptDTO> getOtherLossPptListByReportNo(String reportNo, Integer caseTimes, String status,
                                                               String taskCode) {
		return otherLossPptDao.getOtherLossPptListByReportNo(reportNo, caseTimes, status, taskCode);
	}

	@Override
	public void addOtherLossPptList(List<OtherLossPptDTO> otherLossPptList, Integer caseTimes, String userId, String channelProcessId) {
		ahcsCommonService.batchHandlerTransactionalWithArgs(OtherLossPptMapper.class, otherLossPptList, ListUtils.GROUP_NUM, "addOtherLossPptList", caseTimes, userId, channelProcessId);
	}

	@Override
	public List<OtherLossPptDTO> getOtherLossPptList(String idAhcsChannelProcess, String taskCode) {
		return otherLossPptDao.getOtherLossPptList(idAhcsChannelProcess, taskCode);
	}

	@Override
	public List<String> getAccidentReasonList(String reportNo){
		return otherLossPptDao.getAccidentReasonList(reportNo);
	}
}
