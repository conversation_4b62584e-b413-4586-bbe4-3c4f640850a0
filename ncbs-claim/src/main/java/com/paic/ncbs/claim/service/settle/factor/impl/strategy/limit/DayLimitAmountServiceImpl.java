package com.paic.ncbs.claim.service.settle.factor.impl.strategy.limit;

import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EverySettleReasonParamsDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.LimitAmountService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日限额
 */
@Service("dayLimitAmountServiceImpl")
public class DayLimitAmountServiceImpl implements LimitAmountService {
    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;

    @Value("#{${orgLimit.day}}")
    private Map<String, BigDecimal> orgDayLimitMap;

    @Value("${special.productPackage}")
    private String specialProductPackage;

    @Override
    public void settleLimt(DutyDetailPayDTO detail, EverySettleReasonParamsDTO dto) {

        BigDecimal maxAmountPay = detail.getMaxAmountPay();//责任剩余理赔金额
        BigDecimal payLimit;
        if (null == orgDayLimitMap || !orgDayLimitMap.containsKey(detail.getProductPackage())) {
            payLimit = detail.getPayLimit();//产品责任属性配置的限额
        } else {
            if (!dto.getHospitalPropertyDes().equals("公立")) {
                payLimit = orgDayLimitMap.get(detail.getProductPackage());
            } else {
                payLimit = detail.getPayLimit();//产品责任属性配置的限额
            }
        }
        if (specialProductPackage.contains(detail.getProductPackage())) {
            List<PolicyMonthDto> monthDtoList = detail.getMonthDtoList();
            PolicyMonthDto startEndDate = getStartEndDate(dto.getBillDate(), monthDtoList);
            if (null != startEndDate && startEndDate.getMonth() == 0) {
                payLimit = orgDayLimitMap.get(detail.getProductPackage());
            }
        }
        dto.setDayLimitAmount(payLimit);

        //查询当前责任账单日期这天的赔付记录
        BigDecimal dayhistoryAmount = getDutyHistory(detail, dto.getBillDate());
        if (detail.getCaseTimes() > 1) {
            //重开案件 需要排查上一次(caseTimes-1)的赔付的金额
            //查询上一次责任赔付记录金额
            BigDecimal reportNoUpCaseTimeAmount = getUpCasetimesAmount(dto.getBillDate(), detail);
            dayhistoryAmount = dayhistoryAmount.subtract(reportNoUpCaseTimeAmount);
        }
        //如果账单日期的历史金额已经大于了每日限额 则本次理算金额为0
        if (dayhistoryAmount.compareTo(payLimit) >= 0) {
            dto.setAutoSettleAmount(BigDecimal.ZERO);
            dto.setSettleZeroFlag("Y");
        } else {//账单日的历史总赔付金额小于每日限额
            //本次理算金额加上历史赔付的总金额比较 是否大于每日限额
            if (dto.getAutoSettleAmount().add(dayhistoryAmount).compareTo(payLimit) >= 0) {
                //本次计算的理算金额加上账单当日赔付历史总金额大于每日限额，那么本次账单日理算的金额等于 每日限额减去历史账单日赔付总金额
                dto.setAutoSettleAmount(payLimit.subtract(dayhistoryAmount));
                dto.setNoLimtsettleFlag("1");//打个标记 后面理算依据显示话术不一样根据这个判断
            }

        }
        updateBIllResult(detail, dto);
    }

    /**
     * 更行每张发票理算金额
     *
     * @param detail
     * @param dto
     */
    private void updateBIllResult(DutyDetailPayDTO detail, EverySettleReasonParamsDTO dto) {

        List<BIllSettleResultDTO> billSettleResultDTOList = detail.getBillSettleResultDTOList();
        List<BIllSettleResultDTO> list = billSettleResultDTOList.stream().filter(bIllSettleResultDTO -> Objects.equals(bIllSettleResultDTO.getPolicyNo(), detail.getPolicyNo()) && Objects.equals(bIllSettleResultDTO.getPlanCode(), detail.getPlanCode()) && Objects.equals(bIllSettleResultDTO.getDutyCode(), detail.getDutyCode()) && Objects.equals(bIllSettleResultDTO.getDutyDetailCode(), detail.getDutyDetailCode()) && Objects.equals(bIllSettleResultDTO.getBillDate(), dto.getBillDate())).collect(Collectors.toList());
        if (Objects.equals("Y", dto.getSettleZeroFlag())) {
            list.forEach(bIllSettleResultDTO -> {
                bIllSettleResultDTO.setAutoSettleAmount(BigDecimal.ZERO);
                bIllSettleResultDTO.setDayLimit("Y");
                bIllSettleResultDTO.setRemark(DateUtils.dateFormat(dto.getBillDate(), DateUtils.SIMPLE_DATE_STR) + "理算金额超日限额，按0元赔付");
            });
        }

        if (Objects.equals("1", dto.getNoLimtsettleFlag())) {

//            List<BIllSettleResultDTO> sortedlIst =  list.stream().sorted(Comparator.comparing(BIllSettleResultDTO::getAutoSettleAmount).reversed()).collect(Collectors.toList());
            list.sort(new Comparator<BIllSettleResultDTO>() {
                @Override
                public int compare(BIllSettleResultDTO h1, BIllSettleResultDTO h2) {
                    // 第一优先级：按医院性质和名称分组（0为优先组，1为非优先组）
                    int priority1 = ("公立".equals(h1.getHospitalPropertyDes()) &&
                            null != h1.getHospitalName() &&
                            !h1.getHospitalName().contains("牙") &&
                            !h1.getHospitalName().contains("口腔")) ? 0 : 1;
                    int priority2 = ("公立".equals(h2.getHospitalPropertyDes()) &&
                            null != h2.getHospitalName() &&
                            !h2.getHospitalName().contains("牙") &&
                            !h2.getHospitalName().contains("口腔")) ? 0 : 1;

                    // 先比较优先级
                    if (priority1 != priority2) {
                        return priority1 - priority2;
                    }

                    // 优先级相同后，按金额降序（使用BigDecimal的compareTo方法）
                    return h2.getAutoSettleAmount().compareTo(h1.getAutoSettleAmount());
                }
            });
            List<BIllSettleResultDTO> sortedlIst = list;
            BigDecimal resudeLimit = dto.getAutoSettleAmount();
            for (BIllSettleResultDTO bIllSettleResultDTO : sortedlIst) {
                if (resudeLimit.compareTo(BigDecimal.ZERO) <= 0) {
                    bIllSettleResultDTO.setAutoSettleAmount(BigDecimal.ZERO);
                    bIllSettleResultDTO.setDayLimit("Y");
                    bIllSettleResultDTO.setRemark(DateUtils.dateFormat(dto.getBillDate(), DateUtils.SIMPLE_DATE_STR) + "理算金额剩余日限额为0，按剩余日限额抵扣后发票号" + bIllSettleResultDTO.getBillNo() + "理算金额按0赔付");
                } else {
                    if (bIllSettleResultDTO.getAutoSettleAmount().compareTo(resudeLimit) <= 0) {
                        resudeLimit = resudeLimit.subtract(bIllSettleResultDTO.getAutoSettleAmount());
                    } else {
                        bIllSettleResultDTO.setAutoSettleAmount(resudeLimit);
                        bIllSettleResultDTO.setRemark(DateUtils.dateFormat(dto.getBillDate(), DateUtils.SIMPLE_DATE_STR) + "理算金额超剩余日限额，按剩余日限额抵扣后发票号发票号" + bIllSettleResultDTO.getBillNo() + "理算金额按" + resudeLimit + "赔付");
                        bIllSettleResultDTO.setDayLimit("Y");
                        resudeLimit = BigDecimal.ZERO;
                    }
                }


            }
        }

    }

    /**
     * //查询当前责任账单日期这天的赔付记录
     *
     * @param detail
     * @param billDate
     * @return
     */
    private BigDecimal getDutyHistory(DutyDetailPayDTO detail, Date billDate) {
        DutyBillLimitInfoDTO paramsDto = new DutyBillLimitInfoDTO();
        paramsDto.setPolicyNo(detail.getPolicyNo());
        paramsDto.setPlanCode(detail.getPlanCode());
        paramsDto.setDutyCode(detail.getDutyCode());
        paramsDto.setBillDate(billDate);
        BigDecimal sum = dutyBillLimitInfoMapper.getDaySum(paramsDto);
        return sum;
    }

    /**
     * 上一次赔付次数的赔付限额
     *
     * @param billDate
     * @param detail
     * @return
     */
    private BigDecimal getUpCasetimesAmount(Date billDate, DutyDetailPayDTO detail) {
        DutyBillLimitInfoDTO paramsDto = new DutyBillLimitInfoDTO();
        paramsDto.setReportNo(detail.getReportNo());
        paramsDto.setPolicyNo(detail.getPolicyNo());
        paramsDto.setPlanCode(detail.getPlanCode());
        paramsDto.setDutyCode(detail.getDutyCode());
        paramsDto.setBillDate(billDate);
        paramsDto.setCaseTimes(detail.getCaseTimes() - 1);
        BigDecimal upAmount = dutyBillLimitInfoMapper.getUpCasetimesAmount(paramsDto);
        LogUtil.info("报案号={},金额={}", detail.getReportNo(), BigDecimalUtils.toString(upAmount), JsonUtils.toJsonString(paramsDto));
        return upAmount;
    }

    private PolicyMonthDto getStartEndDate(Date billDate, List<PolicyMonthDto> monthDtoList) {

        for (PolicyMonthDto monthDto : monthDtoList) {
            if (billDate.compareTo(monthDto.getStartDate()) >= 0 && billDate.compareTo(monthDto.getEndDate()) <= 0) {
                return monthDto;
            }
        }
        return null;
    }
}
