package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;

import java.util.List;

public interface EndorsementService {

    void mergeEndorsementInfo(EndorsementDTO endorsement);

    EndorsementDTO getByReportNoAndCaseTime(String reportNo, Integer caseTimes);

    void deleteByPolicyPayId(String reportNo, Integer caseTimes);

    void addEndorsementInfo(EndorsementDTO endorsement);

    void modifyEndorsementInfo(EndorsementDTO endorsement);

    void autoGenerateEndorsement(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPays);

    void autoGenerateEndorsement4TPA(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPays);

    //void reAutoGenerateEndorsement(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPays);
}
