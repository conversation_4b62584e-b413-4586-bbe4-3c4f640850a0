package com.paic.ncbs.claim.service.settle.factor.impl.strategy.limit;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.DetailSettleReasonTemplateDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EverySettleTemplateDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyBillLimitDto;
import com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.ExtendedLimitService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 定制化产品方案月赔付天数实现
 *
 */
@Slf4j
@Service
@Order(3)
@RefreshScope
public class PolicyMonthFrequencyDayLimitServiceImpl implements ExtendedLimitService {
    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;

    /**
     * 保单月限额配置方案
     */
    @Value("#{${policyLimit.monthFrequencyDay:null}}")
    private Map<String, Integer> policyMonthFrequencyLimitMap;

    /**
     * 执行保单是否匹配的本扩展
     *
     * @param policyPayDTO
     * @return
     */
    @Override
    public boolean isMatch(PolicyPayDTO policyPayDTO) {
        return policyMonthFrequencyLimitMap != null && policyMonthFrequencyLimitMap.containsKey(policyPayDTO.getProductPackage());
    }

    @Override
    public void cumulativeLimit(PolicyPayDTO policyPayDTO) {
        if (!isMatch(policyPayDTO)) {
            return;
        }
        log.info("案件:{},配置了保单月赔付天数开始处理！", policyPayDTO.getReportNo());
        Integer limitCount = policyMonthFrequencyLimitMap.get(policyPayDTO.getProductPackage());
        DutyLimitQueryVo dutyLimitQueryVo = new DutyLimitQueryVo();
        dutyLimitQueryVo.setPolicyNo(policyPayDTO.getPolicyNo());
        List<DutyBillLimitDto> dtos = dutyBillLimitInfoMapper.getAllAlreadyPayPolicy(dutyLimitQueryVo);
        Map<Date, Integer> limitMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(dtos)) {
            if (policyPayDTO.getCaseTimes() > 1) {
                //重开案件去掉当前报案号对应的发票日期
                dtos = dtos.stream().filter(dutyBillLimitDto -> !Objects.equals(policyPayDTO.getReportNo(), dutyBillLimitDto.getReportNo())).collect(Collectors.toList());
                //去除金额为0的发票
                dtos = dtos.stream().filter(dutyBillLimitDto -> !(dutyBillLimitDto.getSettleClaimAmount().compareTo(BigDecimal.ZERO) == 0)).collect(Collectors.toList());
                Set<Date> payDate = dtos.stream().map(DutyBillLimitDto::getBillDate).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(payDate)) {
                    for (Date date : payDate) {
                        Date firstDayOfMonth = DateUtils.getFirstDayOfMonth(date);
                        Integer integer = limitMap.get(firstDayOfMonth);
                        if (Objects.isNull(integer)) {
                            limitMap.put(firstDayOfMonth, 1);
                        } else {
                            limitMap.put(firstDayOfMonth, integer + 1);
                        }
                    }
                }
            }
        }

        List<DutyBillLimitInfoDTO> policyAllBillLimits = new ArrayList<>();
        List<BIllSettleResultDTO> bIllSettleResultDTOLists = new ArrayList<>();
        List<PlanPayDTO> plans = policyPayDTO.getPlanPayArr();
        for (PlanPayDTO planPayDTO : plans) {
            List<DutyPayDTO> dutyPayDTOS = planPayDTO.getDutyPayArr();
            for (DutyPayDTO dutyPayDTO : dutyPayDTOS) {
                List<DutyDetailPayDTO> details = dutyPayDTO.getDutyDetailPayArr();
                for (DutyDetailPayDTO detailPayDTO : details) {
                    if (CollectionUtil.isNotEmpty(detailPayDTO.getDutyBillLimitInfoDTOList())) {
                        for (DutyBillLimitInfoDTO dutyBillLimitInfoDTO : detailPayDTO.getDutyBillLimitInfoDTOList()) {
                            if (dutyBillLimitInfoDTO.getMonth() != -1) {
                                policyAllBillLimits.add(dutyBillLimitInfoDTO);
                            }
                        }
                    }
                    if (CollectionUtil.isNotEmpty(detailPayDTO.getBillSettleResultDTOList())) {
                        bIllSettleResultDTOLists.addAll(detailPayDTO.getBillSettleResultDTOList());
                    }
                }
            }
        }
        log.info("报案号={}下所有限额数据={}", policyPayDTO.getReportNo(), JsonUtils.toJsonString(policyAllBillLimits));

        bIllSettleResultDTOLists = bIllSettleResultDTOLists.stream().sorted(Comparator.comparing(BIllSettleResultDTO::getAutoSettleAmount).reversed()).collect(Collectors.toList());
        for (BIllSettleResultDTO bIllSettleResultDTO : bIllSettleResultDTOLists) {
            Date firstDayOfMonth = DateUtils.getFirstDayOfMonth(bIllSettleResultDTO.getBillDate());
            Integer integer = limitMap.get(firstDayOfMonth);
            if (Objects.isNull(integer)) {
                limitMap.put(firstDayOfMonth, 1);
            } else {
                if (limitCount >= integer) {
                    updateBillSettleResult(bIllSettleResultDTO);
                } else {
                    limitMap.put(firstDayOfMonth, integer + 1);
                }
            }
        }

    }


    /**
     * 更新责任明细发票数据
     *
     */
    private void updateBillSettleResult(BIllSettleResultDTO bIllSettleResultDTO) {
        bIllSettleResultDTO.setAutoSettleAmount(BigDecimal.ZERO);
        bIllSettleResultDTO.setExceedMothPayDays("Y");
        bIllSettleResultDTO.setRemark(bIllSettleResultDTO.getRemark() + "发票日:" + bIllSettleResultDTO.getBillDate() + "所在自然月累计赔付天数已超月限天数本日发票可赔付金额为0");
        log.info("报案号={},发票日={},所在自然月已超月限天数 该发票日 责任明细发票理算数据为0", bIllSettleResultDTO.getReportNo(), bIllSettleResultDTO.getBillDate());

    }


}
