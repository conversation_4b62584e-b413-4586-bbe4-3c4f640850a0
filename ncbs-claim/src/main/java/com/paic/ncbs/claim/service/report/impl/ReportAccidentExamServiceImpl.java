package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentExamEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentExamMapper;
import com.paic.ncbs.claim.service.report.ReportAccidentExamService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportAccidentExamServiceImpl extends BaseServiceImpl<ReportAccidentExamEntity> implements ReportAccidentExamService {

    @Autowired
    private ReportAccidentExamMapper reportAccidentExamMapper;

    @Override
    public BaseDao<ReportAccidentExamEntity> getDao() {
        return reportAccidentExamMapper;
    }

    @Override
    public ReportAccidentExamEntity getReportAccidentExamByReportNo(String reportNo) {
        return reportAccidentExamMapper.getReportAccidentExamByReportNo(reportNo);
    }
}
