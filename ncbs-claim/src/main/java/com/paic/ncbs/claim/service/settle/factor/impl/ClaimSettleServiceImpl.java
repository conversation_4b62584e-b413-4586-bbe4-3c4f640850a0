package com.paic.ncbs.claim.service.settle.factor.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.vo.settle.SettleSelectVO;
import com.paic.ncbs.claim.service.common.RedisService;
import com.paic.ncbs.claim.service.settle.factor.abstracts.settle.SettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.ClaimSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.bill.ExecuteBillFilterService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.modelinit.ExecuteDataInitializeService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.modelsettle.ExecuteModelSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle.ExecuteSaveSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.rulefilter.ExecuteDutyRuleFilterService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;


@Service
@Slf4j
public class ClaimSettleServiceImpl implements ClaimSettleService {
    @Autowired
    private SettleService settleService;

    /**
     * 模型初始化接口
     */
    @Autowired
    private ExecuteDataInitializeService executeDataInitializeService;
    @Autowired
    private ExecuteBillFilterService executeBillFilterService;

    /**
     * 核责接口
     */
    @Autowired
    private ExecuteDutyRuleFilterService executeDutyRuleFilterService;
    /**
     * 模型理算接口
     */

    @Autowired
    private ExecuteModelSettleService executeModelSettleService;
    /**
     * 保存数据接口服务列表
     */
    @Autowired
    private ExecuteSaveSettleService executeSaveSettleService;
    @Autowired
    private PolicyPayMapper policyPayMapper;
    @Autowired
    private RedisService redisService;

    @Override
    @Transactional
    public ClaimCaseDTO settle(String reportNo, Integer caseTimes) {
        LogUtil.audit("#-----重构理算初始化开始-----");
        LogUtil.audit("--重构初始化赔付信息-校验是否已初始化,报案号:{},赔付次数:{}", reportNo, caseTimes);
        if (this.checkExists(reportNo, caseTimes)) {
            LogUtil.audit("--重构已经初始化过理算数据,不需要继续初始化,报案号:{},赔付次数:{}", reportNo, caseTimes);
            return null;
        }

        String lockValue = UuidUtil.getUUID();
        String lockName = String.format(BaseConstant.POLICY_PAY_INIT_LOCK,reportNo);
        if(!redisService.tryLock(lockName,lockValue)){
            LogUtil.audit("重构 初始化中，请稍侯");
            return null;
        }
        //初始化
        initdata();
        //理算
        ClaimCaseDTO result = settleService.settle(reportNo,caseTimes,"0");

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                redisService.removeLock(lockName,lockValue);
            }
        });
        LogUtil.audit("#-----理算初始化结束-----, 报案号: {}, policyPayList: {}", reportNo, JSON.toJSONString(result));

        return result;
    }

    @Override
    @Transactional
    public ClaimCaseDTO settleInit(String reportNo, Integer caseTimes) {
        LogUtil.audit("#-----页面理算初始化开始-----");
        LogUtil.audit("--页面初始化赔付信息-校验是否已初始化,报案号:{},赔付次数:{}", reportNo, caseTimes);
        if (this.checkExists(reportNo, caseTimes)) {
            LogUtil.audit("--页面已经初始化过理算数据,不需要继续初始化,报案号:{},赔付次数:{}", reportNo, caseTimes);
            return null;
        }

        String lockValue = UuidUtil.getUUID();
        String lockName = String.format(BaseConstant.POLICY_PAY_INIT_LOCK,reportNo);
        if(!redisService.tryLock(lockName,lockValue)){
            LogUtil.audit("重构 初始化中，请稍侯");
            return null;
        }
        //初始化
        initdata();
        //理算
        ClaimCaseDTO result = settleService.settleInit(reportNo,caseTimes,"0");

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                redisService.removeLock(lockName,lockValue);
            }
        });
        LogUtil.audit("#-----理算初始化结束-----, 报案号: {}, policyPayList: {}", reportNo, JSON.toJSONString(result));

        return result;
    }

    @Override
    @Transactional
    public ClaimCaseDTO reSettleInit(String reportNo, Integer caseTimes) {
        LogUtil.audit("#-----收单重新理算初始化开始-----");
        //初始化
        initdata();
        //理算
        ClaimCaseDTO result = settleService.settleInit(reportNo,caseTimes,"1");

        LogUtil.audit("#----收单重新理算初始化结束-----, 报案号: {}, policyPayList: {}", reportNo, JSON.toJSONString(result));

        return result;
    }


    /**
     * 初始化
     */
    private void initdata() {

        //设置模型初始化对象
        settleService.setDataInitializeService(executeDataInitializeService);
        settleService.setExecuteBillFilterService(executeBillFilterService);
        //设置核责对象
        settleService.setUwRuleServiceValue(executeDutyRuleFilterService);
        //设置理算实例对象
        settleService.setModelSettleServiceValue(executeModelSettleService);
        //设置保存数据实例对象
        settleService.setExecuteSaveSettleServiceValue(executeSaveSettleService);
    }
    public boolean checkExists(String reportNo, Integer caseTime) {
        return policyPayMapper.checkExists(reportNo, caseTime, SettleConst.CLAIM_TYPE_PAY) > 0;
    }

    /**
     * 重新理算
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    @Transactional
    public ClaimCaseDTO reSettle(String reportNo, Integer caseTimes) {
        //初始化
        initdata();
        //理算
        ClaimCaseDTO result = settleService.settle(reportNo,caseTimes,"1");
        return result;
    }

    @Override
    @Transactional
    public ClaimCaseDTO settleSelect(SettleSelectVO settleSelectVO) {
        log.info("单责任理算开始，参数：{}", JsonUtils.toJsonString(settleSelectVO));
        //初始化
        initdata();
        //理算
        ClaimCaseDTO result = settleService.settleSelect(settleSelectVO);
        return result;
    }
}
