package com.paic.ncbs.claim.service.settle.factor.interfaces.reason;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.DetailSettleReasonTemplateDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EverySettleReasonParamsDTO;

import java.util.List;

public interface ReasonTemplateParamsBuildService {
    DetailSettleReasonTemplateDTO buildParams(List<EverySettleReasonParamsDTO> setttleList, DutyDetailPayDTO dutyDetailPayDTO);
}
