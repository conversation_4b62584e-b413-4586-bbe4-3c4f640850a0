package com.paic.ncbs.claim.service.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PersonBenefitDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface PersonBenefitService {

     void savePersonBenefit(List<List<PersonBenefitDTO>> personBenefitList, String reportNo, Integer caseTimes, String channelId, String loginUm, String taskId, String status);

     void removePersonBenefit(String reportNo, Integer caseTimes, String taskId, String channelId);

     Map<String, Object> getPersonBenefit(String reportNo, Integer caseTimes, String taskId, String channelProcessId);

     List<PersonBenefitDTO> getPersonBenefitByType(String reportNo, Integer caseTimes, String benefitType, String taskId);

     List<PersonBenefitDTO> getPersonBenefitByTypes(String reportNo, Integer caseTimes, List<String> benefitTypes, String taskId, String idAhcsChannelProcess);

     void saveBenefitDays(BigDecimal hospitalDays, String reportNo, Integer caseTimes, String loginUm, String taskId, String status);
}
