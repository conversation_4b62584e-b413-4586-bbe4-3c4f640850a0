package com.paic.ncbs.claim.controller.ahcs;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePolicyFormDTO;
import com.paic.ncbs.claim.model.vo.endcase.CaseRegisterApplyVO;
import com.paic.ncbs.claim.model.vo.estimate.EstimateChangeApplyVO;
import com.paic.ncbs.claim.model.vo.estimate.EstimateChangeVo;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Api(tags = "未决修正")
@RestController
@RequestMapping("/ahcs/do/app/estimateChangeAction")
public class EstimateChangeController{

	@Autowired
	private EstimateChangeService estimateChangeService;
	@Autowired
	private UserInfoService userInfoService;
	@Autowired
	private BpmService bpmService;

	@ApiOperation("获取未决修正记录列表")
	@GetMapping("/getAllEstimateChangeList")
	public ResponseResult<List<EstimateChangeDTO>> getAllEstimateChangeList(@RequestParam("reportNo") String reportNo,
																			@RequestParam("caseTimes")Integer caseTimes) {
		List<EstimateChangeDTO> changeDTOList = estimateChangeService.getAllEstimateChangeList(reportNo, caseTimes);
		if(ListUtils.isNotEmpty(changeDTOList)){
			for (EstimateChangeDTO dto : changeDTOList) {
				String userName = Optional.ofNullable(userInfoService.getUserNameById(dto.getUserId())).orElse(null);

				if(userName != null){
					dto.setUserId(dto.getUserId() + "-" + userName);
				}
			}
		}
		return ResponseResult.success(changeDTOList);
	}

	@ApiOperation("获取未决修正列表")
	@GetMapping("/getPolicyRegisterAmount")
	public ResponseResult<List<EstimateChangeDTO>> getPolicyRegisterAmount(@RequestParam("reportNo") String reportNo,
																		   @RequestParam("caseTimes")Integer caseTimes) {
		return ResponseResult.success(estimateChangeService.getPolicyRegisterAmount(reportNo, caseTimes));
	}

	@ApiOperation("提交未决修正-作废，使用下面/estimateChange")
	@PostMapping("/addEstimateChangeList")
	public ResponseResult addEstimateChangeList(@RequestBody List<EstimateChangeDTO> estimateChangeList){
//		estimateChangeService.addEstimateChangeList(estimateChangeList);
		return ResponseResult.success();
	}

	@ApiOperation("提交未决责任修正")
	@PostMapping("/estimateChange")
	public ResponseResult<Map<String,String>> estimateChange(@RequestBody EstimateChangePolicyFormDTO estimateChangePolicyForm) throws Exception {
		String problemNo = estimateChangeService.estimateChangeApply(estimateChangePolicyForm);
		Map<String,String> problemNoMap = new HashMap<>();
		problemNoMap.put("problemNo",problemNo);
		return ResponseResult.success(problemNoMap);
	}


	@ApiOperation("查询未决修正审批信息")
	@ResponseBody
	@GetMapping(value="/getAuditEstimateChangeApplyVO")
	public ResponseResult<EstimateChangeApplyVO> getAuditEstimateChangeApplyVO(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes") Integer caseTimes) throws Exception {
		LogUtil.audit("查询未决修正审批的信息, reportNo={}, caseTimes={}", reportNo, caseTimes);
		EstimateChangeApplyVO estimateChangeApplyVO = estimateChangeService.getAuditEstimateChangeApplyVO(reportNo, caseTimes);
		return ResponseResult.success(estimateChangeApplyVO);
	}

	@ApiOperation("查询未决修正列表信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "reportNo",value = "报案号",required = true,dataType = "String",dataTypeClass=String.class),
			@ApiImplicitParam(name = "caseTimes",value = "赔付次数",required = true,dataType = "Integer",dataTypeClass=Integer.class)
	})
	@ResponseBody
	@GetMapping(value="/getEstimateChangeApplyVOList")
	public ResponseResult<List<EstimateChangeApplyVO>> getEstimateChangeApplyVOList(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes") Integer caseTimes) throws Exception {
		LogUtil.audit("查询立案申请列表信息, reportNo={}, caseTimes={}", reportNo, caseTimes);
		return ResponseResult.success(estimateChangeService.getEstimateChangeApplyVOList(reportNo, caseTimes));
	}

	@ApiOperation("发送未决修正审批任务")
	@PostMapping(value="/sendAuditEstimateChangeApply")
	public ResponseResult<Object> sendAuditEstimateChangeApply(@RequestBody EstimateChangeApplyVO estimateChangeApplyVO) throws Exception {

		if (ConstValues.AUDIT_DISAGREE_CODE.equals(estimateChangeApplyVO.getAuditOpinion()) && StringUtils.isEmptyStr(estimateChangeApplyVO.getAuditRemark())) {
			throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "审批不同意，需要填写审批说明");
		}
		List<String> msgList = estimateChangeService.sendAuditEstimateChangeApply(estimateChangeApplyVO);
		if (msgList.size() < 1) {
			return ResponseResult.success("");
		}
		return ResponseResult.success(msgList.get(0));
	}
	@ApiOperation("查询未决修正/立案信息")
	@GetMapping("/getEstimationChangeHistry")
	public ResponseResult<EstimateChangeVo> getEstimationChangeHistry(@RequestParam ("reportNo")String reportNo, @RequestParam ("caseTimes")int caseTimes, @RequestParam ("idFlagHistoryChange")String idFlagHistoryChange, @RequestParam ("isEstimate") String isEstimate){
		EstimateChangeVo estimateChangeVo = estimateChangeService.getEstimateChangeVo(reportNo,caseTimes,idFlagHistoryChange,isEstimate);
		return ResponseResult.success(estimateChangeVo);
	}

	@ApiOperation("校验是否未决修正申请提交")
	@GetMapping("/isEstimationChange")
	public ResponseResult<Object> isEstimationChange(@RequestParam("reportNo") String reportNo) {
		bpmService.processCheck(reportNo, BpmConstants.OC_ESTIMATE_CHANGE_REVIEW,BpmConstants.OPERATION_INITIATE);
		return ResponseResult.success();
	}
}

