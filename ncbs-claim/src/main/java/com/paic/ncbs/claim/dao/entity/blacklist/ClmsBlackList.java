package com.paic.ncbs.claim.dao.entity.blacklist;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 黑名单信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Getter
@Setter
@TableName("clms_black_list")
public class ClmsBlackList implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 黑名单角色类型 1-出险人；2-投保单位；3-代理人；4-医院；5-医生；6-公估公司；7-公估员；8-鉴定机构；9-鉴定人员
     */
    @TableField("party_type")
    private String partyType;

    /**
     * 个人/企业 1-个人；2-企业
     */
    @TableField("entity_type")
    private String entityType;

    /**
     * 姓名/名称
     */
    @TableField("party_name")
    private String partyName;

    /**
     * 证件类型
     */
    @TableField("id_type")
    private String idType;

    /**
     * 证件号码
     */
    @TableField("id_num")
    private String idNum;

    /**
     * 风险类型 1-保险欺诈；2-高赔付客户；3-恶意投诉；4-工作质量低劣；5-诉讼仲裁；6-负面信息
     */
    @TableField("risk_type")
    private String riskType;

    /**
     * 电话号码
     */
    @TableField("phone_num")
    private String phoneNum;

    /**
     * 有效标识（Y: 有效，N: 无效）
     */
    @TableField("valid_flag")
    private String validFlag;

    /**
     * 来源
     */
    @TableField("black_source")
    private String blackSource;

    /**
     * 审批状态：1-审批中；2-审批通过；3-审批不通过；4-无需审批
     */
    @TableField("audit_status")
    private String auditStatus;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人员
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;

    /**
     * 关联报案号
     */
    @TableField("related_report_no")
    private String relatedReportNo;

}
