package com.paic.ncbs.claim.service.checkloss;


import com.paic.ncbs.claim.model.dto.checkloss.PropertyLossDTO;

import java.util.List;

public interface PropertyLossService {


	public void savePropertyLoss(List<PropertyLossDTO> propertyLossList, String reportNo, Integer caseTimes, String taskCode,String channelId);


	public void removePropertyLoss(String reportNo, Integer caseTimes, String taskCode,String channelId);

	void updateEffective(PropertyLossDTO propertyLossDTO);


	public List<PropertyLossDTO> getPropertyLoss(String reportNo, Integer caseTimes, String status, String taskCode,String channelId);


	public List<PropertyLossDTO> getPropertyLossAll(String reportNo, Integer caseTimes, String status, String taskCode);


	public List<PropertyLossDTO> getPropertyLossByReportNo(String reportNo, Integer caseTimes, String status, String taskCode);
}
