package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportExcEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportExcMapper;
import com.paic.ncbs.claim.service.report.ReportExcService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportExcServiceImpl extends BaseServiceImpl<ReportExcEntity> implements ReportExcService {

    @Autowired
    private ReportExcMapper reportExcMapper;

    @Override
    public BaseDao<ReportExcEntity> getDao() {
        return reportExcMapper;
    }

    @Override
    public ReportExcEntity getReportExcByReportNo(String reportNo) {
        return reportExcMapper.getReportExcByReportNo(reportNo);
    }


}
