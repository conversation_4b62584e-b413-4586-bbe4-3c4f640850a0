package com.paic.ncbs.claim.controller.duty;


import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDefineDTO;
import com.paic.ncbs.claim.service.checkloss.BigDiseaseDefineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "重大疾病")
@Controller
@RequestMapping("/duty/app/diseaseDefineAction")
public class DiseaseDefineController extends BaseController {

	@Resource(name="bigDiseaseDefineService")
	private BigDiseaseDefineService bigDiseaseDefineService;

	@ApiOperation("获取重疾种类/依据信息")
	@ResponseBody
	@RequestMapping(value="/getBigDiseaseDefineList", method=RequestMethod.GET)
	public ResponseResult getBigDiseaseDefineList() throws GlobalBusinessException {

		LogUtil.audit("获取重疾种类/依据信息");
		List<BigDiseaseDefineDTO> list = bigDiseaseDefineService.getBigDiseaseDefines();
		return ResponseResult.success(list);
	}
	
}
