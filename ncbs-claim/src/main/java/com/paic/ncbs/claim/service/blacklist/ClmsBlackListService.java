package com.paic.ncbs.claim.service.blacklist;

import com.paic.ncbs.claim.model.vo.blacklist.ClmsBlackListVO;
import com.paic.ncbs.claim.utils.PageResult;

/**
 * <p>
 * 黑名单信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface ClmsBlackListService {

    PageResult<ClmsBlackListVO> getBlackListByCondition(ClmsBlackListVO clmsBlackListVO);

    void saveBlackList(ClmsBlackListVO clmsBlackListVO) throws Exception;

    void updateBlackList(ClmsBlackListVO clmsBlackListVO) throws Exception;

    ClmsBlackListVO getBlackListById(String id) throws Exception;


}
