package com.paic.ncbs.claim.service.checkloss;


import com.paic.ncbs.claim.model.dto.duty.PersonRescueDTO;
import com.paic.ncbs.claim.model.vo.duty.PersonRescueVO;

import java.util.List;

public interface PersonRescueService {

	public void savePersonRescue(List<PersonRescueDTO> personRescueList, String reportNo, Integer caseTimes, String taskId);

	public PersonRescueVO getPersonRescueVO(String reportNo, Integer caseTimes, String status, String taskId);

	public List<PersonRescueDTO> getPersonRescue(String reportNo, Integer caseTimes, String status, String taskId);

	public void removePersonRescue(String reportNo, Integer caseTimes, String taskId);


}
