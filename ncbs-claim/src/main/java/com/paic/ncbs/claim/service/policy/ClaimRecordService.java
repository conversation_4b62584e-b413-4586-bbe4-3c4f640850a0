package com.paic.ncbs.claim.service.policy;

import com.paic.ncbs.claim.model.dto.policy.ClaimRecordDTO;
import com.paic.ncbs.claim.model.dto.report.CustomerDTO;
import com.paic.ncbs.claim.model.dto.report.RecordDutyInfo;

import java.util.List;

public interface ClaimRecordService {

    List<CustomerDTO> getCustomerByPolicy(ClaimRecordDTO claimRecordDTO);

    List<CustomerDTO> getUnResolvedReportNo(ClaimRecordDTO claimRecordDTO);

    List<RecordDutyInfo> getRecordDutyByPolicy(ClaimRecordDTO claimRecordDTO);

    void getRiskProperty(ClaimRecordDTO claimRecordDTO);
}
