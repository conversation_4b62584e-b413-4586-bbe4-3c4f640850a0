package com.paic.ncbs.claim.service.prepay;

import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.vo.ahcs.FeeBigVO;
import com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO;
import com.paic.ncbs.claim.model.vo.ahcs.PrePayCaseVO;

import java.util.List;


public interface PrePayService {

	/**
	 *
	 * @Description: 判断是否可以发起预赔申请
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 * 返回类型  List<FeePayDTO>
	 */
	public void isCanPrePayApply(String reportNo, Integer caseTimes);

	/**
	 * 查询预赔次数，逻辑：已审批完成的预赔次数+1
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 * @throws
	 */
	public Integer getApprovedSubTimes(String reportNo, Integer caseTimes);

	/**
	 *
	 * @Description: 获取预赔明细信息
	 * @param prepayInfoDTO
	 * @return
	 * @throws
	 *
	 */
	public List<DutyPrepayInfoDTO> getDutyPrepayInfoList(DutyPrepayInfoDTO prepayInfoDTO);


	PrePayCaseVO getPrePayCaseList(String reportNo, Integer caseTimes,Integer subTimes);

	void savePrePayApply(PrePayCaseVO prePayCaseVO);

	void sendPrePayApprove(PrePayInfoDTO prePayInfoDTO,List<String> msg);

	List<PrePayInfoDTO> getHistoryPrePayApprove( String reportNo, Integer caseTimes);

	PrePayInfoDTO getPrePayWaitApprove( String reportNo, Integer caseTimes);

	/**
	 * 案件是否有预赔，已申请待审批、或已审批同意的都算有预赔
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	boolean hasPrePayHistory(String reportNo,Integer caseTimes);

	FeeBigVO getPrePayFee(String reportNo, Integer caseTimes);

	void savePrePayFee(FeeBigVO feeBig);

	List<PaymentItemDTO> getPrePaymentItem(PaymentItemDTO paymentItemDTO);

	Integer getPrePayApplyCount(String reportNo,Integer caseTimes);

	PrePayCaseVO getPrePaySumList(String reportNo, Integer caseTimes);

	/**
	 * 预赔申请记录飘红提示
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	Integer getPrePayCount(String reportNo, Integer caseTimes);


	/**
	 * 校验用户权限是否满足
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	Boolean checkPermission(String reportNo,Integer caseTimes,String taskId);
}
