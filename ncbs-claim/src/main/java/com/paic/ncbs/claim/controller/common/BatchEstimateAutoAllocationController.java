package com.paic.ncbs.claim.controller.common;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.service.report.EstimateAutoAllocationService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 未决批处理：超15天报案未立案的 自动分配未决金额立案
 */
@RestController
@RequestMapping("/public/app/batchEstimate")
@Api(tags = {"未决批处理"})
@Slf4j
public class BatchEstimateAutoAllocationController {
    @Autowired
    private EstimateAutoAllocationService estimateAutoAllocationService;
    @PostMapping("/autoAllocation")
    public ResponseResult autoAllocation() {
        estimateAutoAllocationService.autoAllocation();
        return ResponseResult.success();
    }

}
