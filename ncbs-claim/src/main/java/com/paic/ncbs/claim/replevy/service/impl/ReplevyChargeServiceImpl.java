package com.paic.ncbs.claim.replevy.service.impl;

import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.PermissionUserMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.replevy.dao.*;
import com.paic.ncbs.claim.replevy.entity.*;
import com.paic.ncbs.claim.replevy.service.ReplevyChargeService;
import com.paic.ncbs.claim.replevy.service.ReplevyService;
import com.paic.ncbs.claim.replevy.vo.*;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.common.ClaimWorkFlowService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.service.user.PermissionService;
import com.paic.ncbs.claim.service.verify.VerifyService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.paic.ncbs.claim.common.constant.SettleConst.CLAIM_TYPE_REP_PAY;


/**
 * <p>
 * 追偿费用信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
@Slf4j
public class ReplevyChargeServiceImpl implements ReplevyChargeService {
    @Autowired
    public ClmsReplevyChargeMapper clmsReplevyChargeMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private TaskInfoService taskInfoService;
    @Autowired
    private ClmsReplevyMainMapper clmsReplevyMainMapper;//主表
    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    private ClaimWorkFlowService claimWorkFlowService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    @Lazy
    private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;

    @Autowired
    private ReinsuranceService reinsuranceService;
    @Autowired
    private ClmsReplevyTextMapper clmsReplevyTextMapper;
    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private CoinsureService coinsureService;
    @Autowired
    private PaymentItemService paymentItemService;
    @Autowired
    private PaymentInfoService paymentInfoService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private CaseProcessService caseProcessService;
    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;
    @Autowired
    private ClmsReplevyLossMapper clmsReplevyLossMapper;
    @Autowired
    PaymentItemMapper paymentItemMapper;
    @Autowired
    ClmsRelatedActualReceiptMapper clmsRelatedActualReceiptMapper;
    @Autowired
    PaymentInfoMapper paymentInfoMapper;
    @Autowired
    private ReplevyService replevyService;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    private TaskPoolService taskPoolService;
    @Autowired
    private PermissionUserMapper permissionUserMapper;
    @Autowired
    private ClmsReplevyDetailMapper clmsReplevyDetailMapper;
    @Value("${ncbs.pay.otherFeeSettlementResultURL:http://ncbs-claim.lb.ssdev.com:48915/claim/public/pay/otherFeeSettlementResult}")
    private String otherFeeSettlementResultURL;
    /**
     * 追偿费用审批
     * @param clmsReplevyTextVo
     * @param msgList
     * @throws Exception
     */
    @Override
    @Transactional
    public void sendRelevyFeeAudit(ClmsReplevyTextVo clmsReplevyTextVo, List<String> msgList) throws Exception {
        String reportNo = clmsReplevyTextVo.getReportNo();
        String approveOpinion = clmsReplevyTextVo.getApproveOpinion();
        Integer caseTimes = 1;
        String opinionType = ReplevyConstant.OPINION_TYPE_F;
        clmsReplevyTextVo.setOpinionType(opinionType);
        String loginUm = WebServletContext.getUserId() == null ? ConstValues.SYSTEM_UM : WebServletContext.getUserId();
        String selectedUserId = clmsReplevyTextVo.getSelectedUserId();
        String applyUm = "";
        String comCode = WebServletContext.getComCode();
        ClmsReplevyMainVo clmsReplevyMainVo = new ClmsReplevyMainVo();
        clmsReplevyMainVo.setReportNo(reportNo);
        clmsReplevyMainVo.setReplevyNo(clmsReplevyTextVo.getReplevyNo());
        clmsReplevyMainVo = clmsReplevyMainMapper.selectClmsReplevyMain(clmsReplevyMainVo);

        //查询当前待审批任务
        String clmsReplevyTextId;
        String applyText;
        ClmsReplevyTextVo clmsReplevyTextVo1 = new ClmsReplevyTextVo();
        clmsReplevyTextVo1.setReportNo(reportNo);
        clmsReplevyTextVo1.setCaseTimes(caseTimes);
        clmsReplevyTextVo1.setOpinionType(opinionType);
        clmsReplevyTextVo1.setReplevyChargeId(clmsReplevyTextVo.getReplevyChargeId());
        clmsReplevyTextVo1.setFlag("0");
        List<ClmsReplevyTextVo> clmsReplevyTextVos = clmsReplevyTextMapper.selectClmsReplevyTextVo(clmsReplevyTextVo1);
        if (!CollectionUtils.isEmpty(clmsReplevyTextVos)){
            clmsReplevyTextId = clmsReplevyTextVos.get(0).getId();
            applyUm = clmsReplevyTextVos.get(0).getApplyUm();
            applyText=clmsReplevyTextVos.get(0).getApplyText();
        }else{
            throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(), "不存在审批中的追偿费用");
        }
        Date nowDate = new Date();
        ClmsReplevyText clmsReplevyText = new ClmsReplevyText();
        clmsReplevyText.setId(clmsReplevyTextId);
        clmsReplevyText.setApproveOpinion(approveOpinion);
        clmsReplevyText.setHandleText(clmsReplevyTextVo.getHandleText());
        clmsReplevyText.setApproveUm(loginUm);
        clmsReplevyText.setApplyUm(applyUm);
        clmsReplevyText.setSysUtime(nowDate);
        clmsReplevyText.setHandleDate(nowDate);
        clmsReplevyText.setFlag("1");
        clmsReplevyTextMapper.updateByPrimaryKeySelective(clmsReplevyText);
        ClmsReplevyCharge clmsReplevyCharge1 = clmsReplevyChargeMapper.selectById(clmsReplevyTextVo.getReplevyChargeId());
        String handleText = clmsReplevyTextVo.getHandleText();
        boolean pass = true;
        boolean agree = ConstValues.AUDIT_AGREE_CODE.equals(approveOpinion);
        if (agree) {
            LogUtil.audit("追偿费用审批同意, reportNo={}, caseTimes={}", reportNo, caseTimes);
            pass = checkReplevyFeePremission(reportNo, clmsReplevyTextVo.getTaskId(), opinionType, loginUm, selectedUserId);
            LogUtil.audit("追偿费用审批权限是否满足={}", pass);
            String tips = "";
            if (pass) {
                LogUtil.audit("追偿费用审批完成");
                ClmsReplevyCharge clmsReplevyCharge = new ClmsReplevyCharge();
                clmsReplevyCharge.setApproveFlag("3");//3-审批通过
                clmsReplevyCharge.setFlag("1");
                clmsReplevyCharge.setUpdatedBy(loginUm);//修改人
                clmsReplevyCharge.setSysUtime(new Date());//修改时间
                clmsReplevyCharge.setId(clmsReplevyTextVo.getReplevyChargeId());
                clmsReplevyCharge.setFinishDate(new Date());//结案时间
                clmsReplevyChargeMapper.updateSelectiveByPrimaryKey(clmsReplevyCharge);
                //存费用支付项数据，送费用数据给收付
                this.sendPaySys(reportNo,clmsReplevyCharge1.getSerialNo(),clmsReplevyTextVo.getReplevyNo(),opinionType,approveOpinion);
                LogUtil.audit("开始同步立案信息");
                // 追偿，发送再保
                RepayCalDTO repayCalDTO = new RepayCalDTO();
                repayCalDTO.setReportNo(reportNo);
                repayCalDTO.setCaseTimes(1);
                repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.REPLEVY);
                repayCalDTO.setSubTimes(clmsReplevyCharge1.getSerialNo());
                repayCalDTO.setPaymentType(SettleConst.PAYMENT_TYPE_REPLEVY_FEE);
                reinsuranceService.sendReinsurance(repayCalDTO);
            }else{
                msgList.add("提交成功，待上级审批人继续处理");
                ClmsReplevyText replevyText = new ClmsReplevyText();
                replevyText.setReplevyId(clmsReplevyMainVo.getId());
                replevyText.setReplevyChargeId(clmsReplevyTextVo.getReplevyChargeId());
                replevyText.setReportNo(reportNo);
                replevyText.setReplevyNo(clmsReplevyMainVo.getReplevyNo());//追偿号
                replevyText.setCaseTimes(caseTimes);
                replevyText.setReplevyTimes(clmsReplevyMainVo.getReplevyTimes());
                replevyText.setMakeCom(comCode);//操作机构
                replevyText.setOpinionType(opinionType);//业务动作
                replevyText.setApplyText(applyText);//申请意见
                saveReplevyText(replevyText);
                tips = " 待上级审批人继续处理";

            }
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_REPLEVY_FEE_REVIEW, "通过", StringUtils.isEmptyStr(handleText) ? tips : handleText.concat(tips), loginUm);
        }else{
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_REPLEVY_FEE_REVIEW, "不通过", handleText, loginUm);
            //更新追偿费用为退回
            ClmsReplevyCharge clmsReplevyCharge = new ClmsReplevyCharge();
            clmsReplevyCharge.setApproveFlag("4");//4-审批退回
            clmsReplevyCharge.setUpdatedBy(loginUm);//修改人
            clmsReplevyCharge.setSysUtime(new Date());//修改时间
            clmsReplevyCharge.setId(clmsReplevyTextVo.getReplevyChargeId());
            clmsReplevyChargeMapper.updateSelectiveByPrimaryKey(clmsReplevyCharge);
            //不同意 删除支付项
            this.sendPaySys(reportNo,clmsReplevyCharge1.getSerialNo(),null,opinionType,approveOpinion);
        }
        //审核完成关闭追偿费用审批任务
        claimWorkFlowService.dealReplevyWorkFlowData(reportNo,1,"FS",clmsReplevyTextVo.getTaskId(),null,null);
    }

    /**
     * 追偿审批
     * @param clmsReplevyTextVo
     * @param msgList
     * @throws Exception
     */
    @Override
    @Transactional
    public void sendRelevyAudit(ClmsReplevyTextVo clmsReplevyTextVo, List<String> msgList) throws Exception {
        String reportNo = clmsReplevyTextVo.getReportNo();
        Integer caseTimes = 1;
        String opinionType = ReplevyConstant.OPINION_TYPE_Z;
        String loginUm = WebServletContext.getUserId() == null ? ConstValues.SYSTEM_UM : WebServletContext.getUserId();
        String selectedUserId = clmsReplevyTextVo.getSelectedUserId();
        String comCode = WebServletContext.getComCode();
        ClmsReplevyMainVo clmsReplevyMainVo = new ClmsReplevyMainVo();
        clmsReplevyMainVo.setReportNo(reportNo);
        clmsReplevyMainVo.setFlag("1");
        clmsReplevyMainVo = clmsReplevyMainMapper.selectClmsReplevyMain(clmsReplevyMainVo);
        //查询
        //checkReplevyFeeAudit(clmsReplevyTextVo);
        //查询当前待审批任务
        String clmsReplevyTextId;
        String applyText;
        ClmsReplevyTextVo clmsReplevyTextVo1 = new ClmsReplevyTextVo();
        clmsReplevyTextVo1.setReportNo(reportNo);
        clmsReplevyTextVo1.setOpinionType(opinionType);
        clmsReplevyTextVo1.setFlag("0");
        List<ClmsReplevyTextVo> clmsReplevyTextVos = clmsReplevyTextMapper.selectClmsReplevyTextVo(clmsReplevyTextVo1);
        if (!CollectionUtils.isEmpty(clmsReplevyTextVos)){
            clmsReplevyTextId = clmsReplevyTextVos.get(0).getId();
            applyText = clmsReplevyTextVos.get(0).getApplyText();
        }else{
            throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(), "不存在审批中的追偿任务");
        }
        String handleText = clmsReplevyTextVo.getHandleText();
        ClmsReplevyText clmsReplevyText = new ClmsReplevyText();
        Date nowDate = new Date();
        clmsReplevyText.setId(clmsReplevyTextId);
        clmsReplevyText.setApproveOpinion(clmsReplevyTextVo.getApproveOpinion());
        clmsReplevyText.setHandleText(handleText);
        clmsReplevyText.setApproveUm(loginUm);
        clmsReplevyText.setApplyUm(clmsReplevyTextVo.getApplyUm());
        clmsReplevyText.setSysUtime(nowDate);
        clmsReplevyText.setHandleDate(nowDate);
        clmsReplevyText.setFlag("1");
        clmsReplevyTextMapper.updateByPrimaryKeySelective(clmsReplevyText);
        String approveOpinion = clmsReplevyTextVo.getApproveOpinion();
        boolean pass = true;
        boolean agree = ConstValues.AUDIT_AGREE_CODE.equals(approveOpinion);
        if (agree) {
            LogUtil.audit("追偿审批同意, reportNo={}, caseTimes={}", reportNo, caseTimes);
            pass = checkReplevyFeePremission(reportNo, clmsReplevyTextVo.getTaskId(),opinionType, loginUm,selectedUserId);
            LogUtil.audit("追偿审批权限是否满足={}", pass);
            String tips = "";
            if (pass) {
                ClmsReplevyMain clmsReplevyMainPo = new ClmsReplevyMain();
                clmsReplevyMainPo.setFlag("2");//0-追偿处理中 1-追偿待审核 2-追偿已完成
                clmsReplevyMainPo.setApproveFlag("3");//4-审批退回
                clmsReplevyMainPo.setUpdatedBy(loginUm);//修改人
                clmsReplevyMainPo.setSysUtime(new Date());
                clmsReplevyMainPo.setFinishDate(new Date());
                clmsReplevyMainPo.setId(clmsReplevyMainVo.getId());
                clmsReplevyMainMapper.updateByPrimaryKeySelective(clmsReplevyMainPo);
                //送收付
                if(clmsReplevyMainVo.getSumRealReplevy()!=null&&clmsReplevyMainVo.getSumRealReplevy().compareTo(BigDecimal.ZERO)>0){
                    //送收付
                    this.sendPaySys(reportNo,clmsReplevyMainVo.getReplevyTimes(),clmsReplevyTextVo.getReplevyNo(),opinionType,approveOpinion);
                    //送再保
                    RepayCalDTO repayCalDTO = new RepayCalDTO();
                    repayCalDTO.setReportNo(reportNo);
                    repayCalDTO.setCaseTimes(caseTimes);
                    repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.REPLEVY);
                    repayCalDTO.setSubTimes(clmsReplevyTextVo.getReplevyTimes());
                    repayCalDTO.setPaymentType(SettleConst.PAYMENT_TYPE_REPLEVY_PAY);
                    reinsuranceService.sendReinsurance(repayCalDTO);
                }
            }else{
                msgList.add("提交成功，待上级审批人继续处理");
                ClmsReplevyText replevyText = new ClmsReplevyText();
                replevyText.setReplevyId(clmsReplevyMainVo.getId()); //主表信息id
                replevyText.setReplevyNo(clmsReplevyMainVo.getReplevyNo());//追偿号
                replevyText.setReportNo(reportNo);
                replevyText.setCaseTimes(caseTimes);
                replevyText.setReplevyTimes(clmsReplevyMainVo.getReplevyTimes());
                replevyText.setMakeCom(comCode);//操作机构
                replevyText.setOpinionType(opinionType);//业务动作
                replevyText.setApplyText(applyText);//申请意见
                saveReplevyText(replevyText);
                tips = " 待上级审批人继续处理";
            }
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_REPLEVY_REVIEW, "通过", StringUtils.isEmptyStr(handleText) ? tips : handleText.concat(tips), loginUm);
            //审核完成关闭追偿审批任务
            claimWorkFlowService.dealReplevyWorkFlowData(reportNo,caseTimes,"ZS",clmsReplevyTextVo.getTaskId(),null,null);
        }else{
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_REPLEVY_REVIEW, "不通过", handleText, loginUm);
            ClmsReplevyMain clmsReplevyMainPo = new ClmsReplevyMain();
            clmsReplevyMainPo.setFlag("0");//0-追偿处理中 1-追偿待审核 2-追偿已完成
            clmsReplevyMainPo.setApproveFlag("4");//4-审批退回
            clmsReplevyMainPo.setUpdatedBy(loginUm);//修改人
            clmsReplevyMainPo.setSysUtime(new Date());
            clmsReplevyMainPo.setId(clmsReplevyMainVo.getId());
            clmsReplevyMainMapper.updateByPrimaryKeySelective(clmsReplevyMainPo);
            //追偿审核退回
            claimWorkFlowService.dealReplevyWorkFlowData(reportNo,caseTimes,"ZT",clmsReplevyTextVo.getTaskId(),null,null);
            if(clmsReplevyMainVo.getSumRealReplevy()!=null&&clmsReplevyMainVo.getSumRealReplevy().compareTo(BigDecimal.ZERO)>0){
                this.sendPaySys(reportNo,clmsReplevyMainVo.getReplevyTimes(),clmsReplevyMainVo.getReplevyNo(),ReplevyConstant.OPINION_TYPE_Z,approveOpinion);
            }
        }
    }
    /**
     * 校验当前人员是否有权限处理审批任务
     * @param reportNo
     * @param taskId
     * @param loginUm
     * @return
     */
    private boolean checkReplevyFeePremission (String reportNo, String taskId,String opinionType, String loginUm,String selectedUserId){
        LogUtil.audit("当前任务id={}", taskId);
        TaskInfoDTO taskDto = taskInfoService.getTaskDtoByTaskId(taskId);
        if (taskDto == null) {
            LogUtil.audit("taskId查任务为空");
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        if (taskDto.getTaskGrade() == null) {
            throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(), "案件追偿审批权限为空");
        }
        //追偿费用审批和追偿审批都是用核赔级别权限
        Integer userGrade = permissionService.getUserGrade(Constants.PERMISSION_VERIFY,taskDto.getDepartmentCode(),loginUm);
        if(userGrade == null){
            throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"你未配置案件机构的核赔权限");
        }
        if(taskDto.getTaskGrade() == null){
            throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"案件核赔权限为空");
        }
        if(userGrade >= taskDto.getTaskGrade()){
            return true;
        } else {
            List<String> taskIdList = new ArrayList<>();
            //权限不满足，继续找高一个级别的人处理
            String departmentCode = policyInfoMapper.getPolicyDeptByReportNo(reportNo);
            userGrade++;
            PermissionUserDTO permissionUser = permissionService.getLatestGrade(Constants.PERMISSION_VERIFY, departmentCode, userGrade);
            TaskInfoDTO startTask = new TaskInfoDTO();
            BeanUtils.copyProperties(taskDto, startTask);
            String newtaskId = UuidUtil.getUUID();
            taskIdList.add(newtaskId);
            startTask.setTaskId(newtaskId);
            if(ReplevyConstant.OPINION_TYPE_Z.equals(opinionType)){
                startTask.setTaskDefinitionBpmKey(BpmConstants.OC_REPLEVY_REVIEW);
            }else{
                startTask.setTaskDefinitionBpmKey(BpmConstants.OC_REPLEVY_FEE_REVIEW);
            }
            startTask.setAssigneeTime(new Date());
            startTask.setStatus(BpmConstants.TASK_STATUS_PENDING);
            startTask.setCreatedBy(loginUm);
            startTask.setUpdatedBy(loginUm);
            startTask.setAssigner(null);
            startTask.setAssigneeName(null);
            startTask.setBusinessKey(taskDto.getBusinessKey());
            startTask.setApplyer(taskDto.getAssigner());
            startTask.setApplyerName(taskDto.getAssigneeName());
            startTask.setPreTaskId(taskDto.getTaskId());
            startTask.setAuditGrade(userGrade);
            startTask.setIdAhcsTaskInfo(UuidUtil.getUUID());
            startTask.setDepartmentCode(permissionUser.getComCode());
            taskInfoMapper.addTaskInfo(startTask);
            //自动调度
            if(!StringUtils.isEmptyStr(selectedUserId)){
                String [] parts = selectedUserId.split("-",2);
                String managerUserId = parts[0];
                String managerUserName = parts[1];
                String comCode = permissionUserMapper.getComCodeByUserId(managerUserId);
                taskPoolService.dispatchTask(taskIdList,managerUserId,managerUserName,comCode);
            }
            return false;
        }
    }
    /**
     * 追偿费用和追偿金额送收付
     */
    private void sendPaySys(String reportNo,int subTimes,String replevyNo,String opinionType,String approveOpinion) {
        Integer caseTimes = 1;
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setReportNo(reportNo);
        paymentItemDTO.setCaseTimes(caseTimes);
        paymentItemDTO.setClaimType(CLAIM_TYPE_REP_PAY);
        paymentItemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_30);
        List<PaymentItemDTO> allItems = new ArrayList<>();
        if(ReplevyConstant.OPINION_TYPE_F.equals(opinionType)){//追偿费用送收付
            paymentItemDTO.setSubTimes(subTimes);
            paymentItemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_REPLEVY_FEE);
            allItems = paymentItemService.getAllPaymentItem(paymentItemDTO);
            if (CollectionUtils.isEmpty(allItems)){
                return;
            }
            //根据费用Id查询追偿费用信息
            if("1".equals(approveOpinion)){
                //通知收付费
                payInfoNoticeThirdPartyCoreSAO.noticeReplevyPayment(allItems,reportNo,null);
            }else if("2".equals(approveOpinion)){//追偿退回删除支付项
                for (PaymentItemDTO item : allItems){
                    paymentItemMapper.delPaymentPlanAndDutyById(item.getIdClmPaymentItem(),Constants.PAYMENT_ITEM_STATUS_30);
                }
            }
        }else if(ReplevyConstant.OPINION_TYPE_Z.equals(opinionType)){//追偿金额送收付
            paymentItemDTO.setSubTimes(subTimes);
            paymentItemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_REPLEVY_PAY);
            allItems = paymentItemService.getAllPaymentItem(paymentItemDTO);
            if (CollectionUtils.isEmpty(allItems)){
                return;
            }
            if("1".equals(approveOpinion)){//追偿送收付
                //追偿金额送收付
                payInfoNoticeThirdPartyCoreSAO.noticeReplevyPayment(allItems,reportNo,null);
                LogUtil.audit("追偿核销开始");
                //构建收付确认报文，送收付核销 start
                LogUtil.audit("构建收付确认报文-追偿核销开始");
                sendPaymentConfirm(allItems.get(0).getBatchNo(),ReplevyConstant.RECEIPT_TYPE_RELEVY,replevyNo);
                LogUtil.audit("构建收付确认报文-追偿核销结束");
            }else if("2".equals(approveOpinion)){//追偿退回删除支付项
                for (PaymentItemDTO item : allItems){
                    paymentItemMapper.delPaymentPlanAndDutyById(item.getIdClmPaymentItem(),Constants.PAYMENT_ITEM_STATUS_30);
                }
                //解冻
                replevyService.sendPayThaw(reportNo,subTimes,replevyNo, ReplevyConstant.FREEZE_STATUS_R,ReplevyConstant.RECEIPT_TYPE_RELEVY);
            }
        }
    }
    /**
     * 构建支付项
     * @return
     */
    public PaymentItemDTO buildFeePaymemtItem(ClmsReplevyCharge clmsReplevyCharge, ClmsReplevyMain clmsReplevyMainVo){
        PaymentItemDTO itemDTO = new PaymentItemDTO();
        String loginUm = WebServletContext.getUserId() == null ? ConstValues.SYSTEM_UM : WebServletContext.getUserId();
        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(clmsReplevyMainVo.getPolicyNo());
        String departmentCode = MapUtils.getString(productMap,"departmentCode");
        String generateNo = commonService.generateNo(NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO, departmentCode);
        Map<String, List<CoinsureDTO>> coinsMap = coinsureService.getCoinsureListByReportNo(clmsReplevyMainVo.getReportNo());
        PaymentInfoDTO paymentInfoVo = paymentInfoMapper.getPaymentInfoById(clmsReplevyCharge.getPaymentInfoId());
        BeanUtils.copyProperties(paymentInfoVo,itemDTO);
        itemDTO.setCreatedBy(loginUm);
        itemDTO.setUpdatedBy(loginUm);
        itemDTO.setIdClmPaymentItem(UuidUtil.getUUID());
        itemDTO.setPolicyNo(clmsReplevyMainVo.getPolicyNo());
        itemDTO.setCaseNo(clmsReplevyMainVo.getCaseNo());
        itemDTO.setReportNo(clmsReplevyMainVo.getReportNo());
        itemDTO.setCaseTimes(clmsReplevyMainVo.getCaseTimes());
        itemDTO.setClaimType(SettleConst.CLAIM_TYPE_REP_PAY);
        itemDTO.setSubTimes(clmsReplevyCharge.getSerialNo());
        itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_REPLEVY_FEE);//追偿费用
        itemDTO.setIdClmPaymentInfo(clmsReplevyCharge.getPaymentInfoId());
        itemDTO.setCollectPaySign(SettleConst.COLLECTION);//0-收款 1-付款
        itemDTO.setPaymentAmount(clmsReplevyCharge.getChargeMoney());
        itemDTO.setPaymentCurrencyCode(PolicyConstant.CURRENCY_CODE_RMB);//人民币
        itemDTO.setCollectPayApproach(CollectPayApproachEnum.REAL_TIME_PAYMENT.getType());
        itemDTO.setMergeSign(SettleConst.NOT_MERGE);
        itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_30);
        itemDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
        itemDTO.setCompensateNo(generateNo);
        itemDTO.setFeeType(clmsReplevyCharge.getChargeType());
        List<CoinsureDTO> coinsureList = coinsMap.get(clmsReplevyMainVo.getPolicyNo());
        if (CollectionUtils.isNotEmpty(coinsureList)) {
            CoinsureDTO coinsureDTO = coinsureList.stream().filter(i -> BaseConstant.STRING_1.equals(i.getCompanyFlag())).findFirst().orElse(null);
            if (Objects.nonNull(coinsureDTO)) {
                itemDTO.setCoinsuranceMark(BaseConstant.STRING_1);
                itemDTO.setAcceptInsuranceFlag(coinsureDTO.getAcceptInsuranceFlag());
                itemDTO.setCoinsuranceCompanyCode(coinsureDTO.getReinsureCompanyCode());
                itemDTO.setCoinsuranceCompanyName(coinsureDTO.getReinsureCompanyName());
                itemDTO.setCoinsuranceRatio(coinsureDTO.getReinsureScale());
                itemDTO.setIsFullPay(BaseConstant.STRING_1);
                itemDTO.setCoinsuranceActualAmount(itemDTO.getPaymentAmount());
            }
        }
        return itemDTO;
    }
    /**
     * 构建支付项
     * @return
     */
    public PaymentItemDTO buildPaymemtItem(ClmsRelatedActualReceipt clmsRelatedActualReceipt,ClmsReplevyMain clmsReplevyMainVo){
        PaymentItemDTO itemDTO = new PaymentItemDTO();
        String loginUm = WebServletContext.getUserId() == null ? ConstValues.SYSTEM_UM : WebServletContext.getUserId();
        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(clmsReplevyMainVo.getPolicyNo());
        String departmentCode = MapUtils.getString(productMap,"departmentCode");
        String generateNo = commonService.generateNo(NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO, departmentCode);
        Map<String, List<CoinsureDTO>> coinsMap = coinsureService.getCoinsureListByReportNo(clmsReplevyMainVo.getReportNo());
        itemDTO.setClientBankName(clmsRelatedActualReceipt.getPartnerBankName());
        itemDTO.setClientBankAccount(clmsRelatedActualReceipt.getPartnerBankAccount());
        itemDTO.setIdClmPaymentInfo(clmsRelatedActualReceipt.getId());
        itemDTO.setCreatedBy(loginUm);
        itemDTO.setUpdatedBy(loginUm);
        itemDTO.setPolicyNo(clmsReplevyMainVo.getPolicyNo());
        itemDTO.setCaseNo(clmsReplevyMainVo.getCaseNo());//赔案号
        itemDTO.setReportNo(clmsReplevyMainVo.getReportNo());
        itemDTO.setCaseTimes(clmsReplevyMainVo.getCaseTimes());
        itemDTO.setClaimType(SettleConst.CLAIM_TYPE_REP_PAY);
        itemDTO.setSubTimes(clmsReplevyMainVo.getReplevyTimes());
        itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_REPLEVY_PAY);//追偿
        itemDTO.setCollectPaySign(SettleConst.COLLECT_SIGN);//0-收款 1-付款
        itemDTO.setPaymentAmount(clmsRelatedActualReceipt.getWriteOffAmount().multiply(new BigDecimal("-1")));
        itemDTO.setPaymentCurrencyCode(PolicyConstant.CURRENCY_CODE_RMB);//人民币
        itemDTO.setCollectPayApproach(CollectPayApproachEnum.BATCH_TRANSFER.getType());
        itemDTO.setMergeSign(SettleConst.NOT_MERGE);
        itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_30);
        itemDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
        itemDTO.setCompensateNo(generateNo);
        List<CoinsureDTO> coinsureList = coinsMap.get(clmsReplevyMainVo.getPolicyNo());
        if (CollectionUtils.isNotEmpty(coinsureList)) {
            CoinsureDTO coinsureDTO = coinsureList.stream().filter(i -> BaseConstant.STRING_1.equals(i.getCompanyFlag())).findFirst().orElse(null);
            if (Objects.nonNull(coinsureDTO)) {
                itemDTO.setCoinsuranceMark(BaseConstant.STRING_1);
                itemDTO.setAcceptInsuranceFlag(coinsureDTO.getAcceptInsuranceFlag());
                itemDTO.setCoinsuranceCompanyCode(coinsureDTO.getReinsureCompanyCode());
                itemDTO.setCoinsuranceCompanyName(coinsureDTO.getReinsureCompanyName());
                itemDTO.setCoinsuranceRatio(coinsureDTO.getReinsureScale());
                itemDTO.setIsFullPay(BaseConstant.STRING_1);
                itemDTO.setCoinsuranceActualAmount(itemDTO.getPaymentAmount());
            }
        }
        return itemDTO;
    }
    //构建收费确认上报报文
    public void sendPaymentConfirm(String batchNo, String receiptType,String originBusinessNo){
        PayThawBody payThawBody = new PayThawBody();
        //获取当前批次已送收付的收付信息
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        //paymentItemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_11);
        paymentItemDTO.setBatchNo(batchNo);
        List<PaymentItemDTO> paymentItemVOList = paymentItemMapper.getAllPaymentItem(paymentItemDTO);
        int count = 0;
        if(paymentItemVOList!=null&&paymentItemVOList.size()>0){
            count = paymentItemVOList.size();
        }else{
           throw new GlobalBusinessException("未找到收付信息");
        }
        String reportNo = paymentItemVOList.get(0).getReportNo();
        Integer caseTimes = paymentItemVOList.get(0).getCaseTimes();
        //获取银行流水信息
        BigDecimal sumPaymentAmount = BigDecimal.ZERO;
        List<BankTransFlow> bankTransFlowList = new ArrayList<>();
        ClmsRelatedActualReceipt relatedActualReceipt = new ClmsRelatedActualReceipt();
        relatedActualReceipt.setBatchNo(batchNo);
        List<ClmsRelatedActualReceipt> relatedActualReceiptList = clmsRelatedActualReceiptMapper.getRelatedActualReceiptByEntity(relatedActualReceipt);
        if (CollectionUtils.isEmpty(relatedActualReceiptList)){
            throw new GlobalBusinessException("未找到银行流水信息");
        }
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String formattedDate = sdf.format(date);
        BatchRecieveMainInfo batchRecieveMainInfo = new BatchRecieveMainInfo();
        batchRecieveMainInfo.setBatchNo(batchNo);
        batchRecieveMainInfo.setBatchType("3");
        batchRecieveMainInfo.setBatchSeqNo(formattedDate);
        batchRecieveMainInfo.setSystemSource("C");
        //理赔结算单号
        batchRecieveMainInfo.setOriginBusinessNo(originBusinessNo);
        //结算状态回调地址
        batchRecieveMainInfo.setInvokeStatusUrl(otherFeeSettlementResultURL);
        //追偿是否存在差异金额默认为否
        if(ReplevyConstant.RECEIPT_TYPE_RELEVY.equals(receiptType)){
            batchRecieveMainInfo.setDiffFlag("N");
            caseTimes =paymentItemVOList.get(0).getSubTimes();
        }
        payThawBody.setBatchRecieveMainInfo(batchRecieveMainInfo);
        List<BatchPaymentDetailInfo> batchPaymentDetailInfo = new ArrayList<>();

        for(PaymentItemDTO paymentItemVo : paymentItemVOList){
            BatchPaymentDetailInfo detailInfo = new BatchPaymentDetailInfo();
            detailInfo.setBusinessNo(paymentItemVo.getIdClmPaymentItem());
            detailInfo.setCurrency("CNY");
            BigDecimal planFee = paymentItemVo.getPaymentAmount();
            sumPaymentAmount = sumPaymentAmount.add(planFee);
            detailInfo.setPlanFee(planFee);
            batchPaymentDetailInfo.add(detailInfo);
        }
        payThawBody.setBatchPaymentDetailInfo(batchPaymentDetailInfo);
        //追偿核销为实时核销，仅核销本次金额
        batchRecieveMainInfo.setSumAmount(sumPaymentAmount);
        batchRecieveMainInfo.setSumCount(count);
        batchRecieveMainInfo.setTotalAmount(sumPaymentAmount);
        batchRecieveMainInfo.setTotalCount(count);
        for(ClmsRelatedActualReceipt clmsRelatedActualReceipt : relatedActualReceiptList){
            BankTransFlow bankTransFlow = new BankTransFlow();
            bankTransFlow.setBankTransFlowNo(clmsRelatedActualReceipt.getBankTransFlowNo());
            bankTransFlow.setCurrency("CNY");
            bankTransFlow.setPlanFee(clmsRelatedActualReceipt.getWriteOffAmount());
            bankTransFlowList.add(bankTransFlow);
        }
        payThawBody.setBankTransFlowList(bankTransFlowList);
        payInfoNoticeThirdPartyCoreSAO.sendPaymentConfirm(payThawBody, reportNo,caseTimes);
    }
    /**
     * 生成批次号
     * @return
     */
    public String generateBatchNumber() {
        // 获取当前时间并格式化为yyyyMMddHHmmss
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timePart = sdf.format(new Date());

        // 生成6位随机数
        Random random = new Random();
        int randomNum = random.nextInt(900000) + 100000; // 保证是6位数

        return timePart + randomNum;
    }
    public void saveReplevyText(ClmsReplevyText replevyTextPo){
        replevyTextPo.setId(UuidUtil.getUUID());
        String loginUm = WebServletContext.getUserId() == null ? ConstValues.SYSTEM_UM : WebServletContext.getUserId();
        replevyTextPo.setApplyUm(loginUm);//申请人
        replevyTextPo.setMakeCom(WebServletContext.getComCode()); // 操作机构
        Date currentTime = new Date();
        replevyTextPo.setApplyDate(currentTime);//申请时间
        replevyTextPo.setCreatedBy(loginUm);
        replevyTextPo.setSysCtime(currentTime);
        replevyTextPo.setUpdatedBy(WebServletContext.getUserName());
        replevyTextPo.setSysUtime(currentTime);
        replevyTextPo.setValidFlag("Y"); // 有效标志
        replevyTextPo.setFlag("0"); // 标志字段：0-审批中，1-审批通过
        //replevyTextPo.setSerialNo(1); // 序号
        clmsReplevyTextMapper.insertSelective(replevyTextPo);
    }
}