package com.paic.ncbs.claim.service.taskdeal;


import com.paic.ncbs.claim.model.dto.taskdeal.ThirdCarDTO;

import java.util.List;

public interface ThirdCarService {


	public void removeThirdCarList(String idAhcsPersonAccident);


	public List<ThirdCarDTO> getThirdCarList(String idAhcsPersonAccident);


	public void addThirdCarList(List<ThirdCarDTO> thirdCarList, String userId , String idAhcsPersonAccident);


}
