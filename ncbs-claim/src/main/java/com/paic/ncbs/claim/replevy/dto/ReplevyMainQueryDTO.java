package com.paic.ncbs.claim.replevy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 追偿主表查询结果DTO
 */
@Data
@ApiModel("追偿主表查询结果DTO")
public class ReplevyMainQueryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty("报案号")
    private String reportNo;
    
    @ApiModelProperty("追偿号")
    private String replevyNo;
    
    @ApiModelProperty("序号")
    private Integer serialNo;
    
    @ApiModelProperty("总追偿收入金额")
    private BigDecimal sumRealReplevy;
    
    @ApiModelProperty("标志")
    private String flag;
    
    @ApiModelProperty("处理状态")
    private String processStatus;

    @ApiModelProperty("处理状态")
    private String processStatusName;

    @ApiModelProperty("赔偿次数")
    private String caseTimes;

    @ApiModelProperty("当前处理人")
    private String assigner;

    @ApiModelProperty("机构代码")
    private String makeCom;
}
