package com.paic.ncbs.claim.model.vo.blacklist;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class BlackListDictConverter {

    // 黑名单类型映射
    private static final Map<String, String> PARTY_TYPE_MAP = new HashMap<>();
    // 风险类型映射
    private static final Map<String, String> RISK_TYPE_MAP = new HashMap<>();
    // 证件类型映射
    private static final Map<String, String> CERTIFICATE_TYPE_MAP = new HashMap<>();

    static {
        // 黑名单类型字典
        PARTY_TYPE_MAP.put("1", "出险人");
        PARTY_TYPE_MAP.put("2", "投保单位");
        PARTY_TYPE_MAP.put("3", "代理人");
        PARTY_TYPE_MAP.put("4", "医院");
        PARTY_TYPE_MAP.put("5", "医生");
        PARTY_TYPE_MAP.put("6", "公估公司");
        PARTY_TYPE_MAP.put("7", "公估员");
        PARTY_TYPE_MAP.put("8", "鉴定机构");
        PARTY_TYPE_MAP.put("9", "鉴定人员");

        // 风险类型字典
        RISK_TYPE_MAP.put("1", "保险欺诈");
        RISK_TYPE_MAP.put("2", "高赔付客户");
        RISK_TYPE_MAP.put("3", "恶意投诉");
        RISK_TYPE_MAP.put("4", "工作质量低劣");
        RISK_TYPE_MAP.put("5", "诉讼仲裁");
        RISK_TYPE_MAP.put("6", "负面信息");

        // 证件类型字典
        CERTIFICATE_TYPE_MAP.put("01", "居民身份证");
        CERTIFICATE_TYPE_MAP.put("02", "护照");
        CERTIFICATE_TYPE_MAP.put("05", "驾驶证");
        CERTIFICATE_TYPE_MAP.put("06", "港澳通行证");
        CERTIFICATE_TYPE_MAP.put("08", "外国人永居留身份证");
        CERTIFICATE_TYPE_MAP.put("10", "台湾通行证");
        CERTIFICATE_TYPE_MAP.put("12", "军官证");
        CERTIFICATE_TYPE_MAP.put("13", "居民户口薄");
        CERTIFICATE_TYPE_MAP.put("14", "外国护照");
        CERTIFICATE_TYPE_MAP.put("95", "其他");
        CERTIFICATE_TYPE_MAP.put("71", "组织机构代码证");
        CERTIFICATE_TYPE_MAP.put("72", "税务登记证");
        CERTIFICATE_TYPE_MAP.put("74", "营业执照");
        CERTIFICATE_TYPE_MAP.put("75", "统一社会信用代码");
    }

    /**
     * 获取黑名单类型中文名称（支持多个，逗号分隔）
     */
    public String getPartyTypeNames(String partyTypes) {
        if (StringUtils.isBlank(partyTypes)) {
            return "";
        }

        return Arrays.stream(partyTypes.split(","))
                .map(code -> PARTY_TYPE_MAP.getOrDefault(code.trim(), "未知类型"))
                .collect(Collectors.joining(","));
    }

    /**
     * 获取风险类型中文名称（支持多个，逗号分隔）
     */
    public String getRiskTypeNames(String riskTypes) {
        if (StringUtils.isBlank(riskTypes)) {
            return "";
        }

        return Arrays.stream(riskTypes.split(","))
                .map(code -> RISK_TYPE_MAP.getOrDefault(code.trim(), "未知风险"))
                .collect(Collectors.joining(","));
    }

    /**
     * 获取证件类型中文名称
     */
    public String getIdTypeName(String idType) {
        return CERTIFICATE_TYPE_MAP.getOrDefault(idType, "未知证件");
    }

    /**
     * 获取黑名单类型中文名称
     */
    public String getPartyTypeName(String partyType) {
        return PARTY_TYPE_MAP.getOrDefault(partyType, "未知类型");
    }

    /**
     * 获取风险类型中文名称
     */
    public String getRiskTypeName(String riskType) {
        return RISK_TYPE_MAP.getOrDefault(riskType, "未知风险");
    }
}

