package com.paic.ncbs.claim.service.settle.factor.impl.savesettle;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.SettleHelper;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PlanPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyBatchMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskGroupDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyBatchPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.vo.settle.FeeAmountVO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.common.ClmBatchService;
import com.paic.ncbs.claim.service.common.ClmsCommonPolicyService;
import com.paic.ncbs.claim.service.duty.DutyDetailPayService;
import com.paic.ncbs.claim.service.duty.DutyPayService;
import com.paic.ncbs.claim.service.endcase.DutyBillLimitInfoService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.EndorsementService;
import com.paic.ncbs.claim.service.settle.PlanPayService;
import com.paic.ncbs.claim.service.settle.SettlePaymentService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle.ClmsDutyDetailBillSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle.SaveSettleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Slf4j
@Service
public class SaveSettleServiceImpl implements SaveSettleService {
    @Autowired
    private PolicyPayMapper policyPayMapper;
    @Resource(name = "endorsementService")
    private EndorsementService endorsementService;
    @Autowired
    private SettlePaymentService settlePaymentService;
    @Autowired
    private PolicyBatchMapper policyBatchMapper;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private AhcsCommonService ahcsCommonService;
    @Resource(name = "planPayService")
    private PlanPayService planPayService;
    @Resource(name = "dutyPayService")
    private DutyPayService dutyPayService;
    @Resource(name = "dutyDetailPayService")
    private DutyDetailPayService dutyDetailPayService;
    @Autowired
    private ClmsDutyDetailBillSettleService clmsDutyDetailBillSettleService;

    @Autowired
    private  DutyBillLimitInfoService dutyBillLimitInfoService;
    @Autowired
    private PlanPayMapper planPayDao;
    @Autowired
    private DutyPayMapper dutyPayMapper;

    @Autowired
    private DutyDetailPayMapper dutyDetailPayMapper;
    @Autowired
    private ClmsCommonPolicyService clmsPolicyService;
    @Autowired
    private ClmBatchService clmBatchService;

    /**
     * 先删除 在保存
     * @param claimCaseDTO
     */
    @Override
    @Transactional
    public void saveResult(ClaimCaseDTO claimCaseDTO) {
        String reportNo= claimCaseDTO.getReportNo();
        Integer caseTimes= claimCaseDTO.getCaseTimes();
        LogUtil.audit("--初始化赔付信息-统计保单理算金额总和,报案号：" + reportNo);
        SettleHelper.initSettleAmount(claimCaseDTO.getPolicyPayDTOList());
        LogUtil.audit("--初始化赔付信息-插入保单赔付批次明细表,报案号：" + reportNo);
        this.insertPolicyBatch(claimCaseDTO.getPolicyPayDTOList(), claimCaseDTO.getIdClmBatch());
        LogUtil.audit("--初始化赔付信息-删除保单赔付信息,报案号：" + reportNo);
        policyPayMapper.deletePolicyPays(reportNo, caseTimes);

        LogUtil.audit("--初始化赔付信息-插入理算赔付数据,报案号={} ,保单赔付信息={}" , reportNo,JsonUtils.toJsonString(claimCaseDTO.getPolicyPayDTOList()));
        this.insertPayInfo(claimCaseDTO.getPolicyPayDTOList());

        LogUtil.audit("--初始化赔付信息-自动批单,报案号：" + reportNo);
        endorsementService.autoGenerateEndorsement(reportNo, caseTimes, claimCaseDTO.getPolicyPayDTOList());

        LogUtil.audit("--初始化赔付信息-自动生成支付项（赔付信息）,报案号：" + reportNo);
        settlePaymentService.autoGeneratePaymentItems(reportNo,caseTimes, claimCaseDTO.getPolicyPayDTOList(), claimCaseDTO.getIdClmBatch());
        log.info("保存理算结果数据报案号={}，保存入参={}", claimCaseDTO.getReportNo(), JsonUtils.toJsonString(claimCaseDTO));
    }

    @Override
    @Transactional
    public void updateResutl(ClaimCaseDTO claimCaseDTO) {
        SettleHelper.initSettleAmount(claimCaseDTO.getPolicyPayDTOList());
        LogUtil.audit("--重新理算-更新理算赔付数据,报案号={}：重新理算结果={}" , claimCaseDTO.getReportNo(),JsonUtils.toJsonString(claimCaseDTO));
        updatePolicyPays(claimCaseDTO.getPolicyPayDTOList());
        LogUtil.audit("--重新理算-自动批单,报案号：" + claimCaseDTO.getReportNo());
        endorsementService.autoGenerateEndorsement(claimCaseDTO.getReportNo(), claimCaseDTO.getCaseTimes(), claimCaseDTO.getPolicyPayDTOList());
        LogUtil.audit("--重新理算-自动生成支付项（赔付信息）,报案号：" + claimCaseDTO.getReportNo());
        String idClmBatch = clmBatchService.insertBatch(claimCaseDTO.getReportNo(), claimCaseDTO.getCaseTimes(), SettleConst.SETTLE_STATUS_ON);
        settlePaymentService.autoGeneratePaymentItems(claimCaseDTO.getReportNo(),claimCaseDTO.getCaseTimes(),claimCaseDTO.getPolicyPayDTOList(),idClmBatch);

    }

    private void insertPolicyBatch(List<PolicyPayDTO> copyPolicyPays, String idAhcsBatch) {
        List<FeeInfoDTO> feeInfoList = new ArrayList<>();
        for (PolicyPayDTO policyPay : copyPolicyPays) {
            FeeAmountVO policyFee = SettleHelper.getPolicyFee(feeInfoList, policyPay.getPolicyNo(), policyPay.getCaseNo());
            insertPolicyBatch(policyPay, idAhcsBatch, policyFee);
        }
    }
    private void insertPolicyBatch(PolicyPayDTO policyPay, String idAhcsBatch, FeeAmountVO policyFee) {
        BigDecimal settleAmount = nvl(policyPay.getSettleAmount(), 0);
        PolicyBatchPayDTO policyBatch = new PolicyBatchPayDTO();
        policyBatch.setIdAhcsPolicyBatchPay(UuidUtil.getUUID());
        policyBatch.setIdAhcsBatch(idAhcsBatch);
        policyBatch.setCaseNo(policyPay.getCaseNo());
        policyBatch.setPolicyNo(policyPay.getPolicyNo());
        policyBatch.setClaimType(policyPay.getClaimType());
        policyBatch.setCaseTimes(policyPay.getCaseTimes());
        policyBatch.setCreatedBy(policyPay.getCreatedBy());
        policyBatch.setUpdatedBy(policyPay.getUpdatedBy());
        policyBatch.setPolicyFee(policyFee.getFeeAmountSum().subtract(policyFee.getFeeAmountAwa()));
        policyBatch.setPolicyPayAmount(settleAmount);
        BigDecimal prePayFee = new BigDecimal("0");
        policyBatch.setFinalFee(policyFee.getFeeAmountSum().subtract(policyFee.getFeeAmountAwa()).subtract(nvl(prePayFee, 0)));
        policyBatch.setPolicyDecreaseFee(policyFee.getFeeAmountAwa());
        policyBatch.setFinalPayAmount(settleAmount.subtract(nvl(policyPay.getPolicyPrePay(), 0)));
        policyBatch.setArchiveDate(new Date());
        policyBatchMapper.insertPolicyBatch(policyBatch);
    }
    public void insertPayInfo(List<PolicyPayDTO> policyPays) {
        List<PlanPayDTO> planPayArr = new ArrayList<>();
        List<DutyPayDTO> dutyPayArr = new ArrayList<>();
        List<DutyDetailPayDTO> detailPayArr = new ArrayList<>();

        for (PolicyPayDTO policyPayInfo : policyPays) {
            if(ListUtils.isNotEmpty(policyPayInfo.getRiskGroupList())){
                for (RiskGroupDTO group : policyPayInfo.getRiskGroupList()) {
                    for (CaseRiskPropertyDTO risk : group.getRiskPropertyInfoList()) {
                        riskPropertyService.setPlanDutyDetailRiskId(risk.getIdPlyRiskProperty(),risk.getPlanPayArr());
                        planPayArr.addAll(risk.getPlanPayArr());
                        for (PlanPayDTO plan : policyPayInfo.getPlanPayArr()) {
                            dutyPayArr.addAll(plan.getDutyPayArr());
                            for (DutyPayDTO duty : plan.getDutyPayArr()) {
                                detailPayArr.addAll(duty.getDutyDetailPayArr());
                            }
                        }
                    }
                }
            }else{
                planPayArr.addAll(policyPayInfo.getPlanPayArr());
                for (PlanPayDTO plan : policyPayInfo.getPlanPayArr()) {
                    dutyPayArr.addAll(plan.getDutyPayArr());
                    for (DutyPayDTO duty : plan.getDutyPayArr()) {
                        detailPayArr.addAll(duty.getDutyDetailPayArr());
                    }
                }
            }
        }
        if (ListUtils.isEmptyList(policyPays)
                || ListUtils.isEmptyList(planPayArr)
                || ListUtils.isEmptyList(dutyPayArr)
                || ListUtils.isEmptyList(detailPayArr)) {
            return;
        }
        insertPolicyInfo(policyPays, planPayArr, dutyPayArr, detailPayArr);
    }
    private void insertPolicyInfo(List<PolicyPayDTO> policyPays, List<PlanPayDTO> planPays, List<DutyPayDTO> dutyPays, List<DutyDetailPayDTO> detailPays) {
        List<List<PolicyPayDTO>> policyPaysList = ListUtils.getListByGroup(policyPays, 20);
        List<BIllSettleResultDTO> billSettleResultDTOList=new ArrayList<>();
        List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOS=new ArrayList<>();
        for (List<PolicyPayDTO> policyList : policyPaysList) {
            ahcsCommonService.batchHandlerTransactionalWithArgs(PolicyPayMapper.class, policyList,
                    ListUtils.GROUP_NUM, "insertPolicyPayInfoList");
        }
        List<List<PlanPayDTO>> planPaysList = ListUtils.getListByGroup(planPays, 20);
        for (List<PlanPayDTO> planList : planPaysList) {
            planPayService.insertPlanPayList(planList);
        }
        List<List<DutyPayDTO>> dutyPaysList = ListUtils.getListByGroup(dutyPays, 20);
        for (List<DutyPayDTO> dutyList : dutyPaysList) {
            dutyPayService.insertDutyPayList(dutyList);
        }
        List<List<DutyDetailPayDTO>> detailPaysList = ListUtils.getListByGroup(detailPays, 20);
        for (List<DutyDetailPayDTO> dutyDetailList : detailPaysList) {
            dutyDetailPayService.insertDutyDetailPayList(dutyDetailList);
            for (DutyDetailPayDTO detail :dutyDetailList) {
                if(CollectionUtil.isNotEmpty(detail.getBillSettleResultDTOList())){
                    billSettleResultDTOList.addAll(detail.getBillSettleResultDTOList());
                }
                if(CollectionUtil.isNotEmpty(detail.getDutyBillLimitInfoDTOList())){
                    dutyBillLimitInfoDTOS.addAll(detail.getDutyBillLimitInfoDTOList());
                }

            }
        }
        if(CollectionUtil.isNotEmpty(billSettleResultDTOList)){
            //责任明细发票理算信息
            clmsDutyDetailBillSettleService.saveBatch(billSettleResultDTOList);
        }
        //保存日限额信息
        if(CollectionUtil.isNotEmpty(dutyBillLimitInfoDTOS)){
            dutyBillLimitInfoService.saveList(dutyBillLimitInfoDTOS);
        }
    }
    public void updatePolicyPays(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }

        String userId = WebServletContext.getUserId();
        List<PlanPayDTO> planPayArr = new ArrayList<>();
        List<DutyPayDTO> dutyPayArr = new ArrayList<>();
        List<DutyDetailPayDTO> detailPayArr = new ArrayList<>();

        if (!CollectionUtils.isEmpty(policyPays)) {
            for (PolicyPayDTO policyPay : policyPays) {
                planPayArr.addAll(policyPay.getPlanPayArr());
                policyPay.setUpdatedBy(userId);
                policyPayMapper.updatePolicyPayInfoList(policyPay);
            }
        }
        if (!CollectionUtils.isEmpty(planPayArr)) {
            for (PlanPayDTO planPayDTO : planPayArr) {
                dutyPayArr.addAll(planPayDTO.getDutyPayArr());
                planPayDTO.setUpdatedBy(userId);
                planPayDao.updatePlanPayInfoList(planPayDTO);
                log.info("责任金额报案号={},责任理算金额信息={}",policyPays.get(0).getReportNo(),JsonUtils.toJsonString(planPayDTO));
            }
        }
        if (!CollectionUtils.isEmpty(dutyPayArr)) {
            for (DutyPayDTO dutyPayDTO : dutyPayArr) {
                detailPayArr.addAll(dutyPayDTO.getDutyDetailPayArr());
                dutyPayDTO.setUpdatedBy(userId);
                dutyPayMapper.updateDutyPayInfoList(dutyPayDTO);
            }
        }
        List<BIllSettleResultDTO> billSettleResultDTOList=new ArrayList<>();
        List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOS=new ArrayList<>();
        if (!CollectionUtils.isEmpty(detailPayArr)) {
            for (DutyDetailPayDTO dutyDetailPayDTO : detailPayArr) {
                dutyDetailPayDTO.setUpdatedBy(userId);
                dutyDetailPayMapper.updateDutyDetailPayList(dutyDetailPayDTO);
                if(CollectionUtil.isNotEmpty(dutyDetailPayDTO.getBillSettleResultDTOList())){
                    billSettleResultDTOList.addAll(dutyDetailPayDTO.getBillSettleResultDTOList());
                }
                if(CollectionUtil.isNotEmpty(dutyDetailPayDTO.getDutyBillLimitInfoDTOList())){
                    dutyBillLimitInfoDTOS.addAll(dutyDetailPayDTO.getDutyBillLimitInfoDTOList());
                }
            }
        }
        if(CollectionUtil.isNotEmpty(billSettleResultDTOList)){
            //责任明细发票理算信息
            clmsDutyDetailBillSettleService.saveBatch(billSettleResultDTOList);
        }
        //保存日限额信息
        if(CollectionUtil.isNotEmpty(dutyBillLimitInfoDTOS)){
            dutyBillLimitInfoService.saveList(dutyBillLimitInfoDTOS);
        }

    }
}
