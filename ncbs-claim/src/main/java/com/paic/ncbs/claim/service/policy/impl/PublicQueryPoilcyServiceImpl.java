package com.paic.ncbs.claim.service.policy.impl;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.model.dto.other.CommonResultDTO;
import com.paic.ncbs.claim.model.dto.policy.QueryPolicyAbstractInfoListDTO;
import com.paic.ncbs.claim.model.dto.policy.QueryPolicyDetailInfoDTO;
import com.paic.ncbs.claim.common.constant.CommonConstant;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.RapeStringUtils;
import com.paic.ncbs.claim.service.policy.PublicQueryPoilcyService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Service
public class PublicQueryPoilcyServiceImpl implements PublicQueryPoilcyService {

	public static final String IBCS_SOURCE = "99";

	public static Properties getSystemProperties(String resourceName) {
		Properties prop = null;
		try {
			prop = PropertiesLoaderUtils.loadAllProperties(resourceName);
		} catch (IOException e1) {
			return null;
		}
		return prop;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public List queryPolicyAbstractInfoList(QueryPolicyAbstractInfoListDTO queryPolicyAbstractInfoListDTO)
			{
		if(StringUtils.isEmpty(queryPolicyAbstractInfoListDTO.getApplicationId())){
			queryPolicyAbstractInfoListDTO.setApplicationId(CommonConstant.DATA_SOURCE);
		}
		queryPolicyAbstractInfoListDTO.setRequestId(queryPolicyAbstractInfoListDTO.getRequestId() + System.currentTimeMillis());
		List resultList = new ArrayList<String>();
		List IBCSList = new ArrayList<String>();
		List successList = new ArrayList<String>();

		if (null != queryPolicyAbstractInfoListDTO) {
			List<String> policyNos = queryPolicyAbstractInfoListDTO.getPolicyNos();
			if (CollectionUtils.isNotEmpty(policyNos)) {

//				IBCSList = queryForEbcsSao.queryPolicyAbstractListFromIBCS(queryPolicyAbstractInfoListDTO);
			}

			resultList = new ArrayList();//pasPolicyQuerySao.queryPolicyAbstractInfoList(queryPolicyAbstractInfoListDTO);
			if (null != IBCSList && IBCSList.size() > 0) {
				successList.addAll(IBCSList);
			}
			if (null != resultList && resultList.size() > 0) {
				successList.addAll(resultList);
			}
		}
		return successList;
	}

	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Map queryPolicyDetailByPolicyNo(QueryPolicyDetailInfoDTO queryPolicyDetailInfoDTO) {
		Map resultMap = new HashMap();
		CommonResultDTO pasPolicyInfoNew = new CommonResultDTO();
		Object pasPolicyInfo = null;
		Object ibcsPolicyInfo = null;
		Map ebcsPolicyInfo = new HashMap();
		Map aibsPolicyInfo = null;
		String resultType = "0";
		String resultMsg = "无记录";

		String policyNoForIBCS = queryPolicyDetailInfoDTO.getPolicyNo();

		if (StringUtils.isNotBlank(policyNoForIBCS)) {
			LogUtil.info("queryForEbcsSao.queryPolicyListFromIBCS,policyNoForIBCS：" + policyNoForIBCS);
			String sourceCode = "";
			try {
				sourceCode = policyNoForIBCS.substring(7, 9);
				LogUtil.info("queryForEbcsSao.queryPolicyListFromIBCS,sourceCode：" + sourceCode);
			} catch (Exception e) {

			}
			if (IBCS_SOURCE.equals(sourceCode)) {
				try {
					LogUtil.info("调用queryForEbcsSao.queryPolicyListFromIBCS入参：{}" , JSONObject.toJSONString(queryPolicyDetailInfoDTO));
//					ibcsPolicyInfo = queryForEbcsSao.queryPolicyListFromIBCS(queryPolicyDetailInfoDTO);
					resultMap.put("ibcsPolicyInfo", ibcsPolicyInfo);
				} catch (Exception e) {
					LogUtil.error("调用queryForEbcsSao.queryPolicyListFromIBCS接口异常:{}", e);
					ibcsPolicyInfo = null;
				}
			}
		}


		String queryType = queryPolicyDetailInfoDTO.getQueryType();
		try {
			if ((queryType == null || queryType.trim().equals("") || queryType.trim().equals("1"))
					&& ibcsPolicyInfo == null) {
				String applicationId = queryPolicyDetailInfoDTO.getApplicationId();
				if (StringUtils.isEmpty(applicationId)) {
					applicationId = CommonConstant.DATA_SOURCE;
				}
				String requestId = applicationId + System.currentTimeMillis();
				queryPolicyDetailInfoDTO.setRequestId(requestId);
				String policyNo = queryPolicyDetailInfoDTO.getPolicyNo();
				String selfcardNo = queryPolicyDetailInfoDTO.getSelfcardNo();
				String selfcardPolicyNo = queryPolicyDetailInfoDTO.getSelfcardPolicyNo();
				String documentNo = new String();
				Map inParams = new HashMap();
				if (policyNo != null && !"".equals(policyNo)) {
					documentNo = policyNo;
					queryPolicyDetailInfoDTO.setSelfcardNo("");
					queryPolicyDetailInfoDTO.setSelfcardPolicyNo("");
				} else if (selfcardNo != null && !"".equals(selfcardNo)) {
					documentNo = selfcardNo;
					queryPolicyDetailInfoDTO.setSelfcardPolicyNo("");
				} else if (selfcardPolicyNo != null && !"".equals(selfcardPolicyNo)) {
					documentNo = selfcardPolicyNo;
				} else if (StringUtils.isNotBlank(queryPolicyDetailInfoDTO.getSeriesNo())) {
					LogUtil.info("根据产品序列号抄单，序列号为：" + queryPolicyDetailInfoDTO.getSeriesNo());
					inParams.put("seriesNo", queryPolicyDetailInfoDTO.getSeriesNo());
				} else {
					resultType = "-1";
					resultMap.put("resultType", resultType);
					return resultMap;
				}
				queryPolicyDetailInfoDTO.setDocumentNo(documentNo);
				LogUtil.info("调用pasPolicyQuerySao.queryPolDtlByDocNoAndCstmInfo入参{}：", JSONObject.toJSONString(queryPolicyDetailInfoDTO));
				pasPolicyInfo = null;//pasPolicyQuerySao.queryPolDtlByDocNoAndCstmInfo(queryPolicyDetailInfoDTO, pasPolicyInfoNew);
				resultMap.put("pasPolicyInfo", pasPolicyInfo);
			}
		} catch (Exception e) {
			LogUtil.error("调用pasPolicyQuerySao.queryPolDtlByDocNoAndCstmInfo接口异常:{}", e);
			pasPolicyInfo = null;
		}

		try {
			if (ibcsPolicyInfo == null && pasPolicyInfo == null && aibsPolicyInfo == null) {
				Map paramMap = new HashMap();
				paramMap.put("certificateNo", queryPolicyDetailInfoDTO.getCertificateNo());
				paramMap.put("phoneNumber", queryPolicyDetailInfoDTO.getPhoneNumber());
				paramMap.put("accidentTime", queryPolicyDetailInfoDTO.getAccidentTime());
				paramMap.put("partnerName", queryPolicyDetailInfoDTO.getPartnerName());
				paramMap.put("policyNo", queryPolicyDetailInfoDTO.getPolicyNo());
				paramMap.put("orderNum", queryPolicyDetailInfoDTO.getOrderNum());

				String json = JSONObject.toJSONString(paramMap);
				LogUtil.info("调用queryForEbcsSao.queryPolicyForAPI，入参：" + json);
//				ebcsPolicyInfo = queryForEbcsSao.queryPolicyForAPI(paramMap);
				if (ebcsPolicyInfo != null) {
					if (Integer.parseInt(ebcsPolicyInfo.get("resultCode").toString()) == 0) {
						ebcsPolicyInfo = null;
					}
				}
				resultMap.put("ebcsPolicyInfo", ebcsPolicyInfo);
			}
		} catch (Exception e) {
			LogUtil.error("调用queryForEbcsSao.queryPolicyForAPI接口异常:{}", e);
			ebcsPolicyInfo = null;
		}
		if (ibcsPolicyInfo != null) {
			resultType = "5";
			resultMsg = "IBCS有记录";

		} else if (pasPolicyInfo != null) {
			if (pasPolicyInfoNew != null && "0000".equals(pasPolicyInfoNew.getResultCode())) {
				resultType = "1";
				resultMsg = "PAS有记录";
			} else {
				resultMap.put("pasPolicyInfo", null);
			}

		} else if (aibsPolicyInfo != null) {
			resultType = "3";
			resultMsg = "AIBS有记录";

		} else if (ebcsPolicyInfo != null) {
			resultType = "6";
			resultMsg = "EBCS有记录";

		}
		resultMap.put("resultType", resultType);
		resultMap.put("resultMsg", resultMsg);

		return resultMap;
	}


	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Map queryPolicyDetailByDocumentNoAndClientMsg(QueryPolicyDetailInfoDTO queryPolicyDetailInfoDTO)
			throws Exception {
		Map resultMap = new HashMap();
		CommonResultDTO pasPolicyInfoNew = new CommonResultDTO();
		Object pasPolicyInfo = null;
		Object ibcsPolicyInfo = null;

		String resultType = "0";
		String resultMsg = "无记录";
		String documentNo = new String();

		String policyNo = queryPolicyDetailInfoDTO.getPolicyNo();
		String selfcardNo = queryPolicyDetailInfoDTO.getSelfcardNo();
		String selfcardPolicyNo = queryPolicyDetailInfoDTO.getSelfcardPolicyNo();
		String name = queryPolicyDetailInfoDTO.getName();
		String certificateNo = queryPolicyDetailInfoDTO.getCertificateNo();
		String clientNo = queryPolicyDetailInfoDTO.getClientNo();
		if (policyNo != null && !"".equals(policyNo)) {
			documentNo = policyNo;
			queryPolicyDetailInfoDTO.setSelfcardNo("");
			queryPolicyDetailInfoDTO.setSelfcardPolicyNo("");
		} else if (selfcardNo != null && !"".equals(selfcardNo)) {
			documentNo = selfcardNo;
			queryPolicyDetailInfoDTO.setSelfcardPolicyNo("");
		} else if (selfcardPolicyNo != null && !"".equals(selfcardPolicyNo)) {
			documentNo = selfcardPolicyNo;
		} else {
			resultType = "-1";
			resultMsg = "保单号、自助卡号、电子保单号不能同时为空";
			resultMap.put("resultType", resultType);
			resultMap.put("resultMsg", resultMsg);
			return resultMap;
		}

		if (name != null && !"".equals(name)) {
			queryPolicyDetailInfoDTO.setCertificateNo("");
			queryPolicyDetailInfoDTO.setClientNo("");
		} else if (certificateNo != null && !"".equals(certificateNo)) {
			queryPolicyDetailInfoDTO.setClientNo("");
		} else if (clientNo == null || "".equals(clientNo)) {
			resultType = "-1";
			resultMsg = "姓名、证件号码、客户号不能同时为空";
			resultMap.put("resultType", resultType);
			resultMap.put("resultMsg", resultMsg);
			return resultMap;
		}


		if (StringUtils.isNotBlank(policyNo)) {
			LogUtil.info("queryForEbcsSao.queryPolicyListFromIBCS,policyNoForIBCS：" + policyNo);
			String sourceCode = "";
			try {
				sourceCode = policyNo.substring(7, 9);
				LogUtil.info("queryForEbcsSao.queryPolicyListFromIBCS,sourceCode：" + sourceCode);
			} catch (Exception e) {

			}
			if (IBCS_SOURCE.equals(sourceCode)) {
				try {
					LogUtil.info("调用queryForEbcsSao.queryPolicyListFromIBCS入参：{}" , JSONObject.toJSONString(queryPolicyDetailInfoDTO));
//					ibcsPolicyInfo = queryForEbcsSao.queryPolicyListFromIBCS(queryPolicyDetailInfoDTO);
					resultMap.put("ibcsPolicyInfo", ibcsPolicyInfo);
				} catch (Exception e) {
					LogUtil.error("调用queryForEbcsSao.queryPolicyListFromIBCS接口异常:{}", e);
					ibcsPolicyInfo = null;
				}
			}
		}


		String queryType = queryPolicyDetailInfoDTO.getQueryType();

		try {
			if ((queryType == null || queryType.trim().equals("") || queryType.trim().equals("1"))
					&& ibcsPolicyInfo == null) {
				if (RapeStringUtils.isEmptyStr(queryPolicyDetailInfoDTO.getApplicationId())) {
					queryPolicyDetailInfoDTO.setApplicationId(CommonConstant.DATA_SOURCE);
				}
				if(RapeStringUtils.isEmptyStr(queryPolicyDetailInfoDTO.getRequestId())){
					queryPolicyDetailInfoDTO.setRequestId(queryPolicyDetailInfoDTO.getApplicationId() + System.currentTimeMillis());
				}
				queryPolicyDetailInfoDTO.setDocumentNo(documentNo);
				LogUtil.info("调用pasPolicyQuerySao.queryPolDtlByDocNoAndCstmInfo，入参："
						+ JSONObject.toJSONString(queryPolicyDetailInfoDTO));
				pasPolicyInfo = null;//pasPolicyQuerySao.queryPolDtlByDocNoAndCstmInfo(queryPolicyDetailInfoDTO, pasPolicyInfoNew);
				resultMap.put("pasPolicyInfo", pasPolicyInfo);
			}
		} catch (Exception e) {
			LogUtil.error("调用pasPolicyQuerySao.queryPolDtlByDocNoAndCstmInfo接口异常:{}", e);
			pasPolicyInfo = null;
		}
		if (ibcsPolicyInfo != null) {
			resultType = "4";
			resultMsg = "IBCS有记录";

		} else if (pasPolicyInfo != null) {
			if (pasPolicyInfoNew != null && "0000".equals(pasPolicyInfoNew.getResultCode())) {
				resultType = "1";
				resultMsg = "PAS有记录";
			} else {
				resultMap.put("pasPolicyInfo", null);
			}
		}
		resultMap.put("resultType", resultType);
		resultMap.put("resultMsg", resultMsg);

		return resultMap;
	}


}
