package com.paic.ncbs.claim.controller.common;

import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.dao.entity.common.OptionsEntity;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 不存在业务含义
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/common/app/CommonAction")
@Api(tags = {"公共的控制器"})
@Slf4j
@RefreshScope
public class CommonController extends BaseController {

    @Value("${iobs.ak}")
    private String iobsak;

    @ResponseBody
    @PostMapping(value = "/getiobsak")
    public ResponseResult<String> getiobsak() throws GlobalBusinessException {
        return ResponseResult.success(iobsak);
    }

    @ApiOperation(value = "从枚举中获取下拉框选项")
    @ResponseBody
    @PostMapping(value = "/getOptions")
    public ResponseResult<Map<String,List<OptionsEntity>>> getOptions(@RequestBody List<String> types) throws GlobalBusinessException {
        Map<String,List<OptionsEntity>> resultMap = new HashMap<>();
        if(types==null || types.isEmpty()){
            throw new GlobalBusinessException("types不能为空");
        }
        for (String type : types) {
            List<OptionsEntity> optionsByTypes = getOptionsByTypes(type);
            resultMap.put(type,optionsByTypes);
        }
        return ResponseResult.success(resultMap);
    }

    private List<OptionsEntity> getOptionsByTypes(String type) {
        List<OptionsEntity> optionsEntities = new ArrayList<>();

        switch (type) {
            case "accidentType":
                for (AccidentTypeEnum anEnum : AccidentTypeEnum.values()) {
                    OptionsEntity optionsEntity = new OptionsEntity();
                    optionsEntity.setType(anEnum.getType());
                    optionsEntity.setName(anEnum.getName());
                    optionsEntities.add(optionsEntity);
                }
                break;
            case "insuredApplyStatus":
                for (InsuredApplyStatusEnum anEnum : InsuredApplyStatusEnum.values()) {
                    OptionsEntity optionsEntity = new OptionsEntity();
                    optionsEntity.setType(anEnum.getType());
                    optionsEntity.setName(anEnum.getName());
                    optionsEntities.add(optionsEntity);
                }
                break;
            case "insuredApplyType":
                for (InsuredApplyTypeEnum anEnum : InsuredApplyTypeEnum.values()) {
                    OptionsEntity optionsEntity = new OptionsEntity();
                    optionsEntity.setType(anEnum.getType());
                    optionsEntity.setName(anEnum.getName());
                    if (anEnum.getType().contains("IAT_1")){
                        optionsEntities.add(optionsEntity);
                    }
                }
                break;
            case "injuryReasonCode":
                for (InjuryReasonCodeEnum anEnum : InjuryReasonCodeEnum.values()) {
                    OptionsEntity optionsEntity = new OptionsEntity();
                    optionsEntity.setType(anEnum.getType());
                    optionsEntity.setName(anEnum.getName());
                    optionsEntities.add(optionsEntity);
                }
                break;
            case "ClientTypePersonalCode":
                for (ClientTypePersonalEnum anEnum : ClientTypePersonalEnum.values()) {
                    OptionsEntity optionsEntity = new OptionsEntity();
                    optionsEntity.setType(anEnum.getCode());
                    optionsEntity.setName(anEnum.getName());
                    optionsEntities.add(optionsEntity);
                }
                break;
            case "ClientTypeOrgCode":
                for (ClientTypeOrgEnum anEnum : ClientTypeOrgEnum.values()) {
                    OptionsEntity optionsEntity = new OptionsEntity();
                    optionsEntity.setType(anEnum.getCode());
                    optionsEntity.setName(anEnum.getName());
                    optionsEntities.add(optionsEntity);
                }
                break;
            default:
                break;
        }
        return optionsEntities;
    }


}
