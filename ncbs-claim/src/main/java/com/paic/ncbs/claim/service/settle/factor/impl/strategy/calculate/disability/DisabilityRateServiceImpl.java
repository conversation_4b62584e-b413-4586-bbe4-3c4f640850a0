package com.paic.ncbs.claim.service.settle.factor.impl.strategy.calculate.disability;

import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * 伤残比例
 */
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service
public class DisabilityRateServiceImpl extends CalculateAmountService {

    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {

        paramsDTO.getSettleFactor().setDisabilityRate(nvl(paramsDTO.getDutyDetailPayDTO().getDisabilityRate(),0));
        paramsDTO.getSettleFactor().setCalculateAmount(nvl(paramsDTO.getDutyDetailPayDTO().getDisabilityRate(),0));
    }
}
