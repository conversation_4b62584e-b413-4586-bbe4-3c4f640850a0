package com.paic.ncbs.claim.controller.schedule;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.service.schedule.OpsJobService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/schedule/ops")
@Api(tags = {"运维任务控制器"})
public class OpsJobController {

    @Autowired
    private OpsJobService opsJobService;

    /**
     * 运维退运险批量送收付
     *
     * @param reportNos 补传报案号集合
     */
    @GetMapping("/closeCase/saveAfter")
    public ResponseResult<Map<String, List<String>>> saveCloseCaseAfter(@RequestParam(value = "reportNos",required = false) List<String> reportNos,
                                                                        @RequestParam(value = "isManual") String isManual) {
        return ResponseResult.success(opsJobService.saveCloseCaseAfter(reportNos, isManual));
    }

    /**
     * 批量修改支付退回`
     *
     * @param reportNos 补传报案号集合
     */
    @PostMapping("/batchModifyPayBack")
    public ResponseResult<List<String>> batchModifyPayBack(@RequestBody List<String> reportNos) {
        return ResponseResult.success(opsJobService.batchModifyPayBack(reportNos));
    }


    /**
     * 自动核赔之后补传MQ和再保
     *
     * @param reportNos 补传报案号集合
     */
    @PostMapping("/batchAutoVerifyAfter")
    public ResponseResult<List<String>> batchAutoVerifyAfter(@RequestBody List<String> reportNos) {
        return ResponseResult.success(opsJobService.batchAutoVerifyAfter(reportNos));
    }


    /**
     * 批量补推理赔费用信息到收付Q13
     *
     * @param reportNos 补传报案号集合
     */
    @PostMapping("/batchSendClaimVatInfo")
    public ResponseResult<Object> batchSendClaimVatInfo(@RequestBody List<String> reportNos) {
        opsJobService.batchSendClaimVatInfo(reportNos);
        return ResponseResult.success();
    }

    /**
     * 批量补推理赔费用信息到收付Q13通过支付序号
     *
     * @param paySerialNoS 补传支付序号集合
     */
    @PostMapping("/batchSendClaimVatInfoByPaySerialNo")
    public ResponseResult<Object> batchSendClaimVatInfoByPaySerialNo(@RequestBody List<String> paySerialNoS) {
        opsJobService.batchSendClaimVatInfoByPaySerialNo(paySerialNoS);
        return ResponseResult.success();
    }

    /**
     * 退运险数据修改
     *
     * @param reportNos 补传报案号集合
     */
    @PostMapping("/batch/updatePolicyPay")
    public ResponseResult<Object> batchUpdatePolicyPay(@RequestParam(value = "isManual") String isManual,
                                                       @RequestBody List<String> reportNos) {
        opsJobService.batchUpdatePolicyPay(reportNos,isManual);
        return ResponseResult.success();
    }
}
