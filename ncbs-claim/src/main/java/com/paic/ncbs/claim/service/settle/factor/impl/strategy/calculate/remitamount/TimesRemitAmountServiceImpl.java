package com.paic.ncbs.claim.service.settle.factor.impl.strategy.calculate.remitamount;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.RemitAmountDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.SettleFactor;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;

/**
 * 次免赔额计算实现
 */
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service
public class TimesRemitAmountServiceImpl extends CalculateAmountService {
    /**
     * 次免赔
     */
    private BigDecimal timesRemitAmount;
    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {
        DutyDetailPayDTO detail=paramsDTO.getDutyDetailPayDTO();
        if(!Objects.equals("0",detail.getRemitAmountType()) ){
            paramsDTO.getSettleFactor().setRemitAmount(BigDecimal.ZERO);
            paramsDTO.getSettleFactor().setCalculateAmount(BigDecimal.ZERO);
            return;
        }
        if (Objects.equals("N", paramsDTO.getEveryDayBillInfoDTO().getEffectiveFlag())
                || Objects.equals("Y", paramsDTO.getEveryDayBillInfoDTO().getExceedMothPayDays())
                || Objects.equals("Y", paramsDTO.getEveryDayBillInfoDTO().getExceedYearlyPayDays())
                || Objects.equals("Y", paramsDTO.getEveryDayBillInfoDTO().getWaitFlag())) {
            paramsDTO.getSettleFactor().setRemitAmount(BigDecimal.ZERO);
            paramsDTO.getSettleFactor().setCalculateAmount(BigDecimal.ZERO);
            return;
        }
        SettleFactor factor =paramsDTO.getSettleFactor();
        //合理费用
        BigDecimal reasonableAmount  = factor.getReasonableAmount();
        if(reasonableAmount.subtract(timesRemitAmount).compareTo(BigDecimal.ZERO)>=0){
            paramsDTO.getSettleFactor().setRemitAmount(timesRemitAmount);
            paramsDTO.getSettleFactor().setCalculateAmount(timesRemitAmount);
            timesRemitAmount=BigDecimal.ZERO;

        }else {
            paramsDTO.getSettleFactor().setRemitAmount(reasonableAmount);
            paramsDTO.getSettleFactor().setCalculateAmount(reasonableAmount);
            //paramsDTO.getSettleReasonParamsDTO().setLessThanRemitAmountFlag("Y");
            timesRemitAmount=timesRemitAmount.subtract(reasonableAmount);
        }
    }

    /**
     * 责任次免赔额 每次报案 责任免赔额为配置的免赔额
     * @param remitAmountDTO
     */
    @Override
    public void initBuild(RemitAmountDTO remitAmountDTO){
        timesRemitAmount=remitAmountDTO.getConfigRemitAmount();
    }
}
