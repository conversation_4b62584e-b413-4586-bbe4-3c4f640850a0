package com.paic.ncbs.claim.service.report;

import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO;
import com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO;
import com.paic.ncbs.claim.model.dto.report.HistoryReportAgrsDTO;
import com.paic.ncbs.claim.model.dto.report.ReportAhcsBaseInfoDTO;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.dto.report.ReportStatDTO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.report.ReportInfoForSX;
import com.paic.ncbs.claim.model.vo.report.ReportQueryVO;
import com.paic.ncbs.claim.service.base.BaseService;

import java.util.List;

public interface ReportInfoService extends BaseService<ReportInfoEntity> {

    ReportInfoEntity getReportInfo(String reportNo);

    ReportBaseInfoResData requestReportBaseInfo(String reportNo);

    ReportAhcsBaseInfoDTO getReportBaseInfo(String reportNo);

    List<HistoryCaseDTO> getHistoryCaseByReportNo(String reportNo, String specialCaseType);

    List<HistoryCaseDTO> getHistoryCaseByReportNoFilter(String reportNo, String specialCaseType);

    List<HistoryCaseDTO> getHistoryCaseByCertificateNo(String certificateNo, String name);

    List<HistoryCaseDTO> getHistoryCaseBetweenTime(String clientNo, String name,String certificateType, String beginTime, String endTime);


    List<HistoryCaseDTO> getHistoryReportByCertificateNo(String certificateNo, String specialCaseType);

    List<HistoryCaseDTO> getHistoryReportByPolicyNoAndName(String policyNo, String name, String specialCaseType);

    List<HistoryCaseDTO> getHistoryReportByBirthdayAndName(String birthday, String name);

    List<HistoryCaseDTO> getHistoryReportByElectronicNoNew(String electronicNo);

    List<HistoryCaseDTO> getHistoryReportByTelephoneNo(String telephoneNo);

    List<HistoryCaseDTO> getHistoryReportByDateAndDepartmentCode(HistoryReportAgrsDTO dto);

    List<HistoryCaseDTO> getHistoryReportByReportDateAndName(HistoryReportAgrsDTO dto);


    List<HistoryCaseDTO> getHistoryCaseByReportBatchNo(String reportBatchNo);

    List<HistoryCaseDTO> getHistoryReportFilter(HistoryReportAgrsDTO dto);

    List<HistoryCaseDTO> getHistoryReport(HistoryReportAgrsDTO dto);


    List<HistoryCaseDTO> getHistoryByPolicyNos(List<ReportCustomerInfoEntity> customerInfo, String policyNo);
    List<HistoryCaseDTO> getHistoryCaseNew(WholeCaseVO wholeCase);

    List<HistoryCaseDTO> getHistoryCaseNewByCopy(WholeCaseVO queryVO);

    List<ReportInfoForSX> getReportNoInfoForSX(String reportNo);

    String getAccidentPlace(PersonAccidentDTO accidentDTO);

    // 获取报案统计
	ReportStatDTO getReportStat(ReportQueryVO queryVO);
}
