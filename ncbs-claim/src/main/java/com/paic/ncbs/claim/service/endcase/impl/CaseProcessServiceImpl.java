package com.paic.ncbs.claim.service.endcase.impl;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseProcessMapper;
import com.paic.ncbs.claim.dao.mapper.settle.BatchMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.mq.claim.ClaimSendMqDto;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.EndCaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("caseProcessService")
public class CaseProcessServiceImpl implements CaseProcessService {

    @Autowired
    private CaseProcessMapper caseProcessDao;
    @Autowired
    private EndCaseService endCaseService;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    BatchMapper batchDao;
    @Autowired
    private EstimateService estimateService;
    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;

    private static final String REPEAT_ADD_CASE_PROCESS = "#新增流程案件信息#:流程信息已经存在不需要重复插入";

    @Override
    public String isDeletePaymentItem(CaseProcessDTO caseProcess) {
        String reportNo = caseProcess.getReportNo();
        CaseProcessDTO caseProcessDTO = caseProcessDao.getCaseProcessDTO(caseProcess);
        LogUtil.audit("报案号={},删除支付项信息.caseProcess={}", reportNo, JSONObject.toJSONString(caseProcess));
        if (null == caseProcessDTO) {
            return ConstValues.YES;
        }
        List<String> caseProcessStatusList = new ArrayList<>();
        caseProcessStatusList.add(ConfigConstValues.PROCESS_STATUS_PENDING_CASE);
        caseProcessStatusList.add(ConfigConstValues.PROCESS_STATUS_CASE_CLOSED);
        caseProcessStatusList.add(ConfigConstValues.PROCESS_STATUS_ZORE_CANCEL);
        caseProcessStatusList.add(ConfigConstValues.PROCESS_STATUS_CANCELLATION);

        if (caseProcessStatusList.contains(caseProcessDTO.getProcessStatus())) {
            return ConstValues.NO;
        }
        return ConstValues.YES;
    }

    @Override
    public void updateCaseProcessDTO(CaseProcessDTO caseProcessDTO) throws GlobalBusinessException {
        LogUtil.audit("#重开审核通过更新流程案件信息、结案#入参caseProcessDTO=" + caseProcessDTO);

        String reportNo = caseProcessDTO.getReportNo();
        Integer caseTimes = caseProcessDTO.getCaseTimes();
        String processStatus = caseProcessDTO.getProcessStatus();
        String userId = caseProcessDTO.getUserId();

        caseProcessDao.updateCaseProcessByReportNo(caseProcessDTO);


        if (ConfigConstValues.PROCESS_STATUS_CASE_CLOSED.equals(processStatus)) {
            WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
            wholeCaseBaseDTO.setUpdatedBy(userId);
            wholeCaseBaseDTO.setCaseFinisherUm(userId);
            endCaseService.endCase(wholeCaseBaseDTO);
        }
    }


    @Override
    public Date getArchiveTimeByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        return caseProcessDao.getArchiveTimeByReportNo(reportNo, caseTimes);
    }

    @Override
    public void updateCaseProcess(String reportNo, Integer caseTimes, String processStatus) {
        LogUtil.audit("#更新流程案件信息reportNo=%s,processStatus=%s", reportNo, processStatus);
        CaseProcessDTO caseProcessDTO = new CaseProcessDTO();
        caseProcessDTO.setReportNo(reportNo);
        caseProcessDTO.setCaseTimes(caseTimes);
        caseProcessDTO.setProcessStatus(processStatus);

        caseProcessDao.updateCaseProcessByReportNo(caseProcessDTO);

        if (CaseProcessStatus.CASE_CLOSED.getCode().equals(processStatus)) {
            WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase2(reportNo, caseTimes);
            String settleUserUm = WebServletContext.getUserId();
            wholeCaseBaseDTO.setUpdatedBy(settleUserUm);
            wholeCaseBaseDTO.setCaseFinisherUm(settleUserUm);
            endCaseService.endCase(wholeCaseBaseDTO);
            //发送大案邮件(核赔)
//            sendReviewMajorCaseMail(reportNo,caseTimes);
        }

    }

    @Override
    public String getCaseProcessStatus(String reportNo, Integer caseTimes) {
        return caseProcessDao.getCaseProcessStatus(reportNo, caseTimes);
    }

    @Override
    public String getCaseProcessStatusName(String reportNo, Integer caseTimes) {
        String caseProcessStatus = caseProcessDao.getCaseProcessStatus(reportNo, caseTimes);
        return CaseProcessStatus.getName(caseProcessStatus);
    }

    @Override
    public List<CaseProcessDTO> getCaseByReportNo( String reportNo) {
        return caseProcessDao.getCaseByReportNo(reportNo);
    }

    /**
      *
      * @Description  重开后取最新的案件状态
      * <AUTHOR>
      * @Date 2023/6/1 11:55
      **/
    @Override
    public String getCaseProcessStatusNameNew(String reportNo) {
        String caseProcessStatus = caseProcessDao.getCaseProcessStatusNew(reportNo);
        return CaseProcessStatus.getName(caseProcessStatus);
    }

    @Override
    public CaseProcessDTO getCaseProcessDTO(String reportNo, Integer caseTimes) {
        CaseProcessDTO param = new CaseProcessDTO();
        param.setReportNo(reportNo);
        param.setCaseTimes(caseTimes);
        return caseProcessDao.getCaseProcessDTO(param);
    }

    @Override
    public void updateCaseRegisterDept(CaseProcessDTO caseProcessDTO) {
        caseProcessDao.updateRegisterDept(caseProcessDTO);
    }

    @Override
    public boolean getIsNewProcess(String reportNo, Integer caseTimes) {
        String isNewProcess = caseProcessDao.getIsNewProcess(reportNo, caseTimes);
        return ConfigConstValues.YES.equals(isNewProcess) ? true : false;
    }

    @Override
    public void updateCaseProcessByReportNo(CaseProcessDTO caseProcessDTO) {
        caseProcessDao.updateCaseProcessByReportNo(caseProcessDTO);
    }

    @Override
    public CaseProcessDTO getProcessStatusAndEndAmount(CaseProcessDTO caseProcess) {
        if (caseProcess.getReportNo() == null && caseProcess.getCaseNo() == null) {
            LogUtil.audit("赔案查询-参数为空。");
            return null;
        }

        CaseProcessDTO cp = caseProcessDao.getCaseProcessDTO(caseProcess);
        if (cp == null) {
            return null;
        }

        BigDecimal estimateAmount = estimateService.getAmount(caseProcess.getReportNo(), caseProcess.getCaseNo(), caseProcess.getCaseTimes());
        if (estimateAmount != null && !BigDecimalUtils.isEqual(estimateAmount, BigDecimal.ZERO)) {

            if (cp.getCoInsureRatio() != null) {
                estimateAmount = estimateAmount.multiply(new BigDecimal(cp.getCoInsureRatio()));
            }
            estimateAmount = estimateAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        cp.setEstimateAmount(estimateAmount);
        String processStatus = cp.getProcessStatus();
        if (ConfigConstValues.PROCESS_STATUS_WAIT_CONFIRM.equals(processStatus)) {
            cp.setProcessStatus(ConfigConstValues.PROCESS_STATUS_PENDING_ACCEPT);
            cp.setProcessStatusName(ConfigConstValues.PROCESS_STATUS_PENDING_ACCEPT_NAME);
        }
        if (ConfigConstValues.PROCESS_STATUS_CASE_CLOSED.equals(processStatus)) {
            cp.setEndCaseAmount(policyPayService.getSumPayFee(caseProcess.getReportNo(), caseProcess.getCaseTimes()));
            if (cp.getCoInsureRatio() != null && cp.getEndCaseAmount() != null) {
                LogUtil.audit(caseProcess.getReportNo() + "结案金额" + caseProcess.getCaseNo() + "--" + estimateAmount + "，预估金额" + estimateAmount + "，我司主承保比例为" + cp.getCoInsureRatio());
                cp.setEndCaseAmount((cp.getEndCaseAmount().multiply(new BigDecimal(cp.getCoInsureRatio()))).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }
        cp.setProcessStatusName(CaseProcessStatus.getName(processStatus));
        return cp;
    }

    @Override
    public void addCaseProcess(String reportNo, Integer caseTimes, String processStatus, Date reportDate) {
        LogUtil.audit("#新增流程案件信息#参数{reportNo=" + reportNo + "," + "reportNo=" + caseTimes + "processStatus=" + processStatus + "}");
        CaseProcessDTO caseProcessDTO = new CaseProcessDTO();
        caseProcessDTO.setReportNo(reportNo);
        caseProcessDTO.setCaseTimes(caseTimes);
        caseProcessDTO.setProcessStatus(processStatus);
        caseProcessDTO.setArchiveTime(reportDate);
        caseProcessDTO.setCompanyCode(policyInfoMapper.getPolicyDeptByReportNo(reportNo));

        int recordCount = caseProcessDao.getCaseProcess(caseProcessDTO);
        if (recordCount < 1) {
            caseProcessDao.addCaseProcess(caseProcessDTO);
        } else {
            LogUtil.audit("#新增流程案件信息#:流程信息已经存在不需要重复插入");
        }
    }

    @Override
    public void addCaseProcessNew(String reportNo, Integer caseTimes, String isNewProcess, Date reportDate) {
        LogUtil.audit("#新增流程案件信息#参数{reportNo=" + reportNo + ",isNewProcess=" + isNewProcess + "}");
        CaseProcessDTO caseProcessDTO = new CaseProcessDTO();
        caseProcessDTO.setReportNo(reportNo);
        caseProcessDTO.setCaseTimes(caseTimes);
        //首次写入待报案跟踪
        caseProcessDTO.setProcessStatus(CaseProcessStatus.PENDING_REGISTER.getCode());
        caseProcessDTO.setIsNewProcess(isNewProcess);
        caseProcessDTO.setArchiveTime(reportDate);
        caseProcessDTO.setCompanyCode(policyInfoMapper.getPolicyDeptByReportNo(reportNo));

        int recordCount = caseProcessDao.getCaseProcess(caseProcessDTO);
        if (recordCount < 1) {
            caseProcessDao.addCaseProcess(caseProcessDTO);
        } else {
            LogUtil.audit(REPEAT_ADD_CASE_PROCESS);
        }
    }

    @Override
    public CaseProcessDTO getCaseCommissionInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        return caseProcessDao.getCaseCommissionInfo(reportNo, caseTimes);
    }

    /**
     * 根据报案号赔付次数查询案件状态,
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    public CaseProcessDTO getCaseProcessInfo(String reportNo, Integer caseTimes) {
        CaseProcessDTO caseProcessDTO =  caseProcessDao.getCaseProcessInfo(reportNo,caseTimes);
        return caseProcessDTO;
    }

//    private void sendReviewMajorCaseMail(String reportNo,Integer caseTimes){
//        //发送大案邮件(核赔/结案)
//        try {
//            BigDecimal endcaseAmount = policyPayService.getSumPayFee(reportNo,caseTimes);
//            if(BigDecimalUtils.compareBigDecimalPlusOrEqual(endcaseAmount,MailScopeEnum.END_4.getMin())){
//                String deptCode = policyInfoMapper.getPolicyDeptByReportNo(reportNo);
//                //取二级机构
//                deptCode = departmentDefineService.getLevel2DeptCode(deptCode);
//                Map<String,String> receiverMap = receiverConfigService.getMajorCaseReceiverList(deptCode, MailNodeEnum.END.getType(),MailScopeEnum.END_4.getType());
//                String receiver = receiverMap.get("receivers");
//                if(StringUtils.isNotEmpty(receiver)){
//                    //收件人不为空发邮件
////                    mailSendService.sendMajorCaseMail(reportNo,receiver,receiverMap.get("copyers"));
//                }
//            }
//        }catch (Exception e){
//
//        }
//
//    }

}
