package com.paic.ncbs.claim.service.settle.factor.impl.base;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.*;
import com.paic.ncbs.claim.service.settle.factor.interfaces.base.EveryBillTotalService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.reason.SettleReasonParamsBuildServie;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.LimitAmountService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.print.util.PdfHelpUtils;
import freemarker.template.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service
public class EveryBillTotalServiceImpl implements EveryBillTotalService {
    @Autowired
    private Map<String,LimitAmountService> limitAmountServiceMap;
    @Autowired
    private SettleReasonParamsBuildServie settleReasonParamsBuildServie;

    /**
     * 统计每一天发票的金额
     *
     * @param dutyDetailSettleReasonDTO
     * @param billSettleResultDTOList
     * @param detailPayDTO

     */
    @Override
    public void everyBillTotalAmount(DutyDetailSettleReasonDTO dutyDetailSettleReasonDTO, List<BIllSettleResultDTO> billSettleResultDTOList, DutyDetailPayDTO detailPayDTO) {
        Map<Date,List<BIllSettleResultDTO>> dateResultMap = billSettleResultDTOList.stream().sorted(Comparator.comparing(BIllSettleResultDTO::getBillDate)).collect(Collectors.groupingBy(BIllSettleResultDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
        BigDecimal maxAmountPay  = calMaxAmountPay(detailPayDTO,dateResultMap);
        //日限额数据集合
        List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOList=new ArrayList<>();
        List<EverySettleReasonParamsDTO> everySettReasonLists=new ArrayList<>();
        //计算结果
        BigDecimal sumSettleAmount=BigDecimal.ZERO;
        //所有计算完的发票 按发票日期分组

        for (Map.Entry<Date,List<BIllSettleResultDTO>> entry :dateResultMap.entrySet()) {
            //每天理算依据
            BigDecimal everyAutoSettleAmount=BigDecimal.ZERO;

            //每一天的发票计算结果集
            List<BIllSettleResultDTO> everyBIllResults= entry.getValue();
            //处理超赔付天数，不在保单有效期,等待期发票，超年度赔付天数 等发票的理算依据数据
            EverySettleReasonParamsDTO dto = settleReasonParamsBuildServie.build(everyBIllResults,entry.getKey());
            if (Objects.nonNull(dto)) {
                everySettReasonLists.add(dto);
                dutyBillLimitInfoDTOList.add(setDutyLimitValue(entry.getKey(), detailPayDTO, BigDecimal.ZERO));
                continue;
            }

            EverySettleReasonParamsDTO everySettleReasonParamsDTO=new EverySettleReasonParamsDTO();
            if(Objects.equals("Y",detailPayDTO.getIsDistinguishSocia())){  //区分社保的情况
                if(Objects.equals("2",detailPayDTO.getIsSociaSecurity())){ //有社保
                    if(Objects.equals("3",detailPayDTO.getIsSocialMedicalSettle())){
                        //需要区分经医保结算
                        everyAutoSettleAmount= dealEveryBillResult(everyBIllResults,everySettleReasonParamsDTO,entry.getKey());
                    }else{
                        //不区分经医保结算
                        everyAutoSettleAmount =  dealNoDistinguishSocia(everyBIllResults,everySettleReasonParamsDTO,entry.getKey(),everySettleReasonParamsDTO);
                    }
                }else{ //无社保
                    if(Objects.equals("3",detailPayDTO.getNoSocialMedicalSettle())){
                        //区分经医保结算
                        everyAutoSettleAmount= dealEveryBillResult(everyBIllResults,everySettleReasonParamsDTO,entry.getKey());
                    }else{
                        //不区分经医保结算
                        everyAutoSettleAmount =  dealNoDistinguishSocia(everyBIllResults,everySettleReasonParamsDTO,entry.getKey(),everySettleReasonParamsDTO);
                    }
                }
            }else{
                //不区分社保的情况：直接取固定赔付比列，不会存在这种情况，业务保证客户默认投保时就是有社保身份的，此处只是做一个逻辑兜底
                everyAutoSettleAmount =  dealNoDistinguishSocia(everyBIllResults,everySettleReasonParamsDTO,entry.getKey(),everySettleReasonParamsDTO);
            }

            //带入公式后得到的理算结果
            everySettleReasonParamsDTO.setFormulaSettleAmount(everyAutoSettleAmount.setScale(2, RoundingMode.HALF_UP));
            //最终的理算结果金额，可能会因为日限额，等原因会由变化
            everySettleReasonParamsDTO.setAutoSettleAmount(everyAutoSettleAmount.setScale(2, RoundingMode.HALF_UP));
            long count = everyBIllResults.stream().filter(i -> "0".equals(i.getSettleType()) && "公立".equals(i.getHospitalPropertyDes()) && !i.getHospitalName().contains("牙") && !i.getHospitalName().contains("口腔")).count();
            if(count > 0){
                everySettleReasonParamsDTO.setHospitalPropertyDes("公立");
            }else {
                everySettleReasonParamsDTO.setHospitalPropertyDes("民营");
            }
            //日限额判断
            //限额判断：次限额，日限额，
            String strLimiServie= Constants.LIMIT_IMPL_MAP.get(detailPayDTO.getPayLimitType());
            LimitAmountService limitAmountService=limitAmountServiceMap.get(strLimiServie);
            if(Objects.nonNull(limitAmountService)){
                limitAmountService.settleLimt(detailPayDTO,everySettleReasonParamsDTO);
            }
            //每日发票理算信息保存
            //记录保单下的责任 每天的历史赔付金额
            DutyBillLimitInfoDTO limiDto =setDutyLimitValue(entry.getKey(),detailPayDTO,everySettleReasonParamsDTO.getAutoSettleAmount());
            limiDto.setHospitalPropertyDes(everySettleReasonParamsDTO.getHospitalPropertyDes());
            dutyBillLimitInfoDTOList.add(limiDto);
            everySettReasonLists.add(everySettleReasonParamsDTO);
            sumSettleAmount=sumSettleAmount.add(everySettleReasonParamsDTO.getAutoSettleAmount());
        }
        //责任明细下所有发票的理算依据参数
        dutyDetailSettleReasonDTO.setEverySettleReasonList(everySettReasonLists);
        //责任明细下所有发票理算的金额总和
        dutyDetailSettleReasonDTO.setSettleAmount(sumSettleAmount.setScale(2, RoundingMode.HALF_UP));
        detailPayDTO.setDutyDetailSettleReasonDTO(dutyDetailSettleReasonDTO);
        if(maxAmountPay.compareTo(sumSettleAmount) < 0){
            LogUtil.audit("--计算理算金额结果-- 理算结果超过剩余赔付额！");
            detailPayDTO.setNotice("理算结果"+sumSettleAmount+"超过剩余赔付额！按剩余赔付额"+maxAmountPay+"赔付");
            detailPayDTO.setAutoSettleAmount(maxAmountPay);
            //更新日限额的数据,把剩余赔付金额分摊到没日金额上
            updateDutyLimitData(maxAmountPay,dutyBillLimitInfoDTOList,detailPayDTO.getBillSettleResultDTOList());
        }else{
            detailPayDTO.setAutoSettleAmount(sumSettleAmount.setScale(2, RoundingMode.HALF_UP));
        }

        if(CollectionUtil.isNotEmpty(dutyBillLimitInfoDTOList)){
            detailPayDTO.setDutyBillLimitInfoDTOList(dutyBillLimitInfoDTOList);
        }
    }

    private void updateDutyLimitData(BigDecimal maxAmountPay, List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOList, List<BIllSettleResultDTO> billSettleResultDTOList) {
        if(CollectionUtil.isEmpty(dutyBillLimitInfoDTOList)){
            return;
        }
        List<DutyBillLimitInfoDTO> dutlist= dutyBillLimitInfoDTOList.stream().sorted(Comparator.comparing(DutyBillLimitInfoDTO::getSettleClaimAmount)).collect(Collectors.toList());
        LogUtil.info("更新日限额的数据,把剩余赔付金额分摊到没日金额上={}", JsonUtils.toJsonString(dutlist));
        BigDecimal reduceMaxAmountPay=maxAmountPay;
        for (DutyBillLimitInfoDTO dto : dutlist) {
            if(reduceMaxAmountPay.compareTo(BigDecimal.ZERO)>0){
              if(reduceMaxAmountPay.compareTo(dto.getSettleClaimAmount())<=0){
                  dto.setSettleClaimAmount(reduceMaxAmountPay);
                  reduceMaxAmountPay=BigDecimal.ZERO;
              }else{
                  reduceMaxAmountPay=reduceMaxAmountPay.subtract(dto.getSettleClaimAmount());
              }
            }else{
                dto.setSettleClaimAmount(BigDecimal.ZERO);
            }
            List<BIllSettleResultDTO> bIllSettleLists =  billSettleResultDTOList.stream().filter(bIllSettleResultDTO -> Objects.equals(bIllSettleResultDTO.getPolicyNo(),dto.getPolicyNo()) && Objects.equals(bIllSettleResultDTO.getPlanCode(),dto.getPlanCode()) && Objects.equals(bIllSettleResultDTO.getDutyCode(),dto.getDutyCode()) && Objects.equals(bIllSettleResultDTO.getDutyDetailCode(),dto.getDutyDetailCode())&& Objects.equals(bIllSettleResultDTO.getBillDate(),dto.getBillDate())).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(bIllSettleLists)){
                continue;
            }
            BigDecimal amount=dto.getSettleClaimAmount();
            for (BIllSettleResultDTO billdto  :bIllSettleLists) {
                if(amount.compareTo(BigDecimal.ZERO)<=0){
                    billdto.setAutoSettleAmount(BigDecimal.ZERO);
                } else if (amount.compareTo(billdto.getReasonableAmount())<0) {
                    billdto.setAutoSettleAmount(amount);
                    amount=BigDecimal.ZERO;
                }else{
                    billdto.setAutoSettleAmount(billdto.getReasonableAmount());
                    amount=amount.subtract(billdto.getReasonableAmount());
                }
            }
        }

    }

    /**
     * 每日发票统计
     * @param everyBIllResults
     */
    private boolean isLessThanRemitAmount(List<BIllSettleResultDTO> everyBIllResults) {
        BigDecimal sumReasonableAmount=BigDecimal.ZERO;
        BigDecimal sumRemitAmount=BigDecimal.ZERO;
        for (BIllSettleResultDTO result : everyBIllResults) {
            if(Objects.equals("N",result.getEffectiveFlag())){
               continue;
            }
            if(Objects.equals("Y",result.getExceedMothPayDays())){
                continue;
            }
            if(Objects.equals("Y",result.getExceedYearlyPayDays())){
                continue;
            }
            if(Objects.equals("Y",result.getWaitFlag())){
                continue;
            }
            sumReasonableAmount=sumReasonableAmount.add(result.getReasonableAmount());
            sumRemitAmount=sumRemitAmount.add(nvl(result.getRemitAmount(), 0)).add(nvl(result.getExpendDayDeductible(), 0));
        }
        if(sumReasonableAmount.compareTo(sumRemitAmount)<=0){
           return true;//不足免赔额
        }
        return false;
    }

    private BigDecimal calMaxAmountPay(DutyDetailPayDTO detailPayDTO,Map<Date,List<BIllSettleResultDTO>> sortDateListMap) {
        BigDecimal maxAmountPay = detailPayDTO.getMaxAmountPay();//责任剩余理赔金额
        // 若有赔偿限额，取较低值
        BigDecimal payLimit = detailPayDTO.getPayLimit();//产品责任属性配置的限额
        if(Objects.equals("2",detailPayDTO.getPayLimitType())){
            if(Objects.isNull(detailPayDTO.getPayLimit())){
                throw new GlobalBusinessException("配置了限额类型且是每日限额 请配置限额金额！");
            }
            int count= sortDateListMap.size();
            if(count>=1){
                BigDecimal counts=new BigDecimal(count);
                BigDecimal sumPaylimt=payLimit.multiply(counts);

                if (sumPaylimt.compareTo(maxAmountPay) < 0) {
                    maxAmountPay = sumPaylimt;
                }
            }

        }else {
            if (Objects.nonNull(payLimit) && payLimit.compareTo(maxAmountPay) < 0) {
                maxAmountPay = payLimit;
            }
        }
        return maxAmountPay;
    }
    /**
     * 区分经医保结算
     *
     * @param everyBIllResults
     * @param everySettleReasonParamsDTO
     * @param billDate
     */
    private BigDecimal dealEveryBillResult(List<BIllSettleResultDTO> everyBIllResults, EverySettleReasonParamsDTO everySettleReasonParamsDTO, Date billDate) {
        Map<String,List<BIllSettleResultDTO>>  everyDayBillMap=  everyBIllResults.stream().collect(Collectors.groupingBy(BIllSettleResultDTO::getMedicalSettleFlag));
        BigDecimal sumEverySettleAmount=BigDecimal.ZERO;
        List<SettleReasonParamsDTO>  settleReasonParamsDTOList=new ArrayList<>();
        for(Map.Entry<String,List<BIllSettleResultDTO>> entry :everyDayBillMap.entrySet()) {
            //
            List<BIllSettleResultDTO> isNoMediclList = entry.getValue();//经医保或没有经医保的结果集
            BigDecimal settleAmount = setObjectValue(isNoMediclList,settleReasonParamsDTOList);
            sumEverySettleAmount=sumEverySettleAmount.add(settleAmount);
        }
        everySettleReasonParamsDTO.setBillDate(billDate);
        everySettleReasonParamsDTO.setStrBillDate(DateUtils.dateFormat(billDate,DateUtils.SIMPLE_DATE_STR));
        everySettleReasonParamsDTO.setAutoSettleAmount(sumEverySettleAmount);
        everySettleReasonParamsDTO.setSettleReasonParamsDTOList(settleReasonParamsDTOList);
        return sumEverySettleAmount;

    }

    private BigDecimal setObjectValue(List<BIllSettleResultDTO> isNoMediclList, List<SettleReasonParamsDTO> settleReasonParamsDTOList) {
        SettleReasonParamsDTO settleReasonParamsDTO =new SettleReasonParamsDTO();
        //每天的经医保或未经医保的合理费用总和
        BigDecimal sumReasonableAmount=BigDecimal.ZERO;
        //每天的经医保或未经医保的免赔额总和
        BigDecimal sumRemitAmount=BigDecimal.ZERO;
        //每天的经医保或未经医保的理算金额和
        BigDecimal sumAutoSettleAmount=BigDecimal.ZERO;
        //经医保或未经医保 每个责任下的所有明细的赔付比列都时一样的
        BigDecimal payProportion=BigDecimal.ONE;
        for (BIllSettleResultDTO billSettleDto : isNoMediclList) {
            if(Objects.equals("N",billSettleDto.getEffectiveFlag())){
                continue;
            }
            if(Objects.equals("Y",billSettleDto.getExceedMothPayDays())){
                continue;
            }
            sumReasonableAmount=sumReasonableAmount.add(nvl(billSettleDto.getReasonableAmount(),0));
            sumRemitAmount=sumRemitAmount.add(nvl(billSettleDto.getRemitAmount(),0)).add(nvl(billSettleDto.getExpendDayDeductible(),0));
            sumAutoSettleAmount=sumAutoSettleAmount.add(nvl(billSettleDto.getAutoSettleAmount(),0));
            payProportion=billSettleDto.getPayProportion();
        }
        //不足免赔额判断处理
        if(isLessThanRemitAmount(isNoMediclList)){
            settleReasonParamsDTO.setLessThanRemitAmountFlag("Y");
        }else{
            settleReasonParamsDTO.setLessThanRemitAmountFlag("N");
        }
        settleReasonParamsDTO.setMedicalSettleFlag(isNoMediclList.get(0).getMedicalSettleFlag());
        settleReasonParamsDTO.setReasonableAmount(sumReasonableAmount.setScale(2, RoundingMode.HALF_UP));
        settleReasonParamsDTO.setRemitAmount(sumRemitAmount.setScale(2, RoundingMode.HALF_UP));
        settleReasonParamsDTO.setPayProportion(payProportion);
        settleReasonParamsDTO.setAutoSettleAmount(sumAutoSettleAmount.setScale(2, RoundingMode.HALF_UP));
        settleReasonParamsDTOList.add(settleReasonParamsDTO);
        //理算依据处理
        setCalReason(settleReasonParamsDTO);
        return sumAutoSettleAmount;
    }

    private void setCalReason(SettleReasonParamsDTO settleDto) {
        String templatePath = "calReason.ftl";
        CalFactoryDTO calFactoryDTO =new CalFactoryDTO();
        calFactoryDTO.setReasonableAmount(BigDecimalUtils.toString(settleDto.getReasonableAmount()));
        calFactoryDTO.setRemitAmount(nvl(settleDto.getRemitAmount(),0).compareTo(BigDecimal.ZERO)<=0? "0": BigDecimalUtils.toString(settleDto.getRemitAmount()));
        String  strProportion=nvl(settleDto.getPayProportion(),1).multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString();
        calFactoryDTO.setPayProportion(strProportion+"%");
        calFactoryDTO.setLessThanRemitAmountFlag(settleDto.getLessThanRemitAmountFlag());
        Map variables = (Map) JSONObject.parseObject(JSONObject.toJSONString(calFactoryDTO), Map.class);
        String str="";
        try {
            Template template = PdfHelpUtils.getConfigurationPdf().getTemplate(templatePath);
            str =  FreeMarkerTemplateUtils.processTemplateIntoString(template, variables);
            LogUtil.info("理算依据："+str.trim());
            settleDto.setFormulaData(str);
        } catch (Exception e) {
            LogUtil.info("理算公式依据处理异常");
            throw new RuntimeException(e);
        }
    }
    /**
     * 不区分社保的情况
     *
     * @param everyBIllResults
     * @param everySettleReasonParamsDTO
     * @param billDate
     * @param settleReasonParamsDTO
     */
    private BigDecimal dealNoDistinguishSocia(List<BIllSettleResultDTO> everyBIllResults, EverySettleReasonParamsDTO everySettleReasonParamsDTO, Date billDate, EverySettleReasonParamsDTO settleReasonParamsDTO) {
        List<SettleReasonParamsDTO>  settleReasonParamsDTOList=new ArrayList<>();
        everySettleReasonParamsDTO.setStrBillDate(DateUtils.dateFormat(billDate,DateUtils.SIMPLE_DATE_STR));
        everySettleReasonParamsDTO.setBillDate(billDate);
        BigDecimal autoAmount = setObjectValue(everyBIllResults,settleReasonParamsDTOList);
        if(isLessThanRemitAmount(everyBIllResults)){
            everySettleReasonParamsDTO.setLessThanRemitAmountFlag("Y");
        }
        everySettleReasonParamsDTO.setAutoSettleAmount(autoAmount.setScale(2, RoundingMode.HALF_UP));
        everySettleReasonParamsDTO.setSettleReasonParamsDTOList(settleReasonParamsDTOList);
        return autoAmount;
    }
    private DutyBillLimitInfoDTO setDutyLimitValue(Date billDate, DutyDetailPayDTO detail, BigDecimal settleAmount) {
        //大于0的才记录,
//        if(settleAmount.compareTo(BigDecimal.ZERO)<=0){
//            return null;
//        }
        DutyBillLimitInfoDTO dutyBillLimitInfoDTO = new DutyBillLimitInfoDTO();
        dutyBillLimitInfoDTO.setIdClmsDutyBillLimit(UuidUtil.getUUID());
        dutyBillLimitInfoDTO.setPolicyNo(detail.getPolicyNo());
        dutyBillLimitInfoDTO.setReportNo(detail.getReportNo());
        dutyBillLimitInfoDTO.setBillDate(billDate);
        dutyBillLimitInfoDTO.setBillAmount(settleAmount);
        dutyBillLimitInfoDTO.setSettleClaimAmount(settleAmount);
        dutyBillLimitInfoDTO.setCaseTimes(detail.getCaseTimes());
        dutyBillLimitInfoDTO.setDutyCode(detail.getDutyCode());
        dutyBillLimitInfoDTO.setPlanCode(detail.getPlanCode());
        dutyBillLimitInfoDTO.setDutyDetailCode(detail.getDutyDetailCode());
        //默认为0 核赔完结案更新为1
        dutyBillLimitInfoDTO.setApprovalStatus("0");
        dutyBillLimitInfoDTO.setIsDeleted("0");
        dutyBillLimitInfoDTO.setCreatedBy("system");
        dutyBillLimitInfoDTO.setUpdatedBy("system");
        dutyBillLimitInfoDTO.setCreatedDate(new Date());
        dutyBillLimitInfoDTO.setUpdatedDate(new Date());
        if(StringUtils.isEmptyStr(detail.getPayLimitType())){
            dutyBillLimitInfoDTO.setLimitType("N");//N-不限额
        }else{
            dutyBillLimitInfoDTO.setLimitType(detail.getPayLimitType());//-
        }
        PolicyMonthDto policyMonthDto = getStartEndDate(billDate,detail.getMonthDtoList());
        if(Objects.isNull(policyMonthDto)){
            dutyBillLimitInfoDTO.setMonth(-1);
        }else{
            dutyBillLimitInfoDTO.setMonth(policyMonthDto.getMonth());
        }
        return dutyBillLimitInfoDTO;
    }

    private PolicyMonthDto getStartEndDate(Date billDate, List<PolicyMonthDto> monthDtoList) {

        for (PolicyMonthDto monthDto : monthDtoList) {
            if(billDate.compareTo(monthDto.getStartDate())>=0 && billDate.compareTo(monthDto.getEndDate())<=0){
                return monthDto;
            }
        }
        return null;
    }
}
