package com.paic.ncbs.claim.service.investigate.impl;

import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskAuditPublicMapper;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditPublicDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskQueryDTO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskAuditPublicService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskAuditService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class InvestigateTaskAuditPublicServiceImpl implements InvestigateTaskAuditPublicService {

    @Autowired
    private InvestigateTaskAuditPublicMapper investigateTaskAuditPublicMapper;

    @Autowired
    private InvestigateTaskAuditService investigateTaskAuditService;

    @Override
    public List<WorkBenchTaskVO> getInvestigateTaskList(InvestigateTaskQueryDTO queryDTO) {
        return investigateTaskAuditPublicMapper.getInvestigateTaskList(queryDTO);
    }

    @Override
    public void finishTaskAudit(InvestigateTaskAuditPublicDTO taskAuditDTO) {
        // 转换为原始DTO，调用现有服务
        InvestigateTaskAuditPublicDTO originalDTO = new InvestigateTaskAuditPublicDTO();
        BeanUtils.copyProperties(taskAuditDTO, originalDTO);
        
        // 使用外部审核人作为用户ID
        String externalAuditor = taskAuditDTO.getExternalAuditor();
        if (externalAuditor == null || externalAuditor.trim().isEmpty()) {
            throw new IllegalArgumentException("外部审核人不能为空");
        }
        
        // 调用现有的审核服务
        investigateTaskAuditService.finishTaskAudit(taskAuditDTO, externalAuditor);
    }
}