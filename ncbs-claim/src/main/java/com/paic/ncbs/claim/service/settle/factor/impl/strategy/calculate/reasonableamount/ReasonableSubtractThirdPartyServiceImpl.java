package com.paic.ncbs.claim.service.settle.factor.impl.strategy.calculate.reasonableamount;

import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EveryDayBillInfoDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * C0035 产品合理费用计算
 * 合理费用=发票金额-不合理金额-第三方支付金额
 */
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service("reasonableSubtractThirdPartyServiceImpl")
public class ReasonableSubtractThirdPartyServiceImpl extends CalculateAmountService {
    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {
        //看病报销金·门诊升级版 产品编码-02P00004  的合理金额计算为：发票金额-不合理金额-第三方支付金额
        //reasonableAmount = nvl(sumBillAmount, 0).subtract(sumimmoderateAmount).subtract(sumPrepaidAmount);
        if(Objects.isNull(paramsDTO.getEveryDayBillInfoDTO())){
          return ;
        }
        EveryDayBillInfoDTO billInfoDTO = paramsDTO.getEveryDayBillInfoDTO();
        BigDecimal  reasonableAmount = nvl(billInfoDTO.getBillAmount(), 0).subtract(nvl(billInfoDTO.getImmoderateAmount(),0)).subtract(nvl(billInfoDTO.getPrepaidAmount(),0));
        if(reasonableAmount.compareTo(BigDecimal.ZERO) < 0){
            reasonableAmount = BigDecimal.ZERO;
        }
        paramsDTO.getSettleFactor().setReasonableAmount(reasonableAmount);
        paramsDTO.getSettleFactor().setCalculateAmount(reasonableAmount);
    }
}
