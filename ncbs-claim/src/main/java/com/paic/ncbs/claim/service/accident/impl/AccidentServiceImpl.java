package com.paic.ncbs.claim.service.accident.impl;

import com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonAccidentMapper;
import com.paic.ncbs.claim.service.accident.AccidentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("accidentService")
public class AccidentServiceImpl implements AccidentService {

    @Autowired
    private PersonAccidentMapper personAccidentDao;

    @Override
    public PersonAccidentDTO getPersonAccidentInfo(String reportNo, String caseTimes, String taskId, String status){
        return personAccidentDao.getPersonAccidentInfo(reportNo, caseTimes, taskId, status);
    }
}
