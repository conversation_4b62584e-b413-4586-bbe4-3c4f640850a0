package com.paic.ncbs.claim.service.settle.factor.impl.strategy.remit;

import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.RemitAmountDTO;
import com.paic.ncbs.claim.service.settle.factor.impl.common.ClaimApplicationAwareUtil;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.remit.RemitAmountInitService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 责任的年免赔初始化
 */
@Service
public class YearRemitAmountInitServiceImpl implements RemitAmountInitService {
    @Autowired
    private ClaimApplicationAwareUtil claimApplicationAwareUtil;

    @Override
    public void initRemitData(String key,String serviceName, DutyPayDTO duty, Map<String, CalculateAmountService> calServiceImplMap, List<MedicalBillInfoDTO> medicalBillInfoDTOList) {
        ApplicationContext context = claimApplicationAwareUtil.getApplicationContext();
        if(Objects.isNull(duty.getYearRemitAmountServiceImpl())){
            CalculateAmountService service = (CalculateAmountService) context.getBean(serviceName);
            RemitAmountDTO initDto=getRemitAmountDTO(duty);
            service.initBuild(initDto);
            duty.setYearRemitAmountServiceImpl(service);
            calServiceImplMap.put(key, service);
        }else{
            calServiceImplMap.put(key, duty.getYearRemitAmountServiceImpl());
        }
    }
    private RemitAmountDTO getRemitAmountDTO(DutyPayDTO duty) {
        RemitAmountDTO initDto=new RemitAmountDTO();
        BeanUtils.copyProperties(duty,initDto);
        initDto.setConfigRemitAmount(duty.getRemitAmount());
        initDto.setInsuranceBeginDate(DateUtils.dateFormat(duty.getInsuranceBeginDate(),DateUtils.FULL_DATE_STR));
        initDto.setInsuranceEndDate(DateUtils.dateFormat(duty.getInsuranceEndDate(),DateUtils.FULL_DATE_STR));
        return initDto;
    }
}
