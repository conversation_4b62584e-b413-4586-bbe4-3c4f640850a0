package com.paic.ncbs.claim.service.settle.factor.impl.strategy.template;


import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.template.AbstractTemplateService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * 医疗
 */
@Slf4j
@Service
public class MedicalTemplateServiceImpl extends AbstractTemplateService {

    @Override
    public void setTemplate(DutyDetailPayDTO detailPayDTO) {
        if(Objects.equals("2",detailPayDTO.getPayProportionType())){
            templatePath = Constants.TEMPLATE_FTL_MAP.get("04_2");
        }else{
            templatePath = Constants.TEMPLATE_FTL_MAP.get(detailPayDTO.getDutyDetailType());
        }
    }

    @Override
    public boolean isCustomized() {
        return false;
    }

    @Override
    public String customizeReason(DutyDetailPayDTO detailPayDTO) {
        return null;
    }
}
