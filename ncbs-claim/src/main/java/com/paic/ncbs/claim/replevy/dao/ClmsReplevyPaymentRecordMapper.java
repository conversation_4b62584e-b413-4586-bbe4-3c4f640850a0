package com.paic.ncbs.claim.replevy.dao;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyPaymentRecord;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyPaymentRecordVo;
import com.paic.ncbs.claim.replevy.vo.ReplevyApiVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 追偿与收付交互表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Mapper
public interface ClmsReplevyPaymentRecordMapper extends BaseDao<ClmsReplevyPaymentRecord> {

    /**
     * 查询收付交心
     * @param replevyApiVo
     * @return
     */
    List<ClmsReplevyPaymentRecordVo> selectClmsReplevyPaymentRecord(ReplevyApiVo replevyApiVo);

}
