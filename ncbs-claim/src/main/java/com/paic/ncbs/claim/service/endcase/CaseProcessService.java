package com.paic.ncbs.claim.service.endcase;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.mq.claim.ClaimSendMqDto;

import java.util.Date;
import java.util.List;

public interface CaseProcessService {

    String isDeletePaymentItem(CaseProcessDTO caseProcess);

    void updateCaseProcessDTO(CaseProcessDTO caseProcessDTO) throws GlobalBusinessException;

    Date getArchiveTimeByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    void updateCaseProcess(String reportNo, Integer caseTimes, String processStatus);

    String getCaseProcessStatus(String reportNo, Integer caseTimes);

    String getCaseProcessStatusName(String reportNo, Integer caseTimes);

    List<CaseProcessDTO> getCaseByReportNo( String reportNo);

    CaseProcessDTO getCaseProcessDTO(String reportNo, Integer caseTimes);

    void updateCaseRegisterDept(CaseProcessDTO caseProcessDTO);

    boolean getIsNewProcess(String reportNo, Integer caseTimes);

    void updateCaseProcessByReportNo(CaseProcessDTO caseProcessDTO);

    CaseProcessDTO getProcessStatusAndEndAmount(CaseProcessDTO caseProcess);

    void addCaseProcess(String reportNo, Integer caseTimes, String processStatus, Date reportDate);

    void addCaseProcessNew(String reportNo, Integer caseTimes, String isNewProcess, Date reportDate);

    CaseProcessDTO getCaseCommissionInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    String getCaseProcessStatusNameNew(String reportNo);

    /**
     * 根据报案号，赔付次数查询案件流程状态
     * @param reportNo
     * @param caseTimes
     * @return
     */
    CaseProcessDTO getCaseProcessInfo(String reportNo, Integer caseTimes);
}
