package com.paic.ncbs.claim.replevy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 追偿与收付交互表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@TableName("clms_replevy_payment_record")
public class ClmsReplevyPaymentRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 追偿案件号
     */
    @TableField("replevy_no")
    private String replevyNo;

    /**
     * 追偿次数
     */
    @TableField("replevy_times")
    private Integer replevyTimes;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 请求类型--C01流水查询,C02流水使用,C03收费确认上报
     */
    @TableField("request_type")
    private String requestType;

    /**
     * 业务编码
     */
    @TableField("business_no")
    private String businessNo;

    /**
     * 交易日期时间戳
     */
    @TableField("trans_date")
    private LocalDateTime transDate;

    /**
     * 收款付款--collection收款 payment付款
     */
    @TableField("direction_type")
    private String directionType;

    /**
     * 交易金额
     */
    @TableField("trans_amount")
    private BigDecimal transAmount;

    /**
     * 用途
     */
    @TableField("trans_purpose")
    private String transPurpose;

    /**
     * 摘要
     */
    @TableField("trans_abstract")
    private String transAbstract;

    /**
     * 银企直联系统支付流水号
     */
    @TableField("payment_flowNo")
    private String paymentFlowno;

    /**
     * 银行流水号
     */
    @TableField("bank_trans_flow_no")
    private String bankTransFlowNo;

    /**
     * 我方银行卡号
     */
    @TableField("our_bank_account")
    private String ourBankAccount;

    /**
     * 他方银行卡号
     */
    @TableField("partner_bank_account")
    private String partnerBankAccount;

    /**
     * 他方银行名称
     */
    @TableField("partner_bank_name")
    private String partnerBankName;

    /**
     * 他方网点名称
     */
    @TableField("partner_bank_branch_name")
    private String partnerBankBranchName;

    /**
     * 他方账户名称
     */
    @TableField("partner_bank_account_name")
    private String partnerBankAccountName;

    /**
     * 银行回单在cos中的url
     */
    @TableField("receipt_file_cosurl")
    private String receiptFileCosurl;

    /**
     * 银行回单在cos中的id
     */
    @TableField("receipt_file_cosid")
    private String receiptFileCosid;

    /**
     * 附言
     */
    @TableField("post_script")
    private String postScript;

    /**
     * 核销余额
     */
    @TableField("write_off_remain_amount")
    private BigDecimal writeOffRemainAmount;

    /**
     * 冻结/解冻标志
     *
     */
    @TableField("freeze_flag")
    private String freezeFlag;

    /**
     * 有效标志
     */
    @TableField("valid_flag")
    private String validFlag;

    /**
     * 标志字段
     */
    @TableField("flag")
    private String flag;

    /**
     * 请求报文
     */
    @TableField("request_param")
    private String requestParam;

    /**
     * 响应报文
     */
    @TableField("response_param")
    private String responseParam;

    /**
     * 响应代码--0000代表成功，9999代表失败
     */
    @TableField("response_code")
    private String responseCode;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;
}
