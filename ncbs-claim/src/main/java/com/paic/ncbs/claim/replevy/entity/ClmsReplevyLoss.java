package com.paic.ncbs.claim.replevy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 追偿损失表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@TableName("clms_replevy_loss")
public class ClmsReplevyLoss implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 追偿明细表id
     */
    @TableField("replevy_detail_id")
    private String replevyDetailId;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 追偿案件号
     */
    @TableField("replevy_no")
    private String replevyNo;

    /**
     * 追偿次数
     */
    @TableField("replevy_times")
    private Integer replevyTimes;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 条款
     */
    @TableField("clause_code")
    private String clauseCode;

    /**
     * 险种
     */
    @TableField("plan_code")
    private String planCode;

    /**
     * 险种名称
     */
    @TableField("plan_name")
    private String planName;

    /**
     * 责任代码
     */
    @TableField("duty_code")
    private String dutyCode;

    /**
     * 责任名称
     */
    @TableField("duty_name")
    private String dutyName;

    /**
     * 责任明细代码
     */
    @TableField("duty_detail_code")
    private String dutyDetailCode;

    /**
     * 追回类型
     */
    @TableField("replevied_type")
    private String repleviedType;

    /**
     * 追回物品
     */
    @TableField("replevied_loss")
    private String repleviedLoss;

    /**
     * 物品的单价
     */
    @TableField("price")
    private Long price;

    /**
     * 物品的单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 币别
     */
    @TableField("currency")
    private String currency;

    /**
     * 实际追回金额/变现金额
     */
    @TableField("replevied_money")
    private BigDecimal repleviedMoney;

    /**
     * 高级审核状态
     */
    @TableField("approve_flag")
    private String approveFlag;

    /**
     * 有效标志
     */
    @TableField("valid_flag")
    private String validFlag;

    /**
     * 标志字段
     */
    @TableField("flag")
    private String flag;

    /**
     * 序号
     */
    @TableField("serial_no")
    private Integer serialNo;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;
}
