package com.paic.ncbs.claim.service.settle.factor.impl.formula;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.formula.FormulaService;
import org.springframework.stereotype.Service;

/**
 * 其他
 */
@Service
public class OtherFormulaServiceImpl implements FormulaService {
    @Override
    public String getFormula(DutyDetailPayDTO detail) {
        return null;
    }
}
