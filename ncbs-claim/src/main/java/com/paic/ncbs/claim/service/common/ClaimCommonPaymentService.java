package com.paic.ncbs.claim.service.common;

import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.settle.SettlesFormVO;

import java.util.List;

/**
 * 支付相关的功能方法 在多个地方使用 抽取成单独服务 减少代码冗余
 */
public interface ClaimCommonPaymentService {
    /**
     * 更新支付信息
     *
     */
    public void updatePaymentItems(List<PaymentItemComData> paymentItemArr, List<PolicyPayDTO> policyPayArr, String reportNo, Integer caseTimes);
    public List<PaymentItemDTO> buildCoinsurePaymemtItem(PaymentItemDTO paymentItem, List<CoinsureDTO> coinsureList);

}
