package com.paic.ncbs.claim.controller.settle;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.service.common.ClmsYearlyPayDaysService;
import com.paic.ncbs.claim.service.endcase.DutyBillLimitInfoService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 日限额赔付记录
 */
@RestController
@RequestMapping("/settle/DutyBillLimit")
@Api(tags = {"日限额赔付记录"})
public class DutyBillLimitInfoController {

    @Autowired
    private DutyBillLimitInfoService dutyBillLimitInfoService;

    @Autowired
    private  ClmsYearlyPayDaysService clmsYearlyPayDaysService;
    @GetMapping(value = "/deleteBillLimitInfo/{reportNo}/{caseTimes}")
    public ResponseResult<Object> deleteBillLimitInfo(@PathVariable("reportNo") String reportNo,@PathVariable("caseTimes") Integer caseTimes){
        dutyBillLimitInfoService.deleteByReportNoAndCaseTimes(reportNo,caseTimes);
        return ResponseResult.success();
    }
    @GetMapping(value = "/testUpdateBillLimt/{reportNo}/{caseTimes}")
    public ResponseResult<Object> testUpdateBillLimt(@PathVariable("reportNo") String reportNo,@PathVariable("caseTimes") Integer caseTimes){
        dutyBillLimitInfoService.dealBIllLimtData(reportNo,caseTimes);
        return ResponseResult.success();
    }
    @GetMapping(value = "/testYeayPayDay/{reportNo}/{caseTimes}")
    public ResponseResult<Object> testYeayPayDay(@PathVariable("reportNo") String reportNo,@PathVariable("caseTimes") Integer caseTimes){
        clmsYearlyPayDaysService.dealData(reportNo,caseTimes);
        return ResponseResult.success();
    }

}
