package com.paic.ncbs.claim.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务健康检查
 */
@RestController
@RequestMapping("/healthCheck")
public class HealthCheckController extends BaseController{

    private static String HTTP_STATUS_OK = "OK";

    @GetMapping("/status")
    public String getStatus(){
        //健康检查接口,预计调用频率极高,返回内容尽可能少,最好不返回body,由调用方通过http状态码判定服务是否健康
        return HTTP_STATUS_OK;
    }
}
