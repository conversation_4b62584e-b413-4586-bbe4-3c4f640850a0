package com.paic.ncbs.claim.service.settle.factor.impl.bill;

import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EveryDayBillInfoDTO;
import com.paic.ncbs.claim.service.settle.ClmsSettleBillRuleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.bill.MedicalBillInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class MedicalBillInfoServiceImpl implements MedicalBillInfoService {
    @Autowired
    private MedicalBillInfoMapper medicalBillInfoMapper;
    @Autowired
    private ClmsSettleBillRuleService clmsSettleBillRuleService;



    /**
     * 计算 每张发票的合理费用
     * billInfoVOList：所有发票数据
     *
     * @param billInfoDTOList
     * @param billDate
     */
    private List<EveryDayBillInfoDTO> getDutyAttributeDTOList(List<MedicalBillInfoDTO> billInfoDTOList, Date billDate) {
        List<EveryDayBillInfoDTO> dtoList = new ArrayList<>();
        //过滤出所有经医保计算的数据
        List<MedicalBillInfoDTO> medicalBillInfoDTOList = billInfoDTOList.stream().filter(b -> Objects.equals("BT_3601", b.getBillType()) || Objects.equals("BT_3603", b.getBillType())).collect(Collectors.toList());
        //过滤所有非医保结算的发票数据
        List<MedicalBillInfoDTO> noMedicalBillInfoDTOList = billInfoDTOList.stream().filter(b -> !Objects.equals("BT_3601", b.getBillType()) && !Objects.equals("BT_3603", b.getBillType())).collect(Collectors.toList());
        List<EveryDayBillInfoDTO> everyDayMediclDTO = getDutyAttributeDTOs(medicalBillInfoDTOList, "Y", billDate);
        if (Objects.nonNull(everyDayMediclDTO)) {
            LogUtil.info("每日发票经医保结算的各项费用信息={}", JsonUtils.toJsonString(everyDayMediclDTO));
            dtoList.addAll(everyDayMediclDTO);
        }
        // //没有经医保结算的
        List<EveryDayBillInfoDTO> everyDayNoMediclDTO = getDutyAttributeDTOs(noMedicalBillInfoDTOList, "N", billDate);
        if (Objects.nonNull(everyDayNoMediclDTO)) {
            LogUtil.info("每日发票没有经医保结算的各项费用信息={}", JsonUtils.toJsonString(everyDayNoMediclDTO));
            dtoList.addAll(everyDayNoMediclDTO);
        }
        return dtoList;
    }

    /**
     * 累计每日发票各项金额总和
     * @param medicalBillInfoDTOList
     * @param mediclSettleFlag
     * @param billDate
     * @return
     */
    private EveryDayBillInfoDTO getDutyAttributeDTO(List<MedicalBillInfoDTO> medicalBillInfoDTOList, String mediclSettleFlag,Date billDate) {
        EveryDayBillInfoDTO everyDayBillInfoDTO = null;
        if (!CollectionUtils.isEmpty(medicalBillInfoDTOList)) {
            BigDecimal sumBillAmount = BigDecimal.ZERO;//发票总金额
            BigDecimal sumPartialDeductible = BigDecimal.ZERO;//部分自费总金额
            BigDecimal sumDeductibleAmount = BigDecimal.ZERO;//自费总金额
            BigDecimal sumPrepaidAmount = BigDecimal.ZERO;//第三方支付总金额
            BigDecimal sumimmoderateAmount = BigDecimal.ZERO;//不合理总金额
            for (MedicalBillInfoDTO medicalBillInfoDTO : medicalBillInfoDTOList) {
                sumBillAmount = sumBillAmount.add(medicalBillInfoDTO.getBillAmount());
                sumPartialDeductible = sumPartialDeductible.add(medicalBillInfoDTO.getPartialDeductible());
                sumDeductibleAmount = sumDeductibleAmount.add(medicalBillInfoDTO.getDeductibleAmount());
                sumPrepaidAmount = BigDecimalUtils.sum(sumPrepaidAmount,medicalBillInfoDTO.getPrepaidAmount());
                sumimmoderateAmount = BigDecimalUtils.sum(sumimmoderateAmount,medicalBillInfoDTO.getImmoderateAmount());
            }


            everyDayBillInfoDTO = new EveryDayBillInfoDTO();
            everyDayBillInfoDTO.setBillDate(billDate);//
            everyDayBillInfoDTO.setMedicalSettleFlag(mediclSettleFlag);
            everyDayBillInfoDTO.setBillAmount(sumBillAmount);
            everyDayBillInfoDTO.setPartialDeductible(sumPartialDeductible);
            everyDayBillInfoDTO.setDeductibleAmount(sumDeductibleAmount);
            everyDayBillInfoDTO.setPrepaidAmount(sumPrepaidAmount);
            everyDayBillInfoDTO.setImmoderateAmount(sumimmoderateAmount);
            if (Objects.equals("N", mediclSettleFlag)) {
                everyDayBillInfoDTO.setSerialNo(1);
            } else {
                everyDayBillInfoDTO.setSerialNo(2);
            }

        }
        return everyDayBillInfoDTO;
    }


    @Override
    public List<EveryDayBillInfoDTO> getEveryDayBillInfoList(List<MedicalBillInfoDTO> medicalBillInfoDTOList) {
        List<EveryDayBillInfoDTO> resultLists=new ArrayList<>();
        //按发票日期分组
        Map<Date, List<MedicalBillInfoDTO>> billDateMap = medicalBillInfoDTOList.stream().collect(Collectors.groupingBy(MedicalBillInfoDTO::getStartDate));
        for (Map.Entry<Date, List<MedicalBillInfoDTO>> entry : billDateMap.entrySet()) {
            List<MedicalBillInfoDTO> billInfoDTOList = entry.getValue();
            List<EveryDayBillInfoDTO> everyDayBIllList = getDutyAttributeDTOList(billInfoDTOList, entry.getKey());
            if (!CollectionUtils.isEmpty(everyDayBIllList)) {
                resultLists.addAll(everyDayBIllList);
            }

        }
        return resultLists;
    }

    /**
     * 按发票号 打标 经医保和未经医保标志
     * @param medicalBillInfoDTOList
     * @return
     */
    @Override
    public List<EveryDayBillInfoDTO> getEveryDayBillInfoLists(List<MedicalBillInfoDTO> medicalBillInfoDTOList) {
        List<EveryDayBillInfoDTO> resultLists=new ArrayList<>();
        if (!CollectionUtils.isEmpty(medicalBillInfoDTOList)) {
            for (MedicalBillInfoDTO dto : medicalBillInfoDTOList) {
                EveryDayBillInfoDTO everyDayBillInfoDTO = new EveryDayBillInfoDTO();
                everyDayBillInfoDTO.setBillDate(dto.getStartDate());//
                if(Objects.equals("BT_3601", dto.getBillType()) || Objects.equals("BT_3603", dto.getBillType())){
                    everyDayBillInfoDTO.setMedicalSettleFlag("Y");
                    everyDayBillInfoDTO.setSerialNo(2);
                }else{
                    everyDayBillInfoDTO.setMedicalSettleFlag("N");
                    everyDayBillInfoDTO.setSerialNo(1);
                }
                everyDayBillInfoDTO.setEffectiveFlag(dto.getEffectiveFlag());
                everyDayBillInfoDTO.setWaitFlag(dto.getWaitFlag());
                everyDayBillInfoDTO.setBillAmount(dto.getBillAmount());
                everyDayBillInfoDTO.setPartialDeductible(dto.getPartialDeductible());
                everyDayBillInfoDTO.setDeductibleAmount(dto.getDeductibleAmount());
                everyDayBillInfoDTO.setPrepaidAmount(dto.getPrepaidAmount());
                everyDayBillInfoDTO.setImmoderateAmount(dto.getImmoderateAmount());
                everyDayBillInfoDTO.setBillNo(dto.getBillNo());
                everyDayBillInfoDTO.setId(dto.getIdAhcsBillInfo());
                everyDayBillInfoDTO.setTherapyType(dto.getTherapyType());
                everyDayBillInfoDTO.setHospitalCode(dto.getHospitalCode());
                everyDayBillInfoDTO.setHospitalName(dto.getHospitalName());
                everyDayBillInfoDTO.setHospitalPropertyDes(dto.getHospitalPropertyDes());
                resultLists.add(everyDayBillInfoDTO);
            }

        }
        return resultLists;
    }
    /**
     * 原始发票信息
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    public List<MedicalBillInfoDTO> getMedicalBillInfoList(String reportNo, Integer caseTimes) {
        //账单日期合理费用  按日期,就诊类型(门诊，住院)，发票类型分组，
        MedicalBillInfoDTO medicalBillInfoDTO = new MedicalBillInfoDTO();
        medicalBillInfoDTO.setReportNo(reportNo);
        medicalBillInfoDTO.setCaseTimes(caseTimes);
        /*List<MedicalBillInfoVO> billInfoVOLists = medicalBillInfoMapper.getBillInfoGroupDateList(reportNo, caseTimes);
        if(CollectionUtil.isEmpty(billInfoVOLists)){
            return null;
        }*/
        return medicalBillInfoMapper.getBillInfoByPage(medicalBillInfoDTO);
    }

    /**
     * 处理发票信息
     * @param medicalBillInfoDTOList
     * @param mediclSettleFlag
     * @param billDate
     * @return
     */
    private List<EveryDayBillInfoDTO> getDutyAttributeDTOs(List<MedicalBillInfoDTO> medicalBillInfoDTOList, String mediclSettleFlag,Date billDate) {
        List<EveryDayBillInfoDTO>  list =new ArrayList<>();
        if (!CollectionUtils.isEmpty(medicalBillInfoDTOList)) {
            for (MedicalBillInfoDTO medicalBillInfoDTO : medicalBillInfoDTOList) {
                EveryDayBillInfoDTO everyDayBillInfoDTO = new EveryDayBillInfoDTO();
                everyDayBillInfoDTO.setBillDate(billDate);//
                everyDayBillInfoDTO.setMedicalSettleFlag(mediclSettleFlag);
                everyDayBillInfoDTO.setBillAmount(medicalBillInfoDTO.getBillAmount());
                everyDayBillInfoDTO.setPartialDeductible(medicalBillInfoDTO.getPartialDeductible());
                everyDayBillInfoDTO.setDeductibleAmount(medicalBillInfoDTO.getDeductibleAmount());
                everyDayBillInfoDTO.setPrepaidAmount(medicalBillInfoDTO.getPrepaidAmount());
                everyDayBillInfoDTO.setImmoderateAmount(medicalBillInfoDTO.getImmoderateAmount());
                if (Objects.equals("N", mediclSettleFlag)) {
                    everyDayBillInfoDTO.setSerialNo(1);
                } else {
                    everyDayBillInfoDTO.setSerialNo(2);
                }
                list.add(everyDayBillInfoDTO);
            }

        }
        return list;
    }
}
