package com.paic.ncbs.claim.service.settle.factor.impl.common;

import cn.hutool.core.date.DateUtil;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.enums.RuleTypeEnums;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.config.ProductConfig;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.service.rule.AutoRuleRecordService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.common.BeforeBillInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 指定产品得发票特殊处理 住院发票过滤，等待期发票过滤
 * 少儿门诊医疗、长鹅成人门诊医疗、长鹅门诊赠险
 */
@Slf4j
@Order(1)
@RefreshScope
@Service("filterDesignateProductBillRuleServiceImpl")
public class FilterDesignateProductBillServiceImpl implements BeforeBillInfoService {
    @Value("${product.productCodeList:02P00040}")
    private List<String> productCodeList;
    @Autowired
    private MedicalBillInfoMapper medicalBillInfoMapper;
    @Autowired
    private CaseClassMapper caseClassMapper;
    @Autowired
    private AutoRuleRecordService autoRuleRecordService;
    @Autowired
    private ProductConfig productConfig;
    @Override
    public List<MedicalBillInfoDTO> expansion(List<MedicalBillInfoDTO> medicalBillInfoDTOList, DutyDetailPayDTO detailPayDTO) {
        // 这里不加产品判断了
        if(detailPayDTO.getWaitDays() > 0){
            // 存在等待期属性时
            Date insuranceBeginDate = DateUtil.beginOfDay(detailPayDTO.getInsuranceBeginTime());
            Integer waitingPeriodExtend = Optional.ofNullable(productConfig.getWaitingPeriodExtend(detailPayDTO.getProductCode(),
                    detailPayDTO.getProductPackage())).orElse(0);
            int waitDays  = detailPayDTO.getWaitDays() + waitingPeriodExtend;
            Date endWaitDate = DateUtil.offsetMillisecond(DateUtils.addDate(detailPayDTO.getInsuranceBeginTime(),waitDays),-1);

            boolean isExistWait = medicalBillInfoDTOList.stream().anyMatch(item -> item.getStartDate().compareTo(insuranceBeginDate)>=0 && item.getStartDate().compareTo(endWaitDate)<=0);
            if(isExistWait){
                //如果有等待期发票 工作量不能理算自动提交 需要 手动提交
                detailPayDTO.setWaitFlag(BaseConstant.BEIN_HOSPITAL_FLAG);
            }
        }

        String reportNo=detailPayDTO.getReportNo();
        Integer caseTimes =detailPayDTO.getCaseTimes();
        List<String> therapyTypeList = medicalBillInfoMapper.getBillClassByReportNo(reportNo, caseTimes);
        String therapyType = null;
        if (therapyTypeList.size() > 1) {
            therapyType = BaseConstant.STRING_3;
        } else if (therapyTypeList.size() == 1 && ChecklossConst.AHCS_THERAPY_ONE.equals(therapyTypeList.get(0))) {
            therapyType = BaseConstant.STRING_2;
        } else if (therapyTypeList.size() == 1 && ChecklossConst.AHCS_THERAPY_TWO.equals(therapyTypeList.get(0))) {
            therapyType = BaseConstant.STRING_1;
        }
        String therapyTypeFinal = therapyType;
        /**
         * 2024/04/29号测试案例评审确定：只考虑	少儿门诊医疗、长鹅成人门诊医疗、长鹅门诊赠险
         */
        List<MedicalBillInfoDTO> returnLists=new ArrayList<>();
        if(!productCodeList.contains(detailPayDTO.getProductCode()) && !productCodeList.contains(detailPayDTO.getProductPackage())){
            return medicalBillInfoDTOList;
        }
        //判断过滤出住门诊发票，住院发票不参与计算：少儿门诊医疗、长鹅成人门诊医疗、长鹅门诊赠险 住院发票不参与理算
        List<MedicalBillInfoDTO> outpatientList=
                medicalBillInfoDTOList.stream().filter(item -> Objects.equals(ChecklossConst.AHCS_THERAPY_ONE,
                        item.getTherapyType())).collect(Collectors.toList());
        //如果同时存在住院和门诊的发票 剔除住院的发票 同时 标记
        if(Objects.equals(BaseConstant.STRING_3,therapyTypeFinal)){
            detailPayDTO.setBeInHospitalFlag(BaseConstant.BEIN_HOSPITAL_FLAG);//打标
        }
        if(CollectionUtils.isEmpty(outpatientList)){
            return outpatientList;
        }
        //案件类别判断:案件类别仅是“意健险”，且二级分类仅含“意外医疗”、“疾病住院医疗”、“疾病门急诊医疗”时，定损信息不得为空
        List<String>  caseClassList = caseClassMapper.getCaseClassParentAll(reportNo,caseTimes, BpmConstants.CHECK_DUTY,"1");
        if(CollectionUtils.isEmpty(caseClassList)){
            throw new GlobalBusinessException("案件类别不存在！");
        }
        //1表示大类为意健险
        List<String>  eqOnelist = caseClassList.stream().filter(s -> Objects.equals("1",s)).collect(Collectors.toList());
        //不为意健险
        List<String>  noEqOnelist = caseClassList.stream().filter(s -> !Objects.equals("1",s)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(eqOnelist) && CollectionUtils.isEmpty(noEqOnelist)){
            //查询二级类型
            List<String> caseSubClassList = caseClassMapper.getCaseClassList(reportNo, caseTimes, BpmConstants.CHECK_DUTY);
            if(CollectionUtils.isEmpty(caseSubClassList)){
                throw new GlobalBusinessException("案件类别不存在！");
            }
            List<String> subClassList = caseSubClassList.stream().filter(s ->Objects.equals(InsuredApplyTypeEnum.SICKNESS_HOSPITALIZATION.getType(),s)||Objects.equals(InsuredApplyTypeEnum.EMERGENCY_MEDICINE.getType(),s)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(subClassList)){
                //查询保单有效期
                String insuranceBeginTime = DateUtils.parseToFormatStr(detailPayDTO.getInsuranceBeginTime(),DateUtils.SIMPLE_DATE_STR);
                Date insuranceBeginDate =DateUtils.formatStringToDate(insuranceBeginTime,DateUtils.SIMPLE_DATE_STR);
                //转格式年月日 保单终止日
                String insuranceEndDate = DateUtils.parseToFormatStr(detailPayDTO.getInsuranceEndTime(),DateUtils.SIMPLE_DATE_STR);
                //在转为日期类型
                Date insendDate=DateUtils.formatStringToDate(insuranceEndDate,DateUtils.SIMPLE_DATE_STR);
                //保单起始日期加等待期
                //筛选出在保单有效期内的发票
                Date endWaitDate = null;//等待期结束日
                if(Objects.nonNull(detailPayDTO.getWaitDays()) && detailPayDTO.getWaitDays()>0){
                    Date endWaitTime=DateUtils.addDate(insuranceBeginDate,detailPayDTO.getWaitDays());
                    endWaitDate= DateUtil.offsetMillisecond(endWaitTime,-1);//处理等待期结束日期
                    log.info("报案号={},等待期到={}",reportNo,DateUtils.parseToFormatStr(endWaitDate,DateUtils.FULL_DATE_STR));
                }

                List<MedicalBillInfoDTO> waitLists= new ArrayList<>();
                for (MedicalBillInfoDTO medicalBillInfoDTO : outpatientList) {
                    String sbilldate = DateUtils.dateFormat(medicalBillInfoDTO.getStartDate(),DateUtils.SIMPLE_DATE_STR);
                    Date billdate =DateUtils.formatStringToDate(sbilldate,DateUtils.SIMPLE_DATE_STR);
                    //保单有效期内判断 billdate<insendDate 返回-1
                    log.info("报案号={},发票日期={},保单起期={}，保单终止日期={}",reportNo,DateUtils.parseToFormatStr(billdate,DateUtils.FULL_DATE_STR),DateUtils.parseToFormatStr(insuranceBeginDate,DateUtils.FULL_DATE_STR),DateUtils.parseToFormatStr(insendDate,DateUtils.FULL_DATE_STR));
                    if(billdate.compareTo(insuranceBeginDate)<0 || billdate.compareTo(insendDate)>0){
                        log.info("发票日期不在保单有效期内");
                        medicalBillInfoDTO.setEffectiveFlag("N");
                        returnLists.add(medicalBillInfoDTO);
                        continue;
                    }
                    if(Objects.nonNull(endWaitDate)){
                        log.info("报案号={},等待期到={}",reportNo,DateUtils.parseToFormatStr(endWaitDate,DateUtils.FULL_DATE_STR));
                        if(billdate.compareTo(insuranceBeginDate)>=0 && billdate.compareTo(endWaitDate)<=0){
                            waitLists.add(medicalBillInfoDTO);
                            medicalBillInfoDTO.setWaitFlag("Y");
                            returnLists.add(medicalBillInfoDTO);
                            log.info("报案号={},等待期发票={}",reportNo, JsonUtils.toJsonString(waitLists));
                        }else{
                            medicalBillInfoDTO.setWaitFlag("N");
                            returnLists.add(medicalBillInfoDTO);
                            log.info("报案号={},过滤后的发票={}",reportNo,JsonUtils.toJsonString(returnLists));
                        }

                    }else{
                        log.info("报案号={},没等待期直接返回发票={}",reportNo,JsonUtils.toJsonString(returnLists));
                        medicalBillInfoDTO.setWaitFlag("N");
                        returnLists.add(medicalBillInfoDTO);
                    }

                }
                //等待期发票集合
                if(CollectionUtils.isNotEmpty(waitLists)){
                    detailPayDTO.setWaitFlag(BaseConstant.BEIN_HOSPITAL_FLAG);//如果有等待期发票 工作量不能理算自动提交 需要 手动提交
                    boolean isexsits= autoRuleRecordService.ruleDetailExist(reportNo,caseTimes,BpmConstants.OC_MANUAL_SETTLE, RuleTypeEnums.RULE_TYPE_000008.getCode());
                    if(!isexsits){
                        List<String> billNolist= medicalBillInfoMapper.getBIllNo(reportNo,caseTimes,insuranceBeginDate,endWaitDate);
                        //ListUtils.getStringWithSeparator(billNolist, Constants.SEPARATOR) +
                        String message= ListUtils.getStringWithSeparator(billNolist, Constants.SEPARATOR)+ RuleTypeEnums.RULE_TYPE_000008.getName();
                        LogUtil.info("触发等待期规则"+message);
                        autoRuleRecordService.addRuleDetailRecord(reportNo,caseTimes,BpmConstants.OC_MANUAL_SETTLE,RuleTypeEnums.RULE_TYPE_000008.getCode(),message);
                    }

                }
                return returnLists;
            }
            return medicalBillInfoDTOList;
        }
        log.info("报案号={},符合计算的发票信息={}",reportNo, JsonUtils.toJsonString(returnLists));
        return medicalBillInfoDTOList;

    }
}
