package com.paic.ncbs.claim.service.settle.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.ModelConsts;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.enums.PaymentInfoTypeEnum;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.SettlePaymentService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service("paymentItemService")
public class SettlePaymentServiceImpl implements SettlePaymentService {
    @Autowired
    private PaymentInfoService paymentInfoService;

    @Autowired
    private PaymentItemService paymentItemService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private CoinsureService coinsureService;

    @Override
    public void autoGeneratePaymentItems(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPays, String idAhcsBatch) {

        //获取理算了金额的保单
        List<PolicyPayDTO> settlePolicyPays = getSettlePolicyPays(policyPays);
        if(CollectionUtils.isEmpty(settlePolicyPays)){
            LogUtil.audit("--自动生成支付项(赔付信息)-没有查到理算了金额的保单");
            return;
        }
        LogUtil.audit("--自动生成支付项(赔付信息)-reportNo={}", reportNo);
        //查询（P1赔款、P2费用）赔款的支付信息、领款人
        List<PaymentInfoDTO> paymentInfos = getPaymentInfosByType(reportNo,caseTimes, ModelConsts.PAYMENT_TYPE_REPARATIONS);
        if(CollectionUtils.isEmpty(paymentInfos)){
            return;
        }

        Map<String,List<CoinsureDTO>> coinsMap = coinsureService.getCoinsureListByReportNo(reportNo);
        List<PaymentItemComData> paymentItems = new ArrayList<>();
        for (PolicyPayDTO policyPay : settlePolicyPays) {

            BigDecimal settleAmount = policyPay.getSettleAmount();
            /*BigDecimal prePayAmount = policyPay.getPolicyPrePay();
            BigDecimal policySettleAmount = nvl(settleAmount,0).subtract(nvl(prePayAmount,0));*/
            String generateCaseNo = commonService.generateNo( NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO,policyPay.getDepartmentCode());
            //生成支付项信息
            for (int i=0;i<paymentInfos.size();i++) {
                PaymentInfoDTO paymentInfo = paymentInfos.get(i);
                PaymentItemComData paymentItem = new PaymentItemComData();
                BeanUtils.copyProperties(paymentInfo,paymentItem);
                paymentItem.setPolicyNo(policyPay.getPolicyNo());
                paymentItem.setCaseNo(policyPay.getCaseNo());
                paymentItem.setPaymentType(SettleConst.PAYMENT_TYPE_PAY);
                paymentItem.setPaymentTypeName(SettleConst.PAYMENT_TYPE_PAY_NAME);
                paymentItem.setCollectPaySign(SettleConst.COLLECTION);
                paymentItem.setClaimType(SettleConst.CLAIM_TYPE_PAY);
                paymentItem.setMergeSign(SettleConst.NOT_MERGE);
                // 塞第三方系统的计算书号
                paymentItem.setCompensateNo(generateCaseNo);
                paymentItem.setMergeSignName("不合并");
                paymentItem.setIdClmBatch(idAhcsBatch);
                paymentItem.setPaymentCurrencyCode(SettleConst.RMB);
                //按领款人均分赔付金
                paymentItem.setPaymentAmount(i==0? averageAmount(settleAmount,paymentInfos.size(),true)
                        : averageAmount(settleAmount,paymentInfos.size(),false));

                setCoinsureField(paymentItem, coinsMap);
                paymentItems.add(paymentItem);
            }
        }
        //保存核赔批单-赔付信息
        if(!CollectionUtils.isEmpty(paymentItems)){
            paymentItemService.addPaymentItemDataList(paymentItems);
            LogUtil.audit("自动生成支付项(赔付信息)-保存支付项：{}",JSON.toJSONString(paymentItems));
        }
    }

    private void setCoinsureField(PaymentItemComData paymentItem, Map<String,List<CoinsureDTO>> coinsMap) {
        List<CoinsureDTO> coinsureList = coinsMap.get(paymentItem.getPolicyNo());
        if (CollectionUtils.isEmpty(coinsureList)) {
            return;
        }

        CoinsureDTO coinsureDTO = coinsureList.stream().filter(i -> BaseConstant.STRING_1.equals(i.getCompanyFlag())).findFirst().orElse(null);
        if (Objects.isNull(coinsureDTO)) {
            return;
        }

        paymentItem.setCoinsuranceMark(BaseConstant.STRING_1);
        paymentItem.setAcceptInsuranceFlag(coinsureDTO.getAcceptInsuranceFlag());
        paymentItem.setCoinsuranceCompanyCode(coinsureDTO.getReinsureCompanyCode());
        paymentItem.setCoinsuranceCompanyName(coinsureDTO.getReinsureCompanyName());
        paymentItem.setCoinsuranceRatio(coinsureDTO.getReinsureScale());

        if (BaseConstant.STRING_2.equals(coinsureDTO.getCoinsuranceType())) {
            // 从共默认全额给付
            paymentItem.setIsFullPay(BaseConstant.STRING_1);
            paymentItem.setCoinsuranceActualAmount(paymentItem.getPaymentAmount());
        } else {
            // 主共默认非全额给付
            paymentItem.setIsFullPay(BaseConstant.STRING_0);

            coinsureList = coinsureList.stream().sorted(Comparator.comparing(CoinsureDTO::getCompanyFlag)).collect(Collectors.toList());
            BigDecimal paymentAmount = paymentItem.getPaymentAmount();
            BigDecimal otherAmount = BigDecimal.ZERO;
            for (int i = 0; i < coinsureList.size() - 1; i++) {
                CoinsureDTO coins = coinsureList.get(i);
                otherAmount = otherAmount.add(paymentAmount.multiply(nvl(coins.getReinsureScale(), 0)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            paymentItem.setCoinsuranceActualAmount(paymentAmount.subtract(otherAmount));
        }
    }

    private BigDecimal averageAmount(BigDecimal policySettleAmount, int size,boolean isFirst){
        double a = policySettleAmount.doubleValue();
        BigDecimal b = BigDecimal.valueOf(a/size).setScale(2,RoundingMode.DOWN);
        return isFirst ? policySettleAmount.subtract(b.multiply(BigDecimal.valueOf(size-1))) : b;
    }

    public static void main(String[] args) {
        BigDecimal policySettleAmount = new BigDecimal("7039.98");
        double a = policySettleAmount.doubleValue();
        BigDecimal b = BigDecimal.valueOf(a/3).setScale(2,RoundingMode.DOWN);
        BigDecimal d = policySettleAmount.subtract(b.multiply(BigDecimal.valueOf(2)));
        System.out.println(b);
        System.out.println(d);

    }


    private List<PolicyPayDTO> getSettlePolicyPays(List<PolicyPayDTO> policyPays){
        //理算了金额的保单
        List<PolicyPayDTO> settlePolicyPays = new ArrayList<>();
        for (PolicyPayDTO policy : policyPays) {
            // 理算初始化改为理算金额0也生成支付项
            /*BigDecimal policyAmount = policy.getSettleAmount();
            //理算金额为空或者0,不生成支付项,当前版本不考虑预赔金额
            if(BigDecimal.ZERO.compareTo(nvl(policyAmount,0)) == 0){
                continue;
            }*/
            settlePolicyPays.add(policy);
        }
        return settlePolicyPays;
    }

    private BigDecimal getPamentRaido(int size){
        return BigDecimal.ONE.divide(new BigDecimal(size),2, RoundingMode.HALF_UP);
    }

    public List<PaymentInfoDTO> getPaymentInfosByType(String reportNo,Integer caseTimes,String paymentType) throws GlobalBusinessException {
        PaymentInfoDTO param = new PaymentInfoDTO();
        param.setReportNo(reportNo);
        param.setCaseTimes(caseTimes);
        param.setPaymentInfoType(PaymentInfoTypeEnum.WHOLE_CASE.getType());
        param.setPaymentUsage(paymentType);
        List<PaymentInfoDTO> paymentInfos = paymentInfoService.getPaymentInfo(param);
        if(CollectionUtils.isEmpty(paymentInfos)){
            return paymentInfos;
        }
        List<PaymentInfoDTO> newPaymentInfos = new ArrayList<>();
        for (PaymentInfoDTO paymentInfo : paymentInfos) {
            LogUtil.audit("requestPaymentInfosByType.paymentInfo={}", JSONObject.toJSONString(paymentInfo));
            //过滤掉预赔相关的支付信息
            if(paymentInfo.getSubTimes() != null){
                LogUtil.audit("requestPaymentInfosByType.过滤掉预配相关的支付信={}", JSONObject.toJSONString(paymentInfo));
                continue;
            }

            if(StringUtils.isEqualStr(paymentType, paymentInfo.getPaymentUsage())){
                newPaymentInfos.add(paymentInfo);
            }
        }
        LogUtil.audit("newPaymentInfos.list.size={}", newPaymentInfos.size());
        return newPaymentInfos;
    }

}
