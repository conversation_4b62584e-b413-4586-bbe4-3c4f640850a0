package com.paic.ncbs.claim.controller.ahcs;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.PrePayReasonEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.common.OptionsEntity;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.vo.ahcs.PrePayCaseVO;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.prepay.PrePayService;
import com.paic.ncbs.claim.service.user.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/who/app/prePayAction")
public class PrePayController extends BaseController {

	@Autowired
	private PrePayService prePayService;
	@Autowired
	private PaymentItemService paymentItemService;
	@Autowired
	private PermissionService permissionService;
	@Autowired
	private TaskInfoMapper taskInfoMapper;

	@RequestMapping(value = "/getPaymentItemListByClaimType", method = RequestMethod.POST)
	public ResponseResult<List<PaymentItemComData>> getPaymentItemListByClaimType(@RequestBody PaymentItemComData param) throws GlobalBusinessException{
		//获取预赔次数
		if(param.getSubTimes() == null){
			param.setSubTimes(prePayService.getApprovedSubTimes(param.getReportNo(),param.getCaseTimes()));
		}

		return ResponseResult.success(paymentItemService.getPaymentItemList(param));

	}

	@GetMapping(value = "/getPrePayCaseList")
	public ResponseResult<PrePayCaseVO > getPrePayCaseList(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes")Integer caseTimes,@RequestParam(value = "subTimes",required = false) Integer subTimes) throws GlobalBusinessException{
		if(StringUtils.isEmptyStr(reportNo)){
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		return ResponseResult.success(prePayService.getPrePayCaseList(reportNo,caseTimes,subTimes));

	}

	@PostMapping(value = "/savePrePayApply")
	public ResponseResult savePrePayApply(@RequestBody PrePayCaseVO prePayCaseVO){
		LogUtil.audit("预赔申请入参={}", JSON.toJSONString(prePayCaseVO));
		prePayService.savePrePayApply(prePayCaseVO);
		return ResponseResult.success();
	}

	@PostMapping(value = "/sendPrePayApprove")
	public ResponseResult<Object> sendPrePayApprove(@RequestBody PrePayInfoDTO prePayInfoDTO){
		if(StringUtils.isEmptyStr(prePayInfoDTO.getReportNo())
				|| StringUtils.isEmptyStr(prePayInfoDTO.getVerifyOptions())
				|| StringUtils.isEmptyStr(prePayInfoDTO.getPrePayInfoId())){
			LogUtil.audit("reportNo="+prePayInfoDTO.getReportNo()+",opt="+prePayInfoDTO.getVerifyOptions()+",id="+prePayInfoDTO.getPrePayInfoId());
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		List<String> msgList = new ArrayList<>();
		prePayService.sendPrePayApprove(prePayInfoDTO,msgList);
		if(msgList.size() < 1 ){
			return ResponseResult.success("");
		}
		return ResponseResult.success(msgList.get(0));
	}

	@GetMapping(value = "/getVerifyUserList")
	public ResponseResult<List<PermissionUserDTO>> getVerifyUserList(@RequestParam("reportNo") String reportNo,
																	 @RequestParam("caseTimes")Integer caseTimes,
																	 @RequestParam("taskId")String taskId){
		return ResponseResult.success(permissionService.getVerifyUserList(reportNo,caseTimes,taskId));
	}

	@GetMapping(value = "/checkAuditPermission")
	public ResponseResult<Boolean> checkAuditPermission(@RequestParam("reportNo") String reportNo,
														@RequestParam("caseTimes")Integer caseTimes,
														@RequestParam("taskId")String taskId){
		return ResponseResult.success(prePayService.checkPermission(reportNo,caseTimes,taskId));
	}

	@GetMapping(value = "/getHistoryPrePayApprove")
	public ResponseResult<List<PrePayInfoDTO>> getHistoryPrePayApprove(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes")Integer caseTimes){
		if(StringUtils.isEmptyStr(reportNo) || caseTimes == null){
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		return ResponseResult.success(prePayService.getHistoryPrePayApprove(reportNo,caseTimes));
	}

	@GetMapping(value = "/getPrePayWaitApprove")
	public ResponseResult<PrePayInfoDTO> getPrePayWaitApprove(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes")Integer caseTimes){
		if(StringUtils.isEmptyStr(reportNo) || caseTimes == null){
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		PrePayInfoDTO prePayInfoDTO = prePayService.getPrePayWaitApprove(reportNo,caseTimes);
		if(StringUtils.isNotEmpty(prePayInfoDTO.getApplyName())){
			prePayInfoDTO.setApplyName(prePayInfoDTO.getApplyBy()+"-"+prePayInfoDTO.getApplyName());
		}else{
			prePayInfoDTO.setApplyName(prePayInfoDTO.getApplyBy());
		}
		return ResponseResult.success(prePayInfoDTO);
	}

	@GetMapping(value = "/hasPrePayHistory")
	public ResponseResult<Boolean> hasPrePayHistory(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes")Integer caseTimes){
		if(StringUtils.isEmptyStr(reportNo) || caseTimes == null){
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		return ResponseResult.success(prePayService.hasPrePayHistory(reportNo,caseTimes));
	}

	@GetMapping(value = "/getPrePayReason")
	public ResponseResult<List<OptionsEntity>> getPrePayReason(){
		List<OptionsEntity> result = new ArrayList<>();
		for (PrePayReasonEnum prePayReason : PrePayReasonEnum.values()) {
			OptionsEntity optionsEntity = new OptionsEntity();
			optionsEntity.setType(prePayReason.getType());
			optionsEntity.setName(prePayReason.getName());
			result.add(optionsEntity);
		}
		return ResponseResult.success(result);
	}

	@PostMapping(value = "/getPrePaymentItem")
	public ResponseResult<List<PaymentItemDTO>> getPrePaymentItem(@RequestBody PaymentItemDTO paymentItemDTO){
		if(paymentItemDTO == null || StringUtils.isEmptyStr(paymentItemDTO.getReportNo())){
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		return ResponseResult.success(prePayService.getPrePaymentItem(paymentItemDTO)
				.stream().filter(i -> !SettleConst.PAYMENT_TYPE_REINSURE_PAY.equals(i.getPaymentType())).collect(Collectors.toList()));

	}

	@GetMapping(value = "/getPrePayApplyCount")
	public ResponseResult<Integer> getPrePayApplyCount(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes")Integer caseTimes){
		if(StringUtils.isEmptyStr(reportNo) || caseTimes == null){
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		return ResponseResult.success(prePayService.getPrePayApplyCount(reportNo,caseTimes));
	}

	@GetMapping(value = "/getPrePaySumList")
	public ResponseResult<PrePayCaseVO> getPrePaySumList(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes")Integer caseTimes){
		if(StringUtils.isEmptyStr(reportNo)){
			throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
		}
		return ResponseResult.success(prePayService.getPrePaySumList(reportNo,caseTimes));

	}

	/**
	 * 预赔按钮飘红显示 记录查询
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	@GetMapping(value = "/getPrePayCount/{reportNo}/{caseTimes}")
	public ResponseResult<Integer> getPrePayCount(@PathVariable("reportNo") String reportNo, @PathVariable("caseTimes")Integer caseTimes){
		return ResponseResult.success(prePayService.getPrePayCount(reportNo,caseTimes));
	}

}
