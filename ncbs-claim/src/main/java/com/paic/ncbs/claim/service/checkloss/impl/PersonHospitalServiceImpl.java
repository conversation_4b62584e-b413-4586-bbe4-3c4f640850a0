package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.dao.mapper.trace.ClmsPersHospitalMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonHospitalMapper;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.vo.trace.ClmsPersHospitalVO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.checkloss.PersonHospitalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;

@Component
@Service("personHospitalService")
public class PersonHospitalServiceImpl implements PersonHospitalService {
	@Autowired
	private PersonHospitalMapper personHospitalDao;
	@Autowired
	private AhcsCommonService ahcsCommonService;
	@Autowired
	private ClmsPersHospitalMapper clmsPersHospitalDao;

	@Autowired
	private MedicalBillInfoMapper medicalBillInfoMapper;


	@Override
	public void addPersonHospitalList(List<PersonHospitalDTO> personHospitalLis) {
		ahcsCommonService.batchHandlerTransactionalWithArgs(PersonHospitalMapper.class, personHospitalLis, ListUtils.GROUP_NUM, "addPersonHospitalList");
	}


	@Override
	public void removePersonHospital(PersonHospitalDTO personHospitalDTO) {
		personHospitalDao.removePersonHospital(personHospitalDTO);
	}


	@Override
	public void modifyPersonHospitalList(List<PersonHospitalDTO> personHospitalList) {
		ahcsCommonService.batchHandlerTransactionalWithArgs(PersonHospitalMapper.class, personHospitalList, ListUtils.GROUP_NUM, "modifyPersonHospitalList");
	}


	@Override
	public List<PersonHospitalDTO> getPersonHospitalList(PersonHospitalDTO personHospitalDTO) {
		return personHospitalDao.getPersonHospitalList(personHospitalDTO);
	}


	@Override
	public List<PersonHospitalDTO> getPersonHospitalByIdAhcsChannelProcess(String idAhcsChannelProcess,String taskId,String status) {
		return personHospitalDao.getPersonHospitalByIdAhcsChannelProcess(idAhcsChannelProcess,taskId,status);
	}


	@Override
	public void removePersonHospitalByIdAhcsChannelProcess(String idAhcsChannelProcess,String taskId) {
		personHospitalDao.removePersonHospitalByIdAhcsChannelProcess(idAhcsChannelProcess,taskId);
	}

	@Override
	public PersonHospitalDTO getPersonHospital(String reportNo, Integer caseTimes,String taskId) {
		PersonHospitalDTO personHospitalDTO = new PersonHospitalDTO();
		//获取是否已存在账单信息，如果存在，则获取历史账单中选择的医院信息
		MedicalBillInfoDTO medicalBillInfoDTO = new MedicalBillInfoDTO();
		medicalBillInfoDTO.setReportNo(reportNo);
		medicalBillInfoDTO.setCaseTimes(caseTimes);
		List<MedicalBillInfoDTO> medicalBillInfoList = medicalBillInfoMapper.getBillInfoByPage(medicalBillInfoDTO);
		if(ListUtils.isNotEmpty(medicalBillInfoList)){
			//获取最近一次提交的账单信息
			MedicalBillInfoDTO dto = medicalBillInfoList.get(medicalBillInfoList.size()-1);
			personHospitalDTO.setHospitalCode(dto.getHospitalCode());
			personHospitalDTO.setHospitalName(dto.getHospitalName());
			personHospitalDTO.setHospitalGrade(dto.getGrade());
			personHospitalDTO.setIsSocialInsurance(dto.getIsSocialInsurance());
			personHospitalDTO.setIsAppointedHospital(dto.getIsAppointedHospital());
			personHospitalDTO.setHospitalPropertyDes(dto.getHospitalPropertyDes());

		}else{
			//如果没有历史账单信息，则获取收单页面显示的医院信息
			personHospitalDTO = personHospitalDao.getPersonHospital(reportNo,caseTimes,taskId);
		}
		//如果没有查询到收单页面显示的医院信息，则返回人伤跟踪录入的医院信息
		if(personHospitalDTO == null) {
			List<ClmsPersHospitalVO> hospitalList = clmsPersHospitalDao.selectClmsPersHospitals(reportNo, caseTimes);
			personHospitalDTO = new PersonHospitalDTO();
			if (hospitalList != null && hospitalList.size() > 0) {
				ClmsPersHospitalVO persHospitalVO = hospitalList.get(0);
				personHospitalDTO.setHospitalCode(persHospitalVO.getHospitalCode());
				personHospitalDTO.setHospitalName(persHospitalVO.getHospitalName());
				personHospitalDTO.setHospitalGrade(persHospitalVO.getHospitalGrade());
//			personHospitalDTO.setIsSocialInsurance();
				personHospitalDTO.setIsAppointedHospital(persHospitalVO.getIsRelativeHospital());
				personHospitalDTO.setHospitalPropertyDes(persHospitalVO.getAgencyAttributes());
			}
		}

		return personHospitalDTO;
	}

	@Override
	public String getHospitalCode(String reportNo, Integer caseTimes) throws GlobalBusinessException {
		return personHospitalDao.getHospitalCode(reportNo, caseTimes);
	}

	@Override
	public String getHospitalName(String reportNo, Integer caseTimes) throws GlobalBusinessException{
		return personHospitalDao.getHospitalName(reportNo, caseTimes);
	}


	public List<PersonHospitalDTO> getPersonHospitals(String idAhcsChannelProcess, String taskId, String status){
		return personHospitalDao.getPersonHospitals(idAhcsChannelProcess, taskId, status);
	}

	@Override
	public PersonHospitalDTO getHospitalInfoByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException{
		return personHospitalDao.getHospitalInfoByReportNo(reportNo, caseTimes);
	}
}
