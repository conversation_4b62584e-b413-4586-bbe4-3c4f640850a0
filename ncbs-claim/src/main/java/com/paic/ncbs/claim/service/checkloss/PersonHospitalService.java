package com.paic.ncbs.claim.service.checkloss;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO;

import java.util.List;

public interface PersonHospitalService {

	public void addPersonHospitalList(List<PersonHospitalDTO> personHospitalList);


	public void removePersonHospital(PersonHospitalDTO personHospitalDTO);


	public void modifyPersonHospitalList(List<PersonHospitalDTO> personHospitalList);


	public List<PersonHospitalDTO> getPersonHospitalList(PersonHospitalDTO personHospitalDTO);


	public List<PersonHospitalDTO> getPersonHospitalByIdAhcsChannelProcess(String idAhcsChannelProcess,String taskId,String status);


	public void removePersonHospitalByIdAhcsChannelProcess(String idAhcsChannelProcess,String taskId);


	public PersonHospitalDTO getPersonHospital(String reportNo, Integer caseTimes, String taskId);


	public String getHospitalCode(String reportNo, Integer caseTimes) throws GlobalBusinessException;


	public String getHospitalName(String reportNo, Integer caseTimes) throws GlobalBusinessException;


	public List<PersonHospitalDTO> getPersonHospitals(String idAhcsChannelProcess, String taskId, String status);


	public PersonHospitalDTO getHospitalInfoByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException;

}
