package com.paic.ncbs.claim.dao.mapper.investigate;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateAuditVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskAuditVO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface InvestigateTaskAuditMapper extends BaseDao<InvestigateTaskAuditDTO> {


    int addTaskAudit(InvestigateTaskAuditDTO taskAuditDTO);


    int modifyTaskAudit( InvestigateTaskAuditDTO taskAuditDTO);


	List<InvestigateTaskAuditVO> getInvestigateTaskAuditByTaskId(
            @Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);


	InvestigateTaskAuditVO getInvestigateTaskAuditForReportByInvestigateId(@Param("idAhcsInvestigate") String idAhcsInvestigate);


	String getTaskAuditIdByTaskId(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);


	void modifyForWorkTransfer(@Param("oldUM") String oldUM, @Param("newUM") String newUM);


    int modifyTaskAuditByTaskId(InvestigateTaskAuditDTO taskAuditDTO);


	List<InvestigateTaskAuditVO> listInvestigateTaskAuditsByTaskId(
			@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);

    InvestigateAuditVO getApproveInfoByIdAhcsInvestigateTask(@Param("idAhcsInvestigateTask")  String idAhcsInvestigateTask);

	String getTaskAuditIdByTaskId2(@Param("idAhcsInvestigateTaskAudit") String idAhcsInvestigateTaskAudit);

    int transferApproval(InvestigateTaskAuditDTO taskAuditDTO);
}