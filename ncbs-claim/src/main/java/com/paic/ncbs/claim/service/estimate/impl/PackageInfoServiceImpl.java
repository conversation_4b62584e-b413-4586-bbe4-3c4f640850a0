package com.paic.ncbs.claim.service.estimate.impl;

import com.paic.ncbs.claim.dao.entity.estimate.PackageInfoEntity;
import com.paic.ncbs.claim.dao.mapper.estimate.PackageInfoEntityMapper;
import com.paic.ncbs.claim.service.estimate.PackageInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PackageInfoServiceImpl implements PackageInfoService {

	@Autowired
	private PackageInfoEntityMapper packageInfoEntityMapper;
	
	@Override
	public PackageInfoEntity getPackageInfoByCode(String packageCode, String productId) {
		return packageInfoEntityMapper.getPackageInfoByCode(packageCode, productId);
	}

}
