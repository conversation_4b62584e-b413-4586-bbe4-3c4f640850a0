package com.paic.ncbs.claim.service.common.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.supplements.SupplementsMaterialMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.service.common.ClaimUpdateDocumentFullDateService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;


@Slf4j
@Service
public class ClaimUpdateDocumentFullDateServiceImpl implements ClaimUpdateDocumentFullDateService {

    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private SupplementsMaterialMapper supplementsMaterialMapper;
    @Override
    @Transactional
    public void updateDocFullDate(String reportNo, Integer caseTimes,String bpmKey) {
        WholeCaseBaseDTO dto =new WholeCaseBaseDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        ////理算环节提交零结案时间，以收单提交时间或补材回销时间两者中较晚者为准
        if(Objects.equals(BpmConstants.OC_MANUAL_SETTLE,bpmKey)){
            Date completeDate = getCompleteDate(reportNo,caseTimes);
            dto.setDocumentFullDate(completeDate);
        }else{
            dto.setDocumentFullDate(new Date());
        }
        LogUtil.info("报案号={}更新资料齐全时间入参={}",reportNo,JsonUtils.toJsonString(dto));
        wholeCaseBaseMapper.updateDocFullDate(dto);
    }
    /**
     * 得到收单完成时间和补材完成时间
     * @param reportNo
     * @param caseTimes
     * @return
     */
    private Date getCompleteDate(String reportNo, int caseTimes){
        //正常赔付  //查询收单完成时间
        TaskInfoDTO taskInfoDTO = taskInfoMapper.getCompleteDate(reportNo,caseTimes);
        //查询材料补材完成时间
        SupplementsMaterialEntity entity = supplementsMaterialMapper.getcompleteDate(reportNo,caseTimes);
        LogUtil.info("报案号={},赔付次数={}, 查询收单完成时间={},查询材料补材完成时间={}",reportNo,caseTimes, JsonUtils.toJsonString(taskInfoDTO),JsonUtils.toJsonString(entity));
        if(Objects.isNull(taskInfoDTO) && Objects.isNull(entity)){
            return null;
        }
        if(Objects.isNull(taskInfoDTO) && !Objects.isNull(entity)){
            return entity.getUpdatedDate();
        }
        if(!Objects.isNull(taskInfoDTO) && Objects.isNull(entity)){
            return taskInfoDTO.getCompleteTime();
        }
        if(taskInfoDTO.getCompleteTime().compareTo(entity.getUpdatedDate())>0){
            return taskInfoDTO.getCompleteTime();
        }else{
            return entity.getUpdatedDate();
        }

    }
}
