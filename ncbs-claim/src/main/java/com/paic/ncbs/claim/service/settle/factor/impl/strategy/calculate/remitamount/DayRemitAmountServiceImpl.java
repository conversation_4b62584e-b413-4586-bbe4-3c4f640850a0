package com.paic.ncbs.claim.service.settle.factor.impl.strategy.calculate.remitamount;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.dao.mapper.settle.ClmsDutyDetailBillSettleMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.*;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日免赔
 */
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service
public class DayRemitAmountServiceImpl extends CalculateAmountService {
    //"免赔额")
    private Map<Date, DayRemitDTO> remitAmountMap;

    @Autowired
    private ClmsDutyDetailBillSettleMapper clmsDutyDetailBillSettleMapper;


    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {

        DutyDetailPayDTO detail = paramsDTO.getDutyDetailPayDTO();
        SettleFactor factor =paramsDTO.getSettleFactor();
        EveryDayBillInfoDTO everyDayBillInfoDTO = paramsDTO.getEveryDayBillInfoDTO();
        //合理费用
        BigDecimal reasonableAmount  = factor.getReasonableAmount();
        DayRemitDTO dayRemitDTO = remitAmountMap.get(everyDayBillInfoDTO.getBillDate());
        BigDecimal remitAmount = dayRemitDTO.getRemitAmount();
        if(reasonableAmount.subtract(remitAmount).compareTo(BigDecimal.ZERO)>=0){
            paramsDTO.getSettleFactor().setExpendDayDeductible(remitAmount);
            paramsDTO.getSettleFactor().setCalculateAmount(remitAmount);
            remitAmount=BigDecimal.ZERO;


        }else {
            paramsDTO.getSettleFactor().setExpendDayDeductible(reasonableAmount);
            paramsDTO.getSettleFactor().setCalculateAmount(reasonableAmount);
            //paramsDTO.getSettleFactor().setLessThanRemitAmountFlag("Y");
            remitAmount=remitAmount.subtract(reasonableAmount);
        }
        dayRemitDTO.setRemitAmount(remitAmount);
        remitAmountMap.put(everyDayBillInfoDTO.getBillDate(), dayRemitDTO);
        everyDayBillInfoDTO.setRemaindDayDeductible(dayRemitDTO.getCaseRemitAmount());
    }

    @Override
    public void initBuild(RemitAmountDTO remitAmountDTO){

        BigDecimal configRemitAmount=remitAmountDTO.getConfigRemitAmount();
        BigDecimal usedRemitAmount;
        Set<Date> dateList = remitAmountDTO.getDateList();
        if(CollectionUtil.isEmpty(dateList)){
            return;
        }

        List<ClmsDutyDetailBillSettleDTO> usedList =clmsDutyDetailBillSettleMapper.getDutyUsedDayRemitAmount(remitAmountDTO);
        usedList = usedList.stream().filter(i -> !i.getReportNo().equals(remitAmountDTO.getReportNo())).collect(Collectors.toList());
        remitAmountMap = new HashMap<>();
        for (Date date : dateList) {
            usedRemitAmount = usedList.stream().filter(i -> date.equals(i.getBillDate()))
                    .map(item -> item.getExpendDayDeductible() != null ? item.getExpendDayDeductible() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subtract = configRemitAmount.subtract(usedRemitAmount);
            BigDecimal remitAmount = BigDecimal.ZERO;
            if(subtract.compareTo(BigDecimal.ZERO) > 0){
                remitAmount = subtract;
            }
            DayRemitDTO dayRemitDTO = new DayRemitDTO();
            dayRemitDTO.setRemitAmount(remitAmount);
            dayRemitDTO.setCaseRemitAmount(remitAmount);
            remitAmountMap.put(date, dayRemitDTO);
        }

    }


}
