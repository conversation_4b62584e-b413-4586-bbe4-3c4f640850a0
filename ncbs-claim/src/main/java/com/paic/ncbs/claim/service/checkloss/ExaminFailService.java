package com.paic.ncbs.claim.service.checkloss;


import com.paic.ncbs.claim.model.dto.checkloss.ExaminFailConditonDTO;
import com.paic.ncbs.claim.model.dto.checkloss.ExaminFailDTO;

public interface ExaminFailService {


	public void saveExaminFail(ExaminFailDTO examinFailDTO);


	public void removeExaminFail(String reportNo, Integer caseTimes, String taskCode,String channelProcessId);


	public ExaminFailDTO getExaminFail(String reportNo, Integer caseTimes, String status, String taskCode,String channelProcessId);




	public Integer countByExaminFailConditon(ExaminFailConditonDTO examinFailConditonDTO);



	public Integer countByExaminFailSubjectConditon(ExaminFailConditonDTO examinFailConditonDTO);


}
