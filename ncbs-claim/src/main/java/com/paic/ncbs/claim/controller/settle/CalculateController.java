package com.paic.ncbs.claim.controller.settle;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.doc.PrintFormalPayInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.vo.settle.SettleSelectVO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.ClaimSettleService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/settle/do/app/calculate")
@Api(tags = {"理算"})
public class CalculateController {

    @Autowired
    private ClaimSettleService claimSettleService;


    @GetMapping(value = "/settle")
    public ResponseResult<Object> calculateSettle(@RequestParam("reportNo") String reportNo,
                                                 @RequestParam("caseTimes") Integer caseTimes) {

        //理算
        ClaimCaseDTO bo = claimSettleService.settle(reportNo,caseTimes);

        //保存数据库

        return ResponseResult.success(bo);
    }

    /**
     * 重新理算
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @GetMapping(value = "/reSettle")
    public ResponseResult<Object> reSettle(@RequestParam("reportNo") String reportNo,
                                                  @RequestParam("caseTimes") Integer caseTimes) {

        //理算
        ClaimCaseDTO bo = claimSettleService.reSettle(reportNo,caseTimes);

        //保存数据库

        return ResponseResult.success(bo);
    }
    @GetMapping(value = "/test_03")
    public void test_03() throws IOException, TemplateException {
        Configuration configuration = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
        String classpath = this.getClass().getResource("/").getPath()+ "templates";
        System.out.println(classpath);
        //设置模板路径
        try {
            configuration.setDirectoryForTemplateLoading(new File(classpath));
        } catch (IOException e) {
            e.printStackTrace();
        }
        //设置字符集
        configuration.setDefaultEncoding("UTF-8");
        Template template = configuration.getTemplate("medicalSettleReason.ftl");
        //???
        PrintFormalPayInfoDTO infoDTO = new PrintFormalPayInfoDTO();
        infoDTO.setInsuredName("TEst");
        Map<String, Object> map = new HashMap<String, Object>() {{
            put("PrintFormalPayInfoDTO", infoDTO);
        }};

        //xml报文
        String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
        System.out.println(content);

    }

    @PostMapping(value = "/settleSelect")
    public ResponseResult<Object> settleSelect(@RequestBody SettleSelectVO settleSelectVO) {

        //理算
        ClaimCaseDTO bo = claimSettleService.settleSelect(settleSelectVO);
        return ResponseResult.success(bo);

    }
}
