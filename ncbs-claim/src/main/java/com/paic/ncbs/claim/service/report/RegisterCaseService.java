package com.paic.ncbs.claim.service.report;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.report.RegisterAmountRelDTO;
import com.paic.ncbs.claim.model.vo.endcase.CaseRegisterApplyVO;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface RegisterCaseService {
    /**
     * 判断是否立案
     * @param reportNo
     * @param caseTimes
     * @return
     * @throws GlobalBusinessException
     */
	boolean isExistRegisterRecord(String reportNo, int caseTimes) throws GlobalBusinessException;

    ResponseResult registerCaseCheck(String reportNo, Integer caseTimes);

    CaseRegisterApplyVO getLastCaseRegisterApplyVO(String reportNo, Integer caseTimes);

    List<CaseRegisterApplyVO> getLastCaseRegisterApplyVOList(String reportNo, Integer caseTimes);

    /**
     * 获取最新立案申请信息
     * @param reportNo
     * @param caseTimes
     * @return
     */
    CaseRegisterApplyDTO getLastCaseRegisterApplyDTO(String reportNo, Integer caseTimes);

    void addCaseRegisterApplyDTO(CaseRegisterApplyDTO caseRegisterApplyDTO);

    void batchAddRegisterAmountRelInfo(@Param("list") List<RegisterAmountRelDTO> registerAmountRelDTOS);

    void registerHistoryCase(WholeCaseBaseDTO wholeCaseBaseDTO) throws GlobalBusinessException;

    void modifyRegisterAuditStatus(CaseRegisterApplyVO caseRegisterApplyVO);

    void addRegisterCaseLog(String reportNo, Integer caseTimes, Date reportDate);

    void registerCaseForReport(String reportNo, Integer caseTimes);

    Map<String, Object> isRegister(String reportNo, Integer caseTimes);

    void saveRegisterAuditInfo(CaseRegisterApplyVO caseRegisterApplyVO);

    void registerForBatch(String reportNo, Integer caseTimes)throws GlobalBusinessException;

    List<String> getNoRegisterData(Integer configDays);
}
