package com.paic.ncbs.claim.service.taskdeal.impl;


import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.dao.mapper.taskdeal.ThirdCarMapper;
import com.paic.ncbs.claim.model.dto.taskdeal.ThirdCarDTO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.taskdeal.ThirdCarService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;

@Service("thirdCarService")
public class ThirdCarServiceImpl implements ThirdCarService {

	@Autowired
	private ThirdCarMapper thirdCarDao;

	@Autowired
	private AhcsCommonService ahcsCommonService;


	public void removeThirdCarList(String idAhcsPersonAccident){
		thirdCarDao.removeThirdCarList(idAhcsPersonAccident);
	}


	public List<ThirdCarDTO> getThirdCarList(String idAhcsPersonAccident){
		List<ThirdCarDTO> thirdCarList = thirdCarDao.getThirdCarList(idAhcsPersonAccident);
		for (ThirdCarDTO thirdCar:thirdCarList) {
			if (ChecklossConst.UN_LICENSED.equals(thirdCar.getThirdCarMark())) {
				thirdCar.setUnlicensed(true);
			}
		}
		return thirdCarList;
	}


	public void addThirdCarList(List<ThirdCarDTO> thirdCarList, String userId , String idAhcsPersonAccident){
		if (ListUtils.isEmptyList(thirdCarList)) {
			return ;
		}
		Iterator<ThirdCarDTO> ite = thirdCarList.iterator();
		if (ite.hasNext()) {
			ThirdCarDTO thirdCar=ite.next();
			if (StringUtils.isEmpty(thirdCar.getThirdCarMark())) {
				ite.remove();
			}
		}
		if (ListUtils.isNotEmpty(thirdCarList)) {
			ahcsCommonService.batchHandlerTransactionalWithArgs(ThirdCarMapper.class, thirdCarList, ListUtils.GROUP_NUM, "addThirdCarList", userId, idAhcsPersonAccident);
		}
	}

}
