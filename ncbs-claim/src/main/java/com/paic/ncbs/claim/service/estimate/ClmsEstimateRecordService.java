package com.paic.ncbs.claim.service.estimate;

import com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord;

import java.math.BigDecimal;
import java.util.List;

/**
 * 未决记录 Service
 *
 * <AUTHOR>
 */
public interface ClmsEstimateRecordService {

	void addEstimateRecord(ClmsEstimateRecord record);

	List<ClmsEstimateRecord> getRecordByReportNoAndType(String reportNo, String caseTimes, String estimateType);

	/**
	 * 获取不等于报案未决的立案金额
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	BigDecimal getEstimateRecordAmount(String reportNo, Integer caseTimes);

	/**
	 * 获取报案估损金额
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	BigDecimal getEstimateLossAmount(String reportNo, Integer caseTimes);
}
