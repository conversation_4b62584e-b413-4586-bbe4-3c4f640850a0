package com.paic.ncbs.claim.service.settle.factor.impl.strategy.limit;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.DetailSettleReasonTemplateDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EverySettleTemplateDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.ExtendedLimitService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 保单日限额 ： 指定产品支持先配置在理赔
 */
@Slf4j
@Service
@Order(2)
@RefreshScope
public class PolicyDayLimitServiceImpl implements ExtendedLimitService {
    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;
    /**
     * 保单日限额配置方案
     */
    @Value("#{${policyLimit.day:null}}")
    private Map<String,BigDecimal> policyDayLimitMap;

    /**
     * 执行保单是否匹配的本扩展
     *
     * @return boolean
     */
    @Override
    public boolean isMatch(PolicyPayDTO policyPayDTO) {
        return policyDayLimitMap != null && policyDayLimitMap.containsKey(policyPayDTO.getProductPackage());
    }

    @Override
    public void cumulativeLimit(PolicyPayDTO policyPayDTO) {
        if(!isMatch(policyPayDTO)){
            return;
        }
        log.info("案件:{},配置了保单日限额开始处理！",policyPayDTO.getReportNo());
        List<DutyBillLimitInfoDTO> policyAllBillLimitList = new ArrayList<>();
        List<String> planList = new ArrayList<>();
        List<PlanPayDTO> plans = policyPayDTO.getPlanPayArr();
        List<String> shareDutyList = new ArrayList<>();
        plans.forEach(planPayDTO -> {
            List<DutyPayDTO> dutyPayDTOS = planPayDTO.getDutyPayArr();
            planList.add(planPayDTO.getPlanCode());
            dutyPayDTOS.forEach(dutyPayDTO -> {
                shareDutyList.add(dutyPayDTO.getDutyCode());
                List<DutyDetailPayDTO> details = dutyPayDTO.getDutyDetailPayArr();
                details.forEach(detailPayDTO -> {
                    if (CollectionUtil.isNotEmpty(detailPayDTO.getDutyBillLimitInfoDTOList())) {
                        policyAllBillLimitList.addAll(detailPayDTO.getDutyBillLimitInfoDTOList());
                    }
                });
            });
        });
        log.info("案件:{},所有限额数据:{}",policyPayDTO.getReportNo(), JSON.toJSONString(policyAllBillLimitList));
        checkData(policyPayDTO,planList,shareDutyList,policyAllBillLimitList);
        log.info("案件:{},保单日限额处理结束！",policyPayDTO.getReportNo());
    }

    private void checkData(PolicyPayDTO policyPayDTO, List<String> sharePlanCodeList,
                           List<String> shareDutyCodeList, List<DutyBillLimitInfoDTO> policyAllBillLimitList) {
        String reportNo = policyPayDTO.getReportNo();
        String policyNo = policyPayDTO.getPolicyNo();
        List<String> billDateList = policyAllBillLimitList.stream().map(item -> DateUtils.dateFormat(item.getBillDate(),
                DateUtils.SIMPLE_DATE_STR)).collect(Collectors.toList());
        // 配置取不到数据时应该不进入到这里才对，不加判断
        BigDecimal baseDayLimit = policyDayLimitMap.get(policyPayDTO.getProductPackage());
        Map<Date, BigDecimal> hisDayLimitMap = getHisDayLimitMap(reportNo,policyNo, sharePlanCodeList, shareDutyCodeList, billDateList);
        Map<Date, BigDecimal> currentPayMap = new HashMap<>();

        for (PlanPayDTO planPayDTO : policyPayDTO.getPlanPayArr()) {
            for (DutyPayDTO dutyPayDTO : planPayDTO.getDutyPayArr()) {
                for (DutyDetailPayDTO dutyDetailPayDTO : dutyPayDTO.getDutyDetailPayArr()) {
                    BigDecimal sumAutoAmount = BigDecimal.ZERO;
                    if(CollectionUtils.isEmpty(dutyDetailPayDTO.getDutyBillLimitInfoDTOList())){
                        log.info("案件:{},责任明细{}-{}，没有DutyBIll信息不更新！",policyPayDTO.getReportNo(),dutyPayDTO.getDutyCode()
                                , dutyDetailPayDTO.getDutyDetailCode());
                        continue;
                    }
                    DetailSettleReasonTemplateDTO detailSettleReasonTemplateDTO = dutyDetailPayDTO.getDetailSettleReasonTemplateDTO();
                    List<BIllSettleResultDTO> billSettleResultDTOList = dutyDetailPayDTO.getBillSettleResultDTOList();
                    List<EverySettleTemplateDTO> everySetttleList = detailSettleReasonTemplateDTO.getEverySetttleList();
                    for (DutyBillLimitInfoDTO dutyBillLimitInfoDTO : dutyDetailPayDTO.getDutyBillLimitInfoDTOList()) {
                        Date billDate = dutyBillLimitInfoDTO.getBillDate();
                        BigDecimal maxPayAmount;
                        BigDecimal currentPay = Optional.ofNullable(currentPayMap.get(billDate)).orElse(BigDecimal.ZERO);
                        if (hisDayLimitMap.containsKey(billDate) || currentPayMap.containsKey(billDate)) {
                            BigDecimal hisPay = Optional.ofNullable(hisDayLimitMap.get(billDate)).orElse(BigDecimal.ZERO);
                            maxPayAmount = baseDayLimit.subtract(hisPay).subtract(currentPay);
                        } else {
                            maxPayAmount = baseDayLimit;
                        }

                        if (maxPayAmount.compareTo(BigDecimal.ZERO) <= 0) {
                            // 定制化开发所以取0，如有多个明细的产品 不能这么处理
                            dutyBillLimitInfoDTO.setSettleClaimAmount(BigDecimal.ZERO);
                            dutyDetailPayDTO.setAutoSettleAmount(BigDecimal.ZERO);
                            setEverySetttleListValue(everySetttleList, dutyBillLimitInfoDTO.getBillDate(), "0", BigDecimal.ZERO);
                            dutyBillLimitInfoDTO.setMoreThanLimit("0");
                        } else if (maxPayAmount.compareTo(dutyBillLimitInfoDTO.getSettleClaimAmount()) < 0) {
                            dutyBillLimitInfoDTO.setSettleClaimAmount(maxPayAmount);
                            dutyDetailPayDTO.setAutoSettleAmount(maxPayAmount);
                            setEverySetttleListValue(everySetttleList, dutyBillLimitInfoDTO.getBillDate(), "1", maxPayAmount);
                            sumAutoAmount = sumAutoAmount.add(maxPayAmount);
                            dutyBillLimitInfoDTO.setMoreThanLimit("1");
                            currentPayMap.put(billDate, currentPay.add(maxPayAmount));
                        } else {
                            sumAutoAmount = sumAutoAmount.add(dutyBillLimitInfoDTO.getSettleClaimAmount());
                            currentPayMap.put(billDate, currentPay.add(dutyBillLimitInfoDTO.getSettleClaimAmount()));
                        }

                        updateBillSettleResult(dutyBillLimitInfoDTO, billSettleResultDTOList);
                    }
                    // 每个责任明细的总金额
                    dutyDetailPayDTO.setAutoSettleAmount(sumAutoAmount);
                    detailSettleReasonTemplateDTO.setAutoSettleAmount(BigDecimalUtils.toString(sumAutoAmount));
                }
            }
        }

        log.info("案件:{}，日限额计算={}", reportNo, JsonUtils.toJsonString(policyAllBillLimitList));
    }

    private Map<Date, BigDecimal> getHisDayLimitMap(String reportNo,String policyNo, List<String> planCodeList,
                                                    List<String> dutyCodeList, List<String> billDateList) {
        DutyBillLimitInfoDTO paramDTO = new DutyBillLimitInfoDTO();
        paramDTO.setReportNo(reportNo);
        paramDTO.setPolicyNo(policyNo);
        paramDTO.setPlanCodeList(planCodeList);
        paramDTO.setDutyCodeList(dutyCodeList);
        paramDTO.setBillDateList(billDateList);

        List<DutyBillLimitInfoDTO> hisDutyBillLimitList = dutyBillLimitInfoMapper.getHisDutyBillLimitList(paramDTO);

        // 记录每个发票日期已赔付的金额
        Map<Date, BigDecimal> hisDayPayMap = new HashMap<>();
        if (CollectionUtils.isEmpty(hisDutyBillLimitList)) {
            log.info("保单:{},没有对应日期的历史赔付数据！", paramDTO.getPolicyNo());
            return hisDayPayMap;
        }

        // 按日期分组
        Map<Date, List<DutyBillLimitInfoDTO>> dutyMap =
                hisDutyBillLimitList.stream().collect(Collectors.groupingBy(DutyBillLimitInfoDTO::getBillDate));
        for (Map.Entry<Date, List<DutyBillLimitInfoDTO>> entry : dutyMap.entrySet()) {
            Date key = entry.getKey();
            List<DutyBillLimitInfoDTO> dtos = entry.getValue();
            BigDecimal settleAmount = BigDecimal.ZERO;
            for (DutyBillLimitInfoDTO dto : dtos) {
                settleAmount = settleAmount.add(dto.getSettleClaimAmount());
            }
            hisDayPayMap.put(key, settleAmount);
        }

        return hisDayPayMap;
    }

    /**
     * 更新理算依据对象
     * @param everySetttleList
     * @param billDate
     * @param flag
     * @param amount
     */
    private void setEverySetttleListValue(List<EverySettleTemplateDTO> everySetttleList, Date billDate, String flag, BigDecimal amount) {
        if(CollectionUtil.isEmpty(everySetttleList)){
            return;
        }
        String strbillDate= DateUtils.dateFormat(billDate,DateUtils.SIMPLE_DATE_STR);
        for (EverySettleTemplateDTO e :everySetttleList) {
            if(Objects.equals(e.getStrBillDate(),strbillDate)){
                e.setExceedDayLimit(flag);
                e.setExceedDayLimitAmount(BigDecimalUtils.toString(amount));
            }
        }
    }

    /**
     * 更新责任明细发票数据
     *
     * @param dto
     * @param billSettleResultList
     */
    private void updateBillSettleResult(DutyBillLimitInfoDTO dto, List<BIllSettleResultDTO> billSettleResultList) {
        if (Objects.equals("0", dto.getMoreThanLimit())) {
            if (CollectionUtil.isNotEmpty(billSettleResultList)) {
                List<BIllSettleResultDTO> resultDTOS =
                        billSettleResultList.stream().filter(billSettleResultDTO -> Objects.equals(billSettleResultDTO.getPolicyNo(), dto.getPolicyNo()) && Objects.equals(billSettleResultDTO.getPlanCode(), dto.getPlanCode()) && Objects.equals(billSettleResultDTO.getDutyCode(), dto.getDutyCode()) && Objects.equals(billSettleResultDTO.getBillDate(), dto.getBillDate())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(resultDTOS)) {
                    resultDTOS.forEach(billSettleResultDTO -> {
                        String strBillDate = DateUtils.dateFormat(dto.getBillDate(), DateUtils.SIMPLE_DATE_STR);
                        String remark = StringUtils.stripToEmpty(StringUtils.appendIfMissing(billSettleResultDTO.getRemark(), "\n"));
                        billSettleResultDTO.setAutoSettleAmount(BigDecimal.ZERO);
                        billSettleResultDTO.setDayLimit("Y");
                        billSettleResultDTO.setRemark(remark + "发票日:" + strBillDate + "累计已超保单日限额可赔付金额为0");
                        log.info("案件:{},发票日:{},已超日限额 该发票日 责任明细发票理算数据为0", dto.getReportNo(), strBillDate);
                    });
                }
            }
        }
        if (Objects.equals("1", dto.getMoreThanLimit())) {
            List<BIllSettleResultDTO> resultDTOS =
                    billSettleResultList.stream().filter(billSettleResultDTO -> Objects.equals(billSettleResultDTO.getPolicyNo(), dto.getPolicyNo()) && Objects.equals(billSettleResultDTO.getPlanCode(), dto.getPlanCode()) && Objects.equals(billSettleResultDTO.getDutyCode(), dto.getDutyCode()) && Objects.equals(billSettleResultDTO.getBillDate(), dto.getBillDate())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(resultDTOS)) {
                BigDecimal amount = dto.getSettleClaimAmount();
                for (BIllSettleResultDTO billSettleDto : resultDTOS) {
                    String remark = StringUtils.stripToEmpty(StringUtils.appendIfMissing(billSettleDto.getRemark(), "\n"));
                    if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                        billSettleDto.setAutoSettleAmount(BigDecimal.ZERO);
                        billSettleDto.setDayLimit("Y");
                        billSettleDto.setRemark(remark + "剩余保单日限额分配" + billSettleDto.getBillNo() + "金额为0,赔付金额为0");
                        log.info("案件:{},发票日:{},剩余保单日限额分配到这张发票金额为0", dto.getReportNo(), dto.getBillDate());
                    } else if (billSettleDto.getAutoSettleAmount().compareTo(amount) <= 0) {
                        amount = amount.subtract(billSettleDto.getAutoSettleAmount());
                    } else {
                        billSettleDto.setAutoSettleAmount(amount);
                        billSettleDto.setDayLimit("Y");
                        billSettleDto.setRemark(remark + "剩余保单日限额分配" + billSettleDto.getBillNo() + "金额为" + amount +
                                "可赔付金额为" + amount);
                        log.info("案件:{},发票日:{},剩余保单日限额分配到这张发票金额为:{}", dto.getReportNo(), dto.getBillDate(), amount);
                    }
                }
            }
        }
    }
}


