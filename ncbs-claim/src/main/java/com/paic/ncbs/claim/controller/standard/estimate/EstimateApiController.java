package com.paic.ncbs.claim.controller.standard.estimate;


import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.model.dto.api.StandardRequestDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePolicyFormDTO;
import com.paic.ncbs.claim.model.vo.report.OnlineReportVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Api(tags = "未决修正")
@RestController
@Validated
@RequestMapping("/public/estimate")
public class EstimateApiController {

    @Autowired
    private EstimateChangeService estimateChangeService;

    @Autowired
    private BpmService bpmService;

    @ApiOperation("提交未决责任修正")
    @PostMapping("/estimateChange")
    public ResponseResult<Map<String,String>> estimateChange(@RequestBody StandardRequestDTO reportDto){
        String jsonStr = JSONObject.toJSONString(reportDto.getRequestData());
        EstimateChangePolicyFormDTO estimateChangePolicyForm = JSONObject.parseObject(jsonStr, EstimateChangePolicyFormDTO.class);
        estimateChangePolicyForm.setCompanyId(reportDto.getCompanyId());
        String problemNo = estimateChangePolicyForm.getReportNo();
        if (Objects.nonNull(estimateChangePolicyForm) && ListUtils.isNotEmpty(estimateChangePolicyForm.getEstimatePolicyList())
                && ListUtils.isNotEmpty(estimateChangePolicyForm.getEstimatePolicyList().get(0).getEstimatePlanList())) {
            //校验当前流程是否有冲突
            bpmService.processCheck(estimateChangePolicyForm.getReportNo(), BpmConstants.OC_ESTIMATE_CHANGE_REVIEW,BpmConstants.OPERATION_INITIATE);
            problemNo = estimateChangeService.estimateChange(estimateChangePolicyForm);
        }
        Map<String, String> problemNoMap = new HashMap<>();
        problemNoMap.put("problemNo", problemNo);
        return ResponseResult.success(problemNoMap);
    }
}
