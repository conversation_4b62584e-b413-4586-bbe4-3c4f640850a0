package com.paic.ncbs.claim.controller.zerocancel;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.vo.casezero.ZeroCancelAuditInfoVO;
import com.paic.ncbs.claim.model.vo.estimate.CaseInfoVO;
import com.paic.ncbs.claim.service.casezero.CaseInfoService;
import com.paic.ncbs.claim.service.casezero.CaseZeroCancelService;
import com.paic.ncbs.claim.service.common.ClaimSendTpaMqInfoService;
import com.paic.ncbs.claim.service.duty.DutySurveyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;


@Api(tags = "零结注销")
@RestController
@RequestMapping("/who/app/caseZeroCancelAction")
public class CaseZeroCancelController extends BaseController {

    @Autowired
    private CaseZeroCancelService caseZeroCancelService;

    @Autowired
    private CaseInfoService caseInfoService;

    @Autowired
    private ClaimSendTpaMqInfoService claimSendTpaMqInfoService;

    @Autowired
    private DutySurveyService dutySurveyService;


    @GetMapping(value = "/getZeroCancelInfo/{reportNo}/{caseTimes}/{status}")
    @ApiOperation(value = "获取零注信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class),
            @ApiImplicitParam(name = "status", value = "状态",dataTypeClass=String.class)
    })
    public ResponseResult<CaseZeroCancelDTO> getZeroCancelInfo(@PathVariable("reportNo") String reportNo,
                                                               @PathVariable("caseTimes") Integer caseTimes,
                                                               @PathVariable("status") String status) {
        LogUtil.audit("#获取零注信息#  reportNo:{},caseTimes:{},status:{}", reportNo, caseTimes, status);
        CaseZeroCancelDTO caseZeroCancelDTO = caseZeroCancelService.getZeroCancelApplyInfo(reportNo, caseTimes, status);

        return ResponseResult.success(caseZeroCancelDTO);
    }

    @ResponseBody
    @ApiOperation(value = "零结注销申请暂存/提交")
    @PostMapping(value = "/saveCaseZeroCancelApply")
    public ResponseResult<Object> saveCaseZeroCancelApply(@RequestBody CaseZeroCancelDTO caseZeroCancelDTO) {
        LogUtil.audit("#发送零结注销申请#  reportNo:{},caseTimes:{}", caseZeroCancelDTO.getReportNo(), caseZeroCancelDTO.getCaseTimes());
        // 保存零注申请信息
        caseZeroCancelService.saveCaseZeroCancelApply(caseZeroCancelDTO);
        //通知TPA:零结申请 需要前端传入操作节点
        if(Objects.equals(BpmConstants.OC_REPORT_TRACK,caseZeroCancelDTO.getTaskDefinitionKey())){
            claimSendTpaMqInfoService.sendTpaMq(caseZeroCancelDTO.getReportNo(),caseZeroCancelDTO.getCaseTimes(), CaseProcessStatus.CANCEL_WAIT_APPROVING.getCode());
        }
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping(value = "/saveCaseZeroCancelAudit")
    @ApiOperation(value = "零结注销审批暂存/提交")
    public ResponseResult<Object> saveCaseZeroCancelAudit(@RequestBody CaseZeroCancelDTO caseZeroCancelDTO) {
        LogUtil.audit("零结注销审批发送reportNo:{},caseTimes:{}", caseZeroCancelDTO.getReportNo(), caseZeroCancelDTO.getCaseTimes());
        try {
            caseZeroCancelService.saveCaseZeroCancelAudit(caseZeroCancelDTO);
            return ResponseResult.success();
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ResponseBody
    @GetMapping(value = "/getAuditInfoList/{reportNo}/{caseTimes}")
    @ApiOperation(value = "查询零注历史审批信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<List<ZeroCancelAuditInfoVO>> getAuditInfoList(@PathVariable("reportNo") String reportNo,
                                                                        @PathVariable("caseTimes") Integer caseTimes) {
        LogUtil.audit("#获取零注的历史审批信息#  reportNo:{},caseTimes:{}", reportNo, caseTimes);
        return ResponseResult.success(caseZeroCancelService.getAuditInfoList(reportNo, caseTimes));
    }


    @ResponseBody
    @GetMapping(value = "/getCaseInfoList/{reportNo}/{caseTimes}")
    @ApiOperation(value = "零注页面-查询案件信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<List<CaseInfoVO>> getCaseInfoList(@PathVariable("reportNo") String reportNo,
                                                            @PathVariable("caseTimes") Integer caseTimes) {
        LogUtil.audit("#获取案件信息#  reportNo:{},caseTimes:{}", reportNo, caseTimes);
        return ResponseResult.success(caseInfoService.getCaseInfoList(reportNo, caseTimes));
    }

    @ResponseBody
    @GetMapping(value = "/isReopen/{reportNo}/{caseTimes}")
    @ApiOperation(value = "判断案件是否重开")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<Object> isReopen(@PathVariable("reportNo") String reportNo,
                                                            @PathVariable("caseTimes") Integer caseTimes) {
        LogUtil.audit("#判断案件是否重开#  reportNo:{},caseTimes:{}", reportNo, caseTimes);
        if (!dutySurveyService.canRejectCase(reportNo, caseTimes)) {
            throw new GlobalBusinessException("重开案件不允许发起零注申请！");
        }else {
            return ResponseResult.success();
        }
    }

}
