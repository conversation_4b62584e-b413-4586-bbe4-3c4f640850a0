package com.paic.ncbs.claim.service.settle.factor.impl.strategy.calculate.reasonableamount;

import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EveryDayBillInfoDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * 默认合理费用计算实现
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service("defaultReasonAmountServiceImpl")
public class DefaultReasonAmountServiceImpl extends CalculateAmountService {

    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {
        if(Objects.isNull(paramsDTO.getEveryDayBillInfoDTO())){
            return;
        }
        EveryDayBillInfoDTO dto=paramsDTO.getEveryDayBillInfoDTO();
        BigDecimal reasonableAmount = nvl(dto.getBillAmount(), 0).subtract(nvl(dto.getPartialDeductible(), 0)).subtract(nvl(dto.getDeductibleAmount(), 0)).subtract(nvl(dto.getPrepaidAmount(), 0)).subtract(nvl(dto.getImmoderateAmount(), 0));
        if(reasonableAmount.compareTo(BigDecimal.ZERO) < 0){
            reasonableAmount = BigDecimal.ZERO;
        }
        paramsDTO.getSettleFactor().setReasonableAmount(reasonableAmount);
        paramsDTO.getSettleFactor().setCalculateAmount(reasonableAmount);
    }
}
