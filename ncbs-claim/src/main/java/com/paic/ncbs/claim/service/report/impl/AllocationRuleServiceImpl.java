package com.paic.ncbs.claim.service.report.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.estimate.AllocationDutyEstimateDTO;
import com.paic.ncbs.claim.model.dto.settle.HistoryPayInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.service.common.ResidueAmountService;
import com.paic.ncbs.claim.service.other.ConfigService;
import com.paic.ncbs.claim.service.report.AllocationRuleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RefreshScope
@Service
public class AllocationRuleServiceImpl implements AllocationRuleService {
    @Autowired
    private PolicyPayMapper policyPayMapper;

    @Autowired
    private ConfigService configService;
    @Autowired
    private ResidueAmountService residueAmountService;
    @Value("${batch.estimateMonth:12}")
    private Integer configMonth;

    @Autowired
    private DutyPayMapper dutyPayMapper;
    @Override
    public AllocationDutyEstimateDTO allocationRule(String reportNo, Integer caseTimes) {
        //产品编码查询
        String productCode =policyPayMapper.getPolicyProductCode(reportNo,caseTimes);
        AllocationDutyEstimateDTO dto = new AllocationDutyEstimateDTO();
        dto.setProductCode(productCode);

        //查询产品历史12个月内赔付金额列表
        long start = System.currentTimeMillis();
        String endDate = DateUtils.getFirstDayStrOfMonth();
        String  startDate= "";
        log.info("追溯查询12个月内的赔付金额信息={},查询入参={},开始日期={}，结束日期={}",reportNo, JsonUtils.toJsonString(dto),JsonUtils.toJsonString(startDate),JsonUtils.toJsonString(endDate));
        //12个月数据分段取
//        List<PolicyPayDTO> resultList = new ArrayList<>();
        BigDecimal sumAmount = BigDecimal.ZERO;
        for(int i=0;i<12;i++) {
            startDate= DateUtils.getFirstDayStrOfAppointMonth(-i-1);
            List<PolicyPayDTO> policyPayList = policyPayMapper.getBackSumPayByProductCode(startDate,endDate,productCode);
            if(!CollectionUtil.isEmpty(policyPayList)){
                Map<String,List<PolicyPayDTO>> listMap= policyPayList.stream().distinct().sorted(Comparator.comparing(PolicyPayDTO::getCaseTimes).reversed()).collect(Collectors.groupingBy(PolicyPayDTO::getReportNo, LinkedHashMap::new,Collectors.toList()));
                for (Map.Entry<String,List<PolicyPayDTO>> entry : listMap.entrySet()) {
                    List<PolicyPayDTO> reportList=entry.getValue();
                    if(!CollectionUtil.isEmpty(reportList)) {
                        sumAmount = sumAmount.add(null == reportList.get(0).getPolicyPay() ? BigDecimal.ZERO : reportList.get(0).getPolicyPay());
                    }
                }
            }
            endDate = startDate;
        }
        long end = System.currentTimeMillis();
        log.info("追溯查询12个月内的赔付金额信息={},赔付金额={},耗时={}",reportNo, sumAmount,end-start);
        if(sumAmount.compareTo(BigDecimal.ZERO) == 0){
            dto.setEstimateFlag("N");
            dto.setMessage("产品历史12个月内无赔付金额");
            return dto;
        }

         Integer allCount= policyPayMapper.getAllContByProductCode(startDate, DateUtils.getFirstDayStrOfMonth(),productCode);
        if(allCount==0){
            dto.setEstimateFlag("N");
            dto.setMessage(productCode+"案件条数为0");
            log.info("案件条数为0 报案号={}，产品编码={}",reportNo,productCode);
            return dto;
        }
        BigDecimal count = new BigDecimal(allCount);
        log.info("开始调用低代码平台配置信息报案号={},产品编码={} 案件总数={}",reportNo,productCode,JsonUtils.toJsonString(count));
        DutyPayDTO dutyConfigDto =configService.getEstimateConfig(productCode);
       // DutyPayDTO dutyConfigDto=testValue();//测试赋值用
        String planCode=dutyConfigDto.getPlanCode();
        String dutyCode=dutyConfigDto.getDutyCode();
        if(StringUtils.isEmptyStr(dutyConfigDto.getDutyCode())||StringUtils.isEmptyStr(dutyConfigDto.getPlanCode())){
            dto.setEstimateFlag("N");
            dto.setMessage(productCode+"产品未配置险种责任，无法确定估损到哪个责任，需要业务配置");
            log.info("业务未配置分配责任请配置后才能分配未决金额 报案号={}，产品编码={}",reportNo,productCode);
            return dto;
        }

        BigDecimal avgAmount=sumAmount.divide(count,BigDecimal.ROUND_CEILING);
        //责任剩余理赔
        BigDecimal reAmount = getResdueAmount(reportNo,planCode,dutyCode);
        if(avgAmount.compareTo(reAmount)>0){
            log.info("报案号={}，责任编码={}，险种={},平均值={}，平局值大于剩余理赔额={}",reportNo,dutyConfigDto.getDutyCode(),planCode,JsonUtils.toJsonString(avgAmount),JsonUtils.toJsonString(reAmount));
            dto.setEstimateMount(reAmount);
            if(reAmount.compareTo(BigDecimal.ZERO)==0){
                dto.setEstimateFlag("N");
                dto.setMessage(productCode+"责任剩余理赔额为0不估损");
                return dto;
            }
        }else{
            log.info("报案号={}，责任编码={}，险种={},平均值={}，平局值小于剩余理赔额={}",reportNo,dutyConfigDto.getDutyCode(),planCode,JsonUtils.toJsonString(avgAmount),JsonUtils.toJsonString(reAmount));
            dto.setEstimateMount(avgAmount);
        }

        dto.setDutyCode(dutyConfigDto.getDutyCode());
        dto.setPlanCode(dutyConfigDto.getPlanCode());
        log.info("追溯查询12个月内的赔付金额={},总金额={}，案件数={}，均值={}",reportNo,JsonUtils.toJsonString(sumAmount),JsonUtils.toJsonString(count), JsonUtils.toJsonString(avgAmount));
        return dto;
    }



    /**
     * 剩余理赔额
     * @return
     */
    private BigDecimal getResdueAmount(String reportNo,String planCode,String dutyCode) {
        //查询是否共享保额信息
        DutyPayDTO duty = dutyPayMapper.getdutyShareInfo(reportNo,planCode,dutyCode);
        boolean isShareAmount=false;
        boolean isDutyShareAmount=false;
        if(Objects.isNull(duty)){
            log.info("查询责任信息失败！报案号={},险种编码={}，责任编码={}",reportNo,planCode,dutyCode);
            throw new RuntimeException( "查询责任信息失败！");
        }
        if(Objects.equals("1",duty.getIsShareAmount())){
            isShareAmount=true;
        }
        if (StringUtils.isNotEmpty(duty.getShareDutyGroup())){
            isDutyShareAmount = true;
        }
        BigDecimal dutyHistoryPay = residueAmountService.getDutyHistoryPay(null, reportNo, duty.getPolicyNo(), planCode,
                dutyCode, duty.getIsDutyShareAmount(), duty.getShareDutyGroup(), isShareAmount);
        HistoryPayInfoDTO historyPayInfo = new HistoryPayInfoDTO();
        historyPayInfo.setPolicyNo(duty.getPolicyNo());
        historyPayInfo.setPlanCode(planCode);
        historyPayInfo.setDutyCode(dutyCode);
        historyPayInfo.setDutyBaseAmount(duty.getBaseAmountPay());
        historyPayInfo.setDutyHistoryPay(dutyHistoryPay);

        BigDecimal dutyMaxPay = residueAmountService.getDutyMaxPay(historyPayInfo);
        return  dutyMaxPay;
    }

    /**
     * 当前时间所在月的上一个月最后一天
     * @return
     */
    private String getUpMontLastDay() {
        SimpleDateFormat dateFormat=new SimpleDateFormat(DateUtils.SIMPLE_DATE_STR);
        Calendar calendar=Calendar.getInstance();
        Date date=new Date();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-1);
        calendar.set(Calendar.DAY_OF_MONTH,calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        date=calendar.getTime();
        String day=dateFormat.format(date);
        return day;
    }

    /**
     * 起始日期-12个月
     * @param edate
     * @return
     */
    private String getStartDate(String edate) {
        Date endDate = DateUtils.formatStringToDate(edate,DateUtils.SIMPLE_DATE_STR);
        SimpleDateFormat dateFormat=new SimpleDateFormat(DateUtils.SIMPLE_DATE_STR);
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(endDate);
        calendar.add(Calendar.MONTH,-configMonth);
        Date date=calendar.getTime();
        return dateFormat.format(date);
    }
    private DutyPayDTO testValue() {
        DutyPayDTO dutyConfigDto=new DutyPayDTO();
        dutyConfigDto.setPlanCode("YLZ0004");
        dutyConfigDto.setDutyCode("C00001");
        return dutyConfigDto;
    }
}
