package com.paic.ncbs.claim.controller.notice;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.notice.NoticesVO;
import com.paic.ncbs.claim.service.notice.NoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "消息提醒")
@RestController
@RequestMapping("/notices/app/noticeAction")
@Slf4j
public class NoticeController {
    @Autowired
    NoticeService noticeService;
    @ApiOperation("提醒列表查询")
    @PostMapping(value = "/getNoticeList")
    public  ResponseResult<Map<String, List<NoticesVO>>> getNoticeListByPage(@RequestBody NoticesVO vo){
         Map<String, List<NoticesVO>> noticesVOList = (Map<String, List<NoticesVO>>) noticeService.getNotDealNoticeList(vo);

        return ResponseResult.success(noticesVOList);
    }
    @ApiOperation(value = "人员消息提醒已读状态修改")
    @PostMapping(value = "/updatePersonStatus")
    public ResponseResult<Object> updatePersonStatus(@RequestParam("noticePersonId")  String noticePersonId,@RequestParam("readStatus") String readStatus){
        noticeService.updatePersonStatus(noticePersonId, readStatus);
        return ResponseResult.success();
    }
    @ApiOperation(value = "批量删除消息提醒")
    @PostMapping(value = "/batchDeleteNotices")
    public ResponseResult<Object> batchDeleteNotices(@RequestBody List<String> noticePersonId){
        noticeService.batchDeleteNotices(noticePersonId);
        return ResponseResult.success();
    }
    @ApiOperation(value = "获取默认提醒列表（最新10条未读）")
    @GetMapping(value = "/getDefNoticeList")
    public ResponseResult<Object> getDefNoticeList(){
        List<NoticesVO> notices = noticeService.getDefNoticeList();
        return ResponseResult.success(notices);
    }
}