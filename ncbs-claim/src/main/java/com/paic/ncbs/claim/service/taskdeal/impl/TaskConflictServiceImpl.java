package com.paic.ncbs.claim.service.taskdeal.impl;

import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskConflictMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.paic.ncbs.claim.model.dto.taskdeal.ClmsTaskConflictDTO;
import com.paic.ncbs.claim.service.taskdeal.TaskConflictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 流程任务控制规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Service
public class TaskConflictServiceImpl extends ServiceImpl<TaskConflictMapper, ClmsTaskConflictDTO> implements TaskConflictService {

    @Autowired
    private TaskConflictMapper taskConflictMapper;

    @Override
    public List<ClmsTaskConflictDTO> findConflictByBpmKeyAndOpr(String bpmKey, String operation){

        List<ClmsTaskConflictDTO> taskConflicts = taskConflictMapper.findByBpmKeyAndOpr(bpmKey, operation);

        return taskConflicts;
    }
}
