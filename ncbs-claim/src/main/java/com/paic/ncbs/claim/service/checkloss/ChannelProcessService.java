package com.paic.ncbs.claim.service.checkloss;



import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseInfoParameterDTO;
import com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO;

import java.util.List;


public interface ChannelProcessService {

	String getChannelProcessId(CaseInfoParameterDTO caseInfoParameter);

	String getChannelProcessId(String reportNo, int caseTimes);

	List<ChannelProcessDTO> getChanelProcessIdList(String reportNo, int caseTimes);

	void addChannelProcessId(CaseInfoParameterDTO caseInfoParameter)  throws GlobalBusinessException;


}
