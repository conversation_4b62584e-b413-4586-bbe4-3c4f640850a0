package com.paic.ncbs.claim.controller.duty;

import com.paic.ncbs.claim.model.dto.settle.SubProfessionDefineDTO;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.service.checkloss.ProfessionDefineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "职业类别")
@Controller
@RequestMapping("/duty/app/professionDefineAction")
public class ProfessionDefineController extends BaseController {

	@Resource(name="professionDefineService")
	private ProfessionDefineService professionDefineService;

	@ApiOperation("获取职业类别信息列表")
	@ResponseBody
	@RequestMapping(value="/getProfessionDefineList", method=RequestMethod.GET)
	public ResponseResult<List<SubProfessionDefineDTO>> getProfessionDefineList()throws GlobalBusinessException {
		LogUtil.audit("获取职业类别信息列表");
		List<SubProfessionDefineDTO> list =  professionDefineService.getProfessionDefines();
		return ResponseResult.success(list);
	}

	@ResponseBody
	@ApiOperation("获取所有职业名称")
	@GetMapping(value="/getSubProfessionDefinesNameAll")
	public ResponseResult<List<String>> getSubProfessionDefinesNameAll()throws GlobalBusinessException{
		LogUtil.audit("获取所有职业名称");
		return ResponseResult.success(professionDefineService.getSubProfessionDefinesNameAll());
	}

	@ApiOperation("判断页面是否需要显示费率比输入框")
	@ResponseBody
	@RequestMapping(value="/getIsShowAmountRate/{reportNo}/{professionGradeCode}", method=RequestMethod.GET)
	@ApiImplicitParams({
			@ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String" ,dataTypeClass=String.class),
			@ApiImplicitParam(name = "professionGradeCode", value = " 职业等级代码",dataType = "String",dataTypeClass=String.class)
	})	public ResponseResult getIsShowAmountRate(@PathVariable("reportNo") String reportNo,
									   @PathVariable("professionGradeCode")  String professionGradeCode)throws GlobalBusinessException{
		LogUtil.audit("#判断页面是否需要显示费率比输入框,reportNo=%s,professionGradeCode=%s",reportNo,professionGradeCode);

		boolean isShowAmountRate= professionDefineService.getIsShowAmountRate(reportNo, professionGradeCode);
		LogUtil.audit("#判断页面是否需要显示费率比输入框,isShowAmountRate=%s",isShowAmountRate);
		return ResponseResult.success(isShowAmountRate);
	}
}
