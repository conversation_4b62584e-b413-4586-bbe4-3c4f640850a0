package com.paic.ncbs.claim.service.settle.factor.impl.common;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EveryDayBillInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyBillLimitDto;
import com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo;
import com.paic.ncbs.claim.service.settle.factor.interfaces.common.AfterBillInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 年度赔付天数 超年度赔付天数的发票不参与理算
 */
@Order(2)
@Service
public class YearlyPayDaysServiceImpl implements AfterBillInfoService {
    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;
    @Autowired
    private DutyDetailPayMapper dutyDetailPayMapper;
    @Override
    public List<EveryDayBillInfoDTO> dealData(List<EveryDayBillInfoDTO> everyBillLists, DutyDetailPayDTO detailPayDTO) {
        if(Objects.isNull(detailPayDTO.getConfiYearlyPayDays())||detailPayDTO.getConfiYearlyPayDays()==0){
            return everyBillLists;
        }
        if(CollectionUtils.isEmpty(everyBillLists)){
            return everyBillLists;
        }
        //查询保单责任年段已经结案赔付的天数
        List<DutyBillLimitDto> dutyBillLimitDtoLists= getPolicyDutyYearlyPayDays(detailPayDTO);
        detailPayDTO.setYearlyPayDays(dutyBillLimitDtoLists.size());//年度已赔付的天数
        //按发票日期分组排序
//        Map<Date,List<EveryDayBillInfoDTO>>  listMap=everyBillLists.stream().sorted(Comparator.comparing(EveryDayBillInfoDTO::getBillDate)).collect(Collectors.groupingBy(EveryDayBillInfoDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
        Map<Date, List<EveryDayBillInfoDTO>> listMap = everyBillLists.stream()
                .collect(Collectors.groupingBy(
                        EveryDayBillInfoDTO::getBillDate,
                        LinkedHashMap::new,
                        Collectors.toList()
                ))
                .entrySet().stream()
                .sorted((e1, e2) -> {
                    BigDecimal sum2 = e2.getValue().stream()
                            .map(dto -> Optional.ofNullable(dto.getBillAmount()).orElse(BigDecimal.ZERO)
                                    .subtract(Optional.ofNullable(dto.getPrepaidAmount()).orElse(BigDecimal.ZERO))
                                    .subtract(Optional.ofNullable(dto.getImmoderateAmount()).orElse(BigDecimal.ZERO)))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal sum1 = e1.getValue().stream()
                            .map(dto -> Optional.ofNullable(dto.getBillAmount()).orElse(BigDecimal.ZERO)
                                    .subtract(Optional.ofNullable(dto.getPrepaidAmount()).orElse(BigDecimal.ZERO))
                                    .subtract(Optional.ofNullable(dto.getImmoderateAmount()).orElse(BigDecimal.ZERO)))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return sum2.compareTo(sum1);
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldVal, newVal) -> oldVal,
                        LinkedHashMap::new
                ));
        for (Map.Entry<Date,List<EveryDayBillInfoDTO>> entry :listMap.entrySet()){
            List<EveryDayBillInfoDTO>  dutyattList = entry.getValue();
            if(CollectionUtils.isEmpty(dutyBillLimitDtoLists)){
                //保单年度内没有查询到赔付记录
                setYearlyBillValue(dutyattList,detailPayDTO);//给发票日期打标 是否能参与本次理算
            }else {
                //保单年度内查询到了记录
                dealYeaylyData(dutyBillLimitDtoLists,dutyattList,detailPayDTO);
            }
        }


        return everyBillLists;
    }
    /**
     * 查询已赔付
     * @param detail
     * @return
     */
    private List<DutyBillLimitDto> getPolicyDutyYearlyPayDays(DutyDetailPayDTO detail) {
        DutyLimitQueryVo dutyLimitQueryVo =new DutyLimitQueryVo();
        dutyLimitQueryVo.setPolicyNo(detail.getPolicyNo());
        dutyLimitQueryVo.setPlanCode(detail.getPlanCode());
        dutyLimitQueryVo.setDutyCode(detail.getDutyCode());
        dutyLimitQueryVo.setSatrtDate(detail.getInsuranceBeginTime());
        dutyLimitQueryVo.setEndDate(detail.getInsuranceEndTime());
        LogUtil.info("年度已已赔付天数报案号={}，责任查询参数={}",detail.getReportNo(), JsonUtils.toJsonString(dutyLimitQueryVo));
        List<DutyBillLimitDto> dtos= dutyBillLimitInfoMapper.getAllAlreadyPayTimes(dutyLimitQueryVo);
        if(CollectionUtils.isEmpty(dtos)){
            return dtos;
        }
        if(detail.getCaseTimes()>1){
            //重开案件去掉当前报案号对应的发票日期
            dtos= dtos.stream().filter(dutyBillLimitDto -> !Objects.equals(detail.getReportNo(),dutyBillLimitDto.getReportNo())).collect(Collectors.toList());
        }
        //去除金额为0的发票
        dtos= dtos.stream().filter(dutyBillLimitDto -> !(dutyBillLimitDto.getSettleClaimAmount().compareTo(BigDecimal.ZERO) == 0)).collect(Collectors.toList());
        List<DutyBillLimitDto> returnList=new ArrayList<>();
        Map<Date,List<DutyBillLimitDto>> mothListMap=dtos.stream().sorted(Comparator.comparing(DutyBillLimitDto::getBillDate)).collect(Collectors.groupingBy(DutyBillLimitDto::getBillDate,LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<Date,List<DutyBillLimitDto>> entry : mothListMap.entrySet()){
            List<DutyBillLimitDto> evDayList = entry.getValue();
            List<String> caseNoList=getCaseNoList(evDayList);
            Integer count= dutyDetailPayMapper.getIndmenityInfo(caseNoList,detail.getDutyCode());
            if(count>=1){
                DutyBillLimitDto dto=new DutyBillLimitDto();
                dto.setBillDate(entry.getKey());
                returnList.add(dto);
            }
        }
        return returnList;
    }
    private List<String> getCaseNoList(List<DutyBillLimitDto> evDayList) {
        List<String> caseNolist=new ArrayList<>();
        for (DutyBillLimitDto dto : evDayList) {
            caseNolist.add(dto.getCaseNo());
        }
        return caseNolist;
    }
    private void setYearlyBillValue(List<EveryDayBillInfoDTO> dutyattList, DutyDetailPayDTO detail) {
        //得到每月赔付天数
        Integer confYearlyPayDays = detail.getConfiYearlyPayDays();
        Map<Date,List<EveryDayBillInfoDTO>>  dateListMap=dutyattList.stream().sorted(Comparator.comparing(EveryDayBillInfoDTO::getBillDate)).collect(Collectors.groupingBy(EveryDayBillInfoDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<Date,List<EveryDayBillInfoDTO>> entry : dateListMap.entrySet()) {
            if(detail.getYearlyPayDays()<confYearlyPayDays){
                if(Objects.equals("Y",entry.getValue().get(0).getExceedMothPayDays())||Objects.equals("N",entry.getValue().get(0).getEffectiveFlag())){
                    setYearlyFlagY(entry.getValue());
                }else{
                    setYearlyFlagN(entry.getValue());//发票日的数据可以参与计算
                    detail.setYearlyPayDays(detail.getYearlyPayDays()+1);
                }

            }else{
                setYearlyFlagY(entry.getValue());
            }
        }
    }

    /**
     * 年度内已有赔付记录的情况
     * 判段是否已超年度赔付天数
     *
     * @param payBillLimitDtoLists
     * @param dutyattList
     * @param detail
     * @param
     */
    private void dealYeaylyData(List<DutyBillLimitDto> payBillLimitDtoLists, List<EveryDayBillInfoDTO> dutyattList, DutyDetailPayDTO detail) {
        //按发票日期排序分组
        Map<Date,List<EveryDayBillInfoDTO>>  dateListMap=dutyattList.stream().sorted(Comparator.comparing(EveryDayBillInfoDTO::getBillDate)).collect(Collectors.groupingBy(EveryDayBillInfoDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<Date,List<EveryDayBillInfoDTO>> entry : dateListMap.entrySet()) {
            List<DutyBillLimitDto> billLimitDtos =  payBillLimitDtoLists.stream().filter(dutyBillLimitDto -> !Objects.equals(entry.getKey(),dutyBillLimitDto.getBillDate())).collect(Collectors.toList());
            //如果不包含当前发票日期的条数都已经大于等于了年度赔付天数，那么当前发票日期的发票就不能参与计算了
            if(billLimitDtos.size()>=detail.getConfiYearlyPayDays()){
                setYearlyFlagY(entry.getValue());
            }else{
                //过滤看是否包含当前发票日期
                List<DutyBillLimitDto> currentDateDtos =  payBillLimitDtoLists.stream().filter(dutyBillLimitDto -> Objects.equals(entry.getKey(),dutyBillLimitDto.getBillDate())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(currentDateDtos)){
                    //包含当前发票日,当前发票日期可以参与计算
                    if(detail.getYearlyPayDays()<=detail.getConfiYearlyPayDays()){
                        setYearlyFlagN(entry.getValue());
                    }else{
                        setYearlyFlagY(entry.getValue());
                    }

                }else{
                    //不包含当前发票日期
                    if(detail.getYearlyPayDays()<detail.getConfiYearlyPayDays()){
                        if(Objects.equals("Y",entry.getValue().get(0).getExceedMothPayDays())||Objects.equals("N",entry.getValue().get(0).getEffectiveFlag())){
                            setYearlyFlagY(entry.getValue());
                        }else{
                            setYearlyFlagN(entry.getValue());
                            detail.setYearlyPayDays(detail.getYearlyPayDays()+1);
                        }

                    }else{
                        setYearlyFlagY(entry.getValue());
                    }

                }


            }

        }


    }
    private void setYearlyFlagY(List<EveryDayBillInfoDTO> list) {
        list.stream().forEach(everyDayBillInfoDTO -> everyDayBillInfoDTO.setExceedYearlyPayDays("Y"));
    }
    private void setYearlyFlagN(List<EveryDayBillInfoDTO> list) {
        list.stream().forEach(everyDayBillInfoDTO -> everyDayBillInfoDTO.setExceedYearlyPayDays("N"));
    }
}
