package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.dao.entity.duty.ReportPlanDutyVo;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;

import java.util.List;
import java.util.Map;

public interface MaxPayService {

    /**
     * 计算剩余赔付额
     * @param policyPayInfoArr
     * @param scene 查询场景（暂时只用于区分查询历史案件，查询历史案件时 计算剩余理赔金额的已赔付金额要包含当前报案号）
     */
    void initPoliciesPayMaxPay(List<PolicyPayDTO> policyPayInfoArr, String scene);

    void initEstPoliciesPayMaxPay(List<EstimatePolicyDTO> estimatePolicyList, String scene);

    /**
     * 根据保单号+客户号，获取出险标责任出险次数
     *
     * @param policyNo
     * @param reportNo
     * @return
     */
    Map<String, Long> getDutyPayNum(String policyNo, String reportNo);

    /**
     * 查询前一次责任赔付金额
     * @param estimatePolicyList
     */
    void initEstClaimCasePayMaxPay(List<EstimatePolicyDTO> estimatePolicyList);

    void initPoliciesPayMaxPayToPersonTrace(List<ReportPlanDutyVo> reportPlanDutyVoList, String scene);
}
