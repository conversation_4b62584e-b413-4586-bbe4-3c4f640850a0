package com.paic.ncbs.claim.service.settle.factor.interfaces.bill;

import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EveryDayBillInfoDTO;

import java.util.List;

public interface MedicalBillInfoService {
     List<MedicalBillInfoDTO> getMedicalBillInfoList(String reportNo, Integer caseTimes);
     List<EveryDayBillInfoDTO> getEveryDayBillInfoList(List<MedicalBillInfoDTO> medicalBillInfoDTOList);
     public List<EveryDayBillInfoDTO> getEveryDayBillInfoLists(List<MedicalBillInfoDTO> medicalBillInfoDTOList);

}
