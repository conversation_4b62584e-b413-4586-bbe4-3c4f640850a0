package com.paic.ncbs.claim.service.checkloss.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.checkloss.BaseProdPifTermEntity;
import com.paic.ncbs.claim.dao.mapper.checkloss.BaseProdPifTermEntityMapper;
import com.paic.ncbs.claim.model.dto.checkloss.SmallTermDTO;
import com.paic.ncbs.claim.service.checkloss.BaseProdPifTermEntityService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BaseProdPifTermEntityServiceImpl extends BaseServiceImpl<BaseProdPifTermEntity> implements BaseProdPifTermEntityService {

    @Autowired
    private BaseProdPifTermEntityMapper prodPifTermEntityMapper;

    @Override
    public BaseProdPifTermEntity getDocumentGroupId(String termCode) {
        return prodPifTermEntityMapper.getDocumentGroupId(termCode);
    }

    @Override
    public List<SmallTermDTO> getTermNamesByTermCodes(List<String> termCodesList) {
        return prodPifTermEntityMapper.getTermNamesByTermCodes(termCodesList);
    }

    @Override
    public BaseDao<BaseProdPifTermEntity> getDao() {
        return prodPifTermEntityMapper;
    }

}
