package com.paic.ncbs.claim.service.settle.factor.impl.modelinit;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.DutyAttributeConst;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeDetailMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.PolicyDutyAttributeDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.modelinit.ModelDataInitializeService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.attributes.DutyAttributeService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 模型责任属性初始胡查询
 */
@Slf4j
@Order(2)
@Service
public class ModelDutyAttributeInitializeServiceImpl implements ModelDataInitializeService {
    @Autowired
    private DutyAttributeMapper dutyAttributeMapper;

    @Autowired
    private DutyAttributeDetailMapper dutyAttributeDetailMapper;
    @Autowired
    private Map<String,DutyAttributeService> attributeServiceMaps;
    @Override
    public ClaimCaseDTO initialize(ClaimCaseDTO claimCaseDTO) {
        List<PolicyPayDTO> policys = claimCaseDTO.getPolicyPayDTOList();
        policys.forEach(policyPayDTO -> dealPolicy(policyPayDTO,claimCaseDTO.getReportNo()));
        return claimCaseDTO;
    }
    private void dealPolicy(PolicyPayDTO policyPayDTO,String reportNo){
        List<PolicyDutyAttributeDTO> dutyAttributeDTOS =  dutyAttributeMapper.getPolicyDutyAttributes(reportNo,policyPayDTO.getPolicyNo());
        Map<String,List<PolicyDutyAttributeDTO>> dutyAttributeMap=dutyAttributeDTOS.stream().collect(Collectors.groupingBy(PolicyDutyAttributeDTO::getIdAhcsPolicyDuty));
        policyPayDTO.getPlanPayArr().forEach(planPayDTO ->dealPlanInfo(planPayDTO,dutyAttributeMap));
    }
    private void dealPlanInfo(PlanPayDTO planPayDTO, Map<String,List<PolicyDutyAttributeDTO>> attributeMap){
        planPayDTO.getDutyPayArr().forEach(dutyPayDTO -> dealDutyInfo(dutyPayDTO,attributeMap));
    }
    private void dealDutyInfo(DutyPayDTO dutyPayDTO, Map<String,List<PolicyDutyAttributeDTO>> attributeMap){
        String idPolicyDuty = dutyPayDTO.getIdCopyDuty();
        Map<String, Map<String, String>> attributesdetailMap = dutyAttributeMapper.getAttributeDetailByidPolicyDuty(idPolicyDuty);

        List<PolicyDutyAttributeDTO> dutyAttributeDTOS = attributeMap.get(idPolicyDuty);
        if(CollectionUtil.isEmpty(dutyAttributeDTOS)){
           return;
        }
        Map<String,Optional<String>> attributes=dutyAttributeDTOS.stream().collect(Collectors.toMap(PolicyDutyAttributeDTO::getAttributeCode,policyDutyAttributeDTO -> Optional.ofNullable(policyDutyAttributeDTO.getAttributeValue())));
        //责任属性免赔额类型
        if (attributes.containsKey(DutyAttributeConst.REMIT_AMOUNT_TYPE)) {
            dutyPayDTO.setRemitAmountType(attributes.get(DutyAttributeConst.REMIT_AMOUNT_TYPE).get());
            if(Arrays.asList("P02P00068002", "P02P00313001").contains(dutyPayDTO.getProductPackage())){
                dutyPayDTO.setRemitAmountType("2");
            }
        }else{
            log.info("报案号={}，未配置免赔额类型，责任编码={}，默认按次免赔额处理={}",dutyPayDTO.getReportNo(),dutyPayDTO.getDutyCode(), JsonUtils.toJsonString(attributes));
            dutyPayDTO.setRemitAmountType("0");
        }
        if(attributes.containsKey(DutyAttributeConst.REMIT_AMOUNT)){
            dutyPayDTO.setRemitAmount(BigDecimalUtils.getBigDecimal(attributes.get(DutyAttributeConst.REMIT_AMOUNT).get()));
        }else{
            dutyPayDTO.setRemitAmount(BigDecimal.ZERO);
        }

        dutyPayDTO.getDutyDetailPayArr().forEach(dutyDetailPayDTO ->{
            String serviceName= Constants.ATTRIBUTES_MAP.get(dutyDetailPayDTO.getDutyDetailType());
            dutyDetailPayDTO.setIdPolicyDuty(idPolicyDuty);
            DutyAttributeService dutyAttributeService = attributeServiceMaps.get(serviceName);
            if(Objects.isNull(dutyAttributeService)){
                return;
            }
            dutyAttributeService.setDutyDetailAttribute(dutyDetailPayDTO,attributes,attributesdetailMap);
        });
    }

}
