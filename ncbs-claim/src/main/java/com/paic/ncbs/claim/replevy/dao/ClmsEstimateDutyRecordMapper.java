package com.paic.ncbs.claim.replevy.dao;

import com.paic.ncbs.claim.replevy.dto.EstimateDutyRecordQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 估损责任记录Mapper接口
 */
@Mapper
public interface ClmsEstimateDutyRecordMapper {
    
    /**
     * 根据报案号查询估损责任记录
     * @param reportNo 报案号
     * @return 估损责任记录列表
     */
    List<EstimateDutyRecordQueryDTO> getEstimateDutyRecordByReportNo(@Param("reportNo") String reportNo);
}
