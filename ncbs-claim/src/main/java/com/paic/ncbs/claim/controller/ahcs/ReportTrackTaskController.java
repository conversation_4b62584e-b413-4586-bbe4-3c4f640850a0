package com.paic.ncbs.claim.controller.ahcs;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.duty.DutySurveyVO;
import com.paic.ncbs.claim.service.report.ReportTrackTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Api(tags = {"报案跟踪"})
@Slf4j
@Controller
@RequestMapping("/ahcs/do/app/reportTrackTaskAction")
public class ReportTrackTaskController extends BaseController {

    @Autowired
    private ReportTrackTaskService reportTrackTaskService;

    @ApiOperation(value = "报案跟踪-立案、暂存、发送")
    @ResponseBody
    @PostMapping(value = "/saveReportTrack")
    public ResponseResult<Object> saveReportTrack(@RequestBody DutySurveyVO dutyVO) throws Exception {
        LogUtil.audit("报案跟踪saveReportTrack报案号{},入参{}",dutyVO.getReportNo(), JSON.toJSONString(dutyVO));
        reportTrackTaskService.saveReportTrack(dutyVO, WebServletContext.getUserId());
        return ResponseResult.success();
    }

    @ApiOperation(value = "获取报案跟踪信息-案件信息、事故信息等")
    @ResponseBody
    @GetMapping(value = "/getReportTrackInfo/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass = String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<Object> getReportTrackInfo( @PathVariable("reportNo") String reportNo,
                                                      @PathVariable("caseTimes") int caseTimes) throws Exception {
        LogUtil.audit("getReportTrackInfo获取报案跟踪信息,reportNo={},caseTimes={}",reportNo, caseTimes);
        try {
            return ResponseResult.success(reportTrackTaskService.getReportTrackInfo(reportNo, caseTimes));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

}
