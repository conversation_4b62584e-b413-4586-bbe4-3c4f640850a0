package com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.template;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.print.util.PdfHelpUtils;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.util.Map;

@Slf4j
public  abstract class  AbstractTemplateService {
    public String templatePath;
    public final String replaceSettleReason(DutyDetailPayDTO detailPayDTO){
        setTemplate(detailPayDTO);
        if(isCustomized()){
            return customizeReason(detailPayDTO);
        }else{
            String reason = dealData(detailPayDTO);
            return reason;


        }

    }

    /**
     * 自定义模版设置
     * @param
     */
    public abstract void setTemplate(DutyDetailPayDTO detailPayDTO);

    public final String dealData(DutyDetailPayDTO detailPayDTO){
        if(StringUtils.isEmptyStr(templatePath)){
            throw new RuntimeException("理算依据模版不能为空！");
        }
        log.info("理算依据入参={}", JsonUtils.toJsonString(detailPayDTO.getDetailSettleReasonTemplateDTO()));
        Map variables = (Map) JSONObject.parseObject(JSONObject.toJSONString(detailPayDTO.getDetailSettleReasonTemplateDTO()), Map.class);
        log.info("理算依据模板参数={}", JsonUtils.toJsonString(variables));
        try {
            Template template = PdfHelpUtils.getConfigurationPdf().getTemplate(templatePath);
            String settleReason =  FreeMarkerTemplateUtils.processTemplateIntoString(template, variables);
            log.info("理算依据替换后={}",settleReason);
            return settleReason;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public abstract boolean isCustomized();

    /**
     * 自定义
     * @param detailPayDTO
     * @return
     */
    public abstract String customizeReason(DutyDetailPayDTO detailPayDTO);
}
