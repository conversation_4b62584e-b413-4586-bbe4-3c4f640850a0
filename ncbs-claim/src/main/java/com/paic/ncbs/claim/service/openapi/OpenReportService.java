package com.paic.ncbs.claim.service.openapi;

import com.paic.ncbs.claim.model.dto.openapi.*;
import com.paic.ncbs.claim.model.vo.openapi.ClaimStatusResponseVo;
import com.paic.ncbs.claim.model.vo.openapi.OpenReportInfoVO;
import com.paic.ncbs.claim.model.vo.openapi.PaymentVO;
import com.paic.ncbs.claim.model.vo.openapi.ReportQueryResVO;

import java.util.List;

public interface OpenReportService {

    /**
     * 查询案件信息
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return OpenReportInfoVO
     */
    OpenReportInfoVO getReportInfo(String reportNo, Integer caseTimes);

    /**
     * 查询领款信息
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return List<PaymentVO>
     */
    List<PaymentVO> getPaymentList(String reportNo, Integer caseTimes);

    /**
     * 保单历史理赔案件信息列表
     * @param requestDto
     * @return
     */
    List<PolicyClaimHistoryReportInfoDTO> getPolicyHistoryClaimInfo(QueryClaimReportRequestDTO requestDto);

    /**
     * 查询案件详情信息
     * @param reportNo
     * @return
     */
    ClaimDetailedInfoDTO getReportDetailInfo(String reportNo);

    /**
     * 查询客户理赔 未决案件列表
     * @param req
     * @return
     */
    ReportQueryResVO queryCaseInfoList(ReportQueryReqDTO req);

    /**
     * 查询客户理赔案件列表
     * @param req
     * @return
     */
    ReportQueryResVO queryReport(ReportQueryReqDTO req);

    ReportQueryResVO queryReport4Severe(ReportQueryReqDTO req);

    /**
     * TPA案件状态查询
     * @param tpaClaimStatusRequestDto
     * @return
     */
    ClaimStatusResponseVo queryClaimStatusTPA(TpaClaimStatusRequestDto tpaClaimStatusRequestDto);

    ReportQueryResVO getQueryCusReportInfo(ReportQueryReqDTO req);

    Object getHistoryCaseList(ReportQueryReqDTO queryVO);

    List<PaymentVO> getPaymentListTOCustServ(String reportNo, Integer caseTimes);
}
