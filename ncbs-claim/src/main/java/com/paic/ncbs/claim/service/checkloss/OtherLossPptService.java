package com.paic.ncbs.claim.service.checkloss;


import com.paic.ncbs.claim.model.dto.duty.OtherLossPptDTO;

import java.util.List;


public interface OtherLossPptService {


	public void removeOtherLossPptList(String reportNo,Integer caseTimes,String taskCode);


	public List<OtherLossPptDTO> getOtherLossPptListByReportNo(String reportNo, Integer caseTimes, String status, String taskCode);


	public void addOtherLossPptList(List<OtherLossPptDTO> otherLossPptList,Integer caseTimes,String userId , String channelProcessId);


	public List<OtherLossPptDTO> getOtherLossPptList(String idAhcsChannelProcess,String taskCode);


	public List<String> getAccidentReasonList(String reportNo);
}
