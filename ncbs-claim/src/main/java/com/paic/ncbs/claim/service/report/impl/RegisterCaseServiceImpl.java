package com.paic.ncbs.claim.service.report.impl;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseRegisterApplyMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.report.RegisterAmountRelMapper;
import com.paic.ncbs.claim.dao.mapper.report.RegisterCaseLogMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.report.AcceptRecordDTO;
import com.paic.ncbs.claim.model.dto.report.RegisterAmountRelDTO;
import com.paic.ncbs.claim.model.dto.report.RegisterCaseLogDTO;
import com.paic.ncbs.claim.model.vo.endcase.CaseRegisterApplyVO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.casezero.CaseZeroCancelService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.EndCaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.EstimateDutyRecordService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.report.AcceptRecordService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

import static com.paic.ncbs.claim.common.constant.ConfigConstValues.AUDIT_END;

@Service("registerCaseService")
@RefreshScope
public class RegisterCaseServiceImpl implements RegisterCaseService {

	@Autowired
	private WholeCaseBaseMapper wholeCaseBaseDao;

	@Autowired
	private AhcsCommonService ahcsCommonService;

	@Autowired
	private CaseRegisterApplyMapper caseRegisterApplyDao;

	@Autowired
	private CaseZeroCancelService caseZeroCancelService;

	@Autowired
	private WholeCaseBaseService wholeCaseBaseService;

	@Autowired
	private EstimatePolicyMapper estimatePolicyDAO;


	@Autowired
	private EstimateService estimateService;


	@Autowired
	private EndCaseService endCaseService;

	@Autowired
	private AcceptRecordService acceptRecordService;

	@Autowired
	private CaseProcessService caseProcessService;

	@Autowired
	private RegisterCaseService registerCaseService;

	@Autowired
	private EstimateDutyRecordService estimateDutyRecordService;


	@Autowired
	private RegisterCaseLogMapper registerCaseLogDao;

	@Autowired
	private UserInfoService userInfoService ;

	private static final String END_CASE_STATUS = "0";

	private static final String REPORTNO = "reportNo";
	private static final String CASETIMES = "caseTimes";
	private static final String SYSTEM = "system";
	@Value("${report.register.time:10}")
	private String configDays;

	@Override
	public boolean isExistRegisterRecord(String reportNo, int caseTimes) throws GlobalBusinessException {
		WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseDao.getWholeCaseBase2(reportNo, caseTimes);
		if (wholeCaseBaseDTO == null) {
			LogUtil.audit("#整案表不存在该报案号的数据# reportNo:" + reportNo + ",caseTimes:" + caseTimes);
			throw new GlobalBusinessException(ErrorCode.RegisterCase.CANNOT_FIND_CASE);
		}
		return ConfigConstValues.YES.equals(wholeCaseBaseDTO.getIsRegister());
	}

	@Override
	public ResponseResult registerCaseCheck(String reportNo, Integer caseTimes) {
		ResponseResult resultVO = null;

		LogUtil.audit("查询案件是否在立案审核中, reportNo={}", reportNo);
		//获取获取最新的立案申请信息记录
		CaseRegisterApplyDTO caseRegisterApplyDTO = caseRegisterApplyDao.getLastestRegisterApplyDTO(reportNo, caseTimes);
		if (caseRegisterApplyDTO != null ){
			if (ConfigConstValues.AUDIT_PROCESSING.equals(caseRegisterApplyDTO.getStatus())) {
				return ResponseResult.success(ErrorCode.RegisterCase.REGISTER_APPLY_IS_PROCESSING);
			}
			if (ConfigConstValues.AUDIT_NOTNEED.equals(caseRegisterApplyDTO.getStatus())
					||(ConfigConstValues.AUDIT_END.equals(caseRegisterApplyDTO.getStatus()) && ConstValues.AUDIT_AGREE_CODE.equals(caseRegisterApplyDTO.getAuditOpinion()))) {
				return ResponseResult.success(ErrorCode.RegisterCase.CASE_IS_END);			}
		}
		LogUtil.audit("查询案件是否在零注中, reportNo={}", reportNo);
		//查询最新的ahcs_zero_cancel_apply零注申请审批表记录
		CaseZeroCancelDTO caseZeroCancelDTO = caseZeroCancelService.getLastZeroCancelInfo(reportNo, caseTimes);
		if (caseZeroCancelDTO != null) {

			boolean isDeptAuditProcessing = (EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_DEPT_AUDIT.equals(caseZeroCancelDTO.getStatus())
					|| EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_PROCESSING.equals(caseZeroCancelDTO.getStatus()));
			if (isDeptAuditProcessing) {
				return ResponseResult.success(ErrorCode.RegisterCase.ZERO_CANCEL_IS_PROCESSING);

			}
			String eoaStatus = caseZeroCancelDTO.getStatus();
			String verifyOptions = caseZeroCancelDTO.getVerifyOptions();

			boolean isEoaAuditProcessing = EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_APPROVED.equals(eoaStatus)
					&& EndCaseConstValues.AUDIT_AGREE_CODE.equals(verifyOptions)
					&& EndCaseConstValues.EOA_PROCESSING.equals(eoaStatus);
			if (isEoaAuditProcessing) {
				return ResponseResult.success(ErrorCode.RegisterCase.ZERO_CANCEL_IS_PROCESSING);

			}
		}

		LogUtil.audit("查询案件是否已结案, reportNo={}", reportNo);

		WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
		if (wholeCaseBaseDTO != null && END_CASE_STATUS.equals(wholeCaseBaseDTO.getWholeCaseStatus())) {
			return ResponseResult.success(ErrorCode.RegisterCase.CASE_IS_END);

		}
		if (wholeCaseBaseDTO != null && ConstValues.YES.equals(wholeCaseBaseDTO.getIsRegister())) {
			return ResponseResult.success(ErrorCode.RegisterCase.CASE_IS_END);
		}
		return ResponseResult.success(ErrorCode.RegisterCase.CAN_APPLY_REGISTER_CASE);
	}

	@Override
	public CaseRegisterApplyVO getLastCaseRegisterApplyVO(String reportNo, Integer caseTimes){
		List<CaseRegisterApplyVO> applyList = caseRegisterApplyDao.getLastestRegisterApplyVOList(reportNo, caseTimes);
		if (ListUtils.isNotEmpty(applyList)) {
			LogUtil.audit("reportNo:{},applyList size:",reportNo,applyList.size());
			CaseRegisterApplyVO caseRegisterApplyVO = applyList.get(applyList.size() - 1);
			LogUtil.audit("reportNo:{},IdAhcsCaseRegisterApply:{}",reportNo,caseRegisterApplyVO.getIdAhcsCaseRegisterApply());
			BigDecimal registerAmount = estimateDutyRecordService.getRegisterAmountByApplyId(caseRegisterApplyVO.getIdAhcsCaseRegisterApply());
			LogUtil.audit("registerAmount:"+registerAmount);
			caseRegisterApplyVO.setRegisterAmount(registerAmount);
			if (null != caseRegisterApplyVO.getApplyUm()){
				caseRegisterApplyVO.setApplyorName(userInfoService.getUserInfoDTO(caseRegisterApplyVO.getApplyUm()).getUserName());
			}
			if (null != caseRegisterApplyVO.getAuditUm()){
				caseRegisterApplyVO.setAuditorName(userInfoService.getUserInfoDTO(caseRegisterApplyVO.getAuditUm()).getUserName());
			}
			if (ConstValues.SYSTEM_UM.toUpperCase().equals(caseRegisterApplyVO.getApplyUm().toUpperCase())) {
				caseRegisterApplyVO.setApplyorName(ConstValues.SYSTEM_NAME);
			}
			return caseRegisterApplyVO;
		}
		return null;
	}

	@Override
	public List<CaseRegisterApplyVO> getLastCaseRegisterApplyVOList(String reportNo, Integer caseTimes) {
		List<CaseRegisterApplyVO> applyInfoList = caseRegisterApplyDao.getLastestRegisterApplyVOList(reportNo, caseTimes);
		applyInfoList.removeIf(e->"1".equals(e.getStatus()));
		if (ListUtils.isNotEmpty(applyInfoList)) {
			for (CaseRegisterApplyVO item : applyInfoList) {
				if (null != item.getApplyUm()){
					item.setApplyorName(userInfoService.getUserInfoDTO(item.getApplyUm()).getUserName());
				}
				if (null != item.getAuditUm()){
					item.setAuditorName(userInfoService.getUserInfoDTO(item.getAuditUm()).getUserName());
				}
				if (ConstValues.SYSTEM_UM.equals(item.getApplyUm())) {
					item.setApplyorName(ConstValues.SYSTEM_NAME);
				}
			}
		}
		return applyInfoList;
	}

	/**
	 * 获取最新立案申请信息
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	@Override
	public CaseRegisterApplyDTO getLastCaseRegisterApplyDTO(String reportNo, Integer caseTimes) {
		return caseRegisterApplyDao.getLastestRegisterApplyDTO(reportNo, caseTimes);
	}

	@Override
	public void addCaseRegisterApplyDTO(CaseRegisterApplyDTO caseRegisterApplyDTO) {
		caseRegisterApplyDao.addCaseRegisterApplyDTO(caseRegisterApplyDTO);
	}

	@Override
	public void batchAddRegisterAmountRelInfo(List<RegisterAmountRelDTO> registerAmountRelDTOS) {
		ahcsCommonService.batchHandlerTransactional(RegisterAmountRelMapper.class, registerAmountRelDTOS, "addRegisterAmountRel");
	}

    @Override
    public void registerHistoryCase(WholeCaseBaseDTO wholeCaseBaseDTO) throws GlobalBusinessException {
		LogUtil.audit("#历史案件收单入参# reportNo:" + wholeCaseBaseDTO.getReportNo() + ",caseTimes:"
				+ wholeCaseBaseDTO.getCaseTimes());
		boolean isExist = isExistRegisterRecord(wholeCaseBaseDTO.getReportNo(), wholeCaseBaseDTO.getCaseTimes());
		if (!isExist) {
			registerCase(wholeCaseBaseDTO.getReportNo(), wholeCaseBaseDTO.getCaseTimes());
		}
    }

	@Override
	public void modifyRegisterAuditStatus(CaseRegisterApplyVO caseRegisterApplyVO) {
		caseRegisterApplyDao.modifyRegisterAuditStatus(caseRegisterApplyVO);

	}

	@Override
	@Transactional
	public void addRegisterCaseLog(String reportNo, Integer caseTimes, Date reportDate) {
		try {

			if (StringUtils.isEmptyStr(configDays)) {
				LogUtil.audit("#自动立案时间没有配置#");
			}

			RegisterCaseLogDTO registerCaseLogDTO = new RegisterCaseLogDTO();
			registerCaseLogDTO.setReportNo(reportNo);
			registerCaseLogDTO.setCaseTimes(caseTimes);
			registerCaseLogDTO.setReportDate(reportDate);
			registerCaseLogDTO.setIsRegister("N");
			registerCaseLogDTO.setIsDeal("N");
			Date waitRegisterDate = DateUtils.addDate(reportDate, Integer.parseInt(configDays));
			Date tomorrowRegisterDate = DateUtils.addDate(DateUtils.getCurrentTime(), 1);

			if (DateUtils.compareTimeBetweenDate(waitRegisterDate, tomorrowRegisterDate)) {
				int betweenDays = DateUtils.getDaysBetween(reportDate, tomorrowRegisterDate);
				int addDays = betweenDays - Integer.parseInt(configDays);
				waitRegisterDate = DateUtils.addDate(waitRegisterDate, addDays);
			}

			registerCaseLogDTO.setWaitRegisterDate(waitRegisterDate);
			registerCaseLogDTO.setIdRegisterCaseLog(UuidUtil.getUUID());
			registerCaseLogDao.addRegisterCaseLog(registerCaseLogDTO);
		} catch (Exception e) {
			LogUtil.info("#报案后新增待立案记录失败reportNo=" + reportNo + ",caseTimes=" + caseTimes, e);
		}
	}

	@Override
	@Transactional
	public void registerCaseForReport(String reportNo, Integer caseTimes) {
		this.registerCase(reportNo, caseTimes);

		LogUtil.audit("#自动立案结束#reportNo:%s,caseTimes:%s", reportNo, caseTimes);
	}

	@Override
	public Map<String, Object> isRegister(String reportNo, Integer caseTimes) {
		LogUtil.audit("#判断案件是否立案#,isRegister方法入参,reportNo={}, caseTimes={}#", reportNo, caseTimes);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
		LogUtil.audit("#判断案件是否立案#,整案信息wholeCaseBaseDTO={}", JSONObject.toJSONString(wholeCaseBaseDTO));
		String isRegister = null;
		String collectionCode = null;
		BigDecimal registerAmount = null;
		if (wholeCaseBaseDTO != null && StringUtils.isEqualStr(ConstValues.YES, wholeCaseBaseDTO.getIsRegister())) {
			isRegister = wholeCaseBaseDTO.getIsRegister();
			collectionCode = EndCaseConstValues.ZERO_CANCEL_COLLECTION_CODE;
			registerAmount = this.getRegisterAmountForUpdated(reportNo, caseTimes)  ;
			LogUtil.audit("#判断案件是否立案#,已立案reportNo={},立案金额registerAmount={}", reportNo, registerAmount);
		}
		if (wholeCaseBaseDTO != null && (StringUtils.isEqualStr(ConstValues.NO, wholeCaseBaseDTO.getIsRegister())
				|| StringUtils.isEmptyStr(wholeCaseBaseDTO.getIsRegister()))) {
			isRegister = ConstValues.NO;
			collectionCode = EndCaseConstValues.ZERO_CANCEL_COLLECTION_CODE_NO_REGISTER;
			LogUtil.audit("#判断案件是否立案#,未立案reportNo={}", reportNo);
		}
		if (wholeCaseBaseDTO == null) {
			isRegister = ConstValues.NO;
			collectionCode = EndCaseConstValues.ZERO_CANCEL_COLLECTION_CODE_NO_REGISTER;
			LogUtil.audit("#判断案件是否立案#,未立案reportNo={}", reportNo);
		}
		resultMap.put(REPORTNO, reportNo);
		resultMap.put(CASETIMES, caseTimes);
		resultMap.put("isRegister", isRegister);
		resultMap.put("collectionCode", collectionCode);
		resultMap.put("registerAmount", registerAmount);
		LogUtil.audit("#判断案件是否立案#,isRegister方法出参,resultMap={}", JSONObject.toJSONString(resultMap));
		return resultMap;
	}


	private BigDecimal getRegisterAmountForUpdated(String reportNo, Integer caseTimes) {
		BigDecimal registerAmout = new BigDecimal(0);
		String estimateType = "03";
		String idAhcsEstimateDutyRecord = estimateService.getIdAhcsEstimateDutyRecord(reportNo, caseTimes,
				estimateType);
		LogUtil.audit("#判断案件是否立案调整#,reportNo={},idAhcsEstimateDutyRecord={}", reportNo, idAhcsEstimateDutyRecord);
		if (!StringUtils.isEmptyStr(idAhcsEstimateDutyRecord)) {
			registerAmout = estimateService.getLatestRegisterAmount(reportNo, caseTimes, estimateType);
			LogUtil.audit("#判断案件是立案调整#,reportNo={},registerAmout={}", reportNo, registerAmout);
		} else {
			registerAmout = estimateService.getRegisterAmount(reportNo, caseTimes);
			LogUtil.audit("#判断案件不是立案调整#,reportNo={},registerAmout={}", reportNo, registerAmout);
		}
		return registerAmout;
	}


	private void registerCase(String reportNo, Integer caseTimes) throws GlobalBusinessException {
		WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseDao.getWholeCaseBase2(reportNo, caseTimes);
		List<EstimatePolicyDTO> estimatePolicyList = estimatePolicyDAO.getByReportNoAndCaseTimes(reportNo, caseTimes);
		isExistEstimateData(estimatePolicyList);

		if (ConfigConstValues.YES.equals(wholeCaseBaseDTO.getIsRegister())) {

			LogUtil.audit("#案件已立案,自动立案不再进行立案# reportNo:{}, caseTimes:{}", reportNo, caseTimes);
			return;
		}
		LogUtil.audit("#调立案方法#入参# reportNo:{}, caseTimes:{}", reportNo, caseTimes);

		WholeCaseBaseDTO tempWholeCaseBaseDTO = new WholeCaseBaseDTO();

		tempWholeCaseBaseDTO.setIsRegister(ConstValues.YES);
		tempWholeCaseBaseDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
		tempWholeCaseBaseDTO.setRegisterUm(ConstValues.SYSTEM_UM);
//		Date registerDate = new Date();
//		tempWholeCaseBaseDTO.setRegisterDate(registerDate);
		tempWholeCaseBaseDTO.setWholeCaseBaseId(wholeCaseBaseDTO.getWholeCaseBaseId());
		tempWholeCaseBaseDTO.setReportNo(reportNo);
		tempWholeCaseBaseDTO.setCaseTimes(caseTimes);

		try {
			LogUtil.audit("#自动立案，更新整案表#,reportNo:%s, caseTimes:%s", reportNo, caseTimes);

			wholeCaseBaseDao.modifyWholeCaseBase(tempWholeCaseBaseDTO);
			LogUtil.audit("#自动立案，更新赔案表#,reportNo:%s, caseTimes:%s", reportNo, caseTimes);

			endCaseService.batchModifyCaseBaseDTO(tempWholeCaseBaseDTO);
		} catch (Exception e) {
			throw new GlobalBusinessException(ErrorCode.RegisterCase.UPDATE_CASE_DATA_FAIL);
		}

		try {

			LogUtil.audit("#调收单查询接口#,reportNo:%s, caseTimes:%s", reportNo, caseTimes);
			AcceptRecordDTO acceptRecordDTO = acceptRecordService.getAcceptRecord(reportNo, caseTimes);

			if (acceptRecordDTO == null && caseTimes < 2) {
				LogUtil.audit("#单证不齐全,修改状态为待收单#,reportNo:%s, caseTimes:%s", reportNo, caseTimes);
				caseProcessService.updateCaseProcess(reportNo, caseTimes,
						ConfigConstValues.PROCESS_STATUS_PENDING_ACCEPT);
			} else if (acceptRecordDTO != null && caseTimes < 2) {
				LogUtil.audit("#单证齐全,修改状态为待审核#,reportNo:%s, caseTimes:%s", reportNo, caseTimes);
				caseProcessService.updateCaseProcess(reportNo, caseTimes,
						ConfigConstValues.PROCESS_STATUS_PENDING_AUDIT);
			} else {

			}
		} catch (Exception e) {
			throw new GlobalBusinessException(ErrorCode.RegisterCase.UPDATE_CASE_PROCESS_FAIL);
		}

		List<EstimateDutyRecordDTO> dutyRecordList = null;
		try {

			CaseRegisterApplyDTO caseRegisterApplyDTO = registerCaseService.getLastCaseRegisterApplyDTO(reportNo, caseTimes);
			if (caseRegisterApplyDTO != null && ConfigConstValues.AUDIT_PROCESSING.equals(caseRegisterApplyDTO.getStatus())) {
				LogUtil.audit("自动立案,有正在立案审批的案件, reportNo={}", reportNo);

				dutyRecordList = estimateDutyRecordService.getRecordsOfRegistCase(estimatePolicyList.get(0).getCaseNo(),
						caseTimes, EstimateConstValues.ESTIMATE_TYPE_REGISTRATION);

				setReportTrackRegisterCaseStatus(reportNo, caseTimes);

				caseRegisterApplyDTO.setStatus(AUDIT_END);
				CaseRegisterApplyVO caseRegisterApplyVO = new CaseRegisterApplyVO();
				BeanUtils.copyProperties(caseRegisterApplyDTO, caseRegisterApplyVO);
				caseRegisterApplyVO.setAuditUm(ConstValues.SYSTEM_UM);
				registerCaseService.modifyRegisterAuditStatus(caseRegisterApplyVO);
			} else {

				dutyRecordList = estimateDutyRecordService.getRecordsOfRegistCase(estimatePolicyList.get(0).getCaseNo(),
						caseTimes, EstimateConstValues.ESTIMATE_TYPE_REGISTRATION);
			}
		} catch (Exception e) {
			throw new GlobalBusinessException(ErrorCode.RegisterCase.QUERY_ESTIMATE_DATA_FAIL);
		}

		if (ListUtils.isEmptyList(dutyRecordList)) {
			LogUtil.audit("2#自动立案,暂存表不存在立案的数据,往暂存表插入立案的数据,reportNo={}, caseTimes={}#", reportNo, caseTimes);

			List<String> caseNoList = new ArrayList<String>();
			EstimateDutyRecordDTO estimateDutyRecordDTO = null;
			List<EstimateDutyRecordDTO> estimateDutyRecordList = new ArrayList<EstimateDutyRecordDTO>();
			if (ListUtils.isNotEmpty(estimatePolicyList)) {
				for (EstimatePolicyDTO estimatePolicyDTO : estimatePolicyList) {
					caseNoList.add(estimatePolicyDTO.getCaseNo());
				}
				List<EstimateDutyDTO> estimateDutyList = estimateService.getEstimateDutyDTOList(caseNoList, caseTimes);
				for (EstimateDutyDTO estimateDutyDTO : estimateDutyList) {
					estimateDutyRecordDTO = new EstimateDutyRecordDTO();
					BeanUtils.copyProperties(estimateDutyDTO, estimateDutyRecordDTO);
					estimateDutyRecordDTO.setChgPayValue(estimateDutyDTO.getEstimateAmount());
					String taskId = TacheConstants.REPORT_TRACK;

					boolean isNewProcess = caseProcessService.getIsNewProcess(reportNo, caseTimes);
					if (!isNewProcess) {
						taskId = TacheConstants.TEL_SURVEY;
					}
					estimateDutyRecordDTO.setTaskId(taskId);
					LogUtil.audit("#立案数据环节为#reportNo=" + reportNo + ",isNewProcess=" + isNewProcess + ",taskId=" + taskId);

					estimateDutyRecordDTO.setEstimateType(EstimateConstValues.ESTIMATE_TYPE_REGISTRATION);
					estimateDutyRecordList.add(estimateDutyRecordDTO);
				}

				try {
					estimateDutyRecordService.addEstimateDutyRecordList(estimateDutyRecordList);
				} catch (Exception e) {
					throw new GlobalBusinessException(ErrorCode.RegisterCase.BATCH_INSERT_TEAMP_ESTIMATE_FAIL);
				}
			}

			if (ListUtils.isNotEmpty(estimateDutyRecordList)) {

				this.asyncModifyEstimateData(reportNo, caseTimes);
			} else {
				LogUtil.audit("该报案号关联的保单在责任预估暂存表中没有相关数据");
				throw new GlobalBusinessException(ErrorCode.RegisterCase.ESTIMATE_DUTY_RECORD_HAS_NO_DATA);
			}
		} else {

			this.asyncModifyEstimateData(reportNo, caseTimes);
		}

	}

	private void asyncModifyEstimateData(String reportNo, Integer caseTimes) throws GlobalBusinessException {
		Map<String, String> taskParam = new HashMap<String, String>();
		taskParam.put(REPORTNO, reportNo);
		taskParam.put(CASETIMES, String.valueOf(caseTimes));
		taskParam.put("updateBy", SYSTEM);
		taskParam.put("autoRegister", EstimateConstValues.ESTIMATE_TYPE_REGISTRATION);
		LogUtil.audit("更新未决类型, reportNo:" + reportNo + ",caseTimes" + caseTimes);
//        asyncTaskService.createTask(SYSTEM, TaskTypeConst.AHCS_MODITY_ESTIAMTE_TYPE, taskParam);
	}

	private void isExistEstimateData(List<EstimatePolicyDTO> estimatePolicyList) throws GlobalBusinessException {
		if (ListUtils.isEmptyList(estimatePolicyList)) {
			throw new GlobalBusinessException(ErrorCode.RegisterCase.HAS_NO_POLICY_DATA);
		}

		List<EstimatePlanDTO> planList = new ArrayList<EstimatePlanDTO>();
		List<EstimateDutyRecordDTO> dutyList = new ArrayList<EstimateDutyRecordDTO>();
		for (EstimatePolicyDTO estimatePolicyDTO : estimatePolicyList) {
			planList.addAll(estimatePolicyDTO.getEstimatePlanList());
			for (EstimatePlanDTO planDTO : estimatePolicyDTO.getEstimatePlanList()) {
				dutyList.addAll(planDTO.getEstimateDutyRecordList());
			}
		}

		if (ListUtils.isEmptyList(planList)) {
			throw new GlobalBusinessException(ErrorCode.RegisterCase.HAS_NO_PLAN_DATA);
		}

		if (ListUtils.isEmptyList(dutyList)) {
			throw new GlobalBusinessException(ErrorCode.RegisterCase.HAS_NO_DUTY_DATA);
		}
	}

	private void setReportTrackRegisterCaseStatus(String reportNo, Integer caseTimes) {

	}

	@Override
	public void saveRegisterAuditInfo(CaseRegisterApplyVO caseRegisterApplyVO) {
		caseRegisterApplyDao.saveRegisterAuditInfo(caseRegisterApplyVO);
	}

	@Override
	public void registerForBatch(String reportNo,Integer caseTimes)throws GlobalBusinessException{
		List<EstimatePolicyDTO> estimatePolicyList = estimateService.getByReportNoAndCaseTimes(reportNo, caseTimes);
		List<EstimateDutyRecordDTO> dutyRecordList = estimateDutyRecordService.getRecordsOfRegistCase(estimatePolicyList.get(0).getCaseNo(),
				caseTimes, EstimateConstValues.ESTIMATE_TYPE_REGISTRATION);
		if(ListUtils.isNotEmpty(dutyRecordList)){
			return;
		}
		LogUtil.audit("暂存表不存在立案的数据,往暂存表插入立案的数据,reportNo={}, caseTimes={}#", reportNo, caseTimes);

		List<String> caseNoList = new ArrayList<String>();
		EstimateDutyRecordDTO estimateDutyRecordDTO = null;
		List<EstimateDutyRecordDTO> estimateDutyRecordList = new ArrayList<EstimateDutyRecordDTO>();
		if (ListUtils.isNotEmpty(estimatePolicyList)) {
			for (EstimatePolicyDTO estimatePolicyDTO : estimatePolicyList) {
				caseNoList.add(estimatePolicyDTO.getCaseNo());
			}
			List<EstimateDutyDTO> estimateDutyList = estimateService.getEstimateDutyDTOList(caseNoList, caseTimes);
			for (EstimateDutyDTO estimateDutyDTO : estimateDutyList) {
				estimateDutyRecordDTO = new EstimateDutyRecordDTO();
				BeanUtils.copyProperties(estimateDutyDTO, estimateDutyRecordDTO);

				String taskId = TacheConstants.REPORT_TRACK;
				estimateDutyRecordDTO.setTaskId(taskId);
				estimateDutyRecordDTO.setEstimateType(EstimateConstValues.ESTIMATE_TYPE_REGISTRATION);
				estimateDutyRecordList.add(estimateDutyRecordDTO);
			}

			try {
				estimateDutyRecordService.addEstimateDutyRecordList(estimateDutyRecordList);
			} catch (Exception e) {
				throw new GlobalBusinessException(ErrorCode.RegisterCase.BATCH_INSERT_TEAMP_ESTIMATE_FAIL);
			}
		}

	}

	@Override
	public List<String> getNoRegisterData(Integer configDays) {
		//查询报案超15天还未立案的报案好
		List<String> reportNoList = caseRegisterApplyDao.getNoRegisterData(configDays);
		return reportNoList;
	}
}
