package com.paic.ncbs.claim.controller.settle;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.settle.DutyDetailBillSettleRequest;
import com.paic.ncbs.claim.model.dto.settle.DutyDetailSettleRequest;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.vo.settle.SettlesFormVO;
import com.paic.ncbs.claim.service.common.ClmsCommonPolicyService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle.ClmsDutyDetailBillSettleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/settle/do/app/dutydeatilSettle")
public class ClmsDutyDetailBillSettleController {
    @Autowired
    private ClmsDutyDetailBillSettleService clmsDutyDetailBillSettleService;

    @Autowired
    private  ClmsCommonPolicyService clmsCommonPolicyService;
    @PostMapping("/updateDutyDetailBillSettle")
    public ResponseResult<Object> updateDutyDetailBillSettle(@RequestBody DutyDetailBillSettleRequest request){
        log.info("手动修改责任明细下的发票理算金额报案号={},修改入参={}",request.getReportNo(), JsonUtils.toJsonString(request));
        clmsDutyDetailBillSettleService.updateListById(request.getDetailBillSettleList());
        return  ResponseResult.success();
    }
    @PostMapping("/autoAllocation")
    public ResponseResult<Object> autoAllocation(@RequestBody DutyDetailSettleRequest request){
        log.info("手动修改责任明细的理算金额自动分摊到日限额数据报案号={},入参={}",request.getReportNo(), JsonUtils.toJsonString(request));
        List<ClmsDutyDetailBillSettleDTO> reponseData = clmsDutyDetailBillSettleService.autoAllocation(request);
        return  ResponseResult.success(reponseData);
    }
    @GetMapping("/test/{reportNo}/{caseTimes}")
    public ResponseResult<Object> test(@PathVariable("reportNo") String reportNo,@PathVariable("caseTimes") Integer caseTimes){
        log.info("手动修改责任明细的理算金额自动分摊到日限额数据报案号={}",reportNo);
        ClmsDutyDetailBillSettleDTO dto=new ClmsDutyDetailBillSettleDTO();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        //dto.setApprovalStatus("1");
        clmsDutyDetailBillSettleService.updateClmsDutyDetailBillSettle(dto);
        return  ResponseResult.success();
    }
    @ApiOperation(value = "理算发送")
    @PostMapping(value = "/TestSubmit")
    public ResponseResult<Object> addPolicyPay(@RequestBody SettlesFormVO settlesFormDTO) throws GlobalBusinessException {
        LogUtil.audit("理算发送报案号{},入参{}",settlesFormDTO.getReportNo(), JSON.toJSONString(settlesFormDTO));
        clmsCommonPolicyService.updatePolicyPays(settlesFormDTO.getPolicyPayArr());
        return ResponseResult.success();
    }
    @ApiOperation(value = "测试核赔校验年年免赔")
    @GetMapping("/testRemiamount/{reportNo}/{caseTimes}")
    public ResponseResult<Object> testRemiamount(@PathVariable("reportNo") String reportNo,@PathVariable("caseTimes") Integer caseTimes) throws GlobalBusinessException {

        clmsDutyDetailBillSettleService.checkRemimount(reportNo,caseTimes);
        return ResponseResult.success();
    }

}
