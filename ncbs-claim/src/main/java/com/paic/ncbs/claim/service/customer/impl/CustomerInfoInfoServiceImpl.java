package com.paic.ncbs.claim.service.customer.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.enums.NonCleanMoneyCerTypeEnum;
import com.paic.ncbs.claim.common.util.XmlUtils;
import com.paic.ncbs.claim.config.CdpConfig;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.other.BaseDataMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.customer.*;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.sao.CustomerInfoStoreSAO;
import com.paic.ncbs.claim.service.customer.CustomerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 获取客客户信息
 */
@Slf4j
@Service
@RefreshScope
public class CustomerInfoInfoServiceImpl implements CustomerInfoService {

    /**
     * 是否调用global开关
     */
    @Value("${global.clientNo.switch:true}")
    private Boolean globalClientNoSwitch;
    /**
     * global环境地址
     * 正式环境：http://**************
     * 测试环境：http://**************:31561
     */
    @Value("${global.env.address:http://**************:31561}")
    private String globalEnvAddress;
    /**
     * global接口地址
     * /ChnWeb/gt/auto.do?method=saveIndividualCustomer 保存个人客户信息
     *
     */
    @Value("${global.individual.interface.url:/ChnWeb/gt/auto.do?method=saveIndividualCustomer}")
    private String globalIndividualCustomerInterfaceUrl;
    /**
     * global接口地址
     * /ChnWeb/gt/auto.do?method=saveCompanyCustomer 保存企业客户信息
     */
    @Value("${global.company.interface.url:/ChnWeb/gt/auto.do?method=saveCompanyCustomer}")
    private String globalCompanyCustomerInterfaceUrl;
    @Autowired
    private CdpConfig cdpConfig;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private PaymentInfoMapper paymentInfoMapper;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private BaseDataMapper baseDataMapper;
    @Autowired
    private CustomerInfoStoreSAO customerInfoStoreSAO;

    /**
     *
     * @param customerName
     * @param bankAccountAttribute  帐号类型:个人帐号=1,公司帐号=0
     * @param cardType
     * @param idNo
     * @return
     */
    @Override
    public String getCustomerNo(String customerName, String bankAccountAttribute, String cardType,String idNo) {
        log.info("调用global开关状态: {}",globalClientNoSwitch);
        String customerNo;
        //调用global获取客户号
        if (!globalClientNoSwitch){
            customerNo = idNo;//测试用
            log.info("调用global开关已关闭 ......");
            return customerNo;
        }
        customerNo = getCustomerNoFromClientInfo(customerName, cardType, idNo);
        if (Objects.nonNull(customerNo)) {
            return customerNo;
        }
        if(cdpConfig.getOpen()){
            // CDPt系统获取客户号
            customerNo = generateCDPClientNo(customerName,cardType,idNo,bankAccountAttribute);
        } else {
            //ply_client_info表不存在客户信息，调用接口获取新客户号
            GlobalSendDTO sendDTO = GlobalSendDTO.builder()
                    .name(customerName)
                    .certificateType(cardType)
                    .certificateNo(idNo)
                    .build();
            customerNo = generateGobalClientNo(sendDTO,bankAccountAttribute);
        }

        if (StringUtils.isBlank(customerNo)) {
            log.error("调用global生成客户号异常， idNo:{},",idNo);
           // throw new GlobalBusinessException("global生成客户号异常"); 不要影响主流程，注释
        } else {
            try {
                ClientManagementDTO clientManagementDTO = new ClientManagementDTO();
                clientManagementDTO.setName(customerName);
                clientManagementDTO.setCertificateNo(idNo);
                clientManagementDTO.setCertificateType(cardType);
                clientManagementDTO.setClientNo(customerNo);
                clientManagementDTO.setNewClientNo(customerNo);
                ocasMapper.saveClientNo(clientManagementDTO);
            } catch (Exception e) {
                log.error("客户号插入异常！");
            }
        }
        return customerNo;
    }

    /**
     * 查询ply_client_info表是否存在
     * @param customerName
     * @param cardType
     * @param idNo
     * @return
     */
    private String getCustomerNoFromClientInfo(String customerName, String cardType, String idNo) {
        ReportCustomerInfoEntity reportCustomerInfo = new ReportCustomerInfoEntity();
        reportCustomerInfo.setName(customerName);
        reportCustomerInfo.setCertificateType(cardType);
        reportCustomerInfo.setCertificateNo(idNo);
        return ocasMapper.getClientNo(reportCustomerInfo);
    }

    @Override
    public void regenerateCustomerNo() throws GlobalBusinessException {
        long start = System.currentTimeMillis();
        try {
            List<PaymentInfoDTO> paymentInfoDTOList = paymentInfoMapper.queryCustomerNoToSupplementedFromInfo();
            if (!CollectionUtils.isEmpty(paymentInfoDTOList)){
                paymentInfoDTOList.stream().forEach(v ->{
                    String customerNo = getCustomerNo(v.getClientName(),v.getBankAccountAttribute(),v.getClientCertificateType(),v.getClientCertificateNo());
                    v.setCustomerNo(customerNo);
                    v.setUpdatedBy("system");
                });
                paymentInfoMapper.batchUpdate(paymentInfoDTOList);
            }
            List<PaymentItemDTO> paymentItemDTOList = paymentItemMapper.queryCustomerNoToSupplementedFromItem();
            if (!CollectionUtils.isEmpty(paymentItemDTOList)){
                paymentItemDTOList.stream().forEach(v ->{
                    String customerNo = getCustomerNo(v.getClientName(),v.getBankAccountAttribute(),v.getClientCertificateType(),v.getClientCertificateNo());
                    v.setCustomerNo(customerNo);
                    v.setUpdatedBy("system");

                });
                paymentItemMapper.batchUpdate(paymentItemDTOList);
            }
        } catch (Exception e){
            log.error("历史客户号补生成失败:",e);
        }
        long end = System.currentTimeMillis();
        log.info("补历史客户号regenerateCustomerNo耗时:{}", end - start);
    }

    private String generateGobalClientNo(GlobalSendDTO sendDTO, String bankAccountAttribute) {
        log.info("开始生成global客户号：sendDTO:{}", JSONObject.toJSON(sendDTO));
        String customerNo;
        if ("0".equals(bankAccountAttribute)){
            log.info("客户是法人 。。。");
            customerNo = saveCompanyCustomer(sendDTO);
        } else {
            log.info("客户是自然人 "+sendDTO.getCertificateType()+"。。。");
            customerNo = saveIndividualCustomer(sendDTO);
        }
        log.info("结束生成global客户号：customerNo:{}", customerNo);
        return customerNo;
    }

    /**
     * Global保存个人客户信息
     * @param sendDTO
     * @return
     */
    private String saveIndividualCustomer(GlobalSendDTO sendDTO){
        String sendParam = getIndividualCustomerParam(sendDTO);
        log.info("组装global个人信息结果  sendParam:{}",JSONObject.toJSON(sendParam));

        String url = globalEnvAddress + globalIndividualCustomerInterfaceUrl;
        log.info("global-saveIndividualCustomer请求地址  url:{}",url);

        String result = customerInfoStoreSAO.sendGobal(url, sendParam);
        log.info("global-saveIndividualCustomer请求结果 result:{}",result);
        if (Objects.isNull(result)){
            return null;
        }
        GlobalPacketDTO globalPacketDTO = (GlobalPacketDTO) XmlUtils.xmlToBean(result, GlobalPacketDTO.class);
        log.info("GlobalPacketDTO转换后参数:{}",JSONObject.toJSON(globalPacketDTO));
        if ("1".equals(globalPacketDTO.getHead().getAppresCode())){
            return globalPacketDTO.getBody().getCustomerCode();
        } else {
            return null;
        }
    }

    /**
     * Global系统处理结果 成功时的返回报文
     * @return
     */
    private String getIndividualCustomerParam(GlobalSendDTO sendDTO){
        log.info("开始组装global个人信息入参  sendDTO:{}",JSONObject.toJSON(sendDTO));
        SendIndividualGlobalPacketDTO sendParam = new SendIndividualGlobalPacketDTO();

        SendIndividualGlobalHeadDTO head = new SendIndividualGlobalHeadDTO();
        head.setUserCode("SYSTEM");
        //03	个人客户
        //02	企业客户
        head.setRequestType("03");
        //非车代码CODE：04
        head.setSystemCode("04");


        SendIndividualGlobalInputDTO inputDTO = new SendIndividualGlobalInputDTO();
        //客户信息操作类型的标识代码, I:统一为插入I ，U:更新 暂时不用
        inputDTO.setRequestType("I");
        inputDTO.setCustomercName(sendDTO.getName());
        //证件映射
        String newCertificateType = NonCleanMoneyCerTypeEnum.getNameByCode(sendDTO.getCertificateType());//自然人证件类型
        inputDTO.setIdentifyType(newCertificateType);
        inputDTO.setIdentifyNumber(sendDTO.getCertificateNo());
        // 文档中固定中国： 1120006 中国人
        inputDTO.setCustomerkKind("1120006");
        // CHN 中国 默认
        if (!StringUtils.isEmpty(sendDTO.getNationality())){
            String newNation = baseDataMapper.queryOutMapByOldCode(sendDTO.getNationality());
            inputDTO.setNationalityCode(newNation);
        } else {
            inputDTO.setNationalityCode("CHN");
        }

        inputDTO.setPhoneNumber("");
        inputDTO.setMobile("");

        SendIndividualGlobalBodyDTO body = new SendIndividualGlobalBodyDTO();
        body.setSendIndividualGlobalInputDTO(inputDTO);

        sendParam.setHead(head);
        sendParam.setBody(body);

        return XmlUtils.beanToXml(sendParam, SendIndividualGlobalPacketDTO.class);
    }

    /**
     * Global保存企业客户信息
     * @param sendDTO
     * @return
     */
    private String saveCompanyCustomer(GlobalSendDTO sendDTO){
        String sendParam = getCompanyParam(sendDTO);
        log.info("组装global公司信息结果  sendParam:{}",JSONObject.toJSON(sendParam));
        String url = globalEnvAddress + globalCompanyCustomerInterfaceUrl;
        log.info("global - saveCompanyCustomer -请求地址  url:{}",url);

        String result = customerInfoStoreSAO.sendGobal(url, sendParam);

        log.info("global-saveCompanyCustomer result:{}",result);
        if (Objects.isNull(result)){
            return null;
        }
        GlobalPacketDTO globalPacketDTO = (GlobalPacketDTO) XmlUtils.xmlToBean(result, GlobalPacketDTO.class);
        if (globalPacketDTO.getHead().getAppresCode().equals("1")){
            return globalPacketDTO.getBody().getCustomerCode();
        } else {
            return null;
        }
    }

    /**
     * 组装公司信息
     * @param sendDTO
     * @return
     */
    private String getCompanyParam(GlobalSendDTO sendDTO){
        log.info("开始组装global公司信息入参  sendDTO:{}",JSONObject.toJSON(sendDTO));
        SendCompanyGlobalPacketDTO sendParam = new SendCompanyGlobalPacketDTO();
        SendCompanyGlobalHeadDTO head = new SendCompanyGlobalHeadDTO();
        head.setUserCode("SYSTEM");
        //03:个人客户 02:企业客户
        head.setRequestType("02");
        //非车代码CODE：04
        head.setSystemCode("04");

        SendCompanyGlobalInputDTO inputDTO = new SendCompanyGlobalInputDTO();
        inputDTO.setRequestType("I");
        inputDTO.setCustomercName(sendDTO.getName());
        // 不传默认：1110001-韩资企业(非三星)-其他
        inputDTO.setCustomerkKind("");
        // 优先传统一社会证信用代码  组织机构代码（9）/统一社会信用证代码（18）
        inputDTO.setOrganizeCode(sendDTO.getCertificateNo());
        inputDTO.setPhoneNumber("");
        inputDTO.setFaxNumber("");
        inputDTO.setComCode("");

        SendCompanyGlobalBodyDTO body = new SendCompanyGlobalBodyDTO();
        body.setSendCompanyGlobalInputDTO(inputDTO);

        sendParam.setHead(head);
        sendParam.setBody(body);

        return XmlUtils.beanToXml(sendParam, SendCompanyGlobalPacketDTO.class);
    }

    /**
     *
     * @param customerName
     * @param idType
     * @param idCode
     * @param customerType  0:表示法人、其它表示自然人
     * @return
     */
    private String generateCDPClientNo(String customerName,String idType,String idCode, String customerType) {
        customerType = this.transCDPCustomerType(customerType);
        idType = this.transCDPIdType(idType);

        String requestId = MDC.get(BaseConstant.REQUEST_ID);
        JSONObject dataJson = new JSONObject();
        dataJson.put("idType",idType);
        dataJson.put("idCode",idCode);
        dataJson.put("customerName",customerName);
        JSONObject reqJsonOb = new JSONObject();
        reqJsonOb.put("requestId",requestId);
        reqJsonOb.put("customerType",customerType);
        reqJsonOb.put("data", Collections.singletonList(dataJson));
        String customerNo = "";
        String url = cdpConfig.getMemberCodeUrl();
        log.info("cdp系统获取客户号入参：{}", reqJsonOb.toJSONString());
        String  respJsonStr = customerInfoStoreSAO.sendCDP(url, reqJsonOb.toJSONString());
        log.info("cdp系统获取客户号返回：{}", respJsonStr);
        try {
            JSONObject respJsonObj = JSONObject.parseObject(respJsonStr);
            if("0".equals(respJsonObj.getString("code"))){
                JSONArray dataArr = respJsonObj.getJSONArray("data");
                if(null != dataArr && !dataArr.isEmpty()){
                    customerNo = dataArr.getJSONObject(0).getString("memberCode");
                }
            }
        } catch (Exception e) {
            customerNo = "";
            log.warn("解析CDP返回客户号异常：",e);
        }
        log.info("结束生成CDP客户号：customerNo:{}", customerNo);
        return customerNo;
    }

    /**
     * 转换为CDP系统客户类型
     * @param customerType
     * @return
     */
    private String transCDPCustomerType(String customerType) {
        // CDP 1、个人客户；2、企业客户  :: 系统代码需要转换 个人帐号=1,公司帐号=0
        return "0".equals(customerType)?"2":"1";
    }

    /**
     * 转换为CDP系统证件类型
     * @param certificateType
     * @return
     */
    private String transCDPIdType(String certificateType) {
        /* CDP代码
         * 01	身份证
         * 02	驾驶执照
         * 03	港澳居民来往通行证
         * 04	外国人永久居住证
         * 05	台湾居民来往通行证
         * 06	军官证
         * 07	户口簿
         * 08	外国护照
         * 09	中国护照
         * 10	士兵证
         * 11	出生证
         * 12	韩国籍身份证件
         * 13	港澳台居民居住证
         * 00	个人其他证件
         * 61	统一社会信用代码
         * 62	组织机构代码证
         * 63	营业执照
         * 64	税务登记证
         * 99	企业其他证件
         */

        /*
         * 系统证件代码：1
         * 01	身份证
         * 02	护照
         * 05	驾驶执照
         * 06	港澳居民来往内地通行证
         * 08	外国人永久居留身份证
         * 10	台湾居民来往大陆通行证
         * 12	军官证
         * 13	户口薄
         * 14	外国护照
         * 95	其他证件
         * 系统证件代码：2
         *   { value: '71', label: '组织机构代码' },
         *   { value: '72', label: '税务登记证' },
         *   { value: '73', label: '其他证件' },
         *   { value: '74', label: '营业执照' },
         *   { value: '75', label: '统一社会信用代码' }
         */
        String idType = "";
        switch (certificateType) {
            case "01":
                idType = "01";
                break;
            case "02":
                idType = "09";
                break;
            case "05":
                idType = "02";
                break;
            case "06":
                idType = "03";
                break;
            case "08":
                idType = "04";
                break;
            case "10":
                idType = "05";
                break;
            case "12":
                idType = "06";
                break;
            case "13":
                idType = "07";
                break;
            case "14":
                idType = "08";
                break;
            case "95":
                idType = "00";
                break;
            case "71":
                idType = "62";
                break;
            case "72":
                idType = "64";
                break;
            case "73":
                idType = "99";
                break;
            case "74":
                idType = "63";
                break;
            case "75":
                idType = "61";
                break;
            default:
                idType = "";
        }
        return idType;
    }
}
