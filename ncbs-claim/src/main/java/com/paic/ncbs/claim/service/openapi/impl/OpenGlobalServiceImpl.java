package com.paic.ncbs.claim.service.openapi.impl;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.model.vo.doc.GlobalOutTimeCaseRespVO;
import com.paic.ncbs.claim.service.openapi.OpenGlobalService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.ResourceHttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.converter.xml.MappingJackson2XmlHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class OpenGlobalServiceImpl implements OpenGlobalService {
    @Override
    public String openGloabl(String url,String json){
        String res = null;
        try {
            RestTemplate restTemplate = new RestTemplate();
            //其他类型转换器配置
            List<HttpMessageConverter<?>> converters = new ArrayList<>();
            converters.add(new MappingJackson2HttpMessageConverter());
            converters.add(new StringHttpMessageConverter());
            converters.add(new MappingJackson2XmlHttpMessageConverter());
            converters.add(new ByteArrayHttpMessageConverter());
            converters.add(new ResourceHttpMessageConverter());
            //设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));

            //创建请求实体
            HttpEntity<String> request = new HttpEntity<>(json,headers);
            //发送post请求
            ResponseEntity<String> response = restTemplate.postForEntity(url,request,String.class);
            //回参解析
            res = response.getBody();
        }catch (Exception e){
            log.error("调用接口"+url+"异常："+e.getMessage()+"入参："+json);
            throw new RuntimeException("调用接口"+url+"异常："+e.getMessage());
        }
        return res;
    };

}
