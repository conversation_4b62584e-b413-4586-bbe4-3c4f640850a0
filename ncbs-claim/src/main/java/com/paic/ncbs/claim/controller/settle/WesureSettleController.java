package com.paic.ncbs.claim.controller.settle;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.service.settle.WesureSettleService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;

/**
 * 微保理算
 */
@RestController
@RequestMapping("/public/app/wesureSettle")
public class WesureSettleController {
    @Resource(name = "wesureSettleService")
    private WesureSettleService wesureSettleService;

    /**
     * 查询已理赔金额
     * @param wesureClaimedDTO
     * @return
     */
    @PostMapping("/getClaimedAmount")
    public ResponseResult<WesureClaimedDTO> getClaimedAmount(@RequestBody WesureClaimedDTO wesureClaimedDTO){
        checkClaimedParams(wesureClaimedDTO);
        return ResponseResult.success(wesureSettleService.getClaimedAmount(wesureClaimedDTO));
    }

    private void checkClaimedParams(WesureClaimedDTO wesureClaimedDTO){
        if(StringUtils.isEmptyStr(wesureClaimedDTO.getPolicyNo())){
            throw new GlobalBusinessException("保单号不能为空");
        }

        if(StringUtils.isEmptyStr(wesureClaimedDTO.getInsurantCredentialNo())){
            throw new GlobalBusinessException("证件号不能为空");
        }

        if(ListUtils.isEmptyList(wesureClaimedDTO.getBenefitList())){
            throw new GlobalBusinessException("责任子项不能为空");
        }
        for (WesureBenefitDTO wesureBenefitDTO : wesureClaimedDTO.getBenefitList()) {
            if(StringUtils.isEmptyStr(wesureBenefitDTO.getBenefitCode())){
                throw new GlobalBusinessException("责任子项编码不能为空");
            }
        }
    }

    /**
     * 查询发票是否已理赔
     * @param wesureReceiptDTO
     * @return
     */
    @PostMapping("/getReceiptStatus")
    public ResponseResult<WesureReceiptDTO> getReceiptStatus(@RequestBody WesureReceiptDTO wesureReceiptDTO){
        checkReceiptStatusParams(wesureReceiptDTO);
        return ResponseResult.success(wesureSettleService.getReceiptStatus(wesureReceiptDTO));
    }

    private void checkReceiptStatusParams(WesureReceiptDTO wesureReceiptDTO){

        if(ListUtils.isEmptyList(wesureReceiptDTO.getReceiptList())){
            throw new GlobalBusinessException("票据清单不能为空");
        }
        for (WesureReceiptClaimDTO receiptClaimDTO : wesureReceiptDTO.getReceiptList()) {
            if(StringUtils.isEmptyStr(receiptClaimDTO.getReceiptNo())){
                throw new GlobalBusinessException("票据号码不能为空");
            }
        }
    }

    /**
     * 同步微保自动理算结果
     * @param wesureSettleDTO
     * @return
     */
    @PostMapping("/saveSettleDetail")
    public ResponseResult saveSettleDetail(@RequestBody WesureSettleDTO wesureSettleDTO){
        checkSettleDetailParams(wesureSettleDTO);
        wesureSettleDTO.setSyncType(Constants.WESURE_SYNC_TYPE_SETTLE);
        wesureSettleService.buildWesureSettle(wesureSettleDTO);
        wesureSettleService.saveSettleDetail(wesureSettleDTO);
        return ResponseResult.success();
    }

    private void checkSettleDetailParams(WesureSettleDTO wesureSettleDTO){
        if(StringUtils.isEmptyStr(wesureSettleDTO.getReportNo())){
            throw new GlobalBusinessException("保司理赔报案号不能为空");
        }
        String conclusion = wesureSettleDTO.getCaseConclusion();
        if(StringUtils.isEmptyStr(conclusion)){
            throw new GlobalBusinessException("案件结论不能为空");
        }
        if(!conclusion.startsWith("1")){
            if(StringUtils.isEmptyStr(wesureSettleDTO.getConclusionDetailCode())
                    || StringUtils.isEmptyStr(wesureSettleDTO.getConclusionDetailMsg())){
                throw new GlobalBusinessException("理赔结论详细描述不能为空");
            }
        }else{
            if(null == wesureSettleDTO.getCasePayAmount()){
                throw new GlobalBusinessException("案件总金额不能为空");
            }
            if(ListUtils.isEmptyList(wesureSettleDTO.getPolicyBenefitList())){
                throw new GlobalBusinessException("保单责任不能为空");
            }
            for (WesurePolicyDTO policy : wesureSettleDTO.getPolicyBenefitList()) {
                if(StringUtils.isEmptyStr(policy.getPolicyNo())){
                    throw new GlobalBusinessException("保司保单号不能为空");
                }
                if(null == policy.getPolicyPayAmount()){
                    throw new GlobalBusinessException("保单理赔总金额不能为空");
                }
                if(ListUtils.isEmptyList(policy.getBenefitPayList())){
                    throw new GlobalBusinessException("责任子项不能为空");
                }
                for (WesureBenefitDTO benefit : policy.getBenefitPayList()) {
                    if(StringUtils.isEmptyStr(benefit.getBenefitCode())){
                        throw new GlobalBusinessException("责任子项编码不能为空");
                    }
                    if(StringUtils.isEmptyStr(benefit.getBenefitName())){
                        throw new GlobalBusinessException("责任子项名称不能为空");
                    }
                    if(null == benefit.getBenefitAdjustAmount()){
                        throw new GlobalBusinessException("责任子项合理金额不能为空");
                    }
                    if(null == benefit.getBenefitPayAmount()){
                        throw new GlobalBusinessException("责任子项理赔金额不能为空");
                    }

                }
            }
            if(ListUtils.isNotEmpty(wesureSettleDTO.getReceiptToBenefitList())){
                for (WesureReceiptDetailDTO receipt : wesureSettleDTO.getReceiptToBenefitList()) {
                    if(StringUtils.isEmptyStr(receipt.getReceiptNo())){
                        throw new GlobalBusinessException("票据号码不能为空");
                    }
                    if(StringUtils.isEmptyStr(receipt.getBenefitCode())){
                        throw new GlobalBusinessException("票据关联的责任子项编码不能为空");
                    }
                }
            }
        }
    }

    /**
     * 同步微保复核结果
     * @param wesureSettleDTO
     * @return
     */
    @PostMapping("/getSettleReview")
    public ResponseResult<WesureSettleDTO> getSettleReview(@RequestBody WesureSettleDTO wesureSettleDTO){
        LogUtil.audit("查询核心复核结论入参reportNo={}",wesureSettleDTO.getReportNo());
        checkSettleReviewParams(wesureSettleDTO);
        wesureSettleDTO.setSyncType(Constants.WESURE_SYNC_TYPE_REVIEW);
        wesureSettleDTO.setCaseTimes(1);
        WesureSettleDTO wesureSettleReview = wesureSettleService.getSettle(wesureSettleDTO);
        if(wesureSettleReview != null){
            wesureSettleReview.setCalculateTime(null);
            wesureSettleReview.setAuditMsg("");
            if(wesureSettleReview.getConclusionDetailCode() == null){
                wesureSettleReview.setConclusionDetailCode(0);
            }
            if(wesureSettleReview.getConclusionDetailMsg() == null){
                wesureSettleReview.setConclusionDetailMsg("");
            }
            if(wesureSettleReview.getPolicyBenefitList() == null){
                wesureSettleReview.setPolicyBenefitList(new ArrayList<>());
            }
            if(wesureSettleReview.getReceiptToBenefitList() == null){
                wesureSettleReview.setReceiptToBenefitList(new ArrayList<>());
            }else{
                for (WesureReceiptDetailDTO receiptDetailDTO : wesureSettleReview.getReceiptToBenefitList()) {
                    receiptDetailDTO.setBenefitCode("");
                }
            }
        }
        LogUtil.audit("查询核心复核结论出参reportNo={},{}",wesureSettleDTO.getReportNo(), JSON.toJSON(wesureSettleReview));
        return ResponseResult.success(wesureSettleReview);
    }

    private void checkSettleReviewParams(WesureSettleDTO wesureSettleDTO){
        if(StringUtils.isEmptyStr(wesureSettleDTO.getReportNo())){
            throw new GlobalBusinessException("保司理赔报案号不能为空");
        }

    }

    /**
     * 同步微保医疗单据
     * @param wesureMedicalSyncDTO
     * @return
     */
    @PostMapping("/saveMedical")
    public ResponseResult saveMedical(@RequestBody WesureMedicalSyncDTO wesureMedicalSyncDTO){
        checkMedicalParams(wesureMedicalSyncDTO);
        wesureSettleService.buildWesureMedical(wesureMedicalSyncDTO);
        wesureSettleService.saveMedical(wesureMedicalSyncDTO);
        return ResponseResult.success();
    }

    private void checkMedicalParams(WesureMedicalSyncDTO wesureMedicalSyncDTO){
        if(StringUtils.isEmptyStr(wesureMedicalSyncDTO.getWesureReportNo())){
            throw new GlobalBusinessException("微保理赔报案号不能为空");
        }
        if(StringUtils.isEmptyStr(wesureMedicalSyncDTO.getReportNo())){
            throw new GlobalBusinessException("保司理赔报案号不能为空");
        }
        if(StringUtils.isEmptyStr(wesureMedicalSyncDTO.getMedicalSource())){
            throw new GlobalBusinessException("医疗数据来源不能为空");
        }
        if(ListUtils.isEmptyList(wesureMedicalSyncDTO.getMedicalList())){
            throw new GlobalBusinessException("医疗单据不能为空");
        }
        for (WesureMedicalDTO medical : wesureMedicalSyncDTO.getMedicalList()) {
            if(StringUtils.isEmptyStr(medical.getVisitType())){
                throw new GlobalBusinessException("就诊类型不能为空");
            }
            if(null == medical.getVisitAmount()){
                throw new GlobalBusinessException("就诊金额不能为空");
            }

            if(ListUtils.isNotEmpty(medical.getReceiptList())){
                for (WesureMedicalReceiptDTO receipt : medical.getReceiptList()) {
                    if(StringUtils.isEmptyStr(receipt.getReceiptNo())){
                        throw new GlobalBusinessException("票据号码不能为空");
                    }
                    if(StringUtils.isEmptyStr(receipt.getReceiptClass())){
                        throw new GlobalBusinessException("材料种类不能为空");
                    }
                    if(StringUtils.isEmptyStr(receipt.getReceiptType())){
                        throw new GlobalBusinessException("材料类型不能为空");
                    }
                    if(null == receipt.getTotalPay()){
                        throw new GlobalBusinessException("发票总金额不能为空");
                    }
                    if(StringUtils.isEmptyStr(receipt.getHospitalName())){
                        receipt.setHospitalName("其他");
                    }
                }
            }
        }
        if(ListUtils.isNotEmpty(wesureMedicalSyncDTO.getDiseaseList())){
            for (WesureMedicalDiseaseDTO disease : wesureMedicalSyncDTO.getDiseaseList()) {
                if(StringUtils.isEmptyStr(disease.getDiseaseName())){
                    throw new GlobalBusinessException("疾病名称不能为空");
                }
            }
        }

    }

    @PostMapping("/testEndCase")
    public ResponseResult testEndCase(@RequestBody WholeCaseBaseDTO wholeCaseBaseDTO){
        wholeCaseBaseDTO.setEndCaseDate(new Date());
        wesureSettleService.generateWesureVerify(wholeCaseBaseDTO);
        return ResponseResult.success();
    }

}
