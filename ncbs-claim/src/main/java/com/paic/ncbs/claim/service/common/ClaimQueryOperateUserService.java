package com.paic.ncbs.claim.service.common;

import com.paic.ncbs.claim.model.vo.taskdeal.UserWithTaskCountVO;

/**
 * 查询保单管理机构（根据保单号查询保单所属机构）
 */
public interface ClaimQueryOperateUserService {
    /**
     * 根据保单号查询
     * @param policyNo
     * @return
     */
    public UserWithTaskCountVO getDepartMentCodeAnyOne(String taskDefinitionBpmKey, String rerportNo, String policyNo, String departMentCode);
}
