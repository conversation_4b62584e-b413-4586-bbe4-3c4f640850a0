package com.paic.ncbs.claim.replevy.vo;

import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyMain;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ReplevyApiVo {
	//页面初始化标志 1代表追偿主页面初始化，2代表直接理赔费用初始化，3代表追偿子页面
	//F代表费用审批，Z代表追偿审批
	private String initFlag;
	/**报案号*/
	private String reportNo;
	/**主信息id*/
	private String replevyId;
	/**保存或者提交标志*/
	private String submitFlag;
	/**业务动作*/
	private String opinionType;
	/**审核意见*/
	private String approveOpinion;
	/**意见说明*/
	private String handleText;
	/**保单号*/
	private String policyNo;
	/**追偿操作员*/
	private String handlerCode;
	/**追偿案件号*/
	private String replevyNo;
	/**
	 * 操作标志C-新增，U-修改，D-删除，V-查看
	 */
	private String operateFlag;
	/**机构代码*/
	//private String comCode;
	/**追偿主表信息*/
	private ClmsReplevyMainVo replevyMainVo;
	/**追偿明细信息*/
	private ClmsReplevyDetailVo replevyDetailVo;
	/**追偿费用信息*/
	private ClmsReplevyChargeVo replevyChargeVo;
	/**追偿费用信息列表*/
	private List<ClmsReplevyChargeVo> replevyChargeVoList;
	/**追偿收入信息*/
	private List<ClmsReplevyLossVo> replevyLossVoList;
	/**追偿明细集合*/
	private List<ClmsReplevyDetailVo> replevyDetailVoList;
	/**追偿意见信息*/
	private ClmsReplevyTextVo replevyTextVo;
	/**追偿意见信息集合*/
	private List<ClmsReplevyTextVo> replevyTextVoList;
	/**节点名称**/
	//private String nodeTypeCName;
	/**是否追偿完毕标志位*/
	private String flag;
	/**是否可以自动金额拆分*/
	//private String isPayMentEx;//1--不可以自动金额拆分2--可以自动金额拆分
	/**承保信息基础币别*/
	//private String baseCurrency;
	/**赔付次数*/
	private Integer caseTimes;
	/**追偿次数*/
	private Integer replevyTimes;
	//领款人信息
	private PaymentInfoVO paymentInfoVo;
	//发票信息
	private InvoiceInfoDTO invoiceInfoDTO;
	//关联实收
	private List<ClmsRelatedActualReceiptVo> relatedActualReceiptVoList;
	//追偿明细主键ID
	private String replevyDetailId;
	//费用信息id
	private String replevyChargeId;
	//关联实收主键ID
	private String relatedActualReceiptId;
	//追偿损失主键ID
	private String relatedLossId;
	//收付流水号
	private String paymentRecordNo;
	//估损责任记录列表
	private List<EstimateDutyRecordDTO> estimateDutyRecordList;
	/**立案号*/
	private String claimNo;

	private BigDecimal sumRepleviedMoney;

	private ClmsReplevyMain clmsReplevyMain;

	/**追偿险种集合*/
	private List<PlanPayDTO> planPayList;
	/**追偿责任集合*/
	private List<DutyPayDTO> dutyPayList;

	/**报案号历史追偿费用信息列表*/
	private List<ClmsReplevyChargeVo> historyReplevyChargeVoList;
	/**报案号历史追偿明细集合*/
	private List<ClmsReplevyDetailVo> historyReplevyDetailVoList;
	/**报案号历史追偿集合*/
	private List<ClmsReplevyMainVo> historyReplevyMainVoList;


}
