package com.paic.ncbs.claim.service.settle.factor.impl.calculate;

import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.service.settle.factor.abstracts.calculate.AbstractCalculateAmountFactor;
import org.springframework.stereotype.Service;


/**
 * 合理费用因子实现
 */
@Service
public class CalculateAmountFactorImpl extends AbstractCalculateAmountFactor {
    @Override
    public boolean isBeforBoolean(CalculateParamsDTO paramsDTO) {
        return true;
    }
}
