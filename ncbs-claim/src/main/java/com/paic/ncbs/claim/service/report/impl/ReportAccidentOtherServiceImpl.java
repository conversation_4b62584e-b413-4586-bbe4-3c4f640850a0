package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentOtherEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentOtherMapper;
import com.paic.ncbs.claim.service.report.ReportAccidentOtherService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportAccidentOtherServiceImpl extends BaseServiceImpl<ReportAccidentOtherEntity> implements ReportAccidentOtherService {

    @Autowired
    private ReportAccidentOtherMapper reportAccidentOtherMapper;

    @Override
    public BaseDao<ReportAccidentOtherEntity> getDao() {
        return reportAccidentOtherMapper;
    }

    @Override
    public ReportAccidentOtherEntity getReportAccidentOtherByReportNo(String reportNo) {
        return reportAccidentOtherMapper.getReportAccidentOtherByReportNo(reportNo);
    }
}
