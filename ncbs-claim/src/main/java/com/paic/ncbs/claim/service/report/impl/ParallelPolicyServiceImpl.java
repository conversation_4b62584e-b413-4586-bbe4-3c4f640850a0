package com.paic.ncbs.claim.service.report.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDomainDTO;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.sao.CustomerInfoStoreSAO;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.report.ParallelPolicyService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@Service
public class ParallelPolicyServiceImpl implements ParallelPolicyService {

    @Autowired
    @Qualifier("batchPool")
    private Executor executor;
    @Autowired
    private CustomerInfoStoreSAO customerInfoStoreSAO;
    @Autowired
    private CommonService commonService;

    @Override
    public List<AhcsPolicyDomainDTO> getPolicyDomainByParallel(List<Map<String, String>> paramList) {
        if(ListUtils.isEmptyList(paramList)){
            return new ArrayList<>();
        }
        List<CompletableFuture<AhcsPolicyDomainDTO>> futureList = new ArrayList<>();
        for (Map<String, String> param : paramList) {
            futureList.add(CompletableFuture.supplyAsync(()->customerInfoStoreSAO.getPolicyDomainInfoByPolicyNo(param),executor));
//            futureList.add(CompletableFuture.supplyAsync(()->test(param),executor));
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
        List<AhcsPolicyDomainDTO> resultList = new ArrayList<>();
        futureList.forEach(f-> resultList.add(f.join()));
        return resultList;
    }

    /**
     * 批量结案批量抄单
     * @param queryVOList
     * @return
     */
    @Override
    public List<AhcsPolicyDomainDTO> queryPolicyDomainByParallel(List<CopyPolicyQueryVO> queryVOList) {
        List<AhcsPolicyDomainDTO> resultList = new ArrayList<>();
        if(ListUtils.isEmptyList(queryVOList)){
            return resultList;
        }
        if (queryVOList.size() == 1) {
            AhcsPolicyDomainDTO policyDomainInfo = customerInfoStoreSAO.getPolicyDomainInfo(queryVOList.get(0));
            resultList.add(policyDomainInfo);
            return resultList;
        }
        List<CompletableFuture<AhcsPolicyDomainDTO>> futureList = new ArrayList<>();
        for (CopyPolicyQueryVO vo : queryVOList) {
            futureList.add(CompletableFuture.supplyAsync(()->customerInfoStoreSAO.getPolicyDomainInfo(vo),executor));
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();

        futureList.forEach(f-> resultList.add(f.join()));
        return resultList;
    }

    @Override
    public List<String> getNoByParallel(String noType, VoucherTypeEnum voucherTypeEnum, List<String> departmentList){
        LogUtil.audit("批量生成器入参deptList={}", JSON.toJSONString(departmentList));
        long time1 = System.currentTimeMillis();
        if(ListUtils.isEmptyList(departmentList)){
            return new ArrayList<>();
        }
        List<CompletableFuture<String>> futureList = new ArrayList<>();
        for (String deptCode : departmentList) {
            futureList.add(CompletableFuture.supplyAsync(()-> commonService.generateNo(noType,voucherTypeEnum,deptCode),executor));
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
        List<String> resultList = new ArrayList<>();
        futureList.forEach(f-> resultList.add(f.join()));
        LogUtil.audit("批量生成器耗时={}",System.currentTimeMillis()-time1);
        LogUtil.audit("批量生成器出参resultList={}", JSON.toJSONString(resultList));
        return resultList;
    }

}
