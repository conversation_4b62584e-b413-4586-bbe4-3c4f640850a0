package com.paic.ncbs.claim.service.common;

import com.baomidou.mybatisplus.extension.service.IService;
import com.paic.ncbs.claim.dao.entity.common.HospitalInfoEntity;
import com.paic.ncbs.claim.model.dto.checkloss.HospitalInfoDTO;

import java.util.Map;


/**
 * <p>
 * 住院表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface IHospitalInfoPlusService extends IService<HospitalInfoEntity> {

    void hospitalInfoUpdateByCase(Map<String, HospitalInfoDTO> hospitalInfoDTOMap, String reportNo);

}
