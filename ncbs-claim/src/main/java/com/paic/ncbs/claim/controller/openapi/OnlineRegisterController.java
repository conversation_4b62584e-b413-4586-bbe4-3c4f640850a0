package com.paic.ncbs.claim.controller.openapi;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO;
import com.paic.ncbs.claim.model.dto.openapi.*;
import com.paic.ncbs.claim.model.dto.settle.MedicalDTO;
import com.paic.ncbs.claim.model.vo.openapi.ClaimStatusResponseVo;
import com.paic.ncbs.claim.model.vo.duty.DutySurveyVO;
import com.paic.ncbs.claim.service.estimate.ClmsTpaRequestRegisterService;
import com.paic.ncbs.claim.service.openapi.OnlineRegisterService;
import com.paic.ncbs.claim.service.openapi.OpenReportService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.settle.CommunicateBaseService;
import com.paic.ncbs.claim.service.settle.MedicalBillService;
import com.paic.ncbs.claim.utils.JsonUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;


/**
 * TPA平台接入立案接口
 */
@Api(tags = "TPA平台接入")
@Slf4j
@RestController
@RequestMapping("/public/registerController")
public class OnlineRegisterController {
    @Autowired
    private OnlineRegisterService onlineRegisterService;

    @Autowired
    private RegisterCaseService registerCaseService;
    @Autowired
    private ClmsTpaRequestRegisterService clmsTpaRequestRegisterService;
    @Autowired
    MedicalBillService medicalBillService;
    @Autowired
    private CommunicateBaseService communicateBaseService;
    @Autowired
    private OpenReportService openReportService;

    /**
     * TPA立案接口：
     * 1先立案保存
     * 2：提交工作流到收单
     * @return
     */
    @PostMapping("/register")
    public ResponseResult<Object> register(@RequestBody TpaRegisterRequestDto request){
        log.info("TPA立案请求入参={}",JsonUtils.toJsonString(request));
        CaseRegisterApplyDTO returnDto=new CaseRegisterApplyDTO();
        ClmsTpaRequestRegisterDTO  registerDTO= clmsTpaRequestRegisterService.getClmsTpaRequestRegisterDTO(request.getRequestData().getReportNo(),request.getRequestData().getCaseTimes());
        if(ObjectUtil.isEmpty(registerDTO)){
            onlineRegisterService.register(request.getRequestData());
            //万一到这里出错或者服务宕机，立案数据已经保存了，但工作流还停留在立案跟踪，给TPA返回的不是成功，所以TPA还会重新发起立案请求
            returnDto= onlineRegisterService.dealWorkFlow(request.getRequestData().getReportNo(),request.getRequestData().getCaseTimes());
        }else{
           if(Objects.equals(registerDTO.getRegister(),"1") && !Objects.equals(registerDTO.getReportTrack(),"1")){
               //立案保存成功 但是工作流还在报案跟踪节点
               returnDto=  onlineRegisterService.dealWorkFlow(request.getRequestData().getReportNo(),request.getRequestData().getCaseTimes());
           }
        }

        return  ResponseResult.success(returnDto);
    }

    @PostMapping("/testRegisterAndSaveDutyInfo")
    public ResponseResult<Object> testRegisterAndSaveDutyInfo(@RequestBody TpaRegisterRequestDto request) throws Exception {
        log.info("TPA立案请求入参={}",JsonUtils.toJsonString(request));
        CaseRegisterApplyDTO returnDto=new CaseRegisterApplyDTO();
        ClmsTpaRequestRegisterDTO  registerDTO= clmsTpaRequestRegisterService.getClmsTpaRequestRegisterDTO(request.getRequestData().getReportNo(),request.getRequestData().getCaseTimes());
        if(ObjectUtil.isEmpty(registerDTO)){
            onlineRegisterService.register(request.getRequestData());
            //万一到这里出错或者服务宕机，立案数据已经保存了，但工作流还停留在立案跟踪，给TPA返回的不是成功，所以TPA还会重新发起立案请求
            returnDto= onlineRegisterService.dealWorkFlow(request.getRequestData().getReportNo(),request.getRequestData().getCaseTimes());
        }else{
            if(Objects.equals(registerDTO.getRegister(),"1") && !Objects.equals(registerDTO.getReportTrack(),"1")){
                //立案保存成功 但是工作流还在报案跟踪节点
                returnDto=  onlineRegisterService.dealWorkFlow(request.getRequestData().getReportNo(),request.getRequestData().getCaseTimes());
            }
        }
        String param="{\n" +
                "    \"companyId\": \"34543243\",\n" +
                "    \"requestTime\": \"2024-01-10 14:43:10\",\n" +
                "    \"requestData\": {\n" +
                "        \"reportNo\": \"98080000000001001383\",\n" +
                "        \"medicalBillDTOList\": [\n" +
                "            {\n" +
                "                \"inputMode\": \"2\",\n" +
                "                \"costCode\": \"CCT_3603\",\n" +
                "                \"billAmount\": \"100.00\",\n" +
                "                \"prepaidAmount\": \"100\",\n" +
                "                \"prepaidType\": \"PT_2701\",\n" +
                "                \"deductibleAmount\": \"10.00\",\n" +
                "                \"partialDeductible\": \"20.00\",\n" +
                "                \"immoderateAmount\": \"30.00\",\n" +
                "                \"diagnoseList\": [\n" +
                "                    {\n" +
                "                        \"diagnoseCode\": \"P04.102\",\n" +
                "                        \"diagnoseName\": \"母体癌症化疗新生儿\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"diagnoseCode\": \"L94.600\",\n" +
                "                        \"diagnoseName\": \"阿洪病\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"billClass\": \"MT_01\",\n" +
                "                \"billType\": \"BT_3604\",\n" +
                "                \"billNo\": \"234565432\",\n" +
                "                \"therapyType\": \"THE_0302\",\n" +
                "                \"startDate\": \"2024-01-03\",\n" +
                "                \"endDate\": \"2024-01-04\",\n" +
                "                \"days\": 1,\n" +
                "                \"hospitalCode\": \"9999999\",\n" +
                "                \"hospitalName\": \"上海市虹口区乍浦路地段医院\",\n" +
                "                \"riskLabel\": \"Y\",\n" +
                "                \"remark\": \"RISK_BILL_200,RISK_BILL_201,RISK_BILL_299\",\n" +
                "                \"medicalBillReduceDTOList\": [\n" +
                "                    {\n" +
                "                        \"costCode\": \"CCT_3601\",\n" +
                "                        \"feeCode\": \"01\",\n" +
                "                        \"feeName\": \"xiyaofei\",\n" +
                "                        \"units\": \"1*12/盒\",\n" +
                "                        \"unitsCode\": \"A00001\",\n" +
                "                        \"feeAmount\": \"100.00\",\n" +
                "                        \"selfSufficiencyRate\": \"10.00\",\n" +
                "                        \"socialSecurityType\": \"社保类型\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";

        JSONObject object = JSONObject.parseObject(param);
        String jsonString = JSONObject.toJSONString(object.get("requestData"));
        MedicalDTO medicalDTO = JSONObject.parseObject(jsonString, MedicalDTO.class);
        medicalDTO.setReportNo(returnDto.getReportNo());
        LogUtil.audit("对接外部-新增账单addOuterMedicalBill报案号{}，请求入参{}",medicalDTO.getReportNo(), JSON.toJSONString(medicalDTO));
        DutySurveyVO dutyVO = medicalBillService.addOuterMedicalBill(medicalDTO);

        MedicalDTO result=new MedicalDTO();
        result.setReportNo(medicalDTO.getReportNo());
        return  ResponseResult.success(returnDto);
    }

    /**
     * TPA发起沟通接口
     * @param tpaCommunicateRequestDto
     * @return
     */
    @PostMapping(value = "/sendCommunicateBaseInfoForTPA")
    public ResponseResult<Object> sendCommunicateBaseInfoForTPA(@RequestBody TpaCommunicateRequestDto tpaCommunicateRequestDto) {
        log.info("TPA发起沟通接口请求入参={}",JsonUtils.toJsonString(tpaCommunicateRequestDto));
        TpaCommunicateResponseDto tpaCommunicateResponseDto = communicateBaseService.sendCommunicateBaseInfoForTPA(tpaCommunicateRequestDto.getRequestData());
        return ResponseResult.success(tpaCommunicateResponseDto);
    }


    /**
     * 案件状态查询接口
     */
    @PostMapping(value = "/queryClaimStatusTPA")
    public ResponseResult<Object> queryClaimStatusTPA(@RequestBody TpaClaimStatusRequestDto tpaClaimStatusRequestDto){
        log.info("TPA案件查询接口请求入参={}",JsonUtils.toJsonString(tpaClaimStatusRequestDto));
        ClaimStatusResponseVo claimStatusResponseVo = openReportService.queryClaimStatusTPA(tpaClaimStatusRequestDto);
        return ResponseResult.success(claimStatusResponseVo);
    }



}
