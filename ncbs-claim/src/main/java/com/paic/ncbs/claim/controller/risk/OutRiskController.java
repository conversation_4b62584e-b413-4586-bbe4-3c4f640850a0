package com.paic.ncbs.claim.controller.risk;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.estimate.RepeatReceiptVO;
import com.paic.ncbs.claim.service.risk.RiskControlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-06-29 17:45
 */
@Api(tags = "外部风控控制类")
@RestController
@RequestMapping("/riskcontrol")
@Validated
public class OutRiskController {

    @Autowired
    private RiskControlService riskControlService;

    @PostMapping("checkRepeatReceipt")
    @ApiOperation("查询是否存在重复发票")
    public ResponseResult<Object> checkRepeatReceipt(@Valid @RequestBody RepeatReceiptVO vo){
        return riskControlService.checkRepeatReceipt(vo);
    }
}
