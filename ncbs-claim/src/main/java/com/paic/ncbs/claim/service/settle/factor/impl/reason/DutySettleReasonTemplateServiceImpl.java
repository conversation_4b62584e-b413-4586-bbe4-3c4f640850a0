package com.paic.ncbs.claim.service.settle.factor.impl.reason;

import cn.hutool.core.collection.CollectionUtil;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.service.settle.factor.impl.common.ClaimApplicationAwareUtil;
import com.paic.ncbs.claim.service.settle.factor.interfaces.reason.DutySettleReasonTemplateService;

import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.template.ClmsFreemarkTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Slf4j
@Service
public class DutySettleReasonTemplateServiceImpl implements DutySettleReasonTemplateService {
    @Autowired
    private ClaimApplicationAwareUtil contextUtil;
    @Autowired
    private ClmsFreemarkTemplateService clmsFreemarkTemplateService;
    @Override
    public void displaySettleReason(DutyPayDTO duty) {
        StringBuilder settleReason = new StringBuilder();//理算依据
        for (DutyDetailPayDTO detail :duty.getDutyDetailPayArr()) {
            {
                if(Objects.equals("N",detail.getIsSettleFlag())){
                   continue;
                }
                if(Objects.isNull(detail.getDetailSettleReasonTemplateDTO())){
                    continue;
                }
                String dutyReason =clmsFreemarkTemplateService.getSettleReason(detail);
                settleReason.append(dutyReason);
            }
        }
        duty.setSettleReason(settleReason.toString());
        log.info("报案号={}，理算依据={}",duty.getReportNo(),settleReason.toString());
    }
}
