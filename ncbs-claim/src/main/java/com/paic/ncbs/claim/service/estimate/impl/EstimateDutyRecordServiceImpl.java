package com.paic.ncbs.claim.service.estimate.impl;

import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyRecordMapper;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.endcase.CaseClassService;
import com.paic.ncbs.claim.service.estimate.EstimateDutyRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service("estimateDutyRecordService")
public class EstimateDutyRecordServiceImpl implements EstimateDutyRecordService {

    @Autowired
    private EstimateDutyRecordMapper estimateDutyRecordDao;

    @Autowired
    private AhcsCommonService ahcsCommonService;

    @Autowired
    private CaseClassService caseClassService;

    @Override
    public void addEstimateDutyRecordList(List<EstimateDutyRecordDTO> paramList) {
        ahcsCommonService.batchHandlerTransactional(EstimateDutyRecordMapper.class,
                paramList, "addEstimateDutyRecordList");
    }

    @Override
    public void addRecordList(List<EstimateDutyRecordDTO> paramList) {
        ahcsCommonService.batchHandlerTransactional(EstimateDutyRecordMapper.class, paramList, "addDutyRecordList");
    }

    @Override
    public List<EstimateDutyRecordDTO> getRecordsOfRegistCase(String caseNo, Integer caseTimes, String estimateType) {
        return estimateDutyRecordDao.getRecordsOfRegistCase(caseNo, caseTimes, estimateType);
    }

    @Override
    public void updateEffectiveByCaseNos(List<String> caseNoList, Integer caseTimes, String updatedBy) {
        estimateDutyRecordDao.updateEffectiveByCaseNos(caseNoList, caseTimes, updatedBy);
    }

    @Override
    public BigDecimal getRegisterAmountByApplyId(String applyId) {
        List<EstimateDutyRecordDTO> list = estimateDutyRecordDao.getDutyRecordInfoByApplyId(applyId);
        List<BigDecimal> amountList = new ArrayList<>();
        for(EstimateDutyRecordDTO item : list){
            amountList.add(item.getEstimateAmount());
            amountList.add(item.getLawsuitFee());
            amountList.add(item.getCommonEstimateFee());
            amountList.add(item.getLawyerFee());
            amountList.add(item.getExecuteFee());
            amountList.add(item.getVerifyAppraiseFee());
            amountList.add(item.getArbitrageFee());
            amountList.add(item.getInquireFee());
            amountList.add(item.getOtherFee());
            amountList.add(item.getSpecialSurveyFee());

        }
        return BigDecimalUtils.sum(amountList);
    }


}