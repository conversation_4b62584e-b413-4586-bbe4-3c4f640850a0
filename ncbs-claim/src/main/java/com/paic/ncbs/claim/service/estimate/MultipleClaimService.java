package com.paic.ncbs.claim.service.estimate;


import com.paic.ncbs.claim.exception.GlobalBusinessException;

public interface MultipleClaimService {

    String getMultiClaimPriorityReason(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    int getMultiClaimIngByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    String getCurrentMultiClaimApplyUm(String reportNo, Integer caseTimes) throws GlobalBusinessException;


}
