package com.paic.ncbs.claim.dao.mapper.blacklist;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListRecord;

/**
 * <p>
 * 黑名单信息记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface ClmsBlackListRecordMapper extends BaseMapper<ClmsBlackListRecord> {

    ClmsBlackListRecord getByBlackListId(String blackListId);

    void saveClmsBlackListRecord(ClmsBlackListRecord clmsBlackListRecord);

    void deleteByBlackListId(String blackListId);

    /**
     * 根据姓名和证件号码查询黑名单记录表
     */
    int countByNameAndCertNo(String partyName, String idNum);



}
