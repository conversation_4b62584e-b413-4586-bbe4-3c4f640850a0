package com.paic.ncbs.claim.controller.ahcs;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.endcase.AiModelStatisticsVO;
import com.paic.ncbs.claim.model.vo.endcase.TrackInfoVO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseTimeVO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "整案信息")
@RestController
@RequestMapping("/ahcs/do/app/wholeCaseAction")
public class WholeCaseController extends BaseController {

    @Autowired
    private WholeCaseService wholeCaseService;

    @ApiOperation("查询历史案件信息")
    @PostMapping(value = "/getHistoryCaseList")
    public ResponseResult<Object> getHistoryCaseList(@RequestBody WholeCaseVO queryVO){
        try{
            //queryVO.setUserId(WebServletContext.getUserId());
            return ResponseResult.success(wholeCaseService.getHistoryCaseList(queryVO));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("根据保单和客户号查询保单赔付的历史信息")
    @GetMapping(value = "/getWholeCaseTime/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<WholeCaseTimeVO> getWholeCaseTime(@PathVariable("reportNo") String reportNo,
                                                            @PathVariable("caseTimes") Integer caseTimes)
            throws GlobalBusinessException {
        LogUtil.audit("#根据保单和客户号查询保单赔付的历史信息，人参reportNo=%s,caseTimes=%s", reportNo, caseTimes);
        return ResponseResult.success(wholeCaseService.getWholeCaseTime(reportNo, caseTimes));
    }

    @ApiOperation("未决管理查询")
    @PostMapping(value = "/getPendingManagementQueryList")
    public ResponseResult<Object> getPendingManagementQueryList(@RequestBody WholeCaseVO queryVO){
        try{
            return wholeCaseService.getPendingManagementQueryList(queryVO);
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("查看案件详情/未决信息获取展示数据")
    @GetMapping(value = "/getTrackInfoList/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<List<TrackInfoVO>> getTrackInfoList(@PathVariable("reportNo") String reportNo,
                                                              @PathVariable("caseTimes") Integer caseTimes) {
        LogUtil.audit("#历史案件查询#未决信息，人参reportNo={" + reportNo + "}," + "caseTimes={" + caseTimes + "}");
        return ResponseResult.success(wholeCaseService.getTrackInfoList(reportNo, caseTimes));
    }

    @ApiOperation("AI模型数据统计")
    @GetMapping(value = "/getAiModelStatistics")
    public ResponseResult<AiModelStatisticsVO> getAiModelStatistics() {
        try {
            AiModelStatisticsVO aiModelStatisticsVO = wholeCaseService.getAiModelStatistics();
            return ResponseResult.success(aiModelStatisticsVO);
        } catch (Exception e) {
            LogUtil.error("AI模型数据统计查询失败,异常信息", e);
        }
        return ResponseResult.success(null);
    }
}
