package com.paic.ncbs.claim.controller.ahcs;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.FileUploadConstants;
import com.paic.ncbs.claim.common.poi.ExcelHelper;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsBatchEntity;
import com.paic.ncbs.claim.model.dto.ahcs.BatchAutoCloseDTO;
import com.paic.ncbs.claim.service.ahcs.AhcsBatchReportService;
import com.paic.ncbs.claim.service.ahcs.BatchAutoCloseService;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

@RestController
@RefreshScope
@RequestMapping("/report/autoClose")
public class BatchAutoCloseController {

    private Integer BATCH_SIZE = 200;

    @Autowired
    private BatchAutoCloseService batchAutoCloseService;

    @Autowired
    @Lazy
    private AhcsBatchReportService ahcsBatchReportService;

    @Autowired
    private IOBSFileUploadService iobsFileUploadService;

    @Value("${iobs.enable:true}")
    private boolean iobs;

    @ApiOperation(value = "上传批量结案模板")
    @PostMapping(value = "/uploadBatchCloseCaseFile")
    public ResponseResult<AhcsBatchEntity> uploadBatchCloseCaseFile(@RequestParam("batchCloseFile") MultipartFile file) throws Exception{
        AhcsBatchEntity result = new AhcsBatchEntity();
        result.setUpFileCode(ErrorCode.FILE_HAVE_PROBLEM);
        result.setUpFileStatus(ConstValues.FAIL);
        result.setBatchUploadNums(0);

        if(file == null){
            result.setOperateShow("请不要上传空文件");
            result.setUpFileCodeDescribe("请不要上传空文件");
            return ResponseResult.success(result);
        }

        result.setFileName(file.getOriginalFilename());
//        String md5 = MD5Util.md5HashCode32(file.getInputStream());
        String md5 = DigestUtil.sha256Hex(file.getInputStream());
        LogUtil.audit("md5={}",md5);
        int count = ahcsBatchReportService.queryMd5Count(md5);
        if (count > 0) {
            result.setOperateShow("请不要重复导入文件");
            result.setUpFileCodeDescribe("请不要重复导入文件") ;
            return ResponseResult.success(result);
        }

        List<BatchAutoCloseDTO> batchCloseList = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream()).sheet(0).head(BatchAutoCloseDTO.class).headRowNumber(2).registerReadListener(new AnalysisEventListener() {
                @Override
                public void invoke(Object data, AnalysisContext context) {
                    batchCloseList.add((BatchAutoCloseDTO) data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                }
            }).doRead();
        }catch (Exception e){
            LogUtil.error("文件解析失败：",e);
            result.setOperateShow("文件解析失败,请检查数据是否符合要求");
            result.setUpFileCodeDescribe("文件解析失败,请检查数据是否符合要求");
            return ResponseResult.success(result);
        }

        result.setBatchUploadNums(batchCloseList.size()) ;
        if(batchCloseList.size() < 1 || batchCloseList.size() > BATCH_SIZE){
            result.setOperateShow("录入数据超限(每次支持1-"+BATCH_SIZE+"行)");
            result.setUpFileCodeDescribe("录入数据超限(每次支持1-"+BATCH_SIZE+"行)") ;
            return ResponseResult.success(result);
        }

        result.setUpFileCode(ErrorCode.FILE_HAVE_PROBLEMS);
        batchAutoCloseService.saveBatchAutoClose(result,batchCloseList,md5);
        writeFile(result,batchCloseList);
        return ResponseResult.success(result);
    }

    public void writeFile(AhcsBatchEntity result,List<BatchAutoCloseDTO> batchCloseAllList){

        String fileName = System.currentTimeMillis()+".xlsx";
        String fileId = null;
        if(iobs){
            try(ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                EasyExcel.write(bos).head(ExcelHelper.batchCloseHeadList).sheet("意健险批量结案模板").doWrite(batchCloseAllList);
                byte[] bytes = bos.toByteArray();
                fileId = iobsFileUploadService.uploadFileToFilePlatform(fileName, bytes);

            }catch (Exception e){
                LogUtil.error("上传失败",e);
//                throw new GlobalBusinessException(GlobalResultStatus.FAIL);
                String batchNo = result.getReportBatchNo();
                batchNo = batchNo == null?"":batchNo;
                result.setUpFileCodeDescribe("生成下载文件失败,如批次号已生成,请查询是否上传成功,批次号="+batchNo);
            }

        }else{
            try (FileOutputStream fos = new FileOutputStream(FileUploadConstants.LOCAL_PATH+fileName)){
                EasyExcel.write(fos).head(ExcelHelper.batchCloseHeadList).sheet("意健险批量结案模板").doWrite(batchCloseAllList);
                fileId = fileName;
            }catch (Exception e){
                LogUtil.error("上传失败",e);
                result.setUpFileCodeDescribe("生成下载文件失败,如批次号已生成,请查询是否上传成功,批次号="+result.getReportBatchNo());
//                throw new GlobalBusinessException(GlobalResultStatus.FAIL);
            }
        }

        result.setFileId(fileId);

        try {
            if(iobs){
                String iobsUrl = iobsFileUploadService.getPerpetualDownloadUrlForBatchUpload(fileId, fileName);
                if (iobsUrl.startsWith("http://")){
                    iobsUrl=iobsUrl.replaceAll("http://","https://");
                }
                result.setFileUrl(iobsUrl);
            }else{
                result.setFileUrl(FileUploadConstants.LOCAL_HOST+fileName);
            }

        }catch (Exception e){

        }
    }

    @ApiOperation(value = "下载模板")
    @GetMapping(value = "/downloadBatchCloseCaseTemplate")
    public void downloadBatchCloseCaseTemplate(HttpServletResponse response) {
        BufferedOutputStream out = null;
        try {
            //获取输入流，原始模板位置
            InputStream bis = this.getClass().getResourceAsStream("/templates/意健险批量结案模板.xlsx");
            //转码，免得文件名中文乱码
            String fileName = "意健险批量结案模板.xlsx";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            //设置文件下载头
            response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
            //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");
            out = new BufferedOutputStream(response.getOutputStream());
            int len = 0;
            while ((len = bis.read()) != -1) {
                out.write(len);
                out.flush();
            }
        } catch (IOException e) {
            LogUtil.error("下载文件异常", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                LogUtil.error("流关闭异常", e);
            }
        }

    }

}
