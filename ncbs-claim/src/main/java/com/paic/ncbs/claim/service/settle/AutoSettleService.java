package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;

import java.util.List;


public interface AutoSettleService {

    void getSettleAmount(String reportNo, Integer caseTimes, List<PolicyPayDTO> copyPolicyPays) throws GlobalBusinessException;

    boolean isDutyMatchCaseClass(List<String> caseClassList,String dutyDetailType);

    void autoSettle(List<DutyDetailPayDTO> detailList);

    void setSettleReason(DutyPayDTO duty);

}
