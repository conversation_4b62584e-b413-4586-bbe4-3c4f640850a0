package com.paic.ncbs.claim.service.settle.factor.interfaces.base;

import com.googlecode.aviator.Expression;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;

import java.util.List;

/**
 * 01-身故，02-伤残，03-重疾，04-医疗费用，05-津贴，06-服务，07-一般定额，08-损失，12-责任，99-其他
 * 理算接口
 */
public interface BaseSettleService {
    void getSettleAmount(ClaimCaseDTO claimCaseDTO, DutyDetailPayDTO detailPayDTO,
                         Expression expression);
}
