package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentFlightEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentFlightMapper;
import com.paic.ncbs.claim.service.report.ReportAccidentFlightService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportAccidentFlightServiceImpl extends BaseServiceImpl<ReportAccidentFlightEntity> implements ReportAccidentFlightService {

    @Autowired
    private ReportAccidentFlightMapper reportAccidentFlightMapper;

    @Override
    public BaseDao<ReportAccidentFlightEntity> getDao() {
        return reportAccidentFlightMapper;
    }

    @Override
    public ReportAccidentFlightEntity getReportAccidentFlightByReportNo(String reportNo) {
        return reportAccidentFlightMapper.getReportAccidentFlightByReportNo(reportNo);
    }
}
