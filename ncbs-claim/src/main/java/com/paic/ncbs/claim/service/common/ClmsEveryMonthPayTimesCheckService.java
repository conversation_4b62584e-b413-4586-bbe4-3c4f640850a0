package com.paic.ncbs.claim.service.common;

import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;

import java.util.List;

/**
 * 理赔每月赔付次数校验
 */
public interface ClmsEveryMonthPayTimesCheckService {
    void checkPayTimes(List<PolicyPayDTO> policyPays,String reportNo);

    /**
     * 查询案件出险日所在月度保单的月赔付案件
     * @param wholeCaseVO
     * @return
     */
    List<String> getMonthEndCase(WholeCaseVO wholeCaseVO);
}
