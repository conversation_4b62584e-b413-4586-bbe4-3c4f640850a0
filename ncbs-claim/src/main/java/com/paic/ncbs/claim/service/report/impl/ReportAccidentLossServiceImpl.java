package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentLossEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentLossMapper;
import com.paic.ncbs.claim.service.report.ReportAccidentLossService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportAccidentLossServiceImpl extends BaseServiceImpl<ReportAccidentLossEntity> implements ReportAccidentLossService {

    @Autowired
    private ReportAccidentLossMapper reportAccidentLossMapper;

    @Override
    public BaseDao<ReportAccidentLossEntity> getDao() {
        return reportAccidentLossMapper;
    }

    @Override
    public ReportAccidentLossEntity getReportAccidentLossByReportNo(String reportNo) {
        return reportAccidentLossMapper.getReportAccidentLossByReportNo(reportNo);
    }
}
