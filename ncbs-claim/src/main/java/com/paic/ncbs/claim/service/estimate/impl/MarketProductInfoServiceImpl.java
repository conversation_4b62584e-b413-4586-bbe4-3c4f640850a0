package com.paic.ncbs.claim.service.estimate.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.estimate.MarketProductInfoEntity;
import com.paic.ncbs.claim.dao.mapper.estimate.MarketProductInfoEntityMapper;
import com.paic.ncbs.claim.service.estimate.MarketProductInfoService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MarketProductInfoServiceImpl extends BaseServiceImpl<MarketProductInfoEntity> implements MarketProductInfoService {

    @Autowired
    private MarketProductInfoEntityMapper marketProductInfoEntityMapper;

    @Override
    public BaseDao<MarketProductInfoEntity> getDao() {
        return marketProductInfoEntityMapper;
    }

    @Override
    public MarketProductInfoEntity getMarketProductInfoByProductCode(String productCode, String productVersion) {
        if (productVersion == null) {
            productVersion = "1.03";
        }
        return marketProductInfoEntityMapper.getMarketProductInfoByProductCode(productCode, productVersion);
    }
}
