package com.paic.ncbs.claim.replevy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 追偿费用信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Getter
@Setter
@TableName("clms_replevy_charge")
public class ClmsReplevyCharge implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 发票明细id
     */
    @TableField("invoice_info_id")
    private String invoiceInfoId;

    /**
     * 领款人信息表id
     */
    @TableField("payment_info_id")
    private String paymentInfoId;
    /**
     * 支付项id
     */
    @TableField("id_clm_payment_item")
    private String idClmPaymentItem;
    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 追偿案件号
     */
    @TableField("replevy_no")
    private String replevyNo;

    /**
     * 追偿次数
     */
    @TableField("replevy_times")
    private Integer replevyTimes;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 险种
     */
    @TableField("plan_code")
    private String planCode;

    /**
     * 责任
     */
    @TableField("duty_code")
    private String dutyCode;

    /**
     * 直接理赔费用类型
     */
    @TableField("charge_type")
    private String chargeType;

    /**
     * 支付对象
     */
    @TableField("pay_object")
    private String payObject;

    /**
     * 费用发生区域
     */
    @TableField("charge_area")
    private String chargeArea;

    /**
     * 币别
     */
    @TableField("currency")
    private String currency;

    /**
     * 费用金额
     */
    @TableField("charge_money")
    private BigDecimal chargeMoney;

    /**
     * 申请环节
     */
    @TableField("apply_link")
    private String applyLink;

    /**
     * 申请人
     */
    @TableField("apply_person")
    private String applyPerson;

    /**
     * 申请时间
     */
    @TableField("apply_time")
    private Date applyTime;

    /**
     * 申请原因
     */
    @TableField("apply_reason")
    private String applyReason;

    /**
     * 费用说明
     */
    @TableField("charge_describe")
    private String chargeDescribe;

    /**
     * 收款人姓名
     */
    @TableField("client_name")
    private String clientName;

    /**
     * 收款人账号
     */
    @TableField("client_account")
    private String clientAccount;

    /**
     * 1-已申请，2-待审核，3-审核通过，4-退回
     */
    @TableField("approve_flag")
    private String approveFlag;

    /**
     * 结算单标识
     */
    @TableField("settlement_flag")
    private String settlementFlag;

    /**
     * 结算单标识
     */
    @TableField("statement_flag")
    private String statementFlag;

    /**
     * 分摊标识
     */
    @TableField("divide_flag")
    private String divideFlag;

    /**
     * 有效标志
     */
    @TableField("valid_flag")
    private String validFlag;

    /**
     * 状态0-进行中 1-完成
     */
    @TableField("flag")
    private String flag;

    /**
     * 序号
     */
    @TableField("serial_no")
    private Integer serialNo;
    /**
     * 结束时间
     */
    @TableField ("finish_date")
    private Date finishDate;
    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;
}
