package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.dao.mapper.report.LinkManMapper;
import com.paic.ncbs.claim.service.report.LinkManService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LinkManServiceImpl extends BaseServiceImpl<LinkManEntity> implements LinkManService {

    @Autowired
    private LinkManMapper linkManMapper;

    @Override
    public BaseDao<LinkManEntity> getDao() {
        return linkManMapper;
    }

    @Override
    public List<LinkManEntity> getLinkMans(String reportNo,Integer caseTimes) {
        return linkManMapper.getLinkMans(reportNo,caseTimes);
    }

}
