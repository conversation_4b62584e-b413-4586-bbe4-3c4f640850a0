package com.paic.ncbs.claim.dao.mapper.entrustment;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface EntrustmentAuditMapper extends BaseDao {

    /**
     * 插入委托审批记录
     * @param entrustmentAudit 委托审批信息
     */
    void insertEntrustmentAudit(EntrustAuditDTO entrustmentAudit);

    /**
     * 更新委托审批记录
     * @param entrustmentAudit 委托审批信息
     */
    void updateEntrustmentAudit(EntrustAuditDTO entrustmentAudit);

    /**
     * 根据主键查询委托审批记录
     * @param idEntrustAudit 委托审核表主键
     * @return 委托审批信息
     */
    EntrustAuditDTO selectById(@Param("idEntrustAudit") String idEntrustAudit);

    /**
     * 根据委托主表ID查询审批记录
     * @param idEntrustMain 委托表主键
     * @return 委托审批信息列表
     */
    EntrustAuditDTO selectByEntrustmentId(@Param("idEntrustMain") String idEntrustMain);

    /**
     * 根据报案号查询审批记录
     * @param reportNo 报案号
     * @return 委托审批信息列表
     */
    List<EntrustAuditDTO> selectByReportNo(@Param("reportNo") String reportNo);

    /**
     * 根据审批人查询待审批列表
     * @param auditorUm 审批人
     * @return 委托审批信息列表
     */
    List<EntrustAuditDTO> selectPendingAuditList(@Param("auditorUm") String auditorUm);

    /**
     * 根据委托ID查询审批历史轨迹
     * @param idEntrust 委托ID
     * @return 审批历史列表
     */
    List<EntrustAuditDTO> selectAuditHistoryByEntrustId(@Param("idEntrust") String idEntrust);

    /**
     * 根据报案号和赔付次数查询审批历史轨迹
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 审批历史列表
     */
    List<EntrustAuditDTO> selectAuditHistoryByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
}