package com.paic.ncbs.claim.controller.secondunderwriting;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.policy.UwRequestDTO;
import com.paic.ncbs.claim.service.secondunderwriting.ReceiveConclusionService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 接收核保结论
 */
@RestController
@RequestMapping("/public/uw/receive")
public class ReceiveConclusionController {

    @Autowired
    private ReceiveConclusionService receiveConclusionService;
    /**
     * 接收核保结论
     */
    @PostMapping(value = "/receiveConclusion")
    public ResponseResult<Object> receiveConclusion(@RequestBody String request){
        UwRequestDTO uwRequestDTO = JsonUtils.toObject(request,UwRequestDTO.class);
        receiveConclusionService.receiveConclusion(uwRequestDTO);
        return  ResponseResult.success();
    }
}
