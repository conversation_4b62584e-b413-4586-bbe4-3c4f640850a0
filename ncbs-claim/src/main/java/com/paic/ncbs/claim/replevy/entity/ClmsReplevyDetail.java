package com.paic.ncbs.claim.replevy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 追偿明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Getter
@Setter
@TableName("clms_replevy_detail")
public class ClmsReplevyDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 追偿主表id
     */
    @TableField("replevy_id")
    private String replevyId;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 追偿案件号
     */
    @TableField("replevy_no")
    private String replevyNo;

    /**
     * 追偿次数
     */
    @TableField("replevy_times")
    private Integer replevyTimes;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 条款
     */
    @TableField("clause_code")
    private String clauseCode;

    /**
     * 险别
     */
    @TableField("kind_code")
    private String kindCode;

    /**
     * 被追偿方名称
     */
    @TableField("replevied_name")
    private String repleviedName;

    /**
     * 被追偿方类型
     */
    @TableField("replevied_type")
    private String repleviedType;

    /**
     * 被追偿方证件类型
     */
    @TableField("replevied_certi_type")
    private String repleviedCertiType;

    /**
     * 被追偿方证件号码
     */
    @TableField("replevied_certi_code")
    private String repleviedCertiCode;

    /**
     * 被追偿方国别代码
     */
    @TableField("replevied_country_code")
    private String repleviedCountryCode;
    /**
     * 被追偿方客户号
     */
    @TableField("customer_no")
    private String customerNo;

    /**
     * 追偿地点
     */
    @TableField("replevied_location")
    private String repleviedLocation;

    /**
     * 应追金额
     */
    @TableField("replevy_sum")
    private BigDecimal replevySum;

    /**
     * 被追偿人手机
     */
    @TableField("replevied_mobile")
    private String repleviedMobile;

    /**
     * 被追偿人电话
     */
    @TableField("replevied_phone")
    private String repleviedPhone;

    /**
     * 被追偿人地址
     */
    @TableField("replevied_address")
    private String repleviedAddress;

    /**
     * 被追偿人邮编
     */
    @TableField("replevied_post_code")
    private String repleviedPostCode;

    /**
     * 被追偿人传真
     */
    @TableField("replevied_tax")
    private String repleviedTax;

    /**
     * 被追偿人邮箱
     */
    @TableField("replevied_email")
    private String repleviedEmail;

    /**
     * 追偿类型
     */
    @TableField("replevy_type")
    private String replevyType;

    /**
     * 追偿机构
     */
    @TableField("replevy_agency")
    private String replevyAgency;

    /**
     * 追偿人员
     */
    @TableField("replevy_person")
    private String replevyPerson;

    /**
     * 追偿人员电话
     */
    @TableField("replevy_person_tel")
    private String replevyPersonTel;

    /**
     * 追偿日期
     */
    @TableField("replevy_date")
    private Date replevyDate;

    /**
     * 追回日期
     */
    @TableField("replevy_handle_date")
    private Date replevyHandleDate;

    /**
     * 权益转让日期
     */
    @TableField("transfer_date")
    private Date transferDate;

    /**
     * 诉讼时效日期
     */
    @TableField("replevye_ffect_date")
    private Date replevyeFfectDate;

    /**
     * 追偿途径
     */
    @TableField("replevy_way")
    private String replevyWay;

    /**
     * 追偿原因
     */
    @TableField("replevy_reason")
    private String replevyReason;

    /**
     * 追偿收入
     */
    @TableField("real_replevy")
    private BigDecimal realReplevy;

    /**
     * 追偿费用
     */
    @TableField("replevy_fee")
    private BigDecimal replevyFee;

    /**
     * 注销标志
     */
    @TableField("status")
    private String status;

    /**
     * 高级审核状态
     */
    @TableField("approve_flag")
    private String approveFlag;

    /**
     * 有效标志
     */
    @TableField("valid_flag")
    private String validFlag;

    /**
     * 0-暂存，1-保存
     */
    @TableField("flag")
    private String flag;

    /**
     * 币别
     */
    @TableField("currency")
    private String currency;

    /**
     * 追偿进度
     */
    @TableField("replevy_progress")
    private String replevyProgress;

    /**
     * 追偿地区
     */
    @TableField("replevied_area")
    private String repleviedArea;

    /**
     * 追偿地区代码
     */
    @TableField("replevied_area_code")
    private String repleviedAreaCode;

    /**
     * 追偿高级审核状态
     */
    @TableField("replevy_approve_flag")
    private String replevyApproveFlag;

    /**
     * 高级审核人员
     */
    @TableField("approve_person")
    private String approvePerson;

    /**
     * 序号
     */
    @TableField("serial_no")
    private Integer serialNo;

    /**
     * 高级审核日期
     */
    @TableField("approve_date")
    private Date approveDate;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;
}
