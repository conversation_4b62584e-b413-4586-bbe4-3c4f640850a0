package com.paic.ncbs.claim.controller.openapi;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.service.common.ClmsEveryMonthPayTimesCheckService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Api(tags = "每月赔付次数校验接口")
@Slf4j
@RestController
@RequestMapping("/public/clms/paytimes")
public class ClmsEveryMonthPayTimesCheckController {
    @Autowired
    private ClmsEveryMonthPayTimesCheckService clmsEveryMonthPayTimesCheckService;
    @Autowired
    private PolicyPayMapper policyPayDao;
    @GetMapping(value = "/testPayTimes/{reportNo}")
    public ResponseResult<Object> testPayTimes(@PathVariable("reportNo") String reportNo){
       List<PolicyPayDTO> policyPays = policyPayDao.selectFromPolicyCopy(reportNo, 1);
        clmsEveryMonthPayTimesCheckService.checkPayTimes(policyPays,reportNo);
        return ResponseResult.success();
    }
}
