package com.paic.ncbs.claim.service.taskdeal;


import com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertDTO;

import java.util.List;

public interface TravelAlertService {


    public void saveTravelAlert(List<TravelAlertDTO> travelAlertList, String reportNo, Integer caseTimes, String taskCode, String channelProcessId);


    public void removeTravelAlert(String reportNo, Integer caseTimes, String taskCode, String channelProcessId);


    public List<TravelAlertDTO> getTravelAlert(String reportNo, Integer caseTimes, String status, String taskCode, String channelProcessId);

    public void insertTravelAlert(TravelAlertDTO travelAlertDTO);


    public void deleteTravelAlertInvoice(String idAhcsTravelAlertInvoice);
}
