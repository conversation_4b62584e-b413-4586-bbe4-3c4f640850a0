package com.paic.ncbs.claim.controller.blacklist;

import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListAuditMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.blacklist.BlackListAuditDetailVO;
import com.paic.ncbs.claim.model.vo.blacklist.ClmsBlackListAuditVO;
import com.paic.ncbs.claim.service.blacklist.ClmsBlackListAuditService;
import com.paic.ncbs.claim.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2025-07-11
 */
@Api(tags = "黑名单审批")
@RestController
@RequestMapping("/blacklist/blackListAuditAction")
public class BlackListAuditController extends BaseController {

    @Autowired
    private ClmsBlackListAuditMapper auditMapper;

    @Autowired
    private ClmsBlackListAuditService auditService;

    @ApiOperation("黑名单审批-获取待审批黑名单")
    @PostMapping(value = "/getPendingAudits")
    public ResponseResult<PageResult<ClmsBlackListAuditVO>> getPendingAudits(@RequestBody ClmsBlackListAuditVO clmsBlackListAuditVO) {
        return ResponseResult.success(auditService.getPendingAudits(clmsBlackListAuditVO));
    }

    @ApiOperation("黑名单审批-根据id获取黑名单审批详情")
    @GetMapping("/getAuditDetail/{id}")
    public ResponseResult<BlackListAuditDetailVO> getAuditDetail(@PathVariable String id) throws  Exception {
        BlackListAuditDetailVO detail = auditService.getBlackListAuditById(id);
        return ResponseResult.success(detail);
    }

    @ApiOperation("黑名单审批-黑名单审批流程")
    @PostMapping("/auditBlackList")
    public ResponseResult<Object> auditBlackList(@RequestBody ClmsBlackListAuditVO clmsBlackListAuditVO) throws Exception {
        auditService.blacklistApprovalProcess(clmsBlackListAuditVO);
        return ResponseResult.success();
    }

    @ApiOperation("黑名单-校验登录机构是否有审批权限")
    @GetMapping("/getDepartmentCode")
    public ResponseResult<Object> getDepartmentCode() {
        String departmentCode = WebServletContext.getDepartmentCode();
        if (!ConfigConstValues.HQ_DEPARTMENT.equals(departmentCode)){
            throw new GlobalBusinessException("该人员没有认领审批权限！");
        }
        return ResponseResult.success();
    }

    @ApiOperation("黑名单-查询本案下的黑名单信息")
    @PostMapping(value = "/getBlackListByCase")
    public ResponseResult<PageResult<ClmsBlackListAuditVO>> getBlackListByCase(@RequestBody ClmsBlackListAuditVO clmsBlackListVO) throws Exception{
        PageResult<ClmsBlackListAuditVO> pageResult = auditService.getBlackListByCase(clmsBlackListVO);
        return ResponseResult.success(pageResult);
    }

}
