package com.paic.ncbs.claim.service.report.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.EndCaseConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.EstimateUtil;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseRegisterApplyMapper;
import com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.*;
import com.paic.ncbs.claim.service.casezero.CaseZeroCancelService;
import com.paic.ncbs.claim.service.common.ClaimSendTpaMqInfoService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.report.AllocationRuleService;
import com.paic.ncbs.claim.service.report.BatchEstimateInfoService;
import com.paic.ncbs.claim.service.report.EstimateAutoAllocationService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.claim.utils.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import org.springframework.stereotype.Service;


import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 自动分配未决赔款金额
 */
@Slf4j
@RefreshScope
@Service
public class EstimateAutoAllocationServiceImpl implements EstimateAutoAllocationService {
    private static final String END_CASE_STATUS = "0";
    @Value("${batch.estimatedays:15}")
    private  Integer configDays;
    @Autowired
    private RegisterCaseService registerCaseService;
    @Autowired
    private EstimateService estimateService;
    @Autowired
    private ClaimSendTpaMqInfoService claimSendTpaMqInfoService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private BatchEstimateInfoService batchEstimateInfoService;

    @Autowired
    private AllocationRuleService allocationRuleService;
    @Autowired
    private CaseRegisterApplyMapper caseRegisterApplyMapper;

    @Autowired
    private CaseZeroCancelService caseZeroCancelService;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Override
    public void autoAllocation() {
        //查询报案超15天还未立案的报案好
        List<String> reportNoList = registerCaseService.getNoRegisterData(configDays);
        if(CollectionUtil.isEmpty(reportNoList)){
            return;
        }
        long start = System.currentTimeMillis();

        Date batchDate=new Date();
        String batchNo= DateUtils.dateFormat(batchDate,DateUtils.FULL_DATE_STR);
        log.info("未决批处理超15天数未立案数据条数={},配置天数={},批次号={}",reportNoList.size(),configDays,batchNo);
        int successCount=0;//成功条数
        int failCount=0;//异常失败条数
        int noDealCount=0;//条件不满足未处理条数
        for (String reportNo:reportNoList) {
            try {
                BatchEstimateInfoDTO dto =  allocation(reportNo);
                if(Objects.equals("Y",dto.getIsSave())){
                    noDealCount++;
                    dto.setErrorType("1");
                    saveData(batchDate,batchNo,dto);
                }else{
                    successCount++;
                }


            }catch (Exception e){
                log.info("报案号={},未决自动分配异常={}",reportNo,e.getMessage());
                failCount++;
                BatchEstimateInfoDTO dto=new BatchEstimateInfoDTO();
                dto.setMessage("批处理未决自动估损异常："+e.getMessage());
                dto.setReportNo(reportNo);
                dto.setErrorType("2");
                saveData(batchDate,batchNo,dto);
            }
        }
        long end = System.currentTimeMillis();
        log.info("未决批处理超15天数未立案数据结束总条数={},耗时={}，成功处理条数={},条件不满足不估损条数={},异常失败条数={}",reportNoList.size(),end-start,successCount,noDealCount,failCount);
    }

    private void saveData(Date batchDate, String batchNo, BatchEstimateInfoDTO dto) {
        dto.setBatchDate(batchDate);
        dto.setBatchNo(batchNo);
        batchEstimateInfoService.saveData(dto);
    }

    private BatchEstimateInfoDTO allocation(String reportNo) {
        long start = System.currentTimeMillis();
        log.info("未决自动分配开始报案号={}",reportNo);
        String userId="system";
        BatchEstimateInfoDTO batchEstimateInfoDTO=new BatchEstimateInfoDTO();
        batchEstimateInfoDTO.setIsSave("N");
        batchEstimateInfoDTO.setReportNo(reportNo);
        String code =registerCaseCheck(reportNo,1);
        if (!GlobalResultStatus.SUCCESS.getCode().equals(code)) {
            batchEstimateInfoDTO.setIsSave("Y");
            String message =MessageUtil.getMessage(code);
            batchEstimateInfoDTO.setMessage(message);
            return batchEstimateInfoDTO;
        }
        ////组装未决入参 查询报案跟踪立案信息
        EstimatePolicyFormDTO estimatePolicyFormDTO  = estimateService.getEstimateDataByTache(reportNo, 1, EstimateUtil.ESTIMATE_TYPE_REGISTRATION, null);
        estimatePolicyFormDTO.setRegisterUm(userId);
       // estimatePolicyFormDTO.setRegisterName(userInfoService.getUserNameById(userId));
        estimatePolicyFormDTO.setRegisterName("system");
        //设置责任预估金额
        AllocationDutyEstimateDTO allocationDutyEstimateDTO= allocationRuleService.allocationRule(reportNo,1);
        log.info("报案号={}，预估责任信息={}",reportNo,JsonUtils.toJsonString(allocationDutyEstimateDTO));
        if(Objects.equals("N",allocationDutyEstimateDTO.getEstimateFlag())){
            log.info("金额为0不估损");
            batchEstimateInfoDTO.setIsSave("Y");
            batchEstimateInfoDTO.setMessage(allocationDutyEstimateDTO.getMessage());
           return batchEstimateInfoDTO;
        }
        setEstimateAmountValue(estimatePolicyFormDTO,allocationDutyEstimateDTO);
        log.info("报案号={},预估金额设置后={}",reportNo,JsonUtils.toJsonString(estimatePolicyFormDTO));

        RegistSaveDTO registSaveDTO =estimateService.saveEstimateByReportTrack(estimatePolicyFormDTO, userId);
        long end = System.currentTimeMillis();
        LogUtil.info("未决自动分配金额批处理处理一个报案号={},耗时:{}", reportNo,end - start);
        //如果是tpa报案数据 业务人员在核心手动立案后 需要通知TPa平台 发送MQ消息通知
        log.info("报案未决超15天未立案数据批处理后发送MQ消息={}",reportNo);
        claimSendTpaMqInfoService.sendTpaMq(estimatePolicyFormDTO.getReportNo(), estimatePolicyFormDTO.getCaseTimes(), registSaveDTO.getProcessStatus());
        return batchEstimateInfoDTO;
    }

    /**
     * 设置责任预估金额
     * @param estimatePolicyFormDTO
     */
    private void setEstimateAmountValue(EstimatePolicyFormDTO estimatePolicyFormDTO,AllocationDutyEstimateDTO allocationDTO) {
        List<EstimatePolicyDTO> estimatePolicyDTOList = estimatePolicyFormDTO.getEstimatePolicyList();
        estimatePolicyDTOList.forEach(estimatePolicyDTO -> {
            List<EstimatePlanDTO> planDTOS = estimatePolicyDTO.getEstimatePlanList();
            planDTOS.forEach(estimatePlanDTO -> {
                if(Objects.equals(allocationDTO.getPlanCode(),estimatePlanDTO.getPlanCode())){
                    List<EstimateDutyRecordDTO>  dutyDTOS = estimatePlanDTO.getEstimateDutyRecordList();
                    dutyDTOS.forEach(estimateDutyDTO -> {
                        String dutyCode = estimateDutyDTO.getDutyCode();
                        if(Objects.equals(allocationDTO.getDutyCode(),dutyCode)){
                            estimateDutyDTO.setEstimateAmount(allocationDTO.getEstimateMount());
                        }
                    });
                }

            });

        });
    }
    public String registerCaseCheck(String reportNo, Integer caseTimes) {
        LogUtil.audit("查询案件是否在立案审核中, reportNo={}", reportNo);
        //获取获取最新的立案申请信息记录
        CaseRegisterApplyDTO caseRegisterApplyDTO = caseRegisterApplyMapper.getLastestRegisterApplyDTO(reportNo, caseTimes);
        if (Objects.nonNull(caseRegisterApplyDTO)){
            return ErrorCode.RegisterCase.REGISTER_APPLY_IS_PROCESSING;
        }
        LogUtil.audit("查询案件是否在零注中, reportNo={}", reportNo);
        //查询最新的ahcs_zero_cancel_apply零注申请审批表记录
        CaseZeroCancelDTO caseZeroCancelDTO = caseZeroCancelService.getLastZeroCancelInfo(reportNo, caseTimes);
        if (Objects.nonNull(caseZeroCancelDTO)) {
            boolean isDeptAuditProcessing = (EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_DEPT_AUDIT.equals(caseZeroCancelDTO.getStatus())
                    || EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_PROCESSING.equals(caseZeroCancelDTO.getStatus()));
            if (isDeptAuditProcessing) {
                return ErrorCode.RegisterCase.ZERO_CANCEL_IS_PROCESSING;

            }
            String eoaStatus = caseZeroCancelDTO.getStatus();
            String verifyOptions = caseZeroCancelDTO.getVerifyOptions();

            boolean isEoaAuditProcessing = EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_APPROVED.equals(eoaStatus)
                    && EndCaseConstValues.AUDIT_AGREE_CODE.equals(verifyOptions)
                    && EndCaseConstValues.EOA_PROCESSING.equals(eoaStatus);
            if (isEoaAuditProcessing) {
                return ErrorCode.RegisterCase.ZERO_CANCEL_IS_PROCESSING;

            }
        }

        LogUtil.audit("查询案件是否已结案, reportNo={}", reportNo);

        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
        if (Objects.nonNull(wholeCaseBaseDTO) && END_CASE_STATUS.equals(wholeCaseBaseDTO.getWholeCaseStatus())) {
            return ErrorCode.RegisterCase.CASE_IS_END;

        }
        if (Objects.nonNull(wholeCaseBaseDTO) && ConstValues.YES.equals(wholeCaseBaseDTO.getIsRegister())) {
            return ErrorCode.RegisterCase.CASE_IS_REGISTER;
        }
        return GlobalResultStatus.SUCCESS.getCode();
    }
}
