package com.paic.ncbs.claim.service.settle.factor.impl.strategy.loss;

import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsAllowanceInfoMapper;
import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * 给付型津贴
 */
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service
public class OnceAllowanceServiceImpl extends CalculateAmountService {

    @Autowired
    private ClmsAllowanceInfoMapper allowanceInfoMapper;

    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {

        List<String> caseClassList = paramsDTO.getCaseClassList();
        BigDecimal lossAmount = BigDecimal.ZERO;
        if(CollectionUtils.isEmpty(caseClassList)){
            paramsDTO.getSettleFactor().setOnceAllowance(lossAmount);
            paramsDTO.getSettleFactor().setCalculateAmount(lossAmount);
            return;
        }
        if (caseClassList.contains(InsuredApplyTypeEnum.OTHER_ALLOWANCE.getType())) {
            lossAmount = allowanceInfoMapper.getSettleAmout(paramsDTO.getDutyDetailPayDTO().getReportNo(), paramsDTO.getDutyDetailPayDTO().getCaseTimes());
        }
        paramsDTO.getSettleFactor().setOnceAllowance(lossAmount);
        paramsDTO.getSettleFactor().setCalculateAmount(lossAmount);

    }
}
