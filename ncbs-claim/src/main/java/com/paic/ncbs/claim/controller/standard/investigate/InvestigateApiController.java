package com.paic.ncbs.claim.controller.standard.investigate;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.api.StandardRequestDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.casezero.ProblemCaseVO;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Api(tags = "对外提调相关接口")
@RestController
@Validated
@RequestMapping("/public/investigate")
public class InvestigateApiController extends BaseController {

    @Autowired
    private InvestigateService investigateService;
    @Autowired
    private TaskInfoService taskInfoService;

    @ApiOperation("提起调查")
    @PostMapping(value = "/addInvestigate")
    public ResponseResult<Object> addInvestigate(@RequestBody StandardRequestDTO standardRequestDTO) throws GlobalBusinessException {

        InvestigateDTO investigate = JSONObject.parseObject(JSONObject.toJSONString(standardRequestDTO.getRequestData()), InvestigateDTO.class);
        LogUtil.audit("#TPA调查·提起调查#入参#" + investigate);
        changeOperator(investigate.getReportNo(),investigate.getCaseTimes());
        UserInfoDTO u = WebServletContext.getUser();
//        if("1".equals(investigate.getClaimDealWay())) {
//            u.setUserCode(tpaUserId);
//            u.setUserName(tpaUserId);
//        }
        // o暂存 1提交
        if (investigate.getOperate() == 0){
            investigateService.initInvestigate(investigate, u) ;
        }else {
            if (!investigateService.checkIsCanSendForMultiClaim(investigate.getReportNo(), investigate.getCaseTimes())) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG,
                        "结案后，重开申请发送之前，不能提起调查");
            }
            investigateService.initInvestigate(investigate, u) ;
        }
        //提调需要传入操作节点，核心通知TPA时需要判断
//        if(Objects.equals(BpmConstants.OC_REPORT_TRACK,investigate.getTaskCode())){
//            claimSendTpaMqInfoService.sendTpaMq(investigate.getReportNo(),investigate.getCaseTimes(), CaseProcessStatus.WAIT_INVESTIGATION_APPROVING.getCode());
//        }
        //查询taskId并返回
        ProblemCaseVO problemCaseVO = new ProblemCaseVO();
        String taskId = taskInfoService.getTaskId(investigate.getReportNo(),investigate.getCaseTimes(),BpmConstants.OC_INVESTIGATE_APPROVAL);
        problemCaseVO.setProblemNo(taskId);
        return ResponseResult.success(problemCaseVO);
    }

    private void changeOperator(String reportNo, Integer caseTimes){
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        if (StringUtils.isEqualStr(userInfoDTO.getUserCode(), ConstValues.SYSTEM_UM)) {
            TaskInfoDTO tdto=  taskInfoService.getTaskAssignerName(reportNo,caseTimes, BpmConstants.OC_REPORT_TRACK);
            UserInfoDTO sysTemUserInfoDTO = new UserInfoDTO();
            sysTemUserInfoDTO.setUserCode(tdto.getAssigner());
            sysTemUserInfoDTO.setUserName(tdto.getAssigneeName());
            sysTemUserInfoDTO.setComCode(ConfigConstValues.HQ_DEPARTMENT);
            Objects.requireNonNull(WebServletContext.getRequest()).getSession().setAttribute(Constants.CURR_USER,
                    sysTemUserInfoDTO);
            WebServletContext.getRequest().getSession().setAttribute(Constants.CURR_COMCODE,
                    ConfigConstValues.HQ_DEPARTMENT);
            MDC.put(BaseConstant.USER_ID, tdto.getAssigner());
        }
    }




}
