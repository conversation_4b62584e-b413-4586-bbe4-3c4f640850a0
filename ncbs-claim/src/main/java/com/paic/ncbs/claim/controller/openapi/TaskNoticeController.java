package com.paic.ncbs.claim.controller.openapi;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.openapi.ClaimNoticeDTO;
import com.paic.ncbs.claim.model.dto.openapi.RequestDto;
import com.paic.ncbs.claim.model.dto.openapi.ResponseDataDto;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.trace.PersonTraceService;
import com.paic.ncbs.claim.utils.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 任务提醒接收接口
 * 接收任务提醒消息
 */
@Api(tags = "任务提醒接口")
@Slf4j
@RestController
@RequestMapping("/public")
public class TaskNoticeController {

    @Autowired
    private NoticeService noticeService;
    @Autowired
    private PersonTraceService personTraceService;


    /**
     * 消息提醒接口
     * @return
     */
    @PostMapping(value = "/taskNotice")
    public String taskNotice(@RequestBody RequestDto requestDTO){
        log.info("接收外部提醒任务接口={}", JsonUtils.toJsonString(requestDTO));
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        String resultCode = "0000";
        String resultMessage = "成功";

        ClaimNoticeDTO claimNoticeDTO = requestDTO.getRequestData().getClaimNotice();

        NoticesDTO noticesDTO = new NoticesDTO();
        noticesDTO.setReportNo(claimNoticeDTO.getReportNo());
        noticesDTO.setNoticeClass(claimNoticeDTO.getNoticeType());
        noticesDTO.setNoticeSubClass(claimNoticeDTO.getNoticeSubType());
        noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_GLOBAL);//默认1-global理赔
        noticesDTO.setCompanyCode(claimNoticeDTO.getComCode());
        if(BpmConstants.NOTICE_CLASS_PAY_FALL.equals(noticesDTO.getNoticeClass())&&(claimNoticeDTO.getClientName()==null || claimNoticeDTO.getClientName().isEmpty())){
            resultCode="0001";
            resultMessage="提醒类型为6-支付失败提醒时，领款人姓名必传";
        }else{
            try {
                noticeService.saveNotices(noticesDTO,claimNoticeDTO.getClientName(),claimNoticeDTO.getDealCode());
            }catch (Exception e){
                resultCode = "0001";
                resultMessage = e.getMessage();
                e.printStackTrace();
            }
        }

        RequestDto responseDto = new RequestDto();
        responseDto.setRequestId(requestDTO.getRequestId());
        responseDto.setRequestTime(requestDTO.getRequestTime());
        responseDto.setRequestType(requestDTO.getRequestType());
        ResponseDataDto responseDataDto = new ResponseDataDto();
        responseDataDto.setResultCode(resultCode);
        responseDataDto.setResultMessage(resultMessage);
        responseDto.setResponseDataDto(responseDataDto);
        responseDto.setResponseTime(df.format(new Date()));
        return JSON.toJSONString(responseDto);
    }
    @ApiOperation(value = "使用机器人小助手发送消息到企业微信群")
    @GetMapping(value = "/sendWechatMessage")
    public ResponseResult<Object> sendWechatMessage(){
        noticeService.sendWechatMessage();
        return ResponseResult.success("");
    }
    @ApiOperation(value = "定时删除消息提醒")
    @GetMapping(value = "/batchDeleteNoticesByTime")
    public ResponseResult<Object> batchDeleteNotices(){
        noticeService.batchDeleteNoticeByTime();
        return ResponseResult.success("");
    }

    @ApiOperation("定时消息任务提醒接口")
    @GetMapping("/personNoticeTask")
    public ResponseResult<Object> personNoticeTask(){
        try{
            return personTraceService.noticeTask();
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }
}