package com.paic.ncbs.claim.service.mistake.impl;


import com.paic.ncbs.claim.dao.mapper.mistake.MistakeDefineMapper;
import com.paic.ncbs.claim.model.dto.mistake.MistakeDefineDTO;
import com.paic.ncbs.claim.service.mistake.MistakeDefineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("mistakeDefineService")
public class MistakeDefineServiceImpl implements MistakeDefineService {

	@Autowired
	private MistakeDefineMapper mistakeDefineMapper;
	

	@Override
	public List<MistakeDefineDTO> getMistakeDefineList(String mistakeType){
		return mistakeDefineMapper.getMistakeDefineList(mistakeType);
	}

	@Override
	public String getMistakeName(String mistakeCode){
		return mistakeDefineMapper.getMistakeName(mistakeCode);
	}

}
