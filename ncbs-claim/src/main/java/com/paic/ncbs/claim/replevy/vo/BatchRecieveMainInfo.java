package com.paic.ncbs.claim.replevy.vo;

import com.paic.ncbs.claim.model.dto.pay.ClaimSettlementPayment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;
@EqualsAndHashCode(callSuper = true)
@Data
public class BatchRecieveMainInfo extends ClaimSettlementPayment {
    @ApiModelProperty("打包批次号")
    private String batchNo;
    @ApiModelProperty("类型：3-理赔收款 (非共保摊回)6-共保摊回赔款")
    private String batchType;
    //数据量大时作为分区间段ID使用，同一批次号内同一区间段ID重复调用保持一致)，建议使用日期时间如：20230601（到天数据量仍过大则区分到时）。超过五千条需要分批
    @ApiModelProperty("打包请求ID")
    private String batchSeqNo;
    @ApiModelProperty("系统来源 C-理赔")
    private String systemSource;
    @ApiModelProperty("批次号下总结算金额")
    private BigDecimal sumAmount;
    @ApiModelProperty("批次号下数据条数")
    private int sumCount;
    @ApiModelProperty("本次请求总金额")
    private BigDecimal totalAmount;
    @ApiModelProperty("本次请求数据条数")
    private int totalCount;
    @ApiModelProperty("理赔结算单号")
    private String originBusinessNo;
    //数据处理结果通知用
    @ApiModelProperty("结算状态回调地址")
    private String invokeStatusUrl;
    @ApiModelProperty("是否有差异：Y-是 N-否")
    private String diffFlag;
    @ApiModelProperty("差异类型：01-尾差")
    private String diffType;
    @ApiModelProperty("差异金额")
    private BigDecimal diffAmount;

}
