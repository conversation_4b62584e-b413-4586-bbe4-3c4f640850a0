package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.settle.WesureClaimedDTO;
import com.paic.ncbs.claim.model.dto.settle.WesureMedicalSyncDTO;
import com.paic.ncbs.claim.model.dto.settle.WesureReceiptDTO;
import com.paic.ncbs.claim.model.dto.settle.WesureSettleDTO;

public interface WesureSettleService {

    /**
     * 查询已理赔金额
     * @param wesureClaimedDTO
     * @return
     */
    WesureClaimedDTO getClaimedAmount(WesureClaimedDTO wesureClaimedDTO);

    /**
     * 查询发票是否已理赔
     * @param wesureReceiptDTO
     * @return
     */
    WesureReceiptDTO getReceiptStatus(WesureReceiptDTO wesureReceiptDTO);

    void buildWesureSettle(WesureSettleDTO wesureSettleDTO);

    /**
     * 同步微保自动理算结果
     * @param wesureSettleDTO
     */
    void saveSettleDetail(WesureSettleDTO wesureSettleDTO);

    /**
     * 同步微保复核结果
     * @param wesureSettleDTO
     * @return
     */
    WesureSettleDTO getSettle(WesureSettleDTO wesureSettleDTO);

    void buildWesureMedical(WesureMedicalSyncDTO wesureMedicalSyncDTO);

    /**
     * 同步微保医疗单据
     * @param wesureMedicalSyncDTO
     * @return
     */
    void saveMedical(WesureMedicalSyncDTO wesureMedicalSyncDTO);

    /**
     * 生成理算复核结果,供微保查询
     * @param wholeCaseBaseDTO
     */
    void generateWesureVerify(WholeCaseBaseDTO wholeCaseBaseDTO);

}
