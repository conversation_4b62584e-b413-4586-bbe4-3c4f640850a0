package com.paic.ncbs.claim.service.accident.impl;

import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.RedisKeyConstants;
import com.paic.ncbs.claim.common.constant.TacheConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.accident.HugeAccidentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.accident.HugeAccidentMeasureMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.HugeAccidentInfoDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO;
import com.paic.ncbs.claim.model.vo.accident.HugeAccidentInfoVO;
import com.paic.ncbs.claim.model.vo.accident.HugeAccidentMeasureVO;
import com.paic.ncbs.claim.model.vo.accident.MeasureVO;
import com.paic.ncbs.claim.service.accident.AccidentService;
import com.paic.ncbs.claim.service.accident.HugeAccidentInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service("hugeAccidentInfoService")
public class HugeAccidentInfoServiceImpl implements HugeAccidentInfoService {

    @Autowired
    private HugeAccidentInfoMapper hugeAccidentInfoDao;

    @Autowired
    private AccidentService accidentService;

    @Autowired
    private HugeAccidentMeasureMapper hugeAccidentMeasureDao;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public String getAccidentCode(String accidentName) {
        if (StringUtils.isBlank(accidentName)) {
            return null;
        }

        HugeAccidentInfoDTO queryDTO = new HugeAccidentInfoDTO();
        queryDTO.setAccidentName(accidentName);
        HugeAccidentInfoDTO accidentInfo = hugeAccidentInfoDao.queryOneByCondition(queryDTO);
        if (Objects.nonNull(accidentInfo)) {
            return accidentInfo.getAccidentCode();
        }

        RLock lock = redissonClient.getLock(RedisKeyConstants.ACCIDENT_CODE_LOCK);
        try {
            lock.lock();

            accidentInfo = hugeAccidentInfoDao.queryOneByCondition(queryDTO);
            if (Objects.nonNull(accidentInfo)) {
                return accidentInfo.getAccidentCode();
            }

            String userId = WebServletContext.getUserId();
            String year = DateUtils.getYear();
            RAtomicLong atomicLong = redissonClient.getAtomicLong(RedisKeyConstants.getAccidentCodeSeq(year));
            long seq = atomicLong.incrementAndGet();
            String accidentCode = String.format("%s-%s", year, seq);

            accidentInfo = new HugeAccidentInfoDTO();
            accidentInfo.setIdAhcsHugeAccidentInfo(UuidUtil.getUUID());
            accidentInfo.setCreatedBy(userId);
            accidentInfo.setUpdatedBy(userId);
            accidentInfo.setAccidentName(accidentName);
            accidentInfo.setAccidentCode(accidentCode);
            accidentInfo.setIsEffective(BaseConstant.UPPER_CASE_Y);
            hugeAccidentInfoDao.addHugeAccidentInfo(accidentInfo);

            return accidentCode;
        } catch (Exception e) {
            log.error("HugeAccidentInfoService.getAccidentCode error", e);
            throw new GlobalBusinessException("生成巨灾编码错误");
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public List<HugeAccidentInfoVO> getHugeAccidentInfo(HugeAccidentInfoVO hugeAccidentInfo) {


        if (StringUtils.isNotEmpty(hugeAccidentInfo.getReportNo()) && null == hugeAccidentInfo.getStartDate()) {
            String taskId = TacheConstants.REPORT + TacheConstants.REPORT_TYPE_NORMAL;
            PersonAccidentDTO personAccidentDTO = accidentService.getPersonAccidentInfo(hugeAccidentInfo.getReportNo(),
                    hugeAccidentInfo.getCaseTimes().toString(), taskId, "1");
            if (personAccidentDTO != null && personAccidentDTO.getAccidentTime() != null) {
                Date accidentDate = personAccidentDTO.getAccidentTime();
                hugeAccidentInfo.setStartDate(accidentDate);
            }
        }

        List<HugeAccidentInfoVO> list = hugeAccidentInfoDao.getHugeAccidentInfo(hugeAccidentInfo);
        if (StringUtils.isNotEmpty(hugeAccidentInfo.getMeasureType())) {
            for (HugeAccidentInfoVO info : list) {
                List<HugeAccidentMeasureVO> measureVOs = new ArrayList<>();
                if (ListUtils.isNotEmpty(info.getAccidentMeasureVOs())) {
                    for (HugeAccidentMeasureVO measure : info.getAccidentMeasureVOs()) {

                        if (measure.getMeasureType().equals(hugeAccidentInfo.getMeasureType())) {
                            measureVOs.add(measure);
                            break;
                        }
                    }
                }
                info.setAccidentMeasureVOs(measureVOs);
            }
        }


        List<MeasureVO> measureAll = hugeAccidentMeasureDao.getMeasureMapAll();

        for (HugeAccidentInfoVO accidentInfoVO : list) {
            for (HugeAccidentMeasureVO measure : accidentInfoVO.getAccidentMeasureVOs()) {
                String codes = measure.getMeasureCode();
                if (StringUtils.isNotEmpty(codes)) {
                    List<MeasureVO> listMeasure = new ArrayList<>();
                    for (String code : codes.split(",")) {
                        for (MeasureVO vo : measureAll) {
                            if (code.equals(vo.getMeasureCode())) {
                                listMeasure.add(vo);
                                break;
                            }
                        }
                    }
                    measure.setMeasureNames(listMeasure);
                }
            }
        }

        return list;

    }
}