//package com.paic.ncbs.claim.interceptor;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.nacos.common.utils.HttpMethod;
//import com.paic.ncbs.claim.common.util.ApplicationContextUtil;
//import io.swagger.annotations.ApiOperation;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//import org.springframework.web.filter.OncePerRequestFilter;
//import org.springframework.web.method.HandlerMethod;
//import org.springframework.web.servlet.HandlerExecutionChain;
//import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
//import org.springframework.web.util.ContentCachingRequestWrapper;
//import org.springframework.web.util.ContentCachingResponseWrapper;
//
//import javax.servlet.FilterChain;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.lang.reflect.Method;
//import java.nio.charset.StandardCharsets;
//import java.util.Collection;
//import java.util.Enumeration;
//import java.util.HashMap;
//import java.util.Map;
//
//@Slf4j
//@Component
//public class CommonLoggingFilter extends OncePerRequestFilter {
//
//    @SneakyThrows
//    @Override
//    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) {
//        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
//        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
//        long start = System.currentTimeMillis();
//        try {
//            log.info("request begin");
//            filterChain.doFilter(requestWrapper, responseWrapper);
//
//        } finally {
//            log.info("----------------------unique log begin-------------------------");
//            log.info("URI             : {}", requestWrapper.getRequestURI());
////            log.info("Method          : {}", requestWrapper.getMethod());
////            log.info("Description     : {}", getMethodDes(request));
////            log.info("Request Headers : {}", JSON.toJSON(getRequestHeaders(requestWrapper)));
//            log.info("Request Body    : {}", getRequestBody(requestWrapper));
////            log.info("Status Code     : {}", responseWrapper.getStatus());
//            //log.info("Response Body   : {}",new String(responseWrapper.getContentAsByteArray());
//            log.info("Time Millis     : {}", (System.currentTimeMillis() - start) + " milliseconds");
//            log.info("----------------------unique log end--------------------------");
//            responseWrapper.copyBodyToResponse();
//        }
//        log.info("request finish");
//    }
//
//
//    private Map<String, Object> getRequestHeaders(HttpServletRequest request) {
//        Map<String, Object> headers = new HashMap<>();
//        Enumeration<String> headerNames = request.getHeaderNames();
//        while (headerNames.hasMoreElements()) {
//            String headerName = headerNames.nextElement();
//            headers.put(headerName, request.getHeader(headerName));
//        }
//        return headers;
//
//    }
//
//    private String getRequestBody(ContentCachingRequestWrapper requestWrapper) {
//        if (requestWrapper.getMethod().equals(HttpMethod.POST)){
//            String params = new String(requestWrapper.getContentAsByteArray(), StandardCharsets.UTF_8).replaceAll("[\r\n]","");
//            return params.length() > 8000 ? "request params is too long to log" : params;
//        }else {
//            return JSON.toJSONString(requestWrapper.getParameterMap());
//        }
//
//    }
//
//    private String getMethodDes(HttpServletRequest request) throws Exception {
//        RequestMappingHandlerMapping handlerMapping = ApplicationContextUtil.getBean("requestMappingHandlerMapping");
//        HandlerExecutionChain handlerExecutionChain = handlerMapping.getHandler(request);
//        if (handlerExecutionChain != null){
//            HandlerMethod handlerMethod = (HandlerMethod)handlerExecutionChain.getHandler();
//            Method method = handlerMethod.getMethod();
//            ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
//            return apiOperation != null ? apiOperation.value() : "";
//        }else {
//            return "";
//        }
//    }
//
//    private Map<String, Object> getResponseHeaders(ContentCachingResponseWrapper response) {
//        Map<String, Object> headers = new HashMap<>();
//        Collection<String> headerNames = response.getHeaderNames();
//        for (String headerName : headerNames) {
//            headers.put(headerName, response.getHeader(headerName));
//        }
//        return headers;
//    }
//
//
//}