package com.paic.ncbs.claim.common.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 第三方系统业务相关常量
 */
public class NcbsConstant {

    /**
     * 第三方系统接口返回结果编码
     */
    public static final String RESULT_CODE = "resultCode";

    /**
     * 第三方系统系统 超级用户
     */
    public static final String ZK_ADMIN_UM = "999999999";

    /**
     * 第三方系统系统 超级用户
     */
    public static final String ZK_ADMIN_NAME = "超级用户";

    /**
     * 第三方系统接口返回结果成功
     */
    public static final String RESULT_SUCCESS = "0000";

    /**
     * 第三方系统接口返回结果成功
     */
    public static final String RESULT_FAIL = "9999";

    /**
     * 第三方系统接口返回结果消息
     */
    public static final String RESULT_MSG = "resultMsg";

    /**
     * 个人账号 1
     */
    public static final String PERSON_ALACCOUNT = "1";
    //据报案号查询所属机构，判断医院编码类型 1-全国，2-北京，3-上海，监管上报需要
    public static final String ORG_TYPE_ONE = "1";
    public static final String HOSPITAL = "hospital";
    public static final String DIAGNOSE = "diagnose";
    public static final String OPERATION = "operation";

    /**
     * 第三方系统 岗位名称(旧)
     */
    public static final String SURVEY_GRADE_NAME = "查勘员";
    public static final String CHECK_DUTY_GRADE_NAME = "收单岗";
    public static final String MANUAL_SETTLE_GRADE_NAME = "理算岗";
    public static final String INVESTIGATE_GRADE_NAME = "调查员";
    public static final String SETTLE_REVIEW_GRADE_NAME = "核赔岗";
    public static final String REGISTER_REVIEW_GRADE_NAME = "立案审核岗";
    public static final String ZERO_CANCEL_APPROVE_GRADE_NAME = "零注审批岗";
    public static final String INVESTIGATE_APPROVE_GRADE_NAME = "调查审批岗";
    public static final String REJECT_APPROVE_GRADE_NAME = "拒赔审批岗";
    public static final String PAY_BACK_MODIFY_REVIEW_GRADE_NAME = "支付修改审批岗";
    public static final String COMMUNICATE_GRADE_NAME = "沟通岗";
    public static final String RESTART_CASE_APPROVAL_NAME = "重开审批岗";

    /**
     * 第三方系统 岗位名称(新)
     */
    public static final String SETTLE_REVIEW_GRADE_NAME_NEW = "核赔岗-新";
    public static final String CLAIM_MANAGER_GRADE_NAME_NEW = "理赔管理岗-新";
    public static final String TPA_OPERATOR_GRADE_NAME_NEW = "TPA作业岗-新";
    public static final String TPA_MANAGER_GRADE_NAME_NEW = "TPA管理岗-新";
    public static final String CUSTOMER_REPORT_NEGOTIATION_GRADE_NAME_NEW = "客服报案协谈岗-新";
    public static final String CASE_QUERY_GRADE_NAME_NEW = "赔案查询岗-新";
    public static final String CLAIM_OPERATOR_GRADE_NAME_NEW = "理赔作业岗-新";

    public static final Map<String,String> GRADE_MAP = new HashMap<>();
    static {
        GRADE_MAP.put(BpmConstants.OC_REPORT_TRACK,SURVEY_GRADE_NAME+ "," + CLAIM_OPERATOR_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_OPERATOR_GRADE_NAME_NEW);//报案跟踪
        GRADE_MAP.put(BpmConstants.OC_REGISTER_REVIEW,REGISTER_REVIEW_GRADE_NAME+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW);//立案审批
        GRADE_MAP.put(BpmConstants.OC_CHECK_DUTY,CHECK_DUTY_GRADE_NAME+ "," + CLAIM_OPERATOR_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_OPERATOR_GRADE_NAME_NEW);//收单
        GRADE_MAP.put(BpmConstants.OC_MANUAL_SETTLE,MANUAL_SETTLE_GRADE_NAME+ "," + CLAIM_OPERATOR_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_OPERATOR_GRADE_NAME_NEW);//理算
        GRADE_MAP.put(BpmConstants.OC_SETTLE_REVIEW,SETTLE_REVIEW_GRADE_NAME+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW);//核赔
        GRADE_MAP.put(BpmConstants.OC_REJECT_REVIEW,REJECT_APPROVE_GRADE_NAME+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW);//拒赔审批
        GRADE_MAP.put(BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT,ZERO_CANCEL_APPROVE_GRADE_NAME+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW);//零注审批
        GRADE_MAP.put(BpmConstants.OC_MAJOR_INVESTIGATE,INVESTIGATE_GRADE_NAME+ "," + CLAIM_OPERATOR_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_OPERATOR_GRADE_NAME_NEW);//调查
        GRADE_MAP.put(BpmConstants.OC_INVESTIGATE_APPROVAL,INVESTIGATE_APPROVE_GRADE_NAME+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW);//提调审批
        GRADE_MAP.put(BpmConstants.OC_INVESTIGATE_REVIEW,INVESTIGATE_APPROVE_GRADE_NAME+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW);//调查审批
        GRADE_MAP.put(BpmConstants.OC_PAY_BACK_MODIFY_REVIEW,PAY_BACK_MODIFY_REVIEW_GRADE_NAME+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW);//支付修改审批
        GRADE_MAP.put(BpmConstants.OC_PAY_BACK_MODIFY,MANUAL_SETTLE_GRADE_NAME+ "," + CLAIM_OPERATOR_GRADE_NAME_NEW+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_OPERATOR_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW+ "," + CUSTOMER_REPORT_NEGOTIATION_GRADE_NAME_NEW);//支付修改
        GRADE_MAP.put(BpmConstants.OC_COMMUNICATE,COMMUNICATE_GRADE_NAME+ "," + CLAIM_OPERATOR_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_OPERATOR_GRADE_NAME_NEW+ "," + CUSTOMER_REPORT_NEGOTIATION_GRADE_NAME_NEW);//沟通
        GRADE_MAP.put(BpmConstants.OC_PREPAY_REVIEW,SETTLE_REVIEW_GRADE_NAME+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW);//预赔审批
        GRADE_MAP.put(BpmConstants.OC_RESTART_CASE_APPROVAL,RESTART_CASE_APPROVAL_NAME+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW);//重开审批
        GRADE_MAP.put(BpmConstants.OC_WAIT_CUSTOMER_SUPPLEMENTS, SURVEY_GRADE_NAME + "," + CHECK_DUTY_GRADE_NAME + "," + MANUAL_SETTLE_GRADE_NAME+ "," + CLAIM_OPERATOR_GRADE_NAME_NEW+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_OPERATOR_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW+ "," + CUSTOMER_REPORT_NEGOTIATION_GRADE_NAME_NEW);//客户补材
        GRADE_MAP.put(BpmConstants.OC_FEE_INVOICE_MODIFY,MANUAL_SETTLE_GRADE_NAME+ "," + CLAIM_OPERATOR_GRADE_NAME_NEW+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_OPERATOR_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW+ "," + CUSTOMER_REPORT_NEGOTIATION_GRADE_NAME_NEW);//费用发票修改
        GRADE_MAP.put(BpmConstants.OC_ESTIMATE_CHANGE_REVIEW,REGISTER_REVIEW_GRADE_NAME+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW);//未决修正审批
        GRADE_MAP.put(BpmConstants.OC_RESTART_CASE_MODIFY,MANUAL_SETTLE_GRADE_NAME+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + CLAIM_OPERATOR_GRADE_NAME_NEW+ "," + TPA_OPERATOR_GRADE_NAME_NEW);//重开修改
        GRADE_MAP.put(BpmConstants.OC_HUMAN_INJURY_TRACKING,CLAIM_MANAGER_GRADE_NAME_NEW+ "," + CLAIM_OPERATOR_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW+ "," + TPA_OPERATOR_GRADE_NAME_NEW);//人伤跟踪
        GRADE_MAP.put(BpmConstants.OC_ENTRUSTMENT_APPROVAL,INVESTIGATE_APPROVE_GRADE_NAME+ "," + SETTLE_REVIEW_GRADE_NAME_NEW+ "," + CLAIM_MANAGER_GRADE_NAME_NEW+ "," + TPA_MANAGER_GRADE_NAME_NEW);//委托审批
    }
    private NcbsConstant(){}
}
