package com.paic.ncbs.claim.service.settle.factor.impl.strategy.limit;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.CumulativeLimitService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.ExtendedLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 月限额
 */
@RefreshScope
@Slf4j
@Service
public class CumulativeLimitServiceImpl implements CumulativeLimitService {
    @Autowired
    private List<ExtendedLimitService> extendedLimitServiceList;

    @Override
    public void cumulativeLimit(ClaimCaseDTO copyClaimCaseDTO) {
        List<PolicyPayDTO> policyPayDTOS = copyClaimCaseDTO.getPolicyPayDTOList();
        if (CollectionUtil.isEmpty(policyPayDTOS)) {
            return;
        }

        log.info("计算扩展限额案件：{},计算月限额之前的数据：{}", copyClaimCaseDTO.getReportNo(),
                JSON.toJSONString(copyClaimCaseDTO));
        policyPayDTOS.forEach(policyPayDTO -> {
            extendedLimitServiceList.forEach(item -> item.cumulativeLimit(policyPayDTO));
        });

    }

}
