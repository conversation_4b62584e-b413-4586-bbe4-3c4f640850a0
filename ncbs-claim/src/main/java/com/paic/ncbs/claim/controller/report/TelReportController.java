package com.paic.ncbs.claim.controller.report;

import com.paic.ncbs.claim.model.vo.report.TelReportResponseVO;
import com.paic.ncbs.claim.model.vo.report.TelReportVO;
import com.paic.ncbs.claim.service.report.TelReportService;
import com.paic.ncbs.claim.common.response.ResponseResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 电话报案
 */
@Api(tags = "电话报案信息")
@RestController
@RequestMapping("/public/telReportAction")
public class TelReportController {
    
    @Autowired
    private TelReportService telReportService;

    /**
     * 电话报案接口
     * @param telReportVO
     * @return
     */
    @PostMapping(value = "/saveTelReport")
    public ResponseResult<TelReportResponseVO> saveTelReport(@RequestBody TelReportVO telReportVO) {
        return ResponseResult.success(telReportService.saveTelReport(telReportVO));
    }

}