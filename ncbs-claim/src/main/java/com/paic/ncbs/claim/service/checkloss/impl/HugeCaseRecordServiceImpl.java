package com.paic.ncbs.claim.service.checkloss.impl;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.checkloss.HugeCaseRecordMapper;
import com.paic.ncbs.claim.service.checkloss.HugeCaseRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("hugeCaseRecordService")
public class HugeCaseRecordServiceImpl implements HugeCaseRecordService {


    @Autowired
    private HugeCaseRecordMapper hugeCaseRecordDao;


    @Override
    public int getHugeCaseRecordCount(String reportNo, Integer caseTimes) {
        LogUtil.audit("#大案呈报#reportNo=%s,caseTimes=%s", reportNo, caseTimes);
        return hugeCaseRecordDao.getHugeCaseRecordCount(reportNo, caseTimes);
    }


}