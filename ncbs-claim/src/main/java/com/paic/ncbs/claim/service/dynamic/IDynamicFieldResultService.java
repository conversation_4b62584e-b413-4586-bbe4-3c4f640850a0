package com.paic.ncbs.claim.service.dynamic;

import com.baomidou.mybatisplus.extension.service.IService;
import com.paic.ncbs.claim.dao.entity.dynamic.DynamicFieldsResultEntity;
import com.paic.ncbs.claim.model.vo.dynamic.DynamicFieldResultVO;

import java.util.List;

/**
 * <p>
 * 动态字段结果表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
public interface IDynamicFieldResultService extends IService<DynamicFieldsResultEntity> {

    void saveDynamicFieldResult(List<DynamicFieldResultVO> dynamicFieldResultVO) throws Exception;

    List<DynamicFieldResultVO.FieldDetailVo> getDynamicFieldResult(DynamicFieldResultVO dynamicFieldResultVO) throws Exception;

    List<DynamicFieldResultVO> getDynamicFieldResultReport(String reportNo, Integer caseTimes, List<String> tempIds) throws Exception;
}
