package com.paic.ncbs.claim.service.openapi;

import com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO;
import com.paic.ncbs.claim.model.vo.duty.DutySurveyVO;

/**
 * TPA立案接口
 */
public interface OnlineRegisterService {
    /**
     * tpa线上立案接口
     */
    public void register(DutySurveyVO dutySurveyVO);

    /**
     * 处理工作流
     * @param reportNo
     * @param caseTimes
     */
    public CaseRegisterApplyDTO dealWorkFlow(String reportNo, Integer caseTimes);
}
