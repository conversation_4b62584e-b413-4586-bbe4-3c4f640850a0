package com.paic.ncbs.claim.replevy.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
@Data
public class ClmsRelatedActualReceiptVo implements Serializable {
    private static final long serialVersionUID = 1L;
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 追偿 次数
     */
    private Integer subTimes;
    /**
     * 业务id
     */
    private String businessId;
    /**
     * 关联实收类型 1-追偿 2-负数重开 3-共保摊回
     */
    private String receiptType;
    /**
     * 业务编码
     */
    private String businessNo;

    /**
     * 交易日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private LocalDateTime transDate;

    /**
     * 收款付款 0-收款 1-付款
     */
    private String directionType;

    /**
     * 交易金额
     */
    private BigDecimal transAmount;
    /**
     * 用途
     * */
    private String transPurpose;
    /**
     *  备注：摘要
     *  */
    private String transAbstract;
    /**
     * 银企直联系统支付流水号
     * */
    private String paymentFlowNo;

    /**
     * 银行交易流水号
     */
    private String bankTransFlowNo;
    /**
     * 我方银行卡号
     */
    private String ourBankAccount;
    /**
     * 他方网点名称
     * */
    private String partnerBankBranchName;
    /**
     * 客户打款银行卡号
     */
    private String partnerBankAccount;

    /**
     * 客户打款银行名称
     */
    private String partnerBankName;
    /**
     * 客户银行账户名称
     */
    private String partnerBankAccountName;
    /**
     * 附言
     */
    private String postScript;

    /**
     * 核销余额
     */
    private BigDecimal writeOffRemainAmount;
    /**
     * 核销金额
     */
    private BigDecimal writeOffAmount;
    /**
     * 冻结标记，F-冻结/R-释放
     */
    private String freezeFlag;
    /**
     * 有效标志
     */
    private String validFlag;
    /**
     * 标志字段 0-待核销，31-核销成功，32-核销失败
     */
    private String flag;
}
