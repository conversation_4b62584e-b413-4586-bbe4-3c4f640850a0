package com.paic.ncbs.claim.controller.ahcs;


import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "整案记录信息")
@RestController
@RequestMapping("/ahcs/do/app/registerCaseAction")
public class AhcsRegisterCaseController extends BaseController {

    @Autowired
    private RegisterCaseService registerCaseService;


    @ApiOperation("是否存在整案记录")
    @RequestMapping(value = "/isExistRegisterRecord/{reportNo}/{caseTimes}", method = RequestMethod.GET)
    ResponseResult<Boolean> isExistRegisterRecord(@PathVariable("reportNo") String reportNo,
                                                  @PathVariable("caseTimes") Integer caseTimes) throws GlobalBusinessException {
        return ResponseResult.success(registerCaseService.isExistRegisterRecord(reportNo, caseTimes));
    }

}
