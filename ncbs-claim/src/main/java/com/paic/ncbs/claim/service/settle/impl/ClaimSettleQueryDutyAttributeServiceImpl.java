package com.paic.ncbs.claim.service.settle.impl;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.service.settle.ClaimSettleQueryDutyAttributeService;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 理赔责任明细属性
 */
public class ClaimSettleQueryDutyAttributeServiceImpl implements ClaimSettleQueryDutyAttributeService {

    @Override
    public void getDutyAttribute(List<PolicyPayDTO> policyPays) {
        if(CollectionUtils.isEmpty(policyPays)){
            throw new GlobalBusinessException("保单信息为空！");
        }
//        for (PolicyPayDTO :
//             ) {
//
//        }
    }
}
