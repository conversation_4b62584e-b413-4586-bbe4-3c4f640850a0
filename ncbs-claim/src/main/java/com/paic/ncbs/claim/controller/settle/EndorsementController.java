package com.paic.ncbs.claim.controller.settle;

import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.service.settle.EndorsementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "批单")
@RestController
@RequestMapping("/who/app/endorsementAction")
public class EndorsementController{

	@Autowired
	private EndorsementService endorsementService;

	@ApiOperation("查询批单信息")
	@GetMapping(value = "/getEndorsement/{reportNo}/{caseTimes}")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "reportNo", value = "报案号",dataTypeClass=String.class),
			@ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
	})
	public ResponseResult<EndorsementDTO> getEndorsementInfo(@PathVariable("reportNo") String reportNo,
                                                             @PathVariable("caseTimes") Integer caseTimes) throws GlobalBusinessException {
		return ResponseResult.success(endorsementService.getByReportNoAndCaseTime(reportNo, caseTimes));
	}

}
