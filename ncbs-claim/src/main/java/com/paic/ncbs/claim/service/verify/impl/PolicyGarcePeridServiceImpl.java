package com.paic.ncbs.claim.service.verify.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.PolicyGarcePeridDTO;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.service.verify.PolicyGarcePeridService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class PolicyGarcePeridServiceImpl implements PolicyGarcePeridService {
    @Autowired
    private FeePayMapper feePayMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private OcasRequest ocasRequest;
    @Override
    public PolicyGarcePeridDTO queyPolicyGarcePerid(String reportNo, Integer caseTimes) {
        //默认 宽限期出险标志 为false
        boolean garcePeridFlag=false;
        //根据reportNo获取出险时间
        Date accidentDate = taskInfoMapper.getAccidentDate(reportNo);
        if(ObjectUtil.isNull(accidentDate)){
            throw new GlobalBusinessException(GlobalResultStatus.NULL_ERROR.getCode(),"出险时间为空");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(
                "yyyy-MM-dd hh:mm:ss");
        String time = simpleDateFormat.format(accidentDate);
        //根据报案号查询所有保单号
        List<String> policyNos = feePayMapper.getAllPolicyByReportNo(reportNo, caseTimes);
        if(CollectionUtil.isEmpty(policyNos)){
            throw new GlobalBusinessException(GlobalResultStatus.NULL_ERROR.getCode(),"保单号为空");
        }


        //循环调用批改接口，判断保单号是否宽限期出险,有一条是则整个流程终止并给出提示
        for (String policyNo: policyNos) {
            CopyPolicyQueryVO vo = new CopyPolicyQueryVO();
            vo.setPolicyNo(policyNo);
            vo.setTime(time);
            String result = ocasRequest.isGarcePerid(vo);
            LogUtil.audit("checkGarcePerid policyNo:{},result:{}",policyNo,result);
            Map resultMap = JSON.parseObject(result, Map.class);
            if(Constants.SUCCESS.equals(resultMap.get("returnMsg")) && null != resultMap.get("data")){
                if(Boolean.valueOf(resultMap.get("data").toString())){
                    garcePeridFlag=true;
                    break;
                }
            }else{
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"调用批改宽限期判断接口异常,请重试");
            }

        }
        PolicyGarcePeridDTO dto = new PolicyGarcePeridDTO();
        dto.setGarcePeridFlag(garcePeridFlag);
        return dto;
    }
}
