package com.paic.ncbs.claim.controller;

import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.policy.ClaimRecordDTO;
import com.paic.ncbs.claim.model.dto.report.CustomerDTO;
import com.paic.ncbs.claim.model.dto.report.RecordDutyInfo;
import com.paic.ncbs.claim.service.policy.ClaimRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 保单理赔历史数据支持服务
 */
@RestController
@RequestMapping("/app/claimRecord")
public class ClaimRecordController {

    @Autowired
    private ClaimRecordService claimRecordService;

    @RequestMapping(value = "/getClaimRecordByPolicy",produces = {"application/json"},method = RequestMethod.POST)
    public ResponseResult<ClaimRecordDTO> getClaimRecordByPolicy(@RequestBody ClaimRecordDTO claimRecordDTO){
        if(claimRecordDTO == null || claimRecordDTO.getPolicyNo() == null){
            throw new GlobalBusinessException(ErrorCode.NOT_NULL_ERROR,"参数不能为空");
        }
        //查询未决客户列表
        List<CustomerDTO> unResolveList = claimRecordService.getUnResolvedReportNo(claimRecordDTO);
        //查询有赔款的客户列表
        List<CustomerDTO> customerList = claimRecordService.getCustomerByPolicy(claimRecordDTO);

        //查询责任是否有赔款
        List<RecordDutyInfo> recordDutyInfos = claimRecordDTO.getDutyList();
        if (ListUtils.isNotEmpty(recordDutyInfos)){
            List<RecordDutyInfo> dutyList = claimRecordService.getRecordDutyByPolicy(claimRecordDTO);
            recordDutyInfos.forEach(recordDutyInfo -> {
                if (ListUtils.isEmptyList(dutyList)){
                    recordDutyInfo.setIsHistoryFlag("N");
                } else {
                    // 2023/8/28
                    dutyList.forEach(duty->{
                        if (duty.getDutyCode().equals(recordDutyInfo.getDutyCode())){
                            recordDutyInfo.setIsHistoryFlag(duty.getIsHistoryFlag());
                        }
                    });
                }
                claimRecordDTO.setDutyList(recordDutyInfos);
            });

        }

        if(ListUtils.isNotEmpty(unResolveList)){
            claimRecordDTO.setUnResolved(true);
            claimRecordDTO.setUnResolvedCustomerList(unResolveList);
        }
        if(ListUtils.isNotEmpty(customerList)){
            claimRecordDTO.setExistsPay(true);
            claimRecordDTO.setCustomerList(customerList);
        }

        claimRecordService.getRiskProperty(claimRecordDTO);
        return ResponseResult.success(claimRecordDTO);
    }
}
