package com.paic.ncbs.claim.service.blacklist.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListRecord;
import com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListRecordMapper;
import com.paic.ncbs.claim.model.vo.blacklist.BlackListDictConverter;
import com.paic.ncbs.claim.model.vo.blacklist.ClmsBlackListRecordVO;
import com.paic.ncbs.claim.service.blacklist.ClmsBlackListRecordService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 黑名单信息记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Service
public class ClmsBlackListRecordServiceImpl implements ClmsBlackListRecordService {

    @Autowired
    private ClmsBlackListRecordMapper blackListRecordMapper;

    @Autowired
    private BlackListDictConverter dictConverter;

    @Override
    public ClmsBlackListRecordVO getBlackListRecordById(String id) throws Exception {
        ClmsBlackListRecord byBlackListId = blackListRecordMapper.getByBlackListId(id);
        if (byBlackListId != null){
            ClmsBlackListRecordVO vo = new ClmsBlackListRecordVO();
            BeanUtils.copyProperties(byBlackListId, vo);

            // 添加黑名单类型中文描述
            if (StringUtils.isNotBlank(byBlackListId.getPartyType())) {
                vo.setPartyTypeName(dictConverter.getPartyTypeNames(byBlackListId.getPartyType()));
            }
            // 添加证件类型中文描述
            if (StringUtils.isNotBlank(byBlackListId.getIdType())) {
                vo.setIdTypeName(dictConverter.getIdTypeName(byBlackListId.getIdType()));
            }
            // 添加风险类型中文描述
            if (StringUtils.isNotBlank(byBlackListId.getRiskType())) {
                vo.setRiskTypeName(dictConverter.getRiskTypeNames(byBlackListId.getRiskType()));
            }
            return vo;
        }
        return null;
    }
}
