package com.paic.ncbs.claim.controller.ahcs;


import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.instalment.PaymentInfoEditableVO;
import com.paic.ncbs.claim.model.vo.instalment.PaymentItemEditableVO;
import com.paic.ncbs.claim.model.vo.instalment.QueryParams;
import com.paic.ncbs.claim.service.instalment.InstalmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@Api(tags = "分期付款")
@Controller
@RequestMapping("/ahcs/do/app/instalmentAction")
public class InstalmentController extends BaseController {

    @Autowired
    private InstalmentService instalmentService;

    @ApiOperation(value = "检查是否存在分期付款案件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", dataType = "String", dataTypeClass=String.class, paramType = "Query"),
            @ApiImplicitParam(name = "caseTimes", value = "保单号", dataType = "Integer",dataTypeClass=Integer.class, paramType = "Query")
    })
    @GetMapping(value = "/checkIfCaseContainsInstalment")
    @ResponseBody
    public ResponseResult<Boolean> checkIfCaseContainsInstalment(@RequestParam(required = false) String reportNo, @RequestParam(required = false) Integer caseTimes) throws GlobalBusinessException {
        requireNotEmpty("报案号", reportNo);
        requireNotEmpty("赔付次数", caseTimes);
        return ResponseResult.success(instalmentService.checkIfCaseContainsInstalment(reportNo, caseTimes));
    }

    @ApiOperation(value = "是否允许更改付款项目")
    @PostMapping(value = "/isAllowChangePaymentItem")
    @ResponseBody
    public ResponseResult<List<PaymentItemEditableVO>> isAllowChangePaymentItem(@RequestBody QueryParams params) throws GlobalBusinessException {
        requireNotEmpty("入参", params);
        requireNotEmpty("报案号", params.getReportNo());
        requireNotEmpty("赔付次数", params.getCaseTimes());
        return ResponseResult.success(instalmentService.isAllowChangePaymentItem(params.getReportNo(), params.getCaseTimes()));
    }

    @ApiOperation(value = "是否允许更改支付信息")
    @RequestMapping(value = "/isAllowChangePaymentInfo", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<PaymentInfoEditableVO>> isAllowChangePaymentInfo(@RequestBody QueryParams params) throws GlobalBusinessException {
        requireNotEmpty("入参", params);
        requireNotEmpty("报案号", params.getReportNo());
        requireNotEmpty("赔付次数", params.getCaseTimes());
        return ResponseResult.success(instalmentService.isAllowChangePaymentInfo(params.getReportNo(), params.getCaseTimes()));
    }



    private void requireNotEmpty(String name, Object value) throws GlobalBusinessException {

        if (Objects.isNull(value)) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, name);
        }

        if (value instanceof String && StringUtils.isEmpty(String.valueOf(value))) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, name);
        }

    }


}
