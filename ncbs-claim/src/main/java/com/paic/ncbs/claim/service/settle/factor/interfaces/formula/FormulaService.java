package com.paic.ncbs.claim.service.settle.factor.interfaces.formula;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;

/**
 * 责任明细计算公式组装：
 *         SETTLE_REASON.put(DETAIL_TYPE_DEATH,"：责任明细保额");
 *         SETTLE_REASON.put(DETAIL_TYPE_DISABILITY,"：责任明细保额 * 伤残比例");
 *         SETTLE_REASON.put(DETAIL_TYPE_MAJOR_DISEASE,"：责任明细保额");
 *         SETTLE_REASON.put(DETAIL_TYPE_MEDICAL,"：（合理费用-免赔额）* 赔付比例");
 *         SETTLE_REASON.put(DETAIL_TYPE_ALLOWANCE,"：（津贴天数-免赔天数）* 津贴日额         ");
 *         SETTLE_REASON.put(DETAIL_TYPE_SERVICE,"");
 *         SETTLE_REASON.put(DETAIL_TYPE_QUOTA,"：责任明细保额");
 *         SETTLE_REASON.put(DETAIL_TYPE_OTHERS,"");
 *         SETTLE_REASON.put(DETAIL_TYPE_LOSS, "：损失额");
 *         SETTLE_REASON.put(DETAIL_TYPE_DUTY, "：责任明细保额");
 */
public interface FormulaService {
    String getFormula(DutyDetailPayDTO detail);
}
