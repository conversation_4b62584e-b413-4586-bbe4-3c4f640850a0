package com.paic.ncbs.claim.controller.report;

import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentInfoDTO;
import com.paic.ncbs.claim.model.vo.accident.HugeAccidentInfoVO;
import com.paic.ncbs.claim.service.accident.HugeAccidentInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.*;

@Api(tags = "事故意外事件")
@RestController
@RequestMapping("/report")
public class HugeAccidentController {

	@Autowired
	private HugeAccidentInfoService hugeAccidentInfoService;

	@RequestMapping(value = "/getHugeAccidentInfo", method = RequestMethod.GET)
	@ApiOperation(value = "查询事故或意外信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "certificateNo", value = "客户身份证号", required = true, dataType = "String", dataTypeClass=String.class,  paramType = "Query"),
			@ApiImplicitParam(name = "name", value = "姓名", required = true, dataType = "String", dataTypeClass=String.class,  paramType = "Query"),
			@ApiImplicitParam(name = "hugeAccidentType", value = " 重灾类型", required = true, dataType = "String", dataTypeClass=String.class,  paramType = "Query"),
			@ApiImplicitParam(name = "accidentDate", value = "出险时间", required = true, dataType = "String", dataTypeClass=String.class , paramType = "Query")
	})
	public ResponseResult<Object> getHugeAccidentInfo(String certificateNo, String name, String hugeAccidentType, String accidentDate) throws ParseException {
		if(StringUtils.isEmpty(hugeAccidentType)&&StringUtils.isEmpty(certificateNo) && StringUtils.isEmpty(name)){
			throw new GlobalBusinessException(GlobalResultStatus.EX_USER_945014);
		}
		if (StringUtils.isNotEmpty(hugeAccidentType)) {
			List<Map<String, Object>> hugeAccidentList = this.getHugeAccidentInfo(hugeAccidentType);
			return ResponseResult.success(hugeAccidentList);
		}
		if (StringUtils.isNotEmpty(certificateNo) && StringUtils.isNotEmpty(name)) {
			List<AccidentInfoDTO> hugeAccidentList = this.getHugeAccidentInfo(certificateNo, name, accidentDate);
			return ResponseResult.success(hugeAccidentList);
		}
		throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("证件号和姓名需同时传值"));
	}

	private List<Map<String, Object>> getHugeAccidentInfo(String hugeAccidentType) {

		LogUtil.info("Ahcs根据重灾类型查询详细重灾接口耗时:accidentType={}", hugeAccidentType);
		HugeAccidentInfoVO param = new HugeAccidentInfoVO();
		param.setAccidentType(hugeAccidentType);
		List<HugeAccidentInfoVO> hugeAccidentInfoVOS = Optional.ofNullable(hugeAccidentInfoService.getHugeAccidentInfo(param)).orElseGet(ArrayList::new);
		List<Map<String, Object>> hugeAccidentList = new ArrayList<>();
		for (HugeAccidentInfoVO hugeAccidentInfoVO : hugeAccidentInfoVOS) {

			Map<String, Object> hugeAccident = new HashMap<String, Object>();
			hugeAccident.put("hugeAccidentName", hugeAccidentInfoVO.getAccidentName());
			hugeAccident.put("hugeAccidentId", hugeAccidentInfoVO.getIdAhcsHugeAccidentInfo());
			hugeAccidentList.add(hugeAccident);
		}
		return hugeAccidentList;
	}

	private List<AccidentInfoDTO> getHugeAccidentInfo(String certificateNo, String name, String accidentDate) throws ParseException {
		LogUtil.info("Ahcs根据证件号和事故时间查询重灾入参:certificateNo={}, insuredName={}, startDate={}", certificateNo, name, accidentDate);
		HugeAccidentInfoVO param = new HugeAccidentInfoVO();
		param.setCertificateNo(certificateNo);
		param.setInsuredName(name);
		param.setStartDate(DateUtils.parseToFormatDate(accidentDate, DateUtils.FULL_DATE_STR));
		List<HugeAccidentInfoVO> hugeAccidentInfoVOS = Optional.ofNullable(hugeAccidentInfoService.getHugeAccidentInfo(param)).orElseGet(ArrayList::new);
		List<AccidentInfoDTO> hugeAccidentInfo = new ArrayList<>();
		for (HugeAccidentInfoVO hugeAccidentInfoVO : hugeAccidentInfoVOS) {
			AccidentInfoDTO hugeAccidentInfoDTO = new AccidentInfoDTO();
			hugeAccidentInfoDTO.setHugeAccidentId(hugeAccidentInfoVO.getIdAhcsHugeAccidentInfo());
			hugeAccidentInfoDTO.setHugeAccidentName(hugeAccidentInfoVO.getAccidentName());
			hugeAccidentInfoDTO.setHugeAccidentType(hugeAccidentInfoVO.getAccidentType());
			hugeAccidentInfo.add(hugeAccidentInfoDTO);
		}
		return hugeAccidentInfo;
	}
}
