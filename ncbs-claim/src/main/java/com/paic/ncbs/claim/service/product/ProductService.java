package com.paic.ncbs.claim.service.product;

import java.math.BigDecimal;
import java.util.Map;

public interface ProductService {


    /**
     * 根据产品大类获取价税分离的税率
     * @param productClassCode 产品大类
     * @return 价税分离的税率
     */
    BigDecimal selectTaxRateByPlanCode(String productClassCode);

    /**
     * 险种是否免税
     * @param paySerialNo
     * @return
     */
    Map<String, Boolean> getPlanTaxRate(String paySerialNo);

}
