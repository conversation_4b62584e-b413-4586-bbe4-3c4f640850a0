package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.dao.mapper.checkloss.VehiclDelayOtherMapper;
import com.paic.ncbs.claim.model.dto.checkloss.VehiclDelayOtherDTO;
import com.paic.ncbs.claim.service.checkloss.VehiclDelayOtherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class VehiclDelayOtherServiceImpl implements VehiclDelayOtherService {

	@Autowired
    VehiclDelayOtherMapper vehiclDelayOtherDao;

	@Override
	@Transactional
	public void saveVehiclDelayOther(VehiclDelayOtherDTO vehiclDelayOtherDTO) {
		String reportNo = vehiclDelayOtherDTO.getReportNo();
		Integer caseTimes = vehiclDelayOtherDTO.getCaseTimes();
		String channelProcessId = vehiclDelayOtherDTO.getIdAhcsChannelProcess();
		VehiclDelayOtherDTO existVehiclDelayOtherDTO = vehiclDelayOtherDao.getVehiclDelayOther(reportNo, caseTimes,null,vehiclDelayOtherDTO.getTaskCode(), channelProcessId);
		if(existVehiclDelayOtherDTO != null){

			vehiclDelayOtherDao.updateEffective(vehiclDelayOtherDTO);

		}
		vehiclDelayOtherDao.addVehiclDelayOther(vehiclDelayOtherDTO);
	}

	@Override
	public void removeVehiclDelay(String reportNo, Integer caseTime, String taskCode, String channelProcessId) {
		vehiclDelayOtherDao.removeVehiclDelayOther(reportNo, caseTime, taskCode, channelProcessId);
	}

	@Override
	public void updateEffective(VehiclDelayOtherDTO vehiclDelayOtherDTO) {
		vehiclDelayOtherDao.updateEffective(vehiclDelayOtherDTO);
	}

	public VehiclDelayOtherDTO getVehiclDelayOther(String reportNo, Integer caseTimes, String status, String taskCode, String channelProcessId) {
		return vehiclDelayOtherDao.getVehiclDelayOther(reportNo, caseTimes, status, taskCode, channelProcessId);
	}
}
