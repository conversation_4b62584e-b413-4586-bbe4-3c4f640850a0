package com.paic.ncbs.claim.model.vo.blacklist;

import com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListAudit;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("组装审批详情vo")
public class BlackListAuditDetailVO {
    private ClmsBlackListVO clmsBlackListVO;
    private ClmsBlackListAudit clmsBlackListAudit;
    private ClmsBlackListRecordVO clmsBlackListRecordVO;

    public BlackListAuditDetailVO(ClmsBlackListVO clmsBlackListVO, ClmsBlackListAudit clmsBlackListAudit, ClmsBlackListRecordVO clmsBlackListRecordVO) {
        this.clmsBlackListVO = clmsBlackListVO;
        this.clmsBlackListAudit = clmsBlackListAudit;
        this.clmsBlackListRecordVO = clmsBlackListRecordVO;
    }
}
