package com.paic.ncbs.claim.service.settle.factor.impl.formula;


import com.paic.ncbs.claim.common.enums.CalculationFactorEnum;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.formula.FormulaService;
import org.springframework.stereotype.Service;

/**
 * 津贴 理算公式：津贴天数-免赔天数）* 津贴日额
 */
@Service
public class AllowanceFormulaServiceImpl implements FormulaService {
    @Override
    public String getFormula(DutyDetailPayDTO detail) {
        return "(" +
                CalculationFactorEnum.ALLOWANCE_APPLY_DAY_IMPL.getCode() +
                "-" +
                CalculationFactorEnum.ALLOWANCE_REMIT_DAY_IMPL.getCode() +
                ")*" +
                CalculationFactorEnum.ALLOWANCE_AMOUNT_IMPL.getCode();
    }
}
