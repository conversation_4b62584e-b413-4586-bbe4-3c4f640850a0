package com.paic.ncbs.claim.controller.verify;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.RedisKeyConstants;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.DutyAttributeCodeEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.ahcs.PolicyGarcePeridDTO;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.settle.DutyAttributeValueDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyPayInfoDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyTaskDTO;
import com.paic.ncbs.claim.model.vo.settle.VerifyInfoVO;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.settle.MaxPayService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.verify.PolicyGarcePeridService;
import com.paic.ncbs.claim.service.verify.VerifyService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/ahcs/do/app/verifyAction")
@Api(tags = {"核赔"})
public class VerifyController extends BaseController {

    @Autowired
    private VerifyService verifyService;

    @Autowired
    private PolicyPayService policyPayService;

    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private PolicyGarcePeridService policyGarcePeridService;

    @Autowired
    private DutyAttributeMapper dutyAttributeMapper;

    @Autowired
    private MaxPayService maxPayService;
    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;
    @Autowired
    private DutyPayMapper dutyPayMapper;
    @Autowired
    private RedissonClient redissonClient;

    @ApiOperation(value = "核赔发送前校验")
    @PostMapping(value = "/checkVerify")
    public ResponseResult<Object> checkVerify(@RequestBody VerifyInfoVO verifyInfoVO) throws GlobalBusinessException {
        List<String> msgList = new ArrayList<>();
        verifyService.checkVerifyUserGrade(verifyInfoVO.getTaskId(), msgList);
        if (msgList.isEmpty()) {
            return ResponseResult.success("");
        }
        return ResponseResult.success(msgList.get(0));
    }

    @ApiOperation(value = "核赔发送")
    @PostMapping(value = "/addVerify")
    public ResponseResult<Object> addVerify(@RequestBody VerifyInfoVO verifyInfoVO) throws Exception {
        LogUtil.info("addVerify进入核赔报案号={}，赔付次数={},入参={}", verifyInfoVO.getReportNo(), verifyInfoVO.getCaseTimes(), JSON.toJSONString(verifyInfoVO));
        String key = RedisKeyConstants.VERIFY_SAVE_LOCK + verifyInfoVO.getReportNo() + ":" + verifyInfoVO.getCaseTimes();
        RLock lock = redissonClient.getLock(key);
        try {
            if (lock.tryLock()) {
                verifyInfoVO.getVerify().setReportNo(verifyInfoVO.getReportNo());
                checkVerifyResult(verifyInfoVO.getVerify());
                String endCseNo = commonService.generateNo(NoConstants.END_CASE_NO, VoucherTypeEnum.END_CASE_NO, WebServletContext.getDepartmentCode());
                wholeCaseBaseMapper.modifyWholeCaseEndCaseNo(endCseNo, verifyInfoVO.getReportNo(), verifyInfoVO.getCaseTimes());
                List<String> msgList = new ArrayList<>();
                verifyService.addVerify(verifyInfoVO, msgList);
                LogUtil.info("#核赔完成,报案号={}，赔付次数={}", verifyInfoVO.getReportNo(), verifyInfoVO.getCaseTimes());
                if (msgList.isEmpty()) {
                    return ResponseResult.success("");
                }
                return ResponseResult.success(msgList.get(0));
            } else {
                throw new GlobalBusinessException("核赔正在处理中！");
            }
        }finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void checkVerifyResult(VerifyDTO verify) throws GlobalBusinessException {
        if (null == verify || StringUtils.isEmptyStr(verify.getVerifyConclusion())) {
            LogUtil.info("参数verify或VerifyConclusion为空,verify=" + verify);
            throw new GlobalBusinessException(ErrorCode.Settle.ILLEGAL_VERIFY_CONCLUSION);
        }

        String verifyConclusion = verify.getVerifyConclusion();
        String[] conclusions = {VerifyDTO.CONCLUSION_PASS, VerifyDTO.CONCLUSION_UPDATE_PASS, VerifyDTO.CONCLUSION_NOT_PASS, VerifyDTO.CONCLUSION_AUDIT_BACK};
        if (!Arrays.asList(conclusions).contains(verifyConclusion)) {
            LogUtil.info("核赔结论不合法,verifyConclusion=" + verifyConclusion);
            throw new GlobalBusinessException(ErrorCode.Settle.ILLEGAL_VERIFY_CONCLUSION);
        }
        checkDutyAttribute(verify);
    }

    private void checkDutyAttribute(VerifyDTO verify) throws GlobalBusinessException {
        if (VerifyDTO.CONCLUSION_AUDIT_BACK.equals(verify.getVerifyConclusion()) || VerifyDTO.CONCLUSION_NOT_PASS.equals(verify.getVerifyConclusion())) {
            return;
        }
        //责任属性明细
        List<DutyAttributeValueDTO> dutyAttributeValueDTOList = dutyAttributeMapper.getAttributeValue(verify.getReportNo());
        if (CollectionUtils.isEmpty(dutyAttributeValueDTOList)) {
            return;
        }
        Map<String, List<DutyAttributeValueDTO>> policyDutyAttributeMap = dutyAttributeValueDTOList.stream().collect(Collectors.groupingBy(DutyAttributeValueDTO::getPolicyNo));
        Set<Map.Entry<String, List<DutyAttributeValueDTO>>> entrySet = policyDutyAttributeMap.entrySet();
        Map<String, Map<String, DutyAttributeValueDTO>> policyDutyAttributeConvertMap = new HashMap<>();
        for (Map.Entry<String, List<DutyAttributeValueDTO>> entry : entrySet) {
            Map<String, DutyAttributeValueDTO> dutyAbttributeMap = entry.getValue().stream().collect(Collectors.toMap(k -> k.getKey(), v -> v));
            policyDutyAttributeConvertMap.put(entry.getKey(), dutyAbttributeMap);
        }
        //客户报案信息
        ReportCustomerInfoEntity reportCustomerInfo = reportCustomerInfoMapper.getReportCustomerInfoByReportNo(verify.getReportNo());
        if (reportCustomerInfo == null) {
            return;
        }
        //查询理算金额大于0的险种责任
        List<DutyPayInfoDTO> dutyPayInfoDTOList = dutyPayMapper.getDutyPayInfoByReportAndCaseTimes(verify.getReportNo());
        if (CollectionUtils.isEmpty(dutyPayInfoDTOList)) {
            return;
        }
        Map<String, List<DutyPayInfoDTO>> policyDutyPayMap = dutyPayInfoDTOList.stream().collect(Collectors.groupingBy(DutyPayInfoDTO::getPolicyNo));
        for (Map.Entry<String, List<DutyPayInfoDTO>> entry : policyDutyPayMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            Map<String, DutyAttributeValueDTO> dutyAbttributeMap = policyDutyAttributeConvertMap.get(entry.getKey());
            if (dutyAbttributeMap == null || dutyAbttributeMap.size() == 0) {
                continue;
            }
            //获取保单责任赔付次数
            Map<String, Long> dutyPayNum = maxPayService.getDutyPayNum(entry.getKey(), verify.getReportNo());
            for (DutyPayInfoDTO dutyPayInfo : entry.getValue()) {
                //541--赔付次数限制
                DutyAttributeValueDTO dutyAttributeValue_541 = dutyAbttributeMap.get(dutyPayInfo.getKey() + "-" + DutyAttributeCodeEnum.ATTRIBUTE_541.getCode());
                if (dutyAttributeValue_541 != null && dutyPayNum.get(dutyPayInfo.getKey()) != null
                        && StringUtils.isNotEmpty(dutyAttributeValue_541.getAttributeValue())) {
                    //已赔付次数
                    BigDecimal payNumValue = new BigDecimal(dutyPayNum.get(dutyPayInfo.getKey()).toString());
                    //限制赔付次数
                    BigDecimal dutyValue = new BigDecimal(dutyAttributeValue_541.getAttributeValue());
                    //已赔付次数大于限制次数，不允许在赔付
                    if (payNumValue.doubleValue() >= dutyValue.doubleValue()) {
                        throw new GlobalBusinessException(GlobalResultStatus.ERROR.format(dutyAttributeValue_541.getDutyName()
                                + DutyAttributeCodeEnum.ATTRIBUTE_541.getName() + "已达到限制次数"));
                    }
                }

                //151--本次责任赔付金额限制
                DutyAttributeValueDTO dutyAttributeValue_151 = dutyAbttributeMap.get(dutyPayInfo.getKey() + "-" + DutyAttributeCodeEnum.ATTRIBUTE_151.getCode());
                if (dutyAttributeValue_151 != null && StringUtils.isNotEmpty(dutyAttributeValue_151.getAttributeValue())) {
                    //配置赔付限额
                    BigDecimal valueAmount = new BigDecimal(dutyAttributeValue_151.getAttributeValue());
                    //责任赔付金额
                    BigDecimal dutyPayAmount = new BigDecimal(dutyPayInfo.getDutyPayAmount());
                    //责任赔付金额不能大于限制金额
                    if (dutyPayAmount.doubleValue() > valueAmount.doubleValue()) {
                        throw new GlobalBusinessException(GlobalResultStatus.ERROR.format(dutyAttributeValue_151.getDutyName()
                                + DutyAttributeCodeEnum.ATTRIBUTE_151.getName() + " 不能大于" + valueAmount));
                    }
                }
            }
        }
    }

    @ApiOperation(value = "获取核赔审批记录")
    @GetMapping("/getVerifyList")
    public ResponseResult<List<VerifyDTO>> getVerifyList(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes") Integer caseTimes) {
        LogUtil.info("获取核赔审批记录reportNo={}", reportNo);
        return ResponseResult.success(verifyService.getVerifyList(reportNo, caseTimes));
    }

    @ApiOperation(value = "核赔页面获取理算信息")
    @GetMapping(value = "/queryCaseInfo/{reportNo}/{caseTimes}")
    public ResponseResult<Object> queryCaseInfo(@PathVariable("reportNo") String reportNo, @PathVariable("caseTimes") Integer caseTimes) {
        return ResponseResult.success(policyPayService.queryCaseInfo(reportNo, caseTimes));

    }

    @GetMapping(value = "/queryVerifyTask")
    public ResponseResult<VerifyTaskDTO> queryVerifyTask(@RequestParam("reportNo") String reportNo,
                                                         @RequestParam("caseTimes") Integer caseTimes,
                                                         @RequestParam("taskId") String taskId) {
        if (StringUtils.isEmptyStr(reportNo) || StringUtils.isEmptyStr(taskId) || caseTimes == null) {
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }

        return ResponseResult.success(verifyService.queryVerifyTask(reportNo, caseTimes, taskId));

    }

    /***
     * 保单宽限期查询
     * @return
     */
    @GetMapping(value = "/queyPolicyGarcePerid/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int", dataTypeClass = Integer.class)
    })
    public ResponseResult<PolicyGarcePeridDTO> queyPolicyGarcePerid(@PathVariable("reportNo") String reportNo,
                                                                    @PathVariable("caseTimes") Integer caseTimes) {
        PolicyGarcePeridDTO dto = policyGarcePeridService.queyPolicyGarcePerid(reportNo, caseTimes);
        return ResponseResult.success(dto);
    }

}
