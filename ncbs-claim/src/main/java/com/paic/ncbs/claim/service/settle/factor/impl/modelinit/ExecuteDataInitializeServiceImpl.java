package com.paic.ncbs.claim.service.settle.factor.impl.modelinit;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.modelinit.ExecuteDataInitializeService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.modelinit.ModelDataInitializeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ExecuteDataInitializeServiceImpl implements ExecuteDataInitializeService {
    /**
     * 模型初始化接口
     */
    @Autowired
    private List<ModelDataInitializeService> modelDataInitializeServices;
    @Override
    public ClaimCaseDTO initialize(ClaimCaseDTO claimCaseDTO) {
        if(CollectionUtil.isEmpty(modelDataInitializeServices)){
            throw new GlobalBusinessException("理算模型初始化实现类不能为空");
        }
        for (ModelDataInitializeService modelDataInitializeService : modelDataInitializeServices) {
            modelDataInitializeService.initialize(claimCaseDTO);
        }
        return claimCaseDTO;
    }
}
