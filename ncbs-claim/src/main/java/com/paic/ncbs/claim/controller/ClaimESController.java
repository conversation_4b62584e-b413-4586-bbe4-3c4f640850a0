package com.paic.ncbs.claim.controller;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimInfoToESVO;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimTaskInfoToESVO;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 理赔对接ES
 */
@RestController
@RequestMapping("/app/claimES")
public class ClaimESController {

    @Autowired
    private TaskListService taskListService;

    /**
     * ES获取理赔最新任务信息
     * @param idAhcsTaskInfo 任务主键
     * @return 任务结果信息
     */
    @RequestMapping(value = "/getClaimTaskInfoListToES", method = RequestMethod.GET)
    @ApiOperation(value = "ES获取理赔最新任务信息")
    public ClaimTaskInfoToESVO getClaimTaskInfoListToES(@RequestParam("idAhcsTaskInfo") String idAhcsTaskInfo){
        return taskListService.getClaimESTaskInfoList(idAhcsTaskInfo);
    }

    /**
     * 通过报案号修改案件信息给ES同步数据
     * @param reportNoList 报案号集合
     */
    @PostMapping(value = "/modifyTaskInfoOnES")
    @ApiOperation(value = "通过报案号修改案件信息给ES同步数据")
    public Map<String,String> modifyTaskInfoOnES(@RequestBody List<String> reportNoList){
        return taskListService.modifyTaskInfoOnES(reportNoList);
    }

    /**
     * 全量同步理赔任务数据到ES
     */
    @RequestMapping(value = "/pushAllTaskInfoToES", method = RequestMethod.GET)
    @ApiOperation(value = "全量同步理赔任务数据到ES")
    public Map<String,String> pushAllTaskInfoToES(@RequestParam("status") String status,@RequestParam("updatedStarTDate") String updatedStarTDate,@RequestParam("updatedEndDate") String updatedEndDate){
        return taskListService.pushAllTaskInfoToES(status,updatedStarTDate,updatedEndDate);
    }

    /**
     * ES中理赔待处理任务数量监控
     */
    @PostMapping(value = "/checkESTaskCount")
    @ApiOperation(value = "ES中理赔待处理任务数量监控")
    public ResponseResult checkESTaskCount(){
        taskListService.checkESTaskCount();
        return ResponseResult.success();
    }

    /**
     * ES获取理赔信息
     * @param idCase 任务主键
     * @return 理赔信息
     */
    @RequestMapping(value = "/getClaimInfoToES", method = RequestMethod.GET)
    @ApiOperation(value = "ES获取理赔信息")
    public ClaimInfoToESVO getClaimInfoToES(@RequestParam("idCase") String idCase){
        return taskListService.getClaimInfoToES(idCase);
    }

    /**
     * 查询ES和数据库之间的差异列表
     *
     * @return 包含差异列表的Map，键为报告编号，值为差异任务编号的集合
     */
    @PostMapping(value = "/queryESAndDBDiffList")
    @ApiOperation(value = "查询ES和数据库之间的差异列表")
    public ResponseResult<Map<String, Set<String>>> queryESAndDBDiffList(){
        return ResponseResult.success(taskListService.queryESAndDBDiffList(null,null));
    }
}