package com.paic.ncbs.claim.service.settle.factor.impl.formula;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.formula.FormulaService;
import org.springframework.stereotype.Service;

/**
 * 身故 理算公式：责任明细保额
 */
@Service
public class DeathFormulaServiceImpl implements FormulaService {
    @Override
    public String getFormula(DutyDetailPayDTO detail) {
        return null;
    }
}
