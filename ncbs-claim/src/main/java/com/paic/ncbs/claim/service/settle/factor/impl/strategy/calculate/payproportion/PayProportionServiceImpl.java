package com.paic.ncbs.claim.service.settle.factor.impl.strategy.calculate.payproportion;

import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EveryDayBillInfoDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * 赔付比例
 */
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service
public class PayProportionServiceImpl extends CalculateAmountService {
    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {
        DutyDetailPayDTO detail = paramsDTO.getDutyDetailPayDTO();
        EveryDayBillInfoDTO  everyBillInfo = paramsDTO.getEveryDayBillInfoDTO();
        BigDecimal proportion = BigDecimal.ZERO;
        if(Objects.equals("Y",detail.getIsDistinguishSocia())){
            //区分社保的情况
            if(Objects.equals("2",detail.getIsSociaSecurity())){
                //有社保
                proportion =  getPayproportion(detail,everyBillInfo);

            }else{
                //无社保
                proportion =  getNoSocialPayproportion(detail,everyBillInfo);
            }
        }else{
            //不区分社保的情况：直接取固定赔付比列，不会存在这种情况，业务保证客户默认投保时就是有社保身份的，此处只是做一个逻辑兜底
            if(Objects.isNull(detail.getPayProportion())){
                proportion=BigDecimal.ONE;
            }else{
                proportion=detail.getPayProportion();
            }
        }

        paramsDTO.getSettleFactor().setPayProportion(proportion);
        paramsDTO.getSettleFactor().setCalculateAmount(proportion);

    }

    private BigDecimal getNoSocialPayproportion(DutyDetailPayDTO detail, EveryDayBillInfoDTO everyBillInfo) {
        BigDecimal proportion=BigDecimal.ZERO;
        //无社保区分经医保结算
        if(Objects.equals("3",detail.getNoSocialMedicalSettle())){
            //无社保经医保结算的发票赔付比例
            if(Objects.equals("Y",everyBillInfo.getMedicalSettleFlag())){
                if(Objects.nonNull(detail.getNoSocMedicalProportion())){
                    proportion=nvl(detail.getNoSocMedicalProportion(),1);
                }else {
                    proportion= nvl(detail.getPayProportion(),1);
                }
            }else{//无社保未经医保结算的赔付比例
                proportion= getNoSocilProportion(detail);
            }
        }else{//无社保不区分经医保结算

            proportion= detail.getPayProportion();
        }
        return proportion;
    }

    /**
     * 获取有社保赔付比例
     *
     * @param detail
     * @param everyBillInfo
     */
    private BigDecimal getPayproportion(DutyDetailPayDTO detail, EveryDayBillInfoDTO everyBillInfo) {
        BigDecimal proportion=BigDecimal.ZERO;
        //区分经医保结算
        if(Objects.equals("3",detail.getIsSocialMedicalSettle())){
            //有社保经医保结算的发票的赔付比例
            if(Objects.equals("Y",everyBillInfo.getMedicalSettleFlag())){
                if(Objects.nonNull(detail.getIsSocMedicalProportion())){
                    proportion=nvl(detail.getIsSocMedicalProportion(),1);
                }else {
                    proportion= nvl(detail.getPayProportion(),1);
                }
            }else{//有社保未经医保结算的发票赔付比例
                //获取有社保未经医保结算的赔付比例
                proportion =getNoMediclProportion(detail);
            }

        }else{ //不区分是否经医保结算
            proportion =  detail.getPayProportion();
        }

        return proportion;
    }
    private BigDecimal getNoMediclProportion(DutyDetailPayDTO detail) {

        BigDecimal proporttion=BigDecimal.ZERO;
        if(Objects.nonNull(detail.getIsSocNoMedicalProportion())){
            proporttion=nvl(detail.getIsSocNoMedicalProportion(),1);
            LogUtil.info("责任编码={}，有社保配置了未经医保结算的赔付比例为 就用这个比例{}",detail.getDutyCode(), BigDecimalUtils.toString(proporttion));
        }else if(Objects.nonNull(detail.getIsSocPenaltyProportion())) {
            if(Objects.nonNull(detail.getIsSocMedicalProportion())){
                //有社保 经医保结算的赔付比例不为空的情况  赔付比例=经医保结算的比例*罚则
                proporttion=nvl(detail.getIsSocMedicalProportion(),1).multiply(nvl(detail.getIsSocPenaltyProportion(),1));
                LogUtil.info("责任编码={}，有社保配置了罚则，经医保结算的赔付比例不为空的情况  赔付比例=经医保结算的比例*罚则={}",detail.getDutyCode(),BigDecimalUtils.toString(proporttion));
            }else{
                //如果经医保结算的赔付比例为空了 就去拿最外层的固定赔付比例
                proporttion=nvl(detail.getPayProportion(),1).multiply(nvl(detail.getIsSocPenaltyProportion(),1));
                LogUtil.info("责任编码={}，经医保结算的赔付比例为空了 就去拿最外层的固定赔付比例{}",detail.getDutyCode(),BigDecimalUtils.toString(proporttion));

            }

        }else{
            proporttion=nvl(detail.getPayProportion(),1);
            LogUtil.info("责任编码={}，有社保没有配置未经医保结算比例和罚则默认拿最外层赔付比例={}",detail.getDutyCode(),BigDecimalUtils.toString(proporttion));

        }
        return proporttion;

    }
    private BigDecimal getNoSocilProportion(DutyDetailPayDTO detail) {

        BigDecimal noProporttion=BigDecimal.ZERO;
        if(Objects.nonNull(detail.getNoSocNoMedicalProportion())){
            noProporttion=nvl(detail.getNoSocNoMedicalProportion(),1);
            LogUtil.info("责任编码={}，被保险人无社保配置了未经医保结算的赔付比例为 就用这个比例{}",detail.getDutyCode(),BigDecimalUtils.toString(noProporttion));
        }else if(Objects.nonNull(detail.getNoSocPenaltyProportion())) {
            if(Objects.nonNull(detail.getNoSocMedicalProportion())){
                //有社保 经医保结算的赔付比例不为空的情况  赔付比例=经医保结算的比例*罚则
                noProporttion=nvl(detail.getNoSocMedicalProportion(),1).multiply(nvl(detail.getNoSocPenaltyProportion(),1));
                LogUtil.info("责任编码={}，被保险人无社保配置了罚则，无社保经医保结算的赔付比例不为空的情况  赔付比例=无社保经医保结算的比例*罚则={}",detail.getDutyCode(),BigDecimalUtils.toString(noProporttion));
            }else{
                //如果经医保结算的赔付比例为空了 就去拿最外层的固定赔付比例
                noProporttion=nvl(detail.getPayProportion(),1).multiply(nvl(detail.getNoSocPenaltyProportion(),1));
                LogUtil.info("责任编码={}，被保险人无社保 经医保结算的赔付比例为空了 就去拿最外层的固定赔付比例{}",detail.getDutyCode(),BigDecimalUtils.toString(noProporttion));

            }

        }else{
            noProporttion=nvl(detail.getPayProportion(),1);
            LogUtil.info("责任编码={}，被保险人无社保没有配置未经医保结算比例和罚则默认拿最外层赔付比例={}",detail.getDutyCode(),BigDecimalUtils.toString(noProporttion));

        }
        return noProporttion;

    }
}
