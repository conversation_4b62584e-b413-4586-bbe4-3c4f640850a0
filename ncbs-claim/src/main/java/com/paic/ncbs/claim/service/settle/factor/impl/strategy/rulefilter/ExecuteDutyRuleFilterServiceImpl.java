package com.paic.ncbs.claim.service.settle.factor.impl.strategy.rulefilter;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.rulefilter.ExecuteDutyRuleFilterService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.rulefilter.commonrule.CommonRuleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 核责实现
 */
@Slf4j
@Service
public class ExecuteDutyRuleFilterServiceImpl implements ExecuteDutyRuleFilterService {
    @Autowired
    private List<CommonRuleService> commonRuleServiceImplList;
    @Override
    public ClaimCaseDTO ruleFilter(ClaimCaseDTO bo) {
        log.info("核责开始报案号={}，入参={}",bo.getReportNo(), JsonUtils.toJsonString(bo));
        //规则执行
        if(CollectionUtil.isNotEmpty(commonRuleServiceImplList)){
            for (CommonRuleService ruleSercieImpl: commonRuleServiceImplList) {
                ruleSercieImpl.exeComonRule(bo);
            }
        }
        log.info("核责结束报案号={}，返回结果数据={}",bo.getReportNo(), JsonUtils.toJsonString(bo));

        return bo;
    }
}
