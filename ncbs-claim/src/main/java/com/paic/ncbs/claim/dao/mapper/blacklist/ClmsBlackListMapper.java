package com.paic.ncbs.claim.dao.mapper.blacklist;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackList;
import com.paic.ncbs.claim.model.vo.blacklist.ClmsBlackListVO;

import java.util.List;

/**
 * <p>
 * 黑名单信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface ClmsBlackListMapper extends BaseMapper<ClmsBlackList> {
    /**
     * 根据id查询黑名单信息
     */
    ClmsBlackList getBlackListById(String id);

    /**
     * 根据条件查询黑名单信息
     */
    List<ClmsBlackList> getBlackListByCondition(ClmsBlackListVO clmsBlackListVO);

    /**
     * 保存黑名单信息
     */
    void saveBlackList(ClmsBlackList clmsBlackList);

    /**
     * 修改黑名单信息
     */
    void updateBlackList(ClmsBlackList clmsBlackList);

    /**
     * 根据姓名和证件号码查询黑名单信息
     */
    int countByNameAndCertNo(String partyName, String idNum);

}
