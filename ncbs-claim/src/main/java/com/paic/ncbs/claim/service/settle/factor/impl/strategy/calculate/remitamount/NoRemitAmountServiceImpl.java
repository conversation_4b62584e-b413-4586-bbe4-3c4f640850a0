package com.paic.ncbs.claim.service.settle.factor.impl.strategy.calculate.remitamount;

import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 没有配置免赔额就没有免赔额
 */
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service("noRemitAmountServiceImpl")
public class NoRemitAmountServiceImpl extends CalculateAmountService {
    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {
        paramsDTO.getSettleFactor().setRemitAmount(BigDecimal.ZERO);
        paramsDTO.getSettleFactor().setCalculateAmount(BigDecimal.ZERO);
    }
}
