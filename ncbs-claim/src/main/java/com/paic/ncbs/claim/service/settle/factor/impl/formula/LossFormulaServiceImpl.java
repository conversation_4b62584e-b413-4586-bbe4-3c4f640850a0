package com.paic.ncbs.claim.service.settle.factor.impl.formula;

import com.paic.ncbs.claim.common.enums.CalculationFactorEnum;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.formula.FormulaService;
import org.springframework.stereotype.Service;

/**
 * 损失理算公式：损失额
 */
@Service
public class LossFormulaServiceImpl implements FormulaService {
    @Override
    public String getFormula(DutyDetailPayDTO detail) {
        return CalculationFactorEnum.SUBSTANCE_LOSS_IMPL.getCode() +
                "+" +
                CalculationFactorEnum.CASH_LOSS_IMPL.getCode() +
                "+" +
                CalculationFactorEnum.ONCE_ALLOWANCE_IMPL.getCode() +
                "+" +
                CalculationFactorEnum.TRAVEL_DELAY_IMPL.getCode() +
                "+" +
                CalculationFactorEnum.RESCUE_INFO_IMPL.getCode();
    }
}
