package com.paic.ncbs.claim.service.checkloss;


import com.paic.ncbs.claim.model.dto.checkloss.BaggageDelayDTO;

public interface BaggageDelayService {


	public void saveBaggageDelay(BaggageDelayDTO baggageDelayDTO);


	public void removeBaggageDelay(String reportNo, Integer caseTimes, String taskCode, String channelProcessId);

	void updateEffective(BaggageDelayDTO baggageDelayDTO);


	public BaggageDelayDTO getBaggageDelay(String reportNo, Integer caseTimes, String status, String taskCode, String channelProcessId);


	public BaggageDelayDTO queryBaggageDelay(String flightNo, String taskCode);

}
