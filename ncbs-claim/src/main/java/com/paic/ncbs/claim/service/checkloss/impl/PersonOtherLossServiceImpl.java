package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.model.dto.duty.PersonOtherLossDTO;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonOtherLossMapper;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.checkloss.PersonOtherLossService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PersonOtherLossServiceImpl implements PersonOtherLossService {

    @Autowired
    PersonOtherLossMapper personOtherLossDao;

    @Autowired
    private AhcsCommonService ahcsCommonService;

    @Override
    public void savePersonOtherLoss(List<PersonOtherLossDTO> personOtherLossList, String reportNo, Integer caseTimes, String taskId, String idAhcsChannelProcess) {
        List<PersonOtherLossDTO> existPersonOtherLossList = personOtherLossDao.getPersonOtherLoss(reportNo, caseTimes, null, taskId, idAhcsChannelProcess);
        if (ListUtils.isNotEmpty(existPersonOtherLossList)) {
            personOtherLossDao.removePersonOtherLoss(reportNo, caseTimes, taskId, idAhcsChannelProcess);
        }
        if (ListUtils.isNotEmpty(personOtherLossList)) {
            ahcsCommonService.batchHandlerTransactionalWithArgs(PersonOtherLossMapper.class, personOtherLossList, ListUtils.GROUP_NUM, "addPersonOtherLoss");
        }
    }

    @Override
    public List<PersonOtherLossDTO> getPersonOtherLoss(String reportNo, Integer caseTimes, String status, String taskId, String channelProcessId) {
        return personOtherLossDao.getPersonOtherLoss(reportNo, caseTimes, status, taskId, channelProcessId);
    }

}
