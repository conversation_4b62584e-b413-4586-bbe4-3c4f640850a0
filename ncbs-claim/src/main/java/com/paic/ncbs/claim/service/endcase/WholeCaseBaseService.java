package com.paic.ncbs.claim.service.endcase;

import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.service.base.BaseService;

import java.util.Date;
import java.util.List;

public interface WholeCaseBaseService extends BaseService<WholeCaseBaseEntity> {

    List<WholeCaseBaseEntity> getWholeCaseBase(String report);

    WholeCaseBaseDTO getWholeCaseBase(String reportNo, int caseTimes);

    WholeCaseBaseDTO getWholeCaseBase2(String reportNo, int caseTimes);

    void modifyHugeAccident(WholeCaseBaseDTO wholeCaseBaseDTO);

    void modifyWholeCaseBase(WholeCaseBaseDTO wholeCaseBaseDTO);

    Date getReportDate(String reportNo);

    String getWholeCaseStatus(String reportNo, Integer caseTimes) throws GlobalBusinessException;

	WholeCaseBaseDTO getHugeInfo(String reportNo, Integer caseTimes);

    void autoEstimateAmount(String reportNo, Integer caseTimes);

    void saveCaseRegisterInfo(String reportNo, Integer caseTimes, String updatedBy) throws GlobalBusinessException;

    WholeCaseBaseDTO getWholeCaseIndemnityStatus(String reportNo, Integer caseTimes) throws GlobalBusinessException;
    void updateIsPersonTrace(String reportNo, Integer caseTimes, String ispPersonTrace);
}
