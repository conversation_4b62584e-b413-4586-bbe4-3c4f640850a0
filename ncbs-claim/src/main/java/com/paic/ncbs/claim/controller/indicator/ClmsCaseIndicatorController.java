package com.paic.ncbs.claim.controller.indicator;

import com.paic.ncbs.claim.common.constant.IndicatorEnum;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.service.dashboard.ManageDashBoardService;
import com.paic.ncbs.claim.service.indicators.impl.*;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 理赔时效计算操作入口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@RestController
@RequestMapping("/public/indicator")
@Api(tags = {"时效计算"})
@Slf4j
public class ClmsCaseIndicatorController {
    @Resource
    private Indicator4firstEndCase firstEndCase;
    @Resource
    private Indicator4claimRegistration claimRegistration;
    @Resource
    private Indicator4reopenEndCase reopenEndCase;
    @Resource
    private Indicator4outInvestigate outInvestigate;
    @Resource
    private Indicator4communicate communicate;
    @Resource
    private Indicator4secondUnderwriting secondUnderwriting;
    @Autowired
    private ManageDashBoardService manageDashBoardService;

    @Operation(summary = "first-end-case", description = "首次结案时效计算")
    @GetMapping("/firstEndCase")
    public void firstEndCase() {
        firstEndCase.calcAndSave(IndicatorEnum.FIRST_END_CASE);
    }

    @Operation(summary = "claim-registration", description = "立案时效计算")
    @GetMapping("/claimRegistration")
    public void claimRegistration() {
        claimRegistration.calcAndSave(IndicatorEnum.CLAIM_REGISTRATION);
    }

    @Operation(summary = "reopen-end-case", description = "重开结案时效计算")
    @GetMapping("/reopenEndCase")
    public void reopenEndCase() {
        reopenEndCase.calcAndSave(IndicatorEnum.REOPEN_END_CASE);
    }

    @Operation(summary = "out-investigate", description = "公估调查时效计算")
    @GetMapping("/outInvestigate")
    public void outInvestigate() {
        outInvestigate.calcAndSave(IndicatorEnum.OUT_INVESTIGATE);
    }

    @Operation(summary = "communicate", description = "沟通时效计算")
    @GetMapping("/communicate")
    public void communicate() {
        communicate.calcAndSave(IndicatorEnum.COMMUNICATE);
    }

    @Operation(summary = "second-underwriting", description = "二核时效计算")
    @GetMapping("/secondUnderwriting")
    public void secondUnderwriting() {
        secondUnderwriting.calcAndSave(IndicatorEnum.SECOND_UNDERWRITING);
    }

    @Operation(summary = "time-data-his", description = "轨迹表定时同步")
    @GetMapping(value = "/timeDataToHis")
    public ResponseResult<String> timeDataToHis(){
        manageDashBoardService.timeDataToHis();
        return ResponseResult.success("success");
    }

}
