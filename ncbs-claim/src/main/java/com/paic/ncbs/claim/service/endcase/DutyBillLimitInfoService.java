package com.paic.ncbs.claim.service.endcase;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface DutyBillLimitInfoService {
    /**
     * 更新状态为已结案
     * @param reportNo
     * @param caseTimes
     */
    void dealBIllLimtData(String reportNo,Integer caseTimes);

    /**
     * 根据报案号和赔付次数删除第一次理算产生的日限额赔付记录
     * @param reportNo
     * @param caseTimes
     */
    void deleteByReportNoAndCaseTimes(String reportNo, Integer caseTimes);

    /**
     * 批量保存
     * @param list
     */
    void saveList(List<DutyBillLimitInfoDTO> list);

    @Transactional
    void saveListBydutyDetailCode(List<DutyBillLimitInfoDTO> list);

    void saveHistoryDutyLimit(Date billDate, DutyDetailPayDTO detailPayDTO, BigDecimal settleAmount);
    List<DutyBillLimitInfoDTO> getDutyLimitData(DutyBillLimitInfoDTO dto);
    void updateDutyBillLimitAmount(List<DutyBillLimitInfoDTO> list);
}
