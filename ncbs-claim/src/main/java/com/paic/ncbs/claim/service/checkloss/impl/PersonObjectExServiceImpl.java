package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.model.dto.duty.PersonObjectExDTO;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonObjectExMapper;
import com.paic.ncbs.claim.service.checkloss.PersonObjectExService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("personObjectExService")
public class PersonObjectExServiceImpl implements PersonObjectExService {

	@Autowired
	private PersonObjectExMapper personObjectExMapper;

	@Override
	public void savePersonOtherInfo(PersonObjectExDTO personObjectExDTO) {
		personObjectExMapper.savePersonOtherInfo(personObjectExDTO);
	}

	@Override
	public void removePersonOtherInfo(String idAhcsChannelProcess) {
		personObjectExMapper.removePersonOtherInfo(idAhcsChannelProcess);
	}

	@Override
	public PersonObjectExDTO getPersonOtherInfoByIdChannelProcess(String idAhcsChannelProcess) {
		return personObjectExMapper.getPersonOtherInfoByIdChannelProcess(idAhcsChannelProcess);
	}

}
