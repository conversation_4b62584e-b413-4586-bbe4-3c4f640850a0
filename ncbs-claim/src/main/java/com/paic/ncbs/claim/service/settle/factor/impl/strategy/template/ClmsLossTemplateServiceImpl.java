package com.paic.ncbs.claim.service.settle.factor.impl.strategy.template;


import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.template.AbstractTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 损失
 */
@Slf4j
@Service
public class ClmsLossTemplateServiceImpl extends AbstractTemplateService {

    @Override
    public void setTemplate(DutyDetailPayDTO detailPayDTO) {
        templatePath= Constants.TEMPLATE_FTL_MAP.get(detailPayDTO.getDutyDetailType());
    }

    @Override
    public boolean isCustomized() {
        return false;
    }

    @Override
    public String customizeReason(DutyDetailPayDTO detailPayDTO) {
        return null;
    }
}
