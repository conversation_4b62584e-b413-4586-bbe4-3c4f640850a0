package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.estimate.*;
import com.paic.ncbs.claim.model.dto.report.BatchReportInfoDTO;
import com.paic.ncbs.claim.service.estimate.EstimateDutyRecordService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.EstimateConstValues;
import com.paic.ncbs.claim.dao.mapper.report.RegisterAmountConfigMapper;
import com.paic.ncbs.claim.service.report.AutoEstimateAmountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("autoEstimateAmountService")
public class AutoEstimateAmountServiceImpl implements AutoEstimateAmountService {

    @Autowired
    private EstimateService estimateService;

    @Autowired
    private AutoEstimateUtil autoEstimateUtil;
    @Autowired
    private EstimateDutyRecordService estimateDutyRecordService;

    @Autowired
    private RegisterAmountConfigMapper registerAmountConfigDao;

    @Override
    public void autoEstimateAmount(String reportNo, Integer caseTimes) {

        if (this.existEstimateData(reportNo, caseTimes)) {
            LogUtil.audit("#已经预估再进行预估#reportNo={},caseTimes={}", reportNo, caseTimes);
            return;
        }
        //获取意键险保单预估列表
        List<EstimatePolicyDTO> policyCopyData = autoEstimateUtil.getPolicyCopyData(reportNo, caseTimes);
        AutoEstimateData autoEstimateData = new AutoEstimateData();
        autoEstimateData.setReportNo(reportNo);
        autoEstimateData.setCaseTimes(caseTimes);
        LogUtil.audit("#初始化年平均值,reportNo={},caseTimes={}", reportNo, caseTimes);
        autoEstimateUtil.initConfigAmount(autoEstimateData);
        autoEstimateData.setPolicyCopyData(policyCopyData);
        autoEstimateData.setLimitEstimateAmount(new BigDecimal("100000000000"));

        this.addEstimateRecord(autoEstimateData.getPolicyCopyData());

        estimateService.addPolicyCopyData(autoEstimateData.getPolicyCopyData());

    }

    @Override
    public void addEstimateRecord(List<EstimatePolicyDTO> policyCopyData) {
        ArrayList<EstimateDutyRecordDTO> estimateDutyRecordList = new ArrayList<EstimateDutyRecordDTO>();
        Map<String, String> paramMap = new HashMap<String, String>();

        for (EstimatePolicyDTO estimatePolicyDTO : policyCopyData) {

            paramMap.put("policyNo", estimatePolicyDTO.getPolicyNo());
            paramMap.put("caseTimes", String.valueOf(estimatePolicyDTO.getCaseTimes()));
            paramMap.put("caseNo", estimatePolicyDTO.getCaseNo());
            paramMap.put("estimateType", EstimateConstValues.ESTIMATE_TYPE_ESTIMATE);

            for (EstimatePlanDTO estimatePlanDTO : estimatePolicyDTO.getEstimatePlanList()) {
                paramMap.put("planCode", estimatePlanDTO.getPlanCode());
                estimateDutyRecordList.addAll(EstimateUtil.convertToEsitmateDutyRecordDTO(estimatePlanDTO.getEstimateDutyList(), paramMap));
            }
            paramMap = new HashMap<String, String>();
        }

        LogUtil.audit("#抄单数据插入未决表开始,reportNo={},caseTimes={}#", policyCopyData.get(0).getReportNo(), policyCopyData.get(0).getCaseTimes());

        estimateDutyRecordService.addEstimateDutyRecordList(estimateDutyRecordList);
    }

    private boolean existEstimateData(String reportNo, Integer caseTimes) {
        List<EstimatePolicyDTO> policyList = estimateService.getByReportNoAndCaseTimes(reportNo, caseTimes);
        if (ListUtils.isEmptyList(policyList)) {
            return false;
        }
        LogUtil.audit("#该案件已经做过自动预估,reportNo={}, caseTimes={}#", reportNo, caseTimes);
        return true;
    }

    @Override
    public void estimateAmountByBatch(String reportNo, Integer caseTimes, String isMerge) throws GlobalBusinessException {
        LogUtil.audit("批量报案预估, reportNo:%s, caseTimes:%s, isMerge:%s", reportNo, caseTimes, isMerge);
        List<BatchReportInfoDTO> reportInfoDTOList = registerAmountConfigDao.getBatchReportInfoList(reportNo, caseTimes);
        List<EstimatePolicyDTO> policyCopyData = autoEstimateUtil.getPolicyCopyData(reportNo, caseTimes);
        if (ListUtils.isEmptyList(reportInfoDTOList)) {
            LogUtil.audit("#查询不到报案临时表的数据,reportNo:%s, caseTimes:%s#", reportNo, caseTimes);
        }

        if (ListUtils.isEmptyList(policyCopyData)) {
            LogUtil.audit("#查询不到抄单数据,reportNo:%s, caseTimes:%s#", reportNo, caseTimes);
        }

        for (BatchReportInfoDTO item : reportInfoDTOList) {
            for (EstimatePolicyDTO policyDTO : policyCopyData) {
                BigDecimal countPolicyAmount = BigDecimal.ZERO;
                String policyId = UuidUtil.getUUID();
                List<EstimatePlanDTO> planList = policyDTO.getEstimatePlanList();
                for (EstimatePlanDTO planDTO : planList) {
                    BigDecimal countPlanAmount = BigDecimal.ZERO;
                    String planId = UuidUtil.getUUID();
                    List<EstimateDutyDTO> dutyList = planDTO.getEstimateDutyList();
                    for (EstimateDutyDTO dutyDTO : dutyList) {
                        if (EstimateConstValues.BATCH_REPORT_MERGE.equals(isMerge)) {
                            String[] planNewList = item.getPlanCode().split(",");
                            String[] dutyNewList = item.getDutyCode().split(",");
                            String[] amountNewList = item.getDutyAmountList().split(",");
                            BigDecimal dutyAmount = BigDecimal.ZERO;
                            for (int i = 0; i < planNewList.length; i++) {
                                if (policyDTO.getCaseNo().equals(item.getCaseNo()) && planDTO.getPlanCode().equals(planNewList[i]) && dutyNewList[i].equals(dutyDTO.getDutyCode())) {
                                    dutyAmount = dutyAmount.add(new BigDecimal(amountNewList[i]));
                                    dutyDTO.setEstimateAmount(dutyAmount);
                                }
                            }
                            countPlanAmount = countPlanAmount.add(dutyAmount);
                        } else {
                            if (policyDTO.getCaseNo().equals(item.getCaseNo()) && planDTO.getPlanCode().equals(item.getPlanCode()) && item.getDutyCode().equals(dutyDTO.getDutyCode())) {
                                BigDecimal dutyAmount = item.getDutyAmount();
                                dutyDTO.setEstimateAmount(dutyAmount);
                                countPlanAmount = countPlanAmount.add(dutyAmount);
                            }
                        }
                        dutyDTO.setCreatedBy(ConstValues.SYSTEM);
                        dutyDTO.setUpdatedBy(ConstValues.SYSTEM);
                        dutyDTO.setCaseNo(policyDTO.getCaseNo());
                        dutyDTO.setCaseTimes(policyDTO.getCaseTimes());
                        dutyDTO.setIdAhcsEstimateDuty(UuidUtil.getUUID());
                        dutyDTO.setEstimateType(EstimateConstValues.ESTIMATE_TYPE_ESTIMATE);
                        dutyDTO.setIdAhcsEstimatePlan(planId);
                    }
                    LogUtil.audit("4批量导入-reportNo预估:" + reportNo + "险种金额:" + countPlanAmount);
                    planDTO.setCreatedBy(ConstValues.SYSTEM);
                    planDTO.setUpdatedBy(ConstValues.SYSTEM);
                    planDTO.setCaseNo(policyDTO.getCaseNo());
                    planDTO.setCaseTimes(policyDTO.getCaseTimes());
                    planDTO.setEstimateAmount(countPlanAmount);
                    planDTO.setIdAhcsEstimatePolicy(policyId);
                    planDTO.setIdAhcsEstimatePlan(planId);
                    countPolicyAmount = countPolicyAmount.add(countPlanAmount);
                }

                policyDTO.setCreatedBy(ConstValues.SYSTEM);
                policyDTO.setUpdatedBy(ConstValues.SYSTEM);
                policyDTO.setEstimateAmount(countPolicyAmount);
                policyDTO.setIdAhcsEstimatePolicy(policyId);
            }
        }

        estimateService.initPolicyClaimCaseData(reportNo, caseTimes, ConstValues.SYSTEM);
        this.addEstimateRecord(policyCopyData);
        estimateService.addBatchEstimatePolicy(policyCopyData);
    }
}
