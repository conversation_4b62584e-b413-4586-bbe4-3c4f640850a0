package com.paic.ncbs.claim.dao.mapper.investigate;

import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskQueryDTO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InvestigateTaskAuditPublicMapper {

    /**
     * 查询调查任务列表
     * @param queryDTO 查询条件
     * @return 任务列表
     */
    List<WorkBenchTaskVO> getInvestigateTaskList(InvestigateTaskQueryDTO queryDTO);

}