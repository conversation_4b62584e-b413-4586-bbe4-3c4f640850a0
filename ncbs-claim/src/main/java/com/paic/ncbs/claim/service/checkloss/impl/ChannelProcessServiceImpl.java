package com.paic.ncbs.claim.service.checkloss.impl;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.PersonObjectDTO;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.TimeMillisSequence;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonObjectMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.ChannelProcessMapper;
import com.paic.ncbs.claim.model.dto.endcase.CaseInfoParameterDTO;
import com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO;
import com.paic.ncbs.claim.service.checkloss.ChannelProcessService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service("channelProcessService")
public class ChannelProcessServiceImpl implements ChannelProcessService {

	@Autowired
	private CommonParameterService commonParameterService;

	@Autowired
	private ChannelProcessMapper channelProcessMapper;

	@Autowired
	private PersonObjectMapper personObjectDao;


	@Transactional
	@Override
	public String getChannelProcessId(CaseInfoParameterDTO caseInfoParameter) {

		List<String> channelProcessIdList = channelProcessMapper.getChannelProcessId(caseInfoParameter);
		if (ListUtils.isEmptyList(channelProcessIdList)) {
			return saveChannelProcess(caseInfoParameter);
		}
		return channelProcessIdList.get(0);
	}


	@Override
	public String getChannelProcessId(String reportNo, int caseTimes) {
		CaseInfoParameterDTO caseInfoParameter = new CaseInfoParameterDTO();
		caseInfoParameter.setReportNo(reportNo);
		caseInfoParameter.setCaseTimes(caseTimes);
		List<String> partyNoList=new ArrayList<>();
		if (ListUtils.isNotEmpty(partyNoList)) {
			caseInfoParameter.setPartyNo(partyNoList.get(0));
		}
		caseInfoParameter.setChannelType(ChecklossConst.CASECLASS_PEOPLE_HURT);
		List<String> channelProcessIdList = channelProcessMapper.getChannelProcessId(caseInfoParameter);
		if (ListUtils.isEmptyList(channelProcessIdList)) {
			return "";
		}
		return channelProcessIdList.get(0);
	}


	private String saveChannelProcess(CaseInfoParameterDTO caseInfoParameter) {
		String userUm = caseInfoParameter.getUserId();
		String reportNo = caseInfoParameter.getReportNo();
		int caseTimes = caseInfoParameter.getCaseTimes();
		String channelType = caseInfoParameter.getChannelType();
		String partyNo = caseInfoParameter.getPartyNo();

		String lossObjectNo = TimeMillisSequence.getTimeMillisSequence();
		PersonObjectDTO personObject = new PersonObjectDTO();
		personObject.setCreatedBy(userUm);
		personObject.setUpdatedBy(userUm);
		personObject.setReportNo(reportNo);
		personObject.setLossObjectNo(lossObjectNo);
		if (StringUtils.isNotEmpty(partyNo)) {
			personObject.setPartyNo(partyNo);
		}
		personObjectDao.savePersonObject(personObject);

		String channelProcessId = UuidUtil.getUUID();
		ChannelProcessDTO channelProcess = new ChannelProcessDTO();
		channelProcess.setCreatedBy(userUm);
		channelProcess.setUpdatedBy(userUm);
		channelProcess.setIdAhcsChannelProcess(channelProcessId);
		channelProcess.setReportNo(reportNo);
		channelProcess.setCaseTimes(caseTimes);
		channelProcess.setChannelType(channelType);
		channelProcess.setAssessUm(userUm);
		channelProcess.setLossObjectNo(lossObjectNo);
		channelProcessMapper.saveChannelProcess(channelProcess);

		return channelProcessId;
	}


	@Override
	public List<ChannelProcessDTO> getChanelProcessIdList(String reportNo, int caseTimes) {
//		List<String> partyNoList=new ArrayList<>();
//		if (ListUtils.isNotEmpty(partyNoList)) {
//			return channelProcessMapper.getChannelProcessIds(reportNo, caseTimes, partyNoList.get(0));
//		}
		return channelProcessMapper.getChannelProcessIds(reportNo, caseTimes);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void addChannelProcessId(CaseInfoParameterDTO caseInfoParameter) throws GlobalBusinessException {
//		List<String> partyNoList=personObjectDao.getPartyNo(caseInfoParameter.getReportNo());
//		if (ListUtils.isNotEmpty(partyNoList)) {
//			caseInfoParameter.setPartyNo(partyNoList.get(0));
//		}

		List<CommonParameterTinyDTO> commonParameterTinyList = commonParameterService.getCommonParameterList(new String[] {"AHCS_CHANNEL_TYPE"});

		if (commonParameterTinyList != null) {
			for (CommonParameterTinyDTO commonParameterTiny : commonParameterTinyList) {
				caseInfoParameter.setChannelType(commonParameterTiny.getValueCode());

				List<String> channelProcessIdList = channelProcessMapper.getChannelProcessId(caseInfoParameter);
				if (ListUtils.isEmptyList(channelProcessIdList)) {
					saveChannelProcess(caseInfoParameter);
				}
			}
		}
	}

}
