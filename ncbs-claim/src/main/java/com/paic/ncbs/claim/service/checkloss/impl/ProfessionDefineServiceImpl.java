package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.model.dto.settle.SubProfessionDefineDTO;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.settle.ProfessionDefineMapper;
import com.paic.ncbs.claim.service.checkloss.ProfessionDefineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service("professionDefineService")
public class ProfessionDefineServiceImpl implements ProfessionDefineService {
	
	@Autowired
	private ProfessionDefineMapper professionDefineMapper;

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

	@Override
	public List<String> getSubProfessionDefinesNameAll() {
		return professionDefineMapper.getSubProfessionDefinesNameAll();
	}

	 
	@Override
	public List<SubProfessionDefineDTO> getProfessionDefines() {
		List<SubProfessionDefineDTO> resultProfessionListx=(List<SubProfessionDefineDTO>)redisTemplate.opsForValue().get("professionInfo");
		if (!ObjectUtils.isEmpty(resultProfessionListx)){
			return  resultProfessionListx ;
		}else {
			List<SubProfessionDefineDTO> resultProfessionList= new ArrayList<>();
			List<SubProfessionDefineDTO> professionList= professionDefineMapper.getAllSubProfessionDefines();
			LogUtil.audit("获取职业类别信息列表记录数:{}",professionList.size());
			if(ListUtils.isNotEmpty(professionList)){
				professionList.forEach(pro->{
					if (pro.getParentCode().equals("0")){
						resultProfessionList.add(pro);
					}
				});
			}
			for  (SubProfessionDefineDTO subProfessionDefineDTO :resultProfessionList){
				dealProfessionData(subProfessionDefineDTO,professionList);
			}
			LogUtil.audit("resultProfessionList-size:{}",resultProfessionList.size());
			if (!ObjectUtils.isEmpty(resultProfessionList)){
				this.redisTemplate.opsForValue().set("professionInfo"  , resultProfessionList);
				this.redisTemplate.expire("professionInfo" , 2L, TimeUnit.HOURS);
			}
			return resultProfessionList;
		}
	}

	private void dealProfessionData(SubProfessionDefineDTO subPro, List<SubProfessionDefineDTO> professionList) {
		List<SubProfessionDefineDTO> sonList = new ArrayList<>() ;
		for  (SubProfessionDefineDTO subProfessionDefineDTO :professionList){
				if (subProfessionDefineDTO.getParentCode().equals(subPro.getProfessionCode())){
					sonList.add(subProfessionDefineDTO) ;
				}
			}
		    subPro.setSubProfessionDefines(sonList) ;
			sonList.forEach(son->{
				dealProfessionData(son,professionList);
			});
      }


	@Override
	public boolean getIsShowAmountRate(String reportNo, String professionGradeCode){
  		if(ChecklossConst.PROFESSION_GRADE_REFUSE.equals(professionGradeCode)){
			return false;
		}
		
  		List<SubProfessionDefineDTO> professionGradeCodeList= professionDefineMapper.getPolicyProfession(reportNo);
		
  		if(ListUtils.isEmptyList(professionGradeCodeList)){
			return false;
		
			
		}else{
			Set<String> professionGradeCodeSet=new HashSet<String>();
			for(SubProfessionDefineDTO professionDefine:professionGradeCodeList){
				professionGradeCodeSet.add(professionDefine.getProfessionGradeCode());
			}
			
			if(professionGradeCodeSet.size()==0){
				return false;
			}
  			if(professionGradeCodeSet.size()>1){
				return true;
			}else{
				String policyProfessionGradeCode=professionGradeCodeList.get(0).getProfessionGradeCode();
				
				if(!ChecklossConst.PROFESSION_GRADE_REFUSE.equals(policyProfessionGradeCode) && Integer.parseInt(professionGradeCode)>Integer.parseInt(policyProfessionGradeCode)){
					return true;
				}
			}
		}
		return false;
	}
}
