package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.estimate.BatchEstimateInfoMapper;
import com.paic.ncbs.claim.model.dto.estimate.BatchEstimateInfoDTO;
import com.paic.ncbs.claim.service.report.BatchEstimateInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class BatchEstimateInfoServiceImpl implements BatchEstimateInfoService {
    @Autowired
    private BatchEstimateInfoMapper batchEstimateInfoMapper;
    @Override
    public void saveData(BatchEstimateInfoDTO dto) {
        dto.setId(UuidUtil.getUUID());
        dto.setCreatedBy("system");
        dto.setCreatedDate(new Date());
        dto.setUpdatedBy("system");
        dto.setUpdatedDate(new Date());
        batchEstimateInfoMapper.saveData(dto);
    }
}
