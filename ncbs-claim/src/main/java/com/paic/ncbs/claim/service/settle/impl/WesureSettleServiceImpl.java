package com.paic.ncbs.claim.service.settle.impl;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.settle.WesureSettleMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.service.settle.WesureSettleService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("wesureSettleService")
public class WesureSettleServiceImpl implements WesureSettleService {
    static Map<String,String> CLAIM_STATUS_MAPPING = new HashMap<>();
    static {
        CLAIM_STATUS_MAPPING.put("UNCLAIM","UNCLAIM");
        CLAIM_STATUS_MAPPING.put("0","CLAIMED");
        CLAIM_STATUS_MAPPING.put("1","CLAIMING");
    }

    @Autowired
    private WesureSettleMapper wesureSettleMapper;

    @Override
    public WesureClaimedDTO getClaimedAmount(WesureClaimedDTO wesureClaimedDTO) {
        List<WesureBenefitDTO> benefitClaimedList = Optional.ofNullable(wesureSettleMapper.getClaimedAmount(wesureClaimedDTO)).orElse(new ArrayList<>());
        Map<String,BigDecimal> claimedMap = benefitClaimedList.stream().collect(Collectors.toMap(WesureBenefitDTO::getBenefitCode,WesureBenefitDTO::getClaimedAmount));
        for (WesureBenefitDTO benefitDTO : wesureClaimedDTO.getBenefitList()) {
            benefitDTO.setClaimedAmount(claimedMap.getOrDefault(benefitDTO.getBenefitCode(),BigDecimal.ZERO));
            benefitDTO.setBenefitName(null);
        }
        wesureClaimedDTO.setBenefitClaimedList(wesureClaimedDTO.getBenefitList());
        wesureClaimedDTO.setBenefitList(null);
        return wesureClaimedDTO;
    }

    @Override
    public WesureReceiptDTO getReceiptStatus(WesureReceiptDTO wesureReceiptDTO) {
        List<WesureReceiptClaimDTO> receiptList = wesureReceiptDTO.getReceiptList();

        buildReceiptStatus(receiptList,wesureSettleMapper.getReceiptStatus(receiptList));
        //未理赔的 过滤出来
//        List<WesureReceiptDetailDTO> receiptSyncList = receiptList.stream().filter(receipt -> "UNCLAIM".equals(receipt.getClaimStatus())).collect(Collectors.toList());
//        if(receiptSyncList.size() > 0){
//            //查询微保同步的医疗票据
//            buildReceiptStatus(receiptList,wesureSettleMapper.getWesureSyncReceiptStatus(receiptSyncList));
//        }
        wesureReceiptDTO.setReceiptClaimList(receiptList);
        wesureReceiptDTO.setReceiptList(null);
        wesureReceiptDTO.setWesureReportNo(null);
        return wesureReceiptDTO;
    }

    private void buildReceiptStatus(List<WesureReceiptClaimDTO> receiptList,List<WesureReceiptClaimDTO> receiptClaimList){
        receiptClaimList = Optional.ofNullable(receiptClaimList).orElse(new ArrayList<>());
        Map<String,String> receiptMap = receiptClaimList.stream().collect(Collectors.toMap(WesureReceiptClaimDTO::getReceiptNo,WesureReceiptClaimDTO::getClaimStatus));
        for (WesureReceiptClaimDTO receiptClaimDTO : receiptList) {
            String claimStatus = receiptMap.get(receiptClaimDTO.getReceiptNo());
            if(claimStatus != null){
                receiptClaimDTO.setClaimStatus(CLAIM_STATUS_MAPPING.get(claimStatus));
            }
            if(StringUtils.isEmptyStr(receiptClaimDTO.getClaimStatus())){
                receiptClaimDTO.setClaimStatus("UNCLAIM");
            }
        }
    }

    @Override
    public void buildWesureSettle(WesureSettleDTO wesureSettleDTO) {

        if(wesureSettleDTO.getCaseTimes() == null){
            wesureSettleDTO.setCaseTimes(1);
        }
        String userId = WebServletContext.getUserId();
        String settleId = UuidUtil.getUUID();
        wesureSettleDTO.setCreatedBy(userId);
        wesureSettleDTO.setUpdatedBy(userId);
        wesureSettleDTO.setIdClmsWesureSettle(settleId);
        if(ListUtils.isEmptyList(wesureSettleDTO.getPolicyBenefitList())){
            return;
        }

        List<WesureBenefitDTO> benefitList = new ArrayList<>();
        for (WesurePolicyDTO policy : wesureSettleDTO.getPolicyBenefitList()) {
            policy.setCreatedBy(userId);
            policy.setUpdatedBy(userId);
            String policyId = UuidUtil.getUUID();
            policy.setIdClmsWesurePolicy(policyId);
            policy.setIdClmsWesureSettle(settleId);
            policy.setReportNo(wesureSettleDTO.getReportNo());
            policy.setCaseTimes(wesureSettleDTO.getCaseTimes());
            if(ListUtils.isNotEmpty(policy.getBenefitPayList())){
                for (WesureBenefitDTO benefit : policy.getBenefitPayList()) {
                    benefit.setCreatedBy(userId);
                    benefit.setUpdatedBy(userId);
                    String benefitId = UuidUtil.getUUID();
                    benefit.setIdClmsWesureBenefit(benefitId);
                    benefit.setIdClmsWesurePolicy(policyId);
                    benefit.setPolicyNo(policy.getPolicyNo());
                    benefitList.add(benefit);
                }
                policy.setBenefitPayList(null);
            }
        }

        wesureSettleDTO.setBenefitPayList(benefitList);

        if(ListUtils.isEmptyList(wesureSettleDTO.getReceiptToBenefitList())){
            return;
        }

        boolean isUnionAmount = Constants.WESURE_SYNC_TYPE_SETTLE.equals(wesureSettleDTO.getSyncType());
        Map<String,WesureMedicalReceiptDTO> medicalReceiptMap = new HashMap<>();
        if(isUnionAmount){
            //微保同步理算信息给保司,需要关联票据金额
            medicalReceiptMap = getMedicalReceiptMap(wesureSettleDTO);
        }

        Map<String,List<WesureBenefitDTO>> benefitMap = benefitList.stream().collect(Collectors.groupingBy((WesureBenefitDTO::getBenefitCode)));
        for (WesureReceiptDetailDTO receipt : wesureSettleDTO.getReceiptToBenefitList()) {
            receipt.setCreatedBy(userId);
            receipt.setUpdatedBy(userId);
            receipt.setIdClmsWesureReceipt(UuidUtil.getUUID());
            List<WesureBenefitDTO> benefits = benefitMap.get(receipt.getBenefitCode());

            if(ListUtils.isNotEmpty(benefits)){
                buildReceiptId(receipt,benefits.get(0));
            }else if("DD".equals(receipt.getBenefitCode()) && ListUtils.isNotEmpty(benefitList)){
                buildReceiptId(receipt,benefitList.get(0));
            }
            else{
                //关联不上责任
                String missId = UuidUtil.getUUID();
                receipt.setIdClmsWesureBenefit(missId);
                receipt.setPolicyNo(missId);
            }
            if(isUnionAmount){
                unionReceiptAmount(receipt,medicalReceiptMap.getOrDefault(receipt.getReceiptNo(),new WesureMedicalReceiptDTO()));
            }
        }
    }

    private void buildReceiptId(WesureReceiptDetailDTO receipt,WesureBenefitDTO benefitDTO){
        receipt.setIdClmsWesureBenefit(benefitDTO.getIdClmsWesureBenefit());
        receipt.setPolicyNo(benefitDTO.getPolicyNo());
    }

    private Map<String,WesureMedicalReceiptDTO> getMedicalReceiptMap(WesureSettleDTO wesureSettleDTO){
        Map<String,WesureMedicalReceiptDTO> medicalReceiptMap = new HashMap<>();
        WesureMedicalDTO medicalParam = new WesureMedicalDTO();
        medicalParam.setReportNo(wesureSettleDTO.getReportNo());
        medicalParam.setCaseTimes(wesureSettleDTO.getCaseTimes());
        List<WesureMedicalDTO> medicalList = wesureSettleMapper.getWesureMedicalList(medicalParam);
        if(ListUtils.isEmptyList(medicalList)){
            return medicalReceiptMap;
        }

        for (WesureMedicalDTO medical : medicalList) {
            if(ListUtils.isEmptyList(medical.getReceiptList())){
                continue;
            }
            for (WesureMedicalReceiptDTO medicalReceipt : medical.getReceiptList()) {
                medicalReceiptMap.put(medicalReceipt.getReceiptNo(),medicalReceipt);
            }
        }
        return medicalReceiptMap;
    }

    private void unionReceiptAmount(WesureReceiptDetailDTO receipt,WesureMedicalReceiptDTO medicalReceipt){
        receipt.setTotalPay(medicalReceipt.getTotalPay());
        receipt.setInsurePay(medicalReceipt.getInsurePay());
        receipt.setInsureSelfPay(medicalReceipt.getInsureSelfPay());
        receipt.setClassResponsibility(medicalReceipt.getClassResponsibility());
        receipt.setPersonalResponsibility(medicalReceipt.getPersonalResponsibility());
        receipt.setPersonalPay(medicalReceipt.getPersonalPay());
        receipt.setSelfPay(medicalReceipt.getSelfPay());
        receipt.setOtherPay(medicalReceipt.getOtherPay());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSettleDetail(WesureSettleDTO wesureSettleDTO) {
        wesureSettleMapper.removeWesureSettle(wesureSettleDTO);
        wesureSettleMapper.saveWesureSettle(wesureSettleDTO);
        if(ListUtils.isNotEmpty(wesureSettleDTO.getPolicyBenefitList())){
            wesureSettleMapper.saveWesurePolicy(wesureSettleDTO.getPolicyBenefitList());
        }
        if(ListUtils.isNotEmpty(wesureSettleDTO.getBenefitPayList())){
            wesureSettleMapper.saveWesureBenefit(wesureSettleDTO.getBenefitPayList());
        }
        if(ListUtils.isNotEmpty(wesureSettleDTO.getReceiptToBenefitList())){
            wesureSettleMapper.saveWesureReceipt(wesureSettleDTO.getReceiptToBenefitList());
        }
    }

    @Override
    public WesureSettleDTO getSettle(WesureSettleDTO wesureSettleDTO) {
        WesureSettleDTO wesureSettleReview = wesureSettleMapper.getWesureSettle(wesureSettleDTO);
        if(wesureSettleReview == null){
            return null;
        }
        wesureSettleReview.setIdClmsWesureSettle(null);
        if(ListUtils.isNotEmpty(wesureSettleReview.getPolicyBenefitList())){
            List<WesureReceiptDetailDTO> receiptList = new ArrayList<>();
            wesureSettleReview.setReceiptToBenefitList(receiptList);
            for (WesurePolicyDTO policy : wesureSettleReview.getPolicyBenefitList()) {
                policy.setIdClmsWesurePolicy(null);
                if(ListUtils.isNotEmpty(policy.getBenefitPayList())){
                    for (WesureBenefitDTO benefit : policy.getBenefitPayList()) {
                        benefit.setIdClmsWesureBenefit(null);
                        if(ListUtils.isNotEmpty(benefit.getReceiptList())){
                            receiptList.addAll(benefit.getReceiptList());
                        }
                        benefit.setReceiptList(null);
                    }
                }
            }
        }

        if(ListUtils.isEmptyList(wesureSettleReview.getReceiptToBenefitList())) {
            WesureMedicalDTO medicalParam = new WesureMedicalDTO();
            medicalParam.setReportNo(wesureSettleDTO.getReportNo());
            medicalParam.setCaseTimes(wesureSettleDTO.getCaseTimes());
            List<MedicalBillInfoDTO> billList = wesureSettleMapper.getBillAmounts(medicalParam);
            if(ListUtils.isNotEmpty(billList)){
                List<WesureReceiptDetailDTO> receiptToBenefitList = getWesureReceiptDetailDTOS(billList);
                wesureSettleReview.setReceiptToBenefitList(receiptToBenefitList);
            }
        }

        return wesureSettleReview;
    }

    @NotNull
    private static List<WesureReceiptDetailDTO> getWesureReceiptDetailDTOS(List<MedicalBillInfoDTO> billList) {
        List<WesureReceiptDetailDTO> receiptToBenefitList = new ArrayList<>();
        for(MedicalBillInfoDTO billInfoDTO : billList) {
            WesureReceiptDetailDTO receiptDetailDTO = new WesureReceiptDetailDTO();
            receiptDetailDTO.setReceiptNo(billInfoDTO.getBillNo());
            receiptDetailDTO.setTotalPay(billInfoDTO.getBillAmount());
            receiptDetailDTO.setUnreasonableAmount(billInfoDTO.getImmoderateAmount());
            receiptToBenefitList.add(receiptDetailDTO);
        }
        return receiptToBenefitList;
    }

    @Override
    public void buildWesureMedical(WesureMedicalSyncDTO wesureMedicalSyncDTO) {
        String userId = WebServletContext.getUserId();
        Integer caseTimes = 1;
        for (WesureMedicalDTO medical : wesureMedicalSyncDTO.getMedicalList()) {
            String medicalId = UuidUtil.getUUID();
            medical.setIdClmsWesureMedical(medicalId);
            medical.setCreatedBy(userId);
            medical.setUpdatedBy(userId);
            medical.setWesureReportNo(wesureMedicalSyncDTO.getWesureReportNo());
            medical.setReportNo(wesureMedicalSyncDTO.getReportNo());
            medical.setCaseTimes(caseTimes);
            medical.setMedicalSource(wesureMedicalSyncDTO.getMedicalSource());
            boolean isOutpatient = "THE_0301".equals(medical.getVisitType()) || "OUTPATIENT".equals(medical.getVisitType());
            if(isOutpatient){
                if(medical.getOutpatientDate() == null){
                    throw new GlobalBusinessException("门诊日期不能为空");
                }
                medical.setVisitType("THE_0301");
            }else{
                if(medical.getInHospitalDate() == null){
                    throw new GlobalBusinessException("入院日期不能为空");
                }
                if(medical.getOutHospitalDate() == null){
                    throw new GlobalBusinessException("出院日期不能为空");
                }
                medical.setVisitType("THE_0302");
            }
            if(ListUtils.isNotEmpty(medical.getReceiptList())){
                List<WesureMedicalReceiptDTO> receiptList = new ArrayList<>();
                wesureMedicalSyncDTO.setReceiptList(receiptList);
                for (WesureMedicalReceiptDTO receipt : medical.getReceiptList()) {
                    receipt.setIdClmsWesureMedical(medicalId);
                    receipt.setIdClmsWesureMedicalReceipt(UuidUtil.getUUID());
                    receipt.setCreatedBy(userId);
                    receipt.setUpdatedBy(userId);
                    receipt.setInputMode(wesureMedicalSyncDTO.getInputMode());
                    receiptList.add(receipt);
                    if(StringUtils.isEmptyStr(receipt.getReceiptDate())){
                        if(isOutpatient){
                            //门诊日期
                            receipt.setReceiptDate(medical.getOutpatientDate());
                        }else{
                            //住院-出院日期
                            receipt.setReceiptDate(medical.getOutHospitalDate());
                        }
                        if(StringUtils.isEmptyStr(receipt.getReceiptDate())){
                            receipt.setReceiptDate(DateUtils.parseToFormatStr(new Date(),"yyyyMMdd"));
                        }
                    }
                }
                medical.setReceiptList(null);
            }
        }

        for (WesureMedicalDiseaseDTO disease : wesureMedicalSyncDTO.getDiseaseList()) {
            disease.setCreatedBy(userId);
            disease.setUpdatedBy(userId);
            disease.setIdClmsWesureMedicalDisease(UuidUtil.getUUID());
            disease.setWesureReportNo(wesureMedicalSyncDTO.getWesureReportNo());
            disease.setReportNo(wesureMedicalSyncDTO.getReportNo());
            disease.setCaseTimes(caseTimes);
        }
    }

    @Override
    @Transactional
    public void saveMedical(WesureMedicalSyncDTO wesureMedicalSyncDTO) {
        if(ListUtils.isNotEmpty(wesureMedicalSyncDTO.getMedicalList())){
            WesureMedicalDTO wesureMedicalDTO = wesureMedicalSyncDTO.getMedicalList().get(0);
            //先删关联表
            wesureSettleMapper.removeWesureMedicalReceipt(wesureMedicalDTO);
            wesureSettleMapper.removeWesureMedical(wesureMedicalDTO);
            wesureSettleMapper.removeWesureMedicalDisease(wesureMedicalDTO);

            wesureSettleMapper.saveWesureMedical(wesureMedicalSyncDTO.getMedicalList());
            if(ListUtils.isNotEmpty(wesureMedicalSyncDTO.getReceiptList())){
                wesureSettleMapper.saveWesureMedicalReceipt(wesureMedicalSyncDTO.getReceiptList());
            }
            if(ListUtils.isNotEmpty(wesureMedicalSyncDTO.getDiseaseList())){

                wesureSettleMapper.saveWesureMedicalDisease(wesureMedicalSyncDTO.getDiseaseList());
            }
        }
    }

    @Override
    public void generateWesureVerify(WholeCaseBaseDTO wholeCaseBaseDTO) {
        if(wholeCaseBaseDTO.getCaseTimes() > 1){
            //重开暂不考虑
            return;
        }
        String wesureAutoClaim = wesureSettleMapper.getWesureAutoClaim(wholeCaseBaseDTO.getReportNo());
        if(!"Y".equals(wesureAutoClaim)){
            return;
        }
        WesureSettleDTO wesureSettleReview = new WesureSettleDTO();
        wesureSettleReview.setReportNo(wholeCaseBaseDTO.getReportNo());
        wesureSettleReview.setCaseTimes(wholeCaseBaseDTO.getCaseTimes());
        //查询微保同步给保司的理算数据
        wesureSettleReview.setSyncType(Constants.WESURE_SYNC_TYPE_SETTLE);
        WesureSettleDTO wesureSettle = getSettle(wesureSettleReview);
        Map<String,List<WesurePolicyDTO>> wesurePolicyMap = new HashMap<>();
        if(wesureSettle != null){
            wesureSettleReview.setWesureReportNo(wesureSettle.getWesureReportNo());
            wesureSettleReview.setCalculateTime(wesureSettle.getCalculateTime());
            buildReviewBillList(wesureSettleReview);
            if(ListUtils.isNotEmpty(wesureSettle.getPolicyBenefitList())){
                wesurePolicyMap = wesureSettle.getPolicyBenefitList().stream().collect(Collectors.groupingBy(WesurePolicyDTO::getPolicyNo));
            }
        }
        String conclusion = wholeCaseBaseDTO.getIndemnityConclusion();
        String model = Optional.ofNullable(wholeCaseBaseDTO.getIndemnityModel()).orElse("");
        wesureSettleReview.setCaseConclusion(conclusion+model);
        if(!"1".equals(conclusion)){
            wesureSettleReview.setConclusionDetailCode(999);
            String conclusionMsg = null;
            if("4".equals(conclusion)){
                //拒赔
                conclusionMsg = wesureSettleMapper.getRefusePayDesc(wholeCaseBaseDTO);
            }else{
                //零注
                conclusionMsg = wesureSettleMapper.getZeroCancelDesc(wholeCaseBaseDTO);
            }
            wesureSettleReview.setConclusionDetailMsg(conclusionMsg);
            wesureSettleReview.setCasePayAmount(BigDecimal.ZERO);
        }

        buildPolicyBenefit(wesureSettleReview,wholeCaseBaseDTO,wesurePolicyMap);

        if(wholeCaseBaseDTO.getEndCaseDate() == null){
            wholeCaseBaseDTO.setEndCaseDate(new Date());
        }
        wesureSettleReview.setAuditTime(DateUtils.parseToFormatStr(wholeCaseBaseDTO.getEndCaseDate(),"yyyyMMddHHmmss"));
//        wesureSettleReview.setAuditMsg();
        wesureSettleReview.setSyncType(Constants.WESURE_SYNC_TYPE_REVIEW);
        buildWesureSettle(wesureSettleReview);
        saveSettleDetail(wesureSettleReview);
    }

    void buildPolicyBenefit(WesureSettleDTO wesureSettleReview,WholeCaseBaseDTO wholeCaseBaseDTO,Map<String,List<WesurePolicyDTO>> wesurePolicyMap){
        List<DutyDetailPayDTO> dutyDetailPayList = wesureSettleMapper.getDutyDetailPayList(wholeCaseBaseDTO);
        if(ListUtils.isEmptyList(dutyDetailPayList)){
            //未经过理算的案件,查抄单数据
            dutyDetailPayList = wesureSettleMapper.getPolicyDutyDetailPayList(wholeCaseBaseDTO);
        }
        if(ListUtils.isNotEmpty(dutyDetailPayList)){
            List<WesurePolicyDTO> policyList = new ArrayList<>();
            Map<String,List<DutyDetailPayDTO>> dutyDetailMap = dutyDetailPayList.stream().collect(Collectors.groupingBy(DutyDetailPayDTO::getPolicyNo));
            BigDecimal casePayAmt = BigDecimal.ZERO;
            for (Map.Entry<String,List<DutyDetailPayDTO>> entry:dutyDetailMap.entrySet()){
                WesurePolicyDTO wesurePolicy = new WesurePolicyDTO();
                policyList.add(wesurePolicy);
                String policyNo = entry.getKey();
                wesurePolicy.setPolicyNo(policyNo);
                List<WesurePolicyDTO> wesurePolicyList = wesurePolicyMap.get(policyNo);
                if(ListUtils.isNotEmpty(wesurePolicyList)){
                    wesurePolicy.setWesurePolicyNo(wesurePolicyList.get(0).getWesurePolicyNo());
                }
                BigDecimal policyPayAmt = BigDecimal.ZERO;
                List<WesureBenefitDTO> benefitList = new ArrayList<>();
                wesurePolicy.setBenefitPayList(benefitList);
                for (DutyDetailPayDTO detail : entry.getValue()) {
                    BigDecimal detailPayAmt = detail.getSettleAmount();
                    if(detailPayAmt == null){
                        detailPayAmt = BigDecimal.ZERO;
                    }
                    policyPayAmt = policyPayAmt.add(detailPayAmt);
                    casePayAmt = casePayAmt.add(detailPayAmt);
                    WesureBenefitDTO benefitDTO = new WesureBenefitDTO();
                    benefitDTO.setBenefitCode(detail.getDutyDetailCode());
                    benefitDTO.setBenefitName(detail.getDutyDetailName());
                    benefitDTO.setBenefitAdjustAmount(detail.getReasonableAmount());
                    benefitDTO.setBenefitPayAmount(detailPayAmt);
                    benefitList.add(benefitDTO);
                }
                wesurePolicy.setPolicyPayAmount(policyPayAmt);
            }
            wesureSettleReview.setCasePayAmount(casePayAmt);
            wesureSettleReview.setPolicyBenefitList(policyList);
        }
    }

    void buildReviewBillList(WesureSettleDTO wesureSettleReview){
        Map<String,MedicalBillInfoDTO> billMap = getBillMap(wesureSettleReview);
        List<WesureReceiptDetailDTO> receiptToBenefitList = new ArrayList<>();
        if(billMap != null && billMap.size() > 0){
            billMap.forEach((k,v)->{
                WesureReceiptDetailDTO receiptDetail = new WesureReceiptDetailDTO();
                receiptDetail.setReceiptNo(v.getBillNo());
                receiptDetail.setBenefitCode("DD");
                receiptDetail.setTotalPay(v.getBillAmount());
                receiptDetail.setUnreasonableAmount(v.getImmoderateAmount());
                receiptToBenefitList.add(receiptDetail);
            });
        }
        wesureSettleReview.setReceiptToBenefitList(receiptToBenefitList);
    }

    private Map<String,MedicalBillInfoDTO> getBillMap(WesureSettleDTO wesureSettleDTO){
        WesureMedicalDTO medicalParam = new WesureMedicalDTO();
        medicalParam.setReportNo(wesureSettleDTO.getReportNo());
        medicalParam.setCaseTimes(wesureSettleDTO.getCaseTimes());
        Map<String,MedicalBillInfoDTO> billMap = new HashMap<>();
        List<MedicalBillInfoDTO> billList = wesureSettleMapper.getBillAmounts(medicalParam);
        if(ListUtils.isNotEmpty(billList)){
            //根据billNo分组，如果有重复的，取金额较大者
            billMap = billList.stream().collect(Collectors.groupingBy(MedicalBillInfoDTO::getBillNo,
                    Collectors.collectingAndThen(Collectors.reducing((dto1,dto2)->
                            dto1.getBillAmount().compareTo(dto2.getBillAmount()) >= 0 ? dto1:dto2),Optional::get)));
        }
        return billMap;
    }
}
