package com.paic.ncbs.claim.controller.openapi;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.model.dto.report.BankInfoDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/public/common")
public class OpenCommonQueryController {

    @Autowired
    private CommonParameterMapper commonParameterMapper;

    @ApiOperation(value = "银行信息查询")
    @GetMapping(value = "/getBankInfoList")
    public ResponseResult<List<BankInfoDTO>> getBankInfoList(@RequestParam("bankName") String bankName){
        LogUtil.info("QueryReportController getBankInfoList bankName = {}", bankName);
        List<BankInfoDTO> result = new ArrayList<>();
        //判断入参是否为空，为空则查询银行大类信息，不为空则根据入参查询对应的开户行信息
        if(StringUtils.isNotEmpty(bankName)){
            result = commonParameterMapper.getBranchBankInfoList(bankName);
        }else{
            result = commonParameterMapper.getBankInfoList();
        }
        return ResponseResult.success(result);
    }
}
