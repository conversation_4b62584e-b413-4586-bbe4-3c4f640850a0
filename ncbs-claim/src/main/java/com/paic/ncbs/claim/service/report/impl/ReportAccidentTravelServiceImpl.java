package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentTravelEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentTravelMapper;
import com.paic.ncbs.claim.service.report.ReportAccidentTravelService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportAccidentTravelServiceImpl extends BaseServiceImpl<ReportAccidentTravelEntity> implements ReportAccidentTravelService {

    @Autowired
    private ReportAccidentTravelMapper reportAccidentTravelMapper;

    @Override
    public BaseDao<ReportAccidentTravelEntity> getDao() {
        return reportAccidentTravelMapper;
    }

    @Override
    public ReportAccidentTravelEntity getReportAccidentTravelByReportNo(String reportNo) {
        return reportAccidentTravelMapper.getReportAccidentTravelByReportNo(reportNo);
    }
}
