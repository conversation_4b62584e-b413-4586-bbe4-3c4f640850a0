package com.paic.ncbs.claim.controller.ahcs;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.RedisKeyConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity;
import com.paic.ncbs.claim.dao.entity.ahcs.ClaimRejectionApprovalRecordDto;
import com.paic.ncbs.claim.dao.mapper.checkloss.LossReduceMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.ClaimRejectionApprovalRecordEntityMapper;
import com.paic.ncbs.claim.dao.mapper.verify.VerifyConclusionMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.checkloss.LossReduceDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.estimate.RegistSaveDTO;
import com.paic.ncbs.claim.model.vo.endcase.CaseRegisterApplyVO;
import com.paic.ncbs.claim.model.vo.policy.EstimatePolicyVO;
import com.paic.ncbs.claim.model.vo.policy.PolicyDutyVO;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.common.ClaimSendTpaMqInfoService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.report.ReportTrackTaskService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.paic.ncbs.claim.common.constant.BpmConstants.SUPEND_USE;

@Api(tags = "立案、拒赔")
@RestController
@RequestMapping("/ahcs/do/app/estimateAction")
public class EstimateController extends BaseController {

	@Autowired
	private EstimateService estimateService;

	@Autowired
	private RegisterCaseService registerCaseService;

	@Autowired
	private TaskInfoService taskInfoService ;

	@Autowired
	private ClaimRejectionApprovalRecordEntityMapper claimRejectionApprovalRecordEntityMapper ;

	@Autowired
	private VerifyConclusionMapper verifyConclusiondao;

	@Autowired
	private LossReduceMapper lossReduceMapper ;
	@Autowired
	private ClaimSendTpaMqInfoService claimSendTpaMqInfoService;

	@Autowired
	private CaseProcessService caseProcessService;
	@Autowired
	private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;

	@Autowired
	private RedissonClient redissonClient;
	@Autowired
	private ReportTrackTaskService reportTrackTaskService;
	@ApiOperation("历史案件页面查询已立案信息")
	@PostMapping(value = "/getRegiterEstimateDataList")
	public ResponseResult<EstimatePolicyFormDTO> getRegiterEstimateDataList(@RequestBody EstimatePolicyFormDTO estimatePolicyFormDTO) throws GlobalBusinessException {
		return ResponseResult.success(estimateService.getRegiterEstimateDataList(estimatePolicyFormDTO));
	}

	@ApiOperation("未决历史信息")
	@PostMapping(value = "/getEstimateHistoryList")
	public ResponseResult<List<EstimateChangePolicyFormDTO>> getEstimateHistoryList(@RequestBody EstimateChangePolicyFormDTO estimateChangePolicyFormDTO) throws GlobalBusinessException {
		return ResponseResult.success(estimateService.getEstimateHistoryList(estimateChangePolicyFormDTO));
	}

	@ApiOperation("报案跟踪立案保存")
	@ResponseBody
	@PostMapping( "/saveEstimateByReportTrack")
	public ResponseResult<RegistSaveDTO> saveEstimateByReportTrack(@RequestBody EstimatePolicyFormDTO estimatePolicyFormDTO) {
		String userId = WebServletContext.getUserId();
		String key = RedisKeyConstants.REGISTER_SAVE_LOCK + userId + ":" + estimatePolicyFormDTO.getReportNo() + ":" + estimatePolicyFormDTO.getCaseTimes();
		RLock lock = redissonClient.getLock(key);
		RegistSaveDTO registSaveDTO = new RegistSaveDTO();
		try {
			if (lock.tryLock()) {
				LogUtil.audit("报案跟踪立案saveEstimateByReport reportNo={},请求入参={}", estimatePolicyFormDTO.getReportNo(), JSON.toJSONString(estimatePolicyFormDTO));
				ResponseResult resultVO = registerCaseService.registerCaseCheck(estimatePolicyFormDTO.getReportNo(), estimatePolicyFormDTO.getCaseTimes());
				if (!ErrorCode.RegisterCase.CAN_APPLY_REGISTER_CASE.equals(resultVO.getData())
						&&!ErrorCode.RegisterCase.REGISTER_APPLY_IS_PROCESSING.equals(resultVO.getData())
						&&!ErrorCode.RegisterCase.CASE_IS_END.equals(resultVO.getData())) {
					throw new GlobalBusinessException(resultVO.getCode(), resultVO.getMsg());
				}
				if (!ErrorCode.RegisterCase.REGISTER_APPLY_IS_PROCESSING.equals(resultVO.getData())
						&&!ErrorCode.RegisterCase.CASE_IS_END.equals(resultVO.getData())){
					registSaveDTO = estimateService.saveEstimateByReportTrack(estimatePolicyFormDTO, userId);
					//如果是tpa报案数据 业务人员在核心手动立案后 需要通知TPa平台 发送MQ消息通知
					claimSendTpaMqInfoService.sendTpaMq(estimatePolicyFormDTO.getReportNo(), estimatePolicyFormDTO.getCaseTimes(), registSaveDTO.getProcessStatus());

				}
				//立案成功调用报案跟踪流程
				String processStatus = reportTrackTaskService.saveReportTrackTask(estimatePolicyFormDTO.getReportNo(), estimatePolicyFormDTO.getCaseTimes());
				registSaveDTO.setProcessStatus(processStatus);
			} else {
				throw new GlobalBusinessException("立案正在处理中！");
			}
		}finally {
			if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
				lock.unlock();
			}
		}
		return ResponseResult.success(registSaveDTO);
	}

	@ApiOperation("校验立案申请列表信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "reportNo",value = "报案号",required = true,dataType = "String", dataTypeClass=String.class),
			@ApiImplicitParam(name = "caseTimes",value = "赔付次数",required = true,dataType = "Integer",dataTypeClass=Integer.class)
	})
	@ResponseBody
	@GetMapping(value = "/registerCaseCheck")
	public ResponseResult registerCaseCheck(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes") Integer caseTimes){
		return registerCaseService.registerCaseCheck(reportNo, caseTimes);
	}

	@ResponseBody
	@GetMapping(value="/getCaseRegisterApplyDTO")
	public ResponseResult<CaseRegisterApplyVO> getCaseRegisterApplyVO(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes") Integer caseTimes){
		LogUtil.audit("查询立案申请的信息, reportNo={}, caseTimes={}", reportNo, caseTimes);
		CaseRegisterApplyVO caseRegisterApplyVO = registerCaseService.getLastCaseRegisterApplyVO(reportNo, caseTimes);
		return ResponseResult.success(caseRegisterApplyVO);
	}

	@ApiOperation("查询立案申请列表信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "reportNo",value = "报案号",required = true,dataType = "String",dataTypeClass=String.class),
			@ApiImplicitParam(name = "caseTimes",value = "赔付次数",required = true,dataType = "Integer",dataTypeClass=Integer.class)
	})
	@ResponseBody
	@GetMapping(value="/getCaseRegisterApplyDTOList")
	public ResponseResult<List<CaseRegisterApplyVO>> getCaseRegisterApplyList(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes") Integer caseTimes){
		LogUtil.audit("查询立案申请列表信息, reportNo={}, caseTimes={}", reportNo, caseTimes);
		return ResponseResult.success(registerCaseService.getLastCaseRegisterApplyVOList(reportNo, caseTimes));
	}

	@ApiOperation("发送立案审批任务")
	@PostMapping(value="/sendRegisterAudit")
	public ResponseResult<Object> sendRegisterAudit(@RequestBody CaseRegisterApplyVO registerApplyVO) throws GlobalBusinessException {

		if(ConstValues.AUDIT_DISAGREE_CODE.equals(registerApplyVO.getAuditOpinion()) && StringUtils.isEmptyStr(registerApplyVO.getAuditRemark())){
			throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "审批不同意，需要填写审批说明");
		}
		List<String> msgList = new ArrayList<>();
		estimateService.sendRegisterAudit(registerApplyVO,msgList, false);
		//立案成功调用报案跟踪流程
		reportTrackTaskService.saveReportTrackTask(registerApplyVO.getReportNo(), registerApplyVO.getCaseTimes());
		if(msgList.size() < 1 ){
			return ResponseResult.success("");
		}
		return ResponseResult.success(msgList.get(0));
	}

	@ApiOperation("根据保单号查询立案信息")
	@PostMapping(value = "/getEstimateByPolicyNo")
	public ResponseResult<List<PolicyDutyVO>> getEstimateByPolicyNo(EstimatePolicyVO estimatePolicyVO) throws GlobalBusinessException {
		List<PolicyDutyVO> policyDutyVOList = estimateService.getEstimateByPolicyNo(estimatePolicyVO);
		return ResponseResult.success(policyDutyVOList);
	}

	@ApiOperation("获取当前状态最新流程节点")
	@PostMapping(value = "/ownNewSuspendProcess")
	public ResponseResult<Object> ownNewSuspendProcess(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes") Integer caseTimes, @RequestParam("status") String status)   {
		return ResponseResult.success(taskInfoService.ownNewSuspendProcess(reportNo, caseTimes,SUPEND_USE,status));
	}

	@ApiOperation("发送拒赔")
	@PostMapping(value="/sendRejectAudit")
	public ResponseResult<Object> sendRejectAudit(@RequestBody ClaimRejectionApprovalRecordEntity claimRejectionApprovalRecordEntity) {
		String reportNo =claimRejectionApprovalRecordEntity.getReportNo() ;
	    Integer caseTimes=claimRejectionApprovalRecordEntity.getCaseTimes() ;
		LogUtil.audit("#发送拒赔申请任务-开始#:reportNo=%s,caseTimes=%s,taskId=%s", reportNo, caseTimes);
		estimateService.sendRejectAudit(claimRejectionApprovalRecordEntity,reportNo,caseTimes);
		LogUtil.audit("#发送拒赔申请任务-结束#:reportNo=%s,caseTimes=%s,taskId=%s", reportNo, caseTimes);
		return ResponseResult.success();
	}

	@ApiOperation("拒赔数据")
	@GetMapping(value="/getRejectAudit")
	public ResponseResult<Object> getRejectAudit(@RequestParam("reportNo") String reportNo,@RequestParam("caseTimes") Integer caseTimes)  {
		ClaimRejectionApprovalRecordEntity recordEntity = claimRejectionApprovalRecordEntityMapper.selectPendingRecord(reportNo, caseTimes);
		ClaimRejectionApprovalRecordDto claimRejectionApprovalRecordDto = new ClaimRejectionApprovalRecordDto();
		if (Objects.nonNull(recordEntity)) {
			BeanUtils.copyProperties(recordEntity, claimRejectionApprovalRecordDto);
			claimRejectionApprovalRecordDto.setRejectReason(recordEntity.getConclusionCauseDesc());
			claimRejectionApprovalRecordDto.setRejectReasonCode(recordEntity.getConclusionCauseCode());

			LossReduceDTO lossReduce = lossReduceMapper.getLossReduce(reportNo, caseTimes);
			if (null != lossReduce ){
				claimRejectionApprovalRecordDto.setReduceAmount(lossReduce.getReduceAmount()) ;
			}
		}
		return ResponseResult.success(claimRejectionApprovalRecordDto);
	}

	@ApiOperation("测试推送MQ")
	@ResponseBody
	@PostMapping( "/testSendMQ")
	public ResponseResult<Object> testSendMQ(@RequestBody EstimatePolicyFormDTO estimatePolicyFormDTO) {
		// 测试调用支付接口
		//payInfoNoticeThirdPartyCoreSAO.noticePayment(estimatePolicyFormDTO.getReportNo(),estimatePolicyFormDTO.getCaseTimes(), null, true, false);
		EstimatePolicyFormDTO dto=new EstimatePolicyFormDTO();
		//如果是tpa报案数据 业务人员在核心手动立案后 需要通知TPa平台 发送MQ消息通知
		claimSendTpaMqInfoService.sendTpaMq(estimatePolicyFormDTO.getReportNo(),estimatePolicyFormDTO.getCaseTimes(), CaseProcessStatus.REGISTRATION_WAIT_APPROVING.getCode());
		return ResponseResult.success(dto);
	}

}
