package com.paic.ncbs.claim.service.estimate.impl;

import cn.hutool.core.util.ObjectUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.estimate.ClmsTpaRequestRegisterMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.openapi.ClmsTpaRequestRegisterDTO;
import com.paic.ncbs.claim.service.estimate.ClmsTpaRequestRegisterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class ClmsTpaRequestRegisterServiceImpl implements ClmsTpaRequestRegisterService {

    @Autowired
    private ClmsTpaRequestRegisterMapper  clmsTpaRequestRegisterMapper;

    @Override
    public void save(ClmsTpaRequestRegisterDTO dto) {
        dto.setId(UuidUtil.getUUID());
        dto.setSource("TPA");
        dto.setRegister("1");
        dto.setCreatedBy("system");
        dto.setCreatedDate(new Date());
        dto.setUpdatedBy("system");
        dto.setUpdatedDate(new Date());
        clmsTpaRequestRegisterMapper.save(dto);
    }

    /**
     * 更新
     * @param String reportNo,Integer caseTimes
     */
    @Override
    public void update(String reportNo,Integer caseTimes) {
        ClmsTpaRequestRegisterDTO updateDto= clmsTpaRequestRegisterMapper.getClmsTpaRequestRegisterDTO(reportNo,caseTimes);
        if(ObjectUtil.isEmpty(updateDto)){
            throw new GlobalBusinessException("更新立案记录信息异常");
        }
        updateDto.setReportTrack("1");
        updateDto.setUpdatedDate(new Date());
        clmsTpaRequestRegisterMapper.updateData(updateDto);

    }

    @Override
    public ClmsTpaRequestRegisterDTO getClmsTpaRequestRegisterDTO(String reportNo, Integer caseTimes) {
        if(StringUtils.isEmptyStr(reportNo)){
            throw new GlobalBusinessException("报案号不能为空！");
        }
        if(ObjectUtil.isEmpty(caseTimes)){
            caseTimes=1;
        }
        return  clmsTpaRequestRegisterMapper.getClmsTpaRequestRegisterDTO(reportNo,caseTimes);
    }
}
