package com.paic.ncbs.claim.service.endcase;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;



public interface EndCaseService {

    void endCase(WholeCaseBaseDTO wholeCaseBaseDTO);

    void batchModifyCaseBaseDTO(WholeCaseBaseDTO wholeCaseBaseDTO);

    void endCaseZeroCancel(String reportNo, Integer caseTimes, String applyType) throws GlobalBusinessException;

}
