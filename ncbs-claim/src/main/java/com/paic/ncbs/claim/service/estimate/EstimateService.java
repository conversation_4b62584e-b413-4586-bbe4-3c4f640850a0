package com.paic.ncbs.claim.service.estimate;

import com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.estimate.*;
import com.paic.ncbs.claim.model.vo.duty.DutySurveyVO;
import com.paic.ncbs.claim.model.vo.endcase.CaseRegisterApplyVO;
import com.paic.ncbs.claim.model.vo.policy.EstimatePolicyVO;
import com.paic.ncbs.claim.model.vo.policy.PolicyDutyVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface EstimateService {

	void modifyEstimateDataList(EstimatePolicyFormDTO estimatePolicyFormDTO) throws GlobalBusinessException;

	void addEstimateDataList(EstimatePolicyFormDTO estimatePolicyFormDTO) throws GlobalBusinessException;

	List<EstimatePolicyDTO> getEstimateList(String reportNo, Integer caseTimes);

	EstimatePolicyFormDTO getEstimateDataList(EstimatePolicyFormDTO estimatePolicyForm) throws GlobalBusinessException;

	EstimatePolicyFormDTO getRegiterEstimateDataList(EstimatePolicyFormDTO estimatePolicyFormDTO) throws GlobalBusinessException;

	RegistSaveDTO saveEstimateByReportTrack(EstimatePolicyFormDTO estimatePolicyFormDTO, String userId);

	void clearEstimateDutyRecordList(List<EstimatePolicyDTO> estimatePolicyDTOList, String estimateType);

	boolean checkExists(String reportNo, Integer caseTimes, String estimateType);

	void asyncHandleEstimateData(EstimatePolicyFormDTO estimatePolicyFormDTO) throws GlobalBusinessException;

	BigDecimal getAmount(String reportNo, String caseNo, Integer caseTimes);

	List<EstimateDutyDTO> getEstimateDutyDTOList(List<String> caseNoList, Integer caseTimes);

	List<EstimatePolicySumDTO> getEstimatePolicySum(String reportNo, Integer caseTimes);

	EstimatePolicyFormDTO getEstimateDataByTache(String reportNo, Integer caseTimes, String tache, String scene);

	EstimatePolicyFormDTO getEstimateDataByPolicy(String policyNo, Integer caseTimes, String tache,String report) throws GlobalBusinessException;

	void initPolicyClaimCaseData(String reportNo, Integer caseTimes, String userId);

	List<EstimatePolicyDTO> getPolicyCopy(String reportNo, Integer caseTimes);

	void addPolicyCopyData(List<EstimatePolicyDTO> estimatePolicyDTOList);

	List<EstimatePolicyDTO> getByReportNoAndCaseTimes(String reportNo, Integer caseTimes);

	void initEstimateData(EstimatePolicyFormDTO epf);

    String getIdAhcsEstimateDutyRecord(String reportNo, Integer caseTimes, String estimateType);

	BigDecimal getLatestRegisterAmount(String reportNo, Integer caseTimes, String estimateType);

	BigDecimal getRegisterAmount(String reportNo, Integer caseTimes);

	void sendRegisterAudit(CaseRegisterApplyVO caseRegisterApplyVO, List<String> msgList, boolean isAutoRegister) throws GlobalBusinessException;

	boolean checkRegistPremission(String reportNo, String taskId, String userId,BigDecimal estimateAmount, String bpmKey, String selectedUserId, String taskIdNew);

	List<PolicyDutyVO> getEstimateByPolicyNo(EstimatePolicyVO estimatePolicyVO) throws GlobalBusinessException;

	List<EstimatePolicyDTO> getEstimatePolicies(String reportNo, Integer caseTimes, Integer subTimes) throws GlobalBusinessException;

    void addBatchEstimatePolicy(List<EstimatePolicyDTO> policyList);

	void sendRejectAudit(ClaimRejectionApprovalRecordEntity claimRejectionApprovalRecordEntity, String reportNo, Integer caseTimes);

	EstimatePolicyFormDTO getEstimateDataByType(String reportNo, Integer caseTimes,String tache) throws GlobalBusinessException;

	/**
	 * 保存监管估损中间表
	 * @param loginUm
	 * @param reportNo
	 * @param caseTimes
	 * @param type
	 */
	void saveEstimateIntermediateData(String loginUm, String reportNo, Integer caseTimes, String type);

	/**
	 * 处理立案信息
	 * @param dutySurveyVO
	 */
	void dealEstimateInfo(DutySurveyVO dutySurveyVO);

	/**
	 * 未决历史信息,展示既往立案信息记录
	 * @param estimateChangePolicyFormDTO
	 * @return
	 */
	List<EstimateChangePolicyFormDTO> getEstimateHistoryList(EstimateChangePolicyFormDTO estimateChangePolicyFormDTO);

	Map<String,BigDecimal> getCoinsRateMap(String reportNo);
}
