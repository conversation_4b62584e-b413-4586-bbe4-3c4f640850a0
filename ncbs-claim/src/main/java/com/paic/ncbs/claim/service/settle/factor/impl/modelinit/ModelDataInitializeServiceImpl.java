package com.paic.ncbs.claim.service.settle.factor.impl.modelinit;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.SettleHelper;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.report.LinkManMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicySumDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.MedicalInfoDTO;
import com.paic.ncbs.claim.model.vo.settle.FeeAmountVO;
import com.paic.ncbs.claim.service.common.ClmBatchService;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyAllInfoService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.fee.FeePayService;
import com.paic.ncbs.claim.service.settle.MaxPayService;
import com.paic.ncbs.claim.service.settle.PlanPayService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.bill.MedicalBillInfoService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.modelinit.ModelDataInitializeService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.sum;

@Slf4j
@Order(1)
@Service
public class ModelDataInitializeServiceImpl implements ModelDataInitializeService {

    @Autowired
    private MedicalBillInfoService medicalBillInfoService;

    @Autowired
    private ClmsQueryPolicyAllInfoService clmsQueryPolicyAllInfoService;

    @Autowired
    private LinkManMapper linkManMapper;
    @Autowired
    private ClmBatchService clmBatchService;

    @Autowired
    private MaxPayService maxPayService;
    @Autowired
    private FeePayService feePayService;
    @Autowired
    private EstimateService estimateService;
    @Autowired
    private PolicyPayMapper policyPayMapper;
    @Resource(name = "planPayService")
    private PlanPayService planPayService;

    @Autowired
    private CaseClassMapper caseClassDao;

    @Override
    public ClaimCaseDTO initialize(ClaimCaseDTO claimCaseDTO) {
        String reportNo= claimCaseDTO.getReportNo();
        Integer caseTimes = claimCaseDTO.getCaseTimes();
        log.info("模型初始化开始报案号={},初始化时参数={}",reportNo, JsonUtils.toJsonString(claimCaseDTO));
        //1:保单信息查询
        List<PolicyPayDTO> policys=  new ArrayList<>();
        if(Objects.equals("0",claimCaseDTO.getFlag())){
            policys =clmsQueryPolicyAllInfoService.getPolicyAllInfo(reportNo,caseTimes);
            if(CollectionUtil.isEmpty(policys)){
                return claimCaseDTO;
            }
        }else{
            policys = policyPayMapper.selectByReportNo(reportNo, caseTimes);
            LogUtil.audit("--重新理算-查询险种责任赔付信息，并重新设置责任属性,报案号：" + reportNo);
            for (PolicyPayDTO policy : policys) {
                planPayService.reQueryDutyDetail(policy);
            }
        }

        claimCaseDTO.setPolicyPayDTOList(policys);
        MedicalInfoDTO medicalInfoDTO = new MedicalInfoDTO();
        //2:发票信息查询
        List<MedicalBillInfoDTO> medicalBillInfoDTOList =medicalBillInfoService.getMedicalBillInfoList(reportNo,caseTimes);
        medicalInfoDTO.setMedicalBillInfoDTOList(medicalBillInfoDTOList);
        claimCaseDTO.setMedicalInfoDTO(medicalInfoDTO);
        String idClmBatch = clmBatchService.insertBatch(reportNo, caseTimes, SettleConst.SETTLE_STATUS_ON);
        log.info("模型初始化数据完成报案号={},初始化后数据={}",reportNo, JsonUtils.toJsonString(claimCaseDTO));
        if(Objects.equals("0",claimCaseDTO.getFlag())){
            LogUtil.audit("--初始化赔付信息-默认不发送赔付短信,报案号：" + reportNo);
            notSendMessage(reportNo,caseTimes);
            LogUtil.audit("--初始化赔付信息-插入理算批次表,报案号：" + reportNo);

            claimCaseDTO.setIdClmBatch(idClmBatch);
            LogUtil.audit("--初始化赔付信息-生成赔付信息uuid,报案号：" + reportNo);
            SettleHelper.setPolicyPays(policys, idClmBatch);

        }else{
            LogUtil.audit("--初始化赔付信息-设置预估、预赔、费用,报案号：" + reportNo);
            this.setPolicyInfo(policys);
        }
        LogUtil.audit("--初始化赔付信息-计算剩余赔付额,报案号：" + reportNo);
        setRollback(policys);
        maxPayService.initPoliciesPayMaxPay(policys, null);

        //案件小类
        List<String> caseClassList = caseClassDao.getCaseClassList(reportNo, caseTimes, BpmConstants.CHECK_DUTY);
        claimCaseDTO.setCaseClassList(caseClassList);

        return claimCaseDTO;
    }
    private void notSendMessage(String reportNo,Integer caseTimes){
        LinkManEntity entity = new LinkManEntity();
        entity.setUpdatedBy(BaseConstant.SYSTEM);
        entity.setReportNo(reportNo);
        entity.setCaseTimes(caseTimes.shortValue());
        entity.setSendMessage(ConstValues.NO);
        linkManMapper.updateByReportNo(entity);

    }
    private void setRollback(List<PolicyPayDTO> copyPolicyPays){
        if(ListUtils.isEmptyList(copyPolicyPays)){
            return;
        }
        copyPolicyPays.get(0).setRollback(true);
    }
    /**
     * 设置保单总预估金额、总预赔、初始化费用金额
     */
    private void setPolicyInfo(List<PolicyPayDTO> policyPays) {
        //设置保单总预估金额
        setEstimatePay(policyPays);
        //设置保单总预赔
        setPrePayInfo(policyPays);
        //初始化费用（都为0）
        setPolicyFeePay(policyPays);
    }
    private void setEstimatePay(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        PolicyPayDTO policy = policyPays.get(0);
        String reportNo = policy.getReportNo();
        Integer caseTimes = policy.getCaseTimes();
        List<EstimatePolicySumDTO> estimatePolicys = estimateService.getEstimatePolicySum(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(estimatePolicys)) {
            return;
        }
        for (PolicyPayDTO policyPay : policyPays) {
            for (EstimatePolicySumDTO estimatePolicy : estimatePolicys) {
                if (policyPay.getPolicyNo().equals(estimatePolicy.getPolicyNo()) && policyPay.getCaseNo().equals(estimatePolicy.getCaseNo())) {
                    policyPay.setPolicySumEstimate(estimatePolicy.getPolicySumEstimate());
                }
            }
        }
    }
    private void setPrePayInfo(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        PolicyPayDTO policy = policyPays.get(0);
        String reportNo = policy.getReportNo();
        Integer caseTimes = policy.getCaseTimes();
        List<PolicyPayDTO> oldPolicyPays = policyPayMapper.getPrePolicyPays(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(oldPolicyPays)) {
            return;
        }
        for (PolicyPayDTO policyPay : policyPays) {
            for (PolicyPayDTO oldPolicyPay : oldPolicyPays) {
                if (policyPay.getPolicyNo().equals(oldPolicyPay.getPolicyNo()) && policyPay.getCaseNo().equals(oldPolicyPay.getCaseNo())) {
                    policyPay.setPolicyPrePay(oldPolicyPay.getPolicyPrePay());
                    policyPay.setPolicyPreFee(oldPolicyPay.getPolicyPreFee());
                }
            }
        }
    }
    private void setPolicyFeePay(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        //获取理赔费用信息
        FeePayDTO feePayDTO = new FeePayDTO();
        feePayDTO.setReportNo(policyPays.get(0).getReportNo());
        feePayDTO.setCaseTimes(policyPays.get(0).getCaseTimes());
        feePayDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
        List<FeeInfoDTO> feeInfoList = Optional.ofNullable(feePayService.getFeePayByParam(feePayDTO)).orElse(new ArrayList<>());
        for (PolicyPayDTO policy : policyPays) {
            FeeAmountVO feeAmount = SettleHelper.getPolicyFee(feeInfoList, policy.getPolicyNo(), policy.getCaseNo());
            BigDecimal policyDecreaseFee = feeAmount.getFeeAmountAwa();
            BigDecimal policySumFee = feeAmount.getFeeAmountSum().subtract(policyDecreaseFee);
            policy.setPolicyDecreaseFee(policyDecreaseFee);
            policy.setPolicySumFee(policySumFee);
            BigDecimal policyPay = policy.getSettleAmount();
            policy.setPolicySumPay(sum(policySumFee, policyPay));
        }
    }
}
