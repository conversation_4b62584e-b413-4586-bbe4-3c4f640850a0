package com.paic.ncbs.claim.controller.policy;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.model.dto.policy.QueryPolicyAbstractInfoListDTO;
import com.paic.ncbs.claim.model.dto.policy.QueryPolicyDetailInfoDTO;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.service.policy.PublicQueryPoilcyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "保单信息查询")
@RestController
@RequestMapping("/policy")
public class PublicQureyPolicyController {

	@Autowired
	private PublicQueryPoilcyService publicQueryPoilcyService;

	@ApiOperation("查询保单摘要信息列表")
	@PostMapping(value = "/app/PolicyQueryAction/queryPolicyAbstractInfoList")
	@ResponseBody
	public ResponseResult<List> queryPolicyAbstractInfoList(@RequestBody QueryPolicyAbstractInfoListDTO queryPolicyAbstractInfoListDTO) {
		try {
			Long startTime = System.currentTimeMillis();
			LogUtil.info("调用PolicyQueryAction/queryPolicyAbstractInfoList入参：{}", JSONObject.toJSONString(queryPolicyAbstractInfoListDTO));
			List list = publicQueryPoilcyService.queryPolicyAbstractInfoList(queryPolicyAbstractInfoListDTO);
			LogUtil.info("接口queryPolicyAbstractInfoList耗时：{}ms", System.currentTimeMillis() - startTime);
			return ResponseResult.success(list);
		} catch (Exception e) {
			throw new GlobalBusinessException(e.getMessage());
		}
	}

	@ApiOperation("根据保单号查询保单详情")
	@PostMapping(value = "/app/PolicyQueryAction/queryPolicyDetailByPolicyNo")
	@ResponseBody
	public ResponseResult<Map> queryPolicyDetailByPolicyNo(@RequestBody QueryPolicyDetailInfoDTO queryPolicyDetailInfoDTO) {
		try {
			Long startTime = System.currentTimeMillis();
			LogUtil.info("调用PolicyQueryAction/queryPolicyDetailByPolicyNo入参：{}", JSONObject.toJSONString(queryPolicyDetailInfoDTO));
			Map map = publicQueryPoilcyService.queryPolicyDetailByPolicyNo(queryPolicyDetailInfoDTO);
			LogUtil.info("接口queryPolicyDetailByPolicyNo耗时：{}ms", System.currentTimeMillis() - startTime);
			return ResponseResult.success(map);
		} catch (Exception e) {
			throw new GlobalBusinessException(e.getMessage());
		}
	}

	@ApiOperation("根据文档编号和客户信息查询保单详情")
	@PostMapping(value = "/app/PolicyQueryAction/queryPolicyDetailByDocumentNoAndClientMsg")
	@ResponseBody
	public ResponseResult<Map> queryPolicyDetailByDocumentNoAndClientMsg(@RequestBody QueryPolicyDetailInfoDTO queryPolicyDetailInfoDTO) {
		try {
			Long startTime = System.currentTimeMillis();
			LogUtil.info("调用PolicyQueryAction/queryPolicyDetailByDocumentNoAndClientMsg入参：{}",
					JSONObject.toJSONString(queryPolicyDetailInfoDTO));
			Map map = publicQueryPoilcyService.queryPolicyDetailByDocumentNoAndClientMsg(queryPolicyDetailInfoDTO);
			LogUtil.info("接口queryPolicyDetailByDocumentNoAndClientMsg耗时：{}ms", System.currentTimeMillis() - startTime);
			return ResponseResult.success(map);
		} catch (Exception e) {
			throw new GlobalBusinessException(e.getMessage());
		}
	}

}
