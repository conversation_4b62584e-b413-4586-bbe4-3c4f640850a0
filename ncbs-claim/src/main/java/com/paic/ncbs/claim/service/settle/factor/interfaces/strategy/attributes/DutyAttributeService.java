package com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.attributes;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;

import java.util.Map;
import java.util.Optional;

/**
 * 责任属性接口
 */
public interface DutyAttributeService {
    void setDutyDetailAttribute(DutyDetailPayDTO detail, Map<String, Optional<String>> attributes,
                           Map<String, Map<String, String>> attributesdetailMap);
}
