package com.paic.ncbs.claim.controller.loss;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.model.vo.duty.AccidentLossVo;
import com.paic.ncbs.claim.model.vo.duty.LossEstimationVO;
import com.paic.ncbs.claim.service.duty.LossEstimationService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api(tags="估损信息")
@RestController
@RequestMapping(value = "loss/lossAction")
public class LossEstimationController extends BaseController {

	@Autowired
	private LossEstimationService lossEstimationService;

	@ApiOperation("获取估损信息")
	@GetMapping("/getLossEstimation")
	public ResponseResult<Object> getLossEstimation(@RequestParam ("reportNo")String reportNo, @RequestParam ("caseTimes")int caseTimes, @RequestParam ("taskId")String taskId){
		AccidentLossVo accidentLossVo = lossEstimationService.getAccisentLossVO(reportNo,caseTimes,taskId);
		return ResponseResult.success(accidentLossVo);
	}

}