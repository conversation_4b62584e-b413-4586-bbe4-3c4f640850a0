package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.dao.mapper.checkloss.BigDiseaseDefineMapper;
import com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDefineDTO;
import com.paic.ncbs.claim.service.checkloss.BigDiseaseDefineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("bigDiseaseDefineService")
public class BigDiseaseDefineServiceImpl implements BigDiseaseDefineService {

	@Autowired
	private BigDiseaseDefineMapper bigDiseaseDefineDao;
	
	 
	@Override
	public List<BigDiseaseDefineDTO> getBigDiseaseDefines() {
		return bigDiseaseDefineDao.getBigDiseaseDefines();
	}

}
