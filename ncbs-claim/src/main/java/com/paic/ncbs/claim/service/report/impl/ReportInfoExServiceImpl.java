package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import com.paic.ncbs.claim.service.report.ReportInfoExService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReportInfoExServiceImpl extends BaseServiceImpl<ReportInfoExEntity> implements ReportInfoExService {

    @Autowired
    private ReportInfoExMapper reportInfoExMapper;

    @Override
    public BaseDao<ReportInfoExEntity> getDao() {
        return reportInfoExMapper;
    }

    @Override
    public List<ReportInfoExEntity> getReportInfoEx(String reportNo) {
        return reportInfoExMapper.getReportInfoEx(reportNo);
    }

    @Override
    public int updataReportInfoExByReportNo(ReportInfoExEntity reportInfoExEntity) {
        return reportInfoExMapper.updataReportInfoExByReportNo(reportInfoExEntity);
    }

    @Override
    public List<ReportInfoExEntity> getReportInfoExByAcceptanceNo(String acceptanceNo) {
        return reportInfoExMapper.getReportInfoExByAcceptanceNo(acceptanceNo);
    }

    @Override
    public void updateCaseClassByReportNo(String reportNo, String lossClass, String loginUm) {
        reportInfoExMapper.updateCaseClassByReportNo(reportNo,lossClass,loginUm);
    }
}
