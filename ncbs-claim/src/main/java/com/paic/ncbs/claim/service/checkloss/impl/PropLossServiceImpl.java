package com.paic.ncbs.claim.service.checkloss.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.duty.PropDetailLossEntity;
import com.paic.ncbs.claim.dao.entity.duty.PropLossEntity;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.PropDetailLossMapper;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.PropLossMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.model.dto.duty.PropDetailLossDTO;
import com.paic.ncbs.claim.model.dto.duty.PropLossDTO;
import com.paic.ncbs.claim.service.checkloss.PropLossService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class PropLossServiceImpl implements PropLossService {
    @Autowired
    private PropLossMapper propLossMapper;
    @Autowired
    private PropDetailLossMapper propDetailLossMapper;
    @Autowired
    private CommonParameterMapper commonParameterMapper;


    @Override
    public void insert(PropLossDTO propLossDTO) {
        if (null == propLossDTO || CollectionUtils.isEmpty(propLossDTO.getPropDetailLossDTOList())) {
            return;
        }
        PropLossEntity propLossEntity = new PropLossEntity();
        BeanUtils.copyProperties(propLossDTO, propLossEntity);
        propLossEntity.setCreatedBy(WebServletContext.getUserId());
        propLossEntity.setCreatedDate(new Date());
        propLossEntity.setUpdatedBy(WebServletContext.getUserId());
        propLossEntity.setUpdatedDate(new Date());
        propLossEntity.setId(UuidUtil.getUUID());
        propLossMapper.insert(propLossEntity);

        List<PropDetailLossDTO> propDetailLossDTOList = propLossDTO.getPropDetailLossDTOList();
        List<PropDetailLossEntity> propDetailLossEntities = new ArrayList<>();
        for (int i = 0; i < propDetailLossDTOList.size(); i++) {
            PropDetailLossDTO propDetailLossDTO = propDetailLossDTOList.get(i);

            PropDetailLossEntity propDetailLossEntity = new PropDetailLossEntity();
            BeanUtils.copyProperties(propDetailLossDTO, propDetailLossEntity);

            propDetailLossEntity.setSerialNo(i + 1);
            if(StringUtils.isBlank(propDetailLossEntity.getLossTypeName())){
                propDetailLossEntity.setLossTypeName(commonParameterMapper.getNameByCode(propDetailLossEntity.getLossType(),
                        "PROP_LOSS_TYPE"));
            }
            propDetailLossEntity.setCreatedBy(WebServletContext.getUserId());
            propDetailLossEntity.setCreatedDate(new Date());
            propDetailLossEntity.setUpdatedBy(WebServletContext.getUserId());
            propDetailLossEntity.setUpdatedDate(new Date());
            propDetailLossEntity.setId(UuidUtil.getUUID());
            propDetailLossEntity.setIdPropLoss(propLossEntity.getId());
            propDetailLossEntities.add(propDetailLossEntity);
        }

        propDetailLossMapper.insertBatch(propDetailLossEntities);
    }

    /**
     * 根据案件号查询财产损失信息
     *
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    public PropLossDTO queryByReportNo(String reportNo, Integer caseTimes) {
        PropLossEntity propLossEntity =
                propLossMapper.selectOne(new LambdaQueryWrapper<PropLossEntity>().eq(PropLossEntity::getReportNo,
                        reportNo).eq(PropLossEntity::getCaseTimes, caseTimes));
        if(null == propLossEntity){
            return null;
        }
        PropLossDTO propLossDTO = new PropLossDTO();
        BeanUtils.copyProperties(propLossEntity,propLossDTO);


        List<PropDetailLossEntity> propDetailLossEntities = propDetailLossMapper.selectList(new LambdaQueryWrapper<PropDetailLossEntity>().eq(PropDetailLossEntity::getIdPropLoss,
                propLossEntity.getId()).orderByAsc(PropDetailLossEntity::getSerialNo));
        List<PropDetailLossDTO> propDetailLossDTOList = new ArrayList<>();
        for (PropDetailLossEntity propDetailLossEntity : propDetailLossEntities) {
            PropDetailLossDTO propDetailLossDTO = new PropDetailLossDTO();
            BeanUtils.copyProperties( propDetailLossEntity,propDetailLossDTO);
            propDetailLossDTOList.add(propDetailLossDTO);
        }
        propLossDTO.setPropDetailLossDTOList(propDetailLossDTOList);
        return propLossDTO;
    }

    /**
     * 根据条件删除
     *
     * @param reportNo
     * @param caseTimes
     */
    @Override
    public void deleteByCondition(String reportNo, int caseTimes) {
        propLossMapper.delete(new LambdaQueryWrapper<PropLossEntity>().eq(PropLossEntity::getReportNo,
                reportNo).eq(PropLossEntity::getCaseTimes, caseTimes));
        propDetailLossMapper.delete(new LambdaQueryWrapper<PropDetailLossEntity>().eq(PropDetailLossEntity::getReportNo,
                reportNo).eq(PropDetailLossEntity::getCaseTimes, caseTimes));
    }
}
