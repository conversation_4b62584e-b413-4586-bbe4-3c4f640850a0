package com.paic.ncbs.claim.replevy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 追偿审核信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Getter
@Setter
@TableName("clms_replevy_text")
public class ClmsReplevyText implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 追偿主表id
     */
    @TableField("replevy_id")
    private String replevyId;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 追偿案件号
     */
    @TableField("replevy_no")
    private String replevyNo;

    /**
     * 追偿次数
     */
    @TableField("replevy_times")
    private Integer replevyTimes;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 角色
     */
    @TableField("replevy_role")
    private String replevyRole;

    /**
     * 审批类型 1-追偿；2-追偿费用
     */
    @TableField("opinion_type")
    private String opinionType;

    /**
     * 申请人
     */
    @TableField("apply_Um")
    private String applyUm;
    /**
     * 申请说明
     */
    @TableField("apply_text")
    private String applyText;
    /**
     * 申请日期
     */
    @TableField("apply_date")
    private Date applyDate;
    /**
     *审批人UM
     */
    @TableField("approve_um")
    private String approveUm;

    /**
     * 操作机构
     */
    @TableField("make_com")
    private String makeCom;

    /**
     * 审核意见
     */
    @TableField("approve_opinion")
    private String approveOpinion;

    /**
     * 意见说明
     */
    @TableField("handle_text")
    private String handleText;

    /**
     * 有效标志
     */
    @TableField("valid_flag")
    private String validFlag;

    /**
     * 标志字段 0-审批中 1-审批通过
     */
    @TableField("flag")
    private String flag;

    /**
     * 序号
     */
    @TableField("serial_no")
    private Integer serialNo;

    /**
     * 审批处理日期
     */
    @TableField("handle_date")
    private Date handleDate;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;

    /**
     * 费用id
     */
    private String replevyChargeId;

}
