package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.dao.mapper.checkloss.ExaminFailMapper;
import com.paic.ncbs.claim.model.dto.checkloss.ExaminFailConditonDTO;
import com.paic.ncbs.claim.model.dto.checkloss.ExaminFailDTO;
import com.paic.ncbs.claim.service.checkloss.ExaminFailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ExaminFailServiceImpl implements ExaminFailService {

	@Autowired
    ExaminFailMapper examinFailDao;

	@Override
	@Transactional
	public void saveExaminFail(ExaminFailDTO examinFailDTO) {
		String reportNo = examinFailDTO.getReportNo();
		Integer caseTimes = examinFailDTO.getCaseTimes();
		String channelProcessId = examinFailDTO.getIdAhcsChannelProcess();
		ExaminFailDTO existExaminFailDTO = examinFailDao.getExaminFail(reportNo, caseTimes, null,examinFailDTO.getTaskCode(), channelProcessId);
		if(existExaminFailDTO != null){
			examinFailDao.removeExaminFail(reportNo, caseTimes,examinFailDTO.getTaskCode(), channelProcessId);
		}
		examinFailDao.addExaminFail(examinFailDTO);

	}

	@Override
	public void removeExaminFail(String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
		examinFailDao.removeExaminFail(reportNo, caseTimes, taskCode, channelProcessId);
	}

	@Override
	public ExaminFailDTO getExaminFail(String reportNo, Integer caseTimes, String status, String taskCode, String channelProcessId) {
		return examinFailDao.getExaminFail(reportNo, caseTimes, status, taskCode, channelProcessId);
	}


	@Override
	public Integer countByExaminFailConditon(ExaminFailConditonDTO examinFailConditonDTO) {
		return examinFailDao.countByExaminFailConditon(examinFailConditonDTO);
	}

	@Override
	public Integer countByExaminFailSubjectConditon(ExaminFailConditonDTO examinFailConditonDTO) {
		return examinFailDao.countByExaminFailSubjectConditon(examinFailConditonDTO);
	}

}
