package com.paic.ncbs.claim.controller.trance;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.dto.trace.PersonTranceMainVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.checkloss.DiagnoseDefineService;
import com.paic.ncbs.claim.service.report.ReportService;
import com.paic.ncbs.claim.service.trace.PersonTraceService;
import com.paic.ncbs.claim.service.trace.impl.ClmsTraceServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "人伤跟踪信息")
@RestController
@RequestMapping("/trace/traceAction")
@Slf4j
public class PersTraceController {

    @Autowired
    private PersonTraceService personTraceService;
    @Autowired
    private BpmService bpmService;
    @Autowired
    private DiagnoseDefineService diagnoseDefineService;
    @Autowired
    private ClmsTraceServiceImpl clmsTraceService;

    @ApiOperation("人伤跟踪初始化数据")
    @PostMapping("/initPersTrace")
    public ResponseResult<Object> initPersTrace(@RequestBody PersonTranceRequestVo personTranceRequestVo){
        try{
            return ResponseResult.success(personTraceService.intPersonTrance(personTranceRequestVo));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("人伤跟踪数据保存")
    @PostMapping("/saveOrSubmitPersTrace")
    public ResponseResult<Object> saveOrSubmitPersTrace(@RequestBody PersonTranceMainVO personTranceMainVO){
        try{
            return personTraceService.saveOrSubmitPersTrace(personTranceMainVO);
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("伤情诊断数据")
    @GetMapping("/getCommonParameter")
    public ResponseResult<Object> getCommonParameter(@RequestParam(value = "collectionCode")String collectionCode,@RequestParam(value = "valueChineseName")String valueChineseName){
        try{
            return ResponseResult.success(personTraceService.getCommonParameter(collectionCode,valueChineseName));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("历史发起的人伤跟踪记录数")
    @PostMapping("/getHisPerTraceCount")
    public ResponseResult<Object> getHisPerTraceCount(@RequestBody PersonTranceRequestVo personTranceRequestVo){
        try{
            return personTraceService.getCountSize(personTranceRequestVo.getReportNo(),personTranceRequestVo.getCaseTimes());
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("理算环节--关联人伤信息模块数据")
    @PostMapping("/getclmsPersTraceExp")
    public ResponseResult<Object> getclmsPersTraceExp(@RequestBody PersonTranceRequestVo personTranceRequestVo){
        try{
            return personTraceService.getclmsPersTraceExp(personTranceRequestVo);
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("理算环节--关联人伤信息模块数据--点击费用明细查询详细数据")
    @PostMapping("/getclmsPersTraceFee")
    public ResponseResult<Object> getclmsPersTraceFee(@RequestBody PersonTranceRequestVo personTranceRequestVo){
        try{
            return personTraceService.getclmsPersTraceFee(personTranceRequestVo);
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("人伤估损信息查询赔款立案金额")
    @GetMapping("/getestimateAmount")
    public ResponseResult<Object> getestimateAmount(@RequestParam(value="reportNo")String reportNo, @RequestParam(value="dutyCode")String dutyCode,
                                                    @RequestParam(value="caseTimes")Integer caseTimes){
        try{
            return ResponseResult.success(personTraceService.getestimateAmount(reportNo,dutyCode,caseTimes));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("提交暂存按钮校验")
    @GetMapping("/getChekStatus")
    public ResponseResult<Object> getChekStatus(@RequestParam(value="reportNo")String reportNo, @RequestParam(value="submitFlag")String submitFlag){
        try{
            return personTraceService.getChekStatus(reportNo,submitFlag);
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("人伤跟踪删除")
    @PostMapping("/removePersonTrace")
    public ResponseResult<Object> removePersonTrace(@RequestBody PersonTranceRequestVo personTranceRequestVo){
        try{
            return personTraceService.removePersonTrace(personTranceRequestVo);
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

    @ApiOperation("根据伤情判断带出疾病代码")
    @PostMapping("/getParamMappingDto")
    public ResponseResult<Object> getParamMappingDto(@RequestBody CommonParameterTinyDTO commonParameterTinyDTO){
        try{
            return personTraceService.getParamMappingDto(commonParameterTinyDTO);
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }


    @ApiOperation("伤情诊断数据")
    @GetMapping("/getDiagnoseDefineList")
    public ResponseResult<Object> getDiagnoseDefineList(@RequestParam("searchStr") String searchStr,@RequestParam("reportNo") String reportNo){
        try{
            return ResponseResult.success(diagnoseDefineService.getDiagnoseDefineVos(searchStr,reportNo));
        }catch (GlobalBusinessException e){
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }
    @Autowired
    private ReportService reportService;

    @ApiOperation("测试醒接口")
    @GetMapping("/persTest")
    public ResponseResult<Object> test(){
        try{
          String  reportNo = "97761000000001000914";
            int caseTimes = 1;
            return ResponseResult.success(reportService.getReportPlanDuty(reportNo));
        }catch (GlobalBusinessException e){
            e.printStackTrace();
            return ResponseResult.fail(e.getCode(),e.getMessage());
        }
    }

}
