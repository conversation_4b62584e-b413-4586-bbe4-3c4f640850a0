package com.paic.ncbs.claim.controller.rule;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyAllInfoService;
import com.paic.ncbs.claim.service.other.ConfigService;
import com.paic.ncbs.claim.service.rule.AutoRuleService;
import com.paic.ncbs.claim.service.settle.impl.PolicyPayServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/rule")
@Validated
public class AutoRuleController {

    @Autowired
    private AutoRuleService autoRuleService;
    @Autowired
    private PolicyPayServiceImpl policyPayService;
    @Autowired
    private ClmsQueryPolicyAllInfoService clmsQueryPolicyAllInfoService;

    @Autowired
    private ConfigService configService;

    @GetMapping("/testSettleRule")
    public ResponseResult<Object> checkRepeatReceipt(@RequestParam(value = "reportNo",required = false) String reportNo,
                                                     @RequestParam(value = "caseTimes",required = false) Integer caseTimes){
        return ResponseResult.success(autoRuleService.executeSettleRule(reportNo,caseTimes));
    }

    @GetMapping("/testDutyConfirmRule")
    public ResponseResult<Object> dutyConfirmRule(@RequestParam(value = "reportNo",required = false) String reportNo,
                                                     @RequestParam(value = "caseTimes",required = false) Integer caseTimes){
        List<PolicyPayDTO> policyPayDTOS = clmsQueryPolicyAllInfoService.getPolicyAllInfo(reportNo,caseTimes);
        autoRuleService.execDutyConfirmRule(reportNo,caseTimes,policyPayDTOS);
        return ResponseResult.success();
    }

    @GetMapping("/testDispatch")
    public ResponseResult<Object> testDispatch(@RequestParam(value = "departmentCode", required = false) Integer departmentCode
            , @RequestParam(value = "profitCenter", required = false) String profitCenter
            , @RequestParam(value = "productCode", required = false) String productCode,
                                        @RequestParam(value = "productGroup", required = false) String productGroup) {
        String dispatchConfig = configService.getDispatchConfigPriority(departmentCode, profitCenter, productCode,
                productGroup);
        return ResponseResult.success(dispatchConfig);
    }
}
