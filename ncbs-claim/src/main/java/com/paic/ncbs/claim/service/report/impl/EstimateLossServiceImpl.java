package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.EstimateTypeEnum;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.report.EstimateLossMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsDomainDTO;
import com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord;
import com.paic.ncbs.claim.model.dto.report.EstimateLossDTO;
import com.paic.ncbs.claim.service.estimate.ClmsEstimateRecordService;
import com.paic.ncbs.claim.service.other.MailSendService;
import com.paic.ncbs.claim.service.report.EstimateLossService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.claim.service.user.ReceiverConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service("estimateLossService")
public class EstimateLossServiceImpl implements EstimateLossService {

    @Autowired
    private EstimateLossMapper estimateLossMapper;
    @Autowired
    private MailSendService mailSendService;

    @Autowired
    private ClmsEstimateRecordService clmsEstimateRecordService;
    @Autowired
    private ReceiverConfigService receiverConfigService;
    @Autowired
    private DepartmentDefineService departmentDefineService;

    private static Map<String,String> SUM_DUTY_INSURED_APPLY_TYPES = new HashMap<>();
    static {
        SUM_DUTY_INSURED_APPLY_TYPES.put(InsuredApplyTypeEnum.ACCIDENTAL_DISEASE.getType(), SettleConst.DETAIL_TYPE_DEATH);
        SUM_DUTY_INSURED_APPLY_TYPES.put(InsuredApplyTypeEnum.DEATH_OF_DISEASE.getType(),SettleConst.DETAIL_TYPE_DEATH);
        SUM_DUTY_INSURED_APPLY_TYPES.put(InsuredApplyTypeEnum.MAJOR_DISEASES.getType(),SettleConst.DETAIL_TYPE_MAJOR_DISEASE);
    }

    @Override
    public List<EstimateLossDTO> getAllEstimatLossConfig() {
        List<EstimateLossDTO> estimateLossList = Optional.ofNullable(estimateLossMapper.getAllEstimatLossConfig()).orElse(new ArrayList<>());
        estimateLossList.forEach( dto -> dto.setInsuredApplyTypeName(InsuredApplyTypeEnum.getName(dto.getInsuredApplyType())));
        return estimateLossList;
    }

    @Override
    public void setAllEstimatLossConfig(List<EstimateLossDTO> estimateLossList) {
        if(ListUtils.isEmptyList(estimateLossList)){
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        String userId = WebServletContext.getUserId();
        estimateLossList.forEach(dto -> dto.setUpdatedBy(userId));
        estimateLossMapper.setAllEstimatLossConfig(estimateLossList);
    }

    @Override
    public EstimateLossDTO getEstimatLossConfig(String insuredApplyType) {
        return Optional.ofNullable(estimateLossMapper.getEstimatLossConfig(insuredApplyType)).orElse(new EstimateLossDTO());
    }


    @Override
    @Async("asyncPool")
    public void runReportEstimateRule(AhcsDomainDTO ahcsDomainDTO, String insuredApplyType, String userId) {
        String reportNo = ahcsDomainDTO.getReportNo();

        BigDecimal amount = null;
        String dutyDetailType = SUM_DUTY_INSURED_APPLY_TYPES.getOrDefault(insuredApplyType,"");
        if(StringUtils.isNotEmpty(dutyDetailType)){
            //汇总相关责任明细保额
            amount =  estimateLossMapper.sumDutyAmount(reportNo,dutyDetailType);
        }else{
            //取配置金额即可
            amount = getEstimatLossConfig(insuredApplyType).getConfigAmount();
        }

        //保存报案未决记录
        Date now = new Date();
        ClmsEstimateRecord dto = new ClmsEstimateRecord();
        dto.setCreatedBy(userId);
        dto.setCreatedDate(now);
        dto.setUpdatedBy(userId);
        dto.setUpdatedDate(now);
        dto.setIdClmsEstimateRecord(UuidUtil.getUUID());
        dto.setReportNo(reportNo);
        Integer caseTimes = 1;
        dto.setCaseTimes(caseTimes);
        dto.setEstimateType(EstimateTypeEnum.REPORT_PENDING.getType());
        dto.setEstimateAmount(amount);
        dto.setEffectiveTime(now);
        dto.setRecordUserId(userId);
        clmsEstimateRecordService.addEstimateRecord(dto);

        //发送大案邮件(报案)
//        try {
//            String scope = MailScopeEnum.getRegistType(amount);
//            if(StringUtils.isNotEmpty(scope)){
//                String deptCode = ahcsDomainDTO.getAhcsPolicyDomainDTOs().get(0).getAhcsPolicyInfo().getDepartmentCode();
//                //取二级机构
//                deptCode = departmentDefineService.getLevel2DeptCode(deptCode);
//                Map<String,String> receiverMap = receiverConfigService.getMajorCaseReceiverList(deptCode, MailNodeEnum.REGIST.getType(),scope);
//                String receiver = receiverMap.get("receivers");
//                if(StringUtils.isNotEmpty(receiver)){
//                    //收件人不为空发邮件
//                    mailSendService.sendMajorCaseMail(reportNo,receiver,receiverMap.get("copyers"));
//                }
//            }
//        }catch (Exception e){
//
//        }

    }
}
