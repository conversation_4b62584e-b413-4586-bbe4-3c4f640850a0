package com.paic.ncbs.claim.service.endcase.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity;
import com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity;
import com.paic.ncbs.claim.dao.mapper.casezero.CaseZeroCancelMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.restartcase.RestartCaseRecordMapper;
import com.paic.ncbs.claim.dao.mapper.supplements.SupplementsMaterialMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.report.AcceptRecordDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.service.estimate.EstimateDutyRecordService;
import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.EndCaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.report.AcceptRecordService;
import com.paic.ncbs.claim.service.report.AutoEstimateAmountService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class WholeCaseBaseServiceImpl extends BaseServiceImpl<WholeCaseBaseEntity> implements WholeCaseBaseService {

    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;

    @Autowired
    private AutoEstimateUtil autoEstimateUtil;

    @Autowired
    private AutoEstimateAmountService autoEstimateAmountService;

    @Autowired
    private EstimateDutyRecordService estimateDutyRecordService;

    @Autowired
    private EndCaseService endCaseService;

    @Autowired
    private AcceptRecordService acceptRecordService;

    @Autowired
    private CaseProcessService caseProcessService;

    @Autowired
    private ReportInfoMapper reportInfoMapper ;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private CaseClassMapper caseClassDao;
    @Autowired
    private SupplementsMaterialMapper supplementsMaterialMapper;

    @Autowired
    private CaseZeroCancelMapper caseZeroCancelMapper;

    @Autowired
    private RestartCaseRecordMapper restartCaseRecordMapper;


    @Override
    public List<WholeCaseBaseEntity> getWholeCaseBase(String report) {
        return wholeCaseBaseMapper.getWholeCaseBaseByReport(report);
    }

    @Override
    public WholeCaseBaseDTO getWholeCaseBase(String reportNo, int caseTimes) {
        WholeCaseBaseDTO wholeCaseBase =  wholeCaseBaseMapper.getWholeCaseBase(reportNo, caseTimes);
        if(Objects.isNull(wholeCaseBase)){
            return new WholeCaseBaseDTO();
        }
        wholeCaseBase.setCaseClass("1");
        //赔付方式(1-赔付  4-拒赔 )
        String conclusion = wholeCaseBase.getIndemnityConclusion();
        //赔偿模式(5-协议赔付,6-通融赔付)
        String indemnityModel = wholeCaseBase.getIndemnityModel();
        String verifyCode = "";
        if ("1".equals(conclusion)) {
            verifyCode = conclusion + (indemnityModel != null ? indemnityModel : "");
        } else if ("4".equals(conclusion)) {
            verifyCode = conclusion;
        }
        //初核结论
        wholeCaseBase.setVerifyConclusion(ChecklossConst.VERIFY_CONCLUSION_RELATIONS.get(verifyCode));
        ReportInfoEntity reportInfo = reportInfoMapper.getReportInfo(reportNo);
        if  (null != reportInfo){
            wholeCaseBase.setReportDateStr(DateUtils.parseToFormatStr(reportInfo.getReportDate(), DateUtils.FULL_DATE_STR));
        }
        //setDocumentFullDateStr(wholeCaseBase,verifyCode,reportNo,caseTimes);
        if (null == wholeCaseBase.getDocumentFullDate()){
            TaskInfoDTO taskInfoDTO = taskInfoMapper.historyDocumentFullDate(reportNo, caseTimes, BpmConstants.OC_REPORT_TRACK);
            if(null != taskInfoDTO){
                try {
                    wholeCaseBase.setDocumentFullDateStr(DateUtils.parseToFormatStr(taskInfoDTO.getUpdatedDate(), DateUtils.FULL_DATE_STR));
                } catch (Exception e) {
                    LogUtil.info("整案信息单证日期转换异常!!{}",e.getMessage());
                }
            }
        }else{
            wholeCaseBase.setDocumentFullDateStr( DateUtils.parseToFormatStr(wholeCaseBase.getDocumentFullDate(), DateUtils.FULL_DATE_STR));
        }

        List<String> caseSubClassList = caseClassDao.getCaseClassList(reportNo, caseTimes, null);
        log.info("caseSubClassList={}", JsonUtils.toJsonString(caseSubClassList));
        if (ListUtils.isNotEmpty(caseSubClassList) && StringUtils.isNotEmpty(caseSubClassList.get(0))) {
            StringBuffer caseSubClassName   =new StringBuffer("") ;
            StringBuffer finalCaseSubClassName = caseSubClassName;
            caseSubClassList.forEach(e->{
                finalCaseSubClassName.append(InsuredApplyTypeEnum.getName(e)).append(",") ;
            });
            if (caseSubClassName.length() > 0){
                caseSubClassName = caseSubClassName.deleteCharAt(caseSubClassName.length()-1);
            }
            wholeCaseBase.setCaseClass(caseSubClassName.toString());
        }
        wholeCaseBase.setCaseStatusName(CaseProcessStatus.getName(wholeCaseBase.getProcessStatus()));
        log.info("wholeCaseBase={}", JsonUtils.toJsonString(wholeCaseBase));
        return wholeCaseBase;

    }

    @Override
    public BaseDao<WholeCaseBaseEntity> getDao() {
        return wholeCaseBaseMapper;
    }


    @Override
    public WholeCaseBaseDTO getWholeCaseBase2(String reportNo, int caseTimes) {
        return wholeCaseBaseMapper.getWholeCaseBase2(reportNo, caseTimes);
    }

    @Override
    public void modifyHugeAccident(WholeCaseBaseDTO wholeCaseBaseDTO) {
        wholeCaseBaseMapper.modifyHugeAccident(wholeCaseBaseDTO);
    }

    @Override
    public void modifyWholeCaseBase(WholeCaseBaseDTO wholeCaseBaseDTO) {
        wholeCaseBaseMapper.modifyWholeCaseBase(wholeCaseBaseDTO);
    }

    @Override
    public Date getReportDate(String reportNo) {
        return wholeCaseBaseMapper.getReportDate(reportNo);
    }

    @Override
    public String getWholeCaseStatus(String reportNo, Integer caseTimes) throws GlobalBusinessException {

        return wholeCaseBaseMapper.getWholeCaseStatus(reportNo, caseTimes);
    }

    @Override
    public WholeCaseBaseDTO getHugeInfo(String reportNo, Integer caseTimes) {
        return wholeCaseBaseMapper.getHugeInfo(reportNo, caseTimes);
    }


    @Override
    public void autoEstimateAmount(String reportNo, Integer caseTimes) {
        LogUtil.audit("#自动预估开始, reportNo={}, caseTimes={}#", reportNo, caseTimes);
        try {
            autoEstimateAmountService.autoEstimateAmount(reportNo, caseTimes);
            LogUtil.audit("after.autoEstimateAmountService.autoEstimateAmount");
        } catch (Exception e) {
            LogUtil.info("自动预估异常, reportNo=" + reportNo + ", caseTimes=" + caseTimes, e);

            LogUtil.audit("#捕获异常删除未决暂存表数据#");
            List<String> caseNoList = new ArrayList<>();
            List<EstimatePolicyDTO> estimatePolicyList = autoEstimateUtil.getPolicyCopyData(reportNo, caseTimes);
            for (EstimatePolicyDTO policyDTO : estimatePolicyList) {
                caseNoList.add(policyDTO.getCaseNo());
            }

            estimateDutyRecordService.updateEffectiveByCaseNos(caseNoList, caseTimes, ConstValues.SYSTEM);

            return;
        }
        LogUtil.audit("#自动预估结束, reportNo={}, caseTimes={}#", reportNo, caseTimes);
    }

    @Override
    public void saveCaseRegisterInfo(String reportNo, Integer caseTimes, String updatedBy) throws GlobalBusinessException {
        WholeCaseBaseDTO wholeCaseBaseDTO = new WholeCaseBaseDTO();
        wholeCaseBaseDTO.setRegisterUm(updatedBy);
        wholeCaseBaseDTO.setIsRegister(EstimateUtil.YES_REGISTR);
        wholeCaseBaseDTO.setRegisterDate(new Date());
        wholeCaseBaseDTO.setUpdatedBy(updatedBy);
        wholeCaseBaseDTO.setReportNo(reportNo);
        wholeCaseBaseDTO.setCaseTimes(caseTimes);
        modifyWholeCaseBase(wholeCaseBaseDTO);
        endCaseService.batchModifyCaseBaseDTO(wholeCaseBaseDTO);
        AcceptRecordDTO acceptRecordDTO = acceptRecordService.getAcceptRecord(reportNo, caseTimes);
        String processStatus = null;
        if (acceptRecordDTO != null) {
            processStatus = ConfigConstValues.PROCESS_STATUS_PENDING_AUDIT;
        } else {
            processStatus = ConfigConstValues.PROCESS_STATUS_PENDING_ACCEPT;
        }
        CaseProcessDTO caseProcessDTO = new CaseProcessDTO();
        caseProcessDTO.setReportNo(reportNo);
        caseProcessDTO.setCaseTimes(caseTimes);
        caseProcessDTO.setProcessStatus(processStatus);
        caseProcessDTO.setUpdatedBy(updatedBy);
        caseProcessDTO.setRegisterDeptCode(WebServletContext.getDepartmentCode());
        caseProcessService.updateCaseRegisterDept(caseProcessDTO);

    }

    @Override
    public WholeCaseBaseDTO getWholeCaseIndemnityStatus(String reportNo, Integer caseTimes) throws GlobalBusinessException {

        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseMapper.getWholeCaseIndemnityStatus(reportNo, caseTimes);
        if (null == wholeCaseBaseDTO || StringUtils.isEmptyStr(wholeCaseBaseDTO.getProcessStatus())) {
            wholeCaseBaseDTO = wholeCaseBaseMapper.getWholeCaseIndemnityStatusZt(reportNo, caseTimes);
            if (null != wholeCaseBaseDTO) {
                if (ConstValues.CASE_STATUS_END.equals(wholeCaseBaseDTO.getWholeCaseStatus())) {
                    if (ConstValues.ZERO_PAY.equals(wholeCaseBaseDTO.getIndemnityConclusion())) {
                        wholeCaseBaseDTO.setProcessStatus(ConfigConstValues.PROCESS_STATUS_ZORE_CANCEL);
                    } else if (ConstValues.CASE_CANCELL.equals(wholeCaseBaseDTO.getIndemnityConclusion())) {
                        wholeCaseBaseDTO.setProcessStatus(ConfigConstValues.PROCESS_STATUS_CANCELLATION);
                    } else {
                        wholeCaseBaseDTO.setProcessStatus(ConfigConstValues.PROCESS_STATUS_CASE_CLOSED);
                    }
                } else {
                    wholeCaseBaseDTO.setProcessStatus(ConfigConstValues.PROCESS_STATUS_AUDITTING);
                }
            }
        }

        return wholeCaseBaseDTO;
    }

    /**
     * 设置资料齐全时间：
     * 资料齐全时间的取数逻辑需调整
     * 1.正常赔付案件：以收单完成时间、补材完成时间，两个时间中较晚的时间为准，
     * 2.零结案案件：
     * 新增限制 报案跟踪阶段不允许零结案(但可以注销) :
     * 收单环节提交零结案时间，以零结案提交时间为准:
     * 理算环节提交零结案时间，以收单提交时间或补材回销时间两者中较晚者为准
     * 3：重开赔案
     * 以本次重开案件得重开审批通过时间为准
     *
     * 2024-5-31号 上午11点16分与思佳沟通确认  3 重开案件这个放在第一位  优先判断如果是重开案件 不管赔付结论 以本次重开案件重开审批
     * 然后在看正常赔付结论
     *
     * @param wholeCaseBase
     * @param reportNo
     * @param caseTimes
     */
    private void setDocumentFullDateStr(WholeCaseBaseDTO wholeCaseBase, String verifyCode, String reportNo, int caseTimes) {
        if(caseTimes>1){
          //查询重开审批时间
            RestartCaseRecordEntity entity = restartCaseRecordMapper.getRestartAuditTime(reportNo,caseTimes);
            LogUtil.info("报案号={},赔付次数={}, 查询重开审批时间结果={}",reportNo,caseTimes,JsonUtils.toJsonString(entity));
            if(Objects.nonNull(entity) && Objects.nonNull(entity.getUpdatedDate())){
                wholeCaseBase.setDocumentFullDateStr(DateUtils.parseToFormatStr(entity.getUpdatedDate(),DateUtils.FULL_DATE_STR));
                wholeCaseBase.setDocumentFullDate(entity.getUpdatedDate());
                return;
            }
        }
        if(Objects.equals(ChecklossConst.CONCLUSION_VERIFY_PAY,verifyCode)){
            Date completeDate = getCompleteDate(reportNo,caseTimes);
            if(!Objects.isNull(completeDate)){
                wholeCaseBase.setDocumentFullDateStr(DateUtils.parseToFormatStr(completeDate,DateUtils.FULL_DATE_STR));
                wholeCaseBase.setDocumentFullDate(completeDate);
                return;
            }
        }
        //查询是否是零结案件
        Date applyDate =getZeroCancel(reportNo,caseTimes);
        if(!Objects.isNull(applyDate)){
            wholeCaseBase.setDocumentFullDateStr(DateUtils.parseToFormatStr(applyDate,DateUtils.FULL_DATE_STR));
            wholeCaseBase.setDocumentFullDate(applyDate);
        }

    }

    /***
     * 查询零结
     * @param reportNo
     * @param caseTimes
     */
    private Date getZeroCancel(String reportNo, int caseTimes) {
        CaseZeroCancelDTO dto =  caseZeroCancelMapper.getZeroCancelApplyTime(reportNo,caseTimes);
        LogUtil.info("报案号={},查询零结={}",reportNo,JsonUtils.toJsonString(dto));
        if(Objects.isNull(dto)){
            return null;
        }
        //收单环节提交零结案时间，以零结案提交时间为准:
        if(Objects.equals(BpmConstants.OC_CHECK_DUTY,dto.getTaskDefinitionKey())){
            return dto.getApplyDate();
        }
       // 理算环节提交零结案时间，以收单提交时间或补材回销时间两者中较晚者为准
        if(Objects.equals(BpmConstants.OC_MANUAL_SETTLE,dto.getTaskDefinitionKey())){
            //查询收单提交时间
            LogUtil.info("报案号={}理算环节提交零结案时间，以收单提交时间或补材回销时间两者中较晚者为准",reportNo);
            return  getCompleteDate(reportNo,caseTimes);
        }
        return null;

    }

    /**
     * 得到收单完成时间和补材完成时间
     * @param reportNo
     * @param caseTimes
     * @return
     */
    private Date getCompleteDate(String reportNo, int caseTimes){
        //正常赔付  //查询收单完成时间
        TaskInfoDTO  taskInfoDTO = taskInfoMapper.getCompleteDate(reportNo,caseTimes);
        //查询材料补材完成时间
        SupplementsMaterialEntity entity = supplementsMaterialMapper.getcompleteDate(reportNo,caseTimes);
        LogUtil.info("报案号={},赔付次数={}, 查询收单完成时间={},查询材料补材完成时间={}",reportNo,caseTimes,JsonUtils.toJsonString(taskInfoDTO),JsonUtils.toJsonString(entity));
        if(Objects.isNull(taskInfoDTO) && Objects.isNull(entity)){
            return null;
        }
        if(Objects.isNull(taskInfoDTO) && !Objects.isNull(entity)){
            return entity.getUpdatedDate();
        }
        if(!Objects.isNull(taskInfoDTO) && Objects.isNull(entity)){
            return taskInfoDTO.getCompleteTime();
        }
        if(taskInfoDTO.getCompleteTime().compareTo(entity.getUpdatedDate())>0){
            return taskInfoDTO.getCompleteTime();
        }else{
            return entity.getUpdatedDate();
        }

    }
    @Override
    public void updateIsPersonTrace(String reportNo, Integer caseTimes, String isPersonTrace){
        wholeCaseBaseMapper.updateIsPersonTrace(reportNo,caseTimes,isPersonTrace);
    }
}
