package com.paic.ncbs.claim.model.dto.investigate;




import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@Setter
@Getter
@ApiModel(description = "审核记录信息")
public class InvestigateTaskAuditDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ahcs_investigate_task_audit表主键")
    private String idAhcsInvestigateTaskAudit;

    @ApiModelProperty(value = "AHCS_INVESTIGATE_TASK表主键")
    private String idAhcsInvestigateTask;

    @ApiModelProperty(value = "审核人")
    private String reviewUserUm;

    @ApiModelProperty(value = "发起人")
    private String initiatorUm;

    @ApiModelProperty(value = "审核意见（1、通过，2、退回）")
    private String reviewOpinion;

    @ApiModelProperty(value = "退回原因")
    private String rejectReason;

    @ApiModelProperty(value = "审核说明/退回补充")
    private String description;

    @ApiModelProperty(value = "公估费审核是否通过(Y/N)")
    private String feeAuditOption;

    @ApiModelProperty(value = "")
    private String hasOffsiteTask;

    @ApiModelProperty(value = "")
    private String needScore;

    @ApiModelProperty(value = "调查类型")
    private String surveyType;

    @ApiModelProperty(value = "外调公司统一社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty(value = "外调公司名称")
    private String companyName;

    @ApiModelProperty(value = "主管审核公估费")
    private BigDecimal commonEstimateFee;

    @ApiModelProperty(value = "是否包含公估费")
    private String isHasAdjustingFee;

    private List<InvestigateScoreDTO> investigateScoreDTOList;

    private List<InvestigateTaskVO> investigateTaskVOS;

    @ApiModelProperty(value = "移交目标用户")
    private String transferToUser;

    @ApiModelProperty(value = "移交原因")
    private String transferReason;

    @ApiModelProperty(value = "移交人")
    private String transferBy;

    @ApiModelProperty(value = "移交时间")
    private Date transferDate;

    @ApiModelProperty(value = "公估师姓名")
    private String adjusterName;

    @ApiModelProperty(value = "公估师电话")
    private String adjusterPhone;

    @ApiModelProperty(value = "减损金额")
    private BigDecimal lossReductionAmount;


}