package com.paic.ncbs.claim.replevy.dao;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyDetail;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 *
 * 表clms_replevy_detail对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface ClmsReplevyDetailMapper extends BaseDao<ClmsReplevyDetail> {

    /**
     * 保存明细接口
     * @param clmsReplevyDetail
     * @return
     */
    int saveClmsReplevyDetail(ClmsReplevyDetail clmsReplevyDetail);

    /**
     * 查询追偿详情信息
     * @return
     */
    List<ClmsReplevyDetailVo> selectClmsReplevyDetail(@Param("reportNo") String reportNo,@Param("replevyNo") String replevyNo);

    /**
     * 更新数据
     * @param clmsReplevyDetail
     * @return
     */
    int updateSelectiveByPrimaryKey(ClmsReplevyDetail clmsReplevyDetail);

    ClmsReplevyDetail selectById(@Param("id") String id);

    int updateClmsReplevyDetailById(ClmsReplevyDetail clmsReplevyDetail);
}