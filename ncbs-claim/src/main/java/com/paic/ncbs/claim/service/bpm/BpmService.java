package com.paic.ncbs.claim.service.bpm;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;


public interface BpmService {

    void startProcess_oc(String reportNo, Integer caseTimes, String defKey) throws GlobalBusinessException;

    /**
     *
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @param defKey 任务环节
     * @param taskId 任务id 沟通 调查等会指定 其它默认uuid
     * @param userCode 沟通会指定用户
     * @param departmentCode 沟通指定处理机构
     * @throws GlobalBusinessException
     */
    void startProcessOc(String reportNo, Integer caseTimes, String defKey, String taskId, String userCode, String departmentCode) throws GlobalBusinessException;

    void completeTask_oc(String reportNo, Integer caseTimes, String defKey) throws GlobalBusinessException;

    void completeTask_oc_css(String reportNo, Integer caseTimes, String defKey) throws GlobalBusinessException;

    void suspendOrActiveTask_oc(String reportNo, Integer caseTimes,String defKey,boolean flag);

    void suspendOrActiveTask_oc_css(String reportNo, Integer caseTimes,String defKey,boolean flag);

    void completeTask_oc(String reportNo, Integer caseTimes, String defKey, String taskId) throws GlobalBusinessException;

    void suspendOrActiveTask_oc(String reportNo, Integer caseTimes, String defKey, boolean flag, String taskId);

    /**
      *
      * @Description 调查专用 指定部门
      * <AUTHOR>
      * @Date 2023/8/11 18:19
      **/
    void startProcess_oc(String reportNo, Integer caseTimes, String defKey, String taskId, String depentmentCode, String  initMode) throws GlobalBusinessException;

    /**
     *
     * @Description 委托专用 指定部门
     **/
    void startProcessEntrustment(EntrustMainDTO entrustMainDTO, String defKey, String entrustAuditId, UserInfoDTO u) throws GlobalBusinessException;

    boolean isExistSuspendTask(String reportNo, Integer caseTimes,String defKey);


    void startProcessOcBatch(String reportNo, String userId  );

    void startProcessTelReport(String reportNo, Integer caseTimes, String defKey, String departmentCode) throws GlobalBusinessException;

    /**
     * 专门提供给案件重开流程使用
     * @param reportNo
     * @param caseTimes
     * @param isRestartReport
     * @param bpmKey
     */
    void startProcess_rc(String reportNo, Integer caseTimes, boolean isRestartReport, String bpmKey,String taskId);

    /**
     * 核赔重启理算
     * @param reportNo
     * @param caseTimes
     * @param defKey
     * @param flag
     */
    void activeManualSettle(String reportNo, Integer caseTimes,String defKey,boolean flag);

    void processCheck(String reportNo, String bpmKey, String operateType);

    void closeAll_oc(String reportNo, Integer caseTimes);

    void newSuspendOrActiveTask_oc(String reportNo, Integer caseTimes, String taskDefinitionKey,boolean flag);
}