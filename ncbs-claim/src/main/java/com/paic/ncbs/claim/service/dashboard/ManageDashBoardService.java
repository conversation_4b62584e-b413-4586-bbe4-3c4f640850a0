package com.paic.ncbs.claim.service.dashboard;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.paic.ncbs.claim.dao.entity.clms.ClmsMngDashboardHis;
import com.paic.ncbs.claim.dao.mapper.taskdeal.ClmsMngDashboardHisMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.ClmsMngDashboardIntfMapper;
import com.paic.ncbs.claim.model.vo.dashboard.ClmsMngDashboardHisVo;
import com.paic.ncbs.claim.model.vo.dashboard.ManageDashBoardReqVo;
import com.paic.ncbs.claim.model.vo.dashboard.ManageDashBoardResVo;
import com.paic.ncbs.claim.model.vo.dashboard.ManageDashBoardVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collector;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ManageDashBoardService {

    /** 本月 */
    private static final String current_month = "current-month";
    /** 上个月 */
    private static final String last_month = "last-month";
    /** 本年 */
    private static final String current_year = "current-year";

    @Autowired
    private ClmsMngDashboardHisMapper clmsMngDashboardHisMapper;

    @Autowired
    private ClmsMngDashboardIntfMapper clmsMngDashboardIntfMapper;

    public ManageDashBoardResVo query(String userCode, String userName, ManageDashBoardReqVo manageDashBoardReqVo) {
        //人员权限校验
//        checkUserPower(userCode,userName);
        // 计算时间参数
        getDateTime(manageDashBoardReqVo);
        //查询数据库数据
        ManageDashBoardResVo manageDashBoardResVo=dataByBI(userCode, userName,manageDashBoardReqVo);
        return manageDashBoardResVo;
    }

    private ManageDashBoardResVo dataByBI(String userCode, String userName, ManageDashBoardReqVo manageDashBoardReqVo) {
        ManageDashBoardResVo manageDashBoardResVo = new ManageDashBoardResVo();
        Map<String,String> productMap = null;
        ManageDashBoardVo manageDashBoardVo = new ManageDashBoardVo();
        //查询数据
        //查询数据接口表 处理当前数据的展示
        List<ClmsMngDashboardHis> clmsMngDashboardHisList = clmsMngDashboardIntfMapper.selectByData(manageDashBoardReqVo);
        //数据处理
        if(clmsMngDashboardHisList!=null && clmsMngDashboardHisList.size()>0){
            //数据汇总
            ClmsMngDashboardHisVo clmsMngDashboardHisVo = countMngDashBoardHisData(clmsMngDashboardHisList);
            //前端数据转换 通用数据计算逻辑
            manageDashBoardVo = transForManageData(clmsMngDashboardHisVo);
//            //查询结果整个给到前端，做图表产品的分项展示
//            manageDashBoardVo.setClmsMngDashboardHisList(clmsMngDashboardHisList);
            //处理数据，获取当前选择维度下的产品分项数据
            productMap = transForManageDataToProductMap(clmsMngDashboardHisList);
        }
        manageDashBoardResVo.setManageDashBoardVo(manageDashBoardVo);
        manageDashBoardResVo.setProductMap(productMap);
        //查询数仓中间表，查询获取各月份数据，返回前端
        List<ClmsMngDashboardHisVo> clmsMngDashboardHisVoList = clmsMngDashboardIntfMapper.selectByTimeToMounth(manageDashBoardReqVo);
        //数据处理
        if(clmsMngDashboardHisVoList!=null && clmsMngDashboardHisVoList.size()>0){
            List<ManageDashBoardVo> manageDashBoardVos = new ArrayList<>();
            for (int i = 0; i < clmsMngDashboardHisVoList.size(); i++) {
                //前端数据转换 通用数据计算逻辑
                manageDashBoardVo = transForManageData(clmsMngDashboardHisVoList.get(i));
                //将每月数据放入前端返回类中
                manageDashBoardVos.add(manageDashBoardVo);
            }
            //月度数据格式处理，方便前端使用
            tranFormngBoardListToMap(manageDashBoardVos,manageDashBoardResVo);
            manageDashBoardResVo.setManageDashBoardVos(manageDashBoardVos);
        }
        return manageDashBoardResVo;
    }

    private void tranFormngBoardListToMap(List<ManageDashBoardVo> manageDashBoardVos, ManageDashBoardResVo manageDashBoardResVo) {
        //月份分组
        Map<String,List<ManageDashBoardVo>> listMap = manageDashBoardVos.stream().collect(Collectors.groupingBy(ManageDashBoardVo::getDateTime));
        //报案数
        Map<String,String> reportCountMap = new HashMap<>();
        //结案率
        Map<String,String> endCaseRateMap = new HashMap<>();
        //代数估损偏差金额
        Map<String,String> estimateDeviationAmountMap = new HashMap<>();
        //代数估损偏差率
        Map<String,String> estimateDeviationRateMap = new HashMap<>();
        //新增损失
        Map<String,String> newEstimateMap = new HashMap<>();

        for (int i = 1; i <= 12; i++) {
            reportCountMap.put(String.valueOf(i),"0");
            endCaseRateMap.put(String.valueOf(i),"0");
            estimateDeviationAmountMap.put(String.valueOf(i),"0");
            estimateDeviationRateMap.put(String.valueOf(i),"0");
            newEstimateMap.put(String.valueOf(i),"0");
        }

        if(ObjectUtil.isNotEmpty(listMap)){
            listMap.forEach((key,value) -> {
                BigDecimal reportCount = BigDecimal.ZERO;
                BigDecimal endCaseRate = BigDecimal.ZERO;
                BigDecimal estimateDeviationAmount = BigDecimal.ZERO;
                BigDecimal estimateDeviationRate = BigDecimal.ZERO;
                BigDecimal newEstimate = BigDecimal.ZERO;
                if(value!=null && value.size()>0){
                    for (int i = 0; i < value.size(); i++) {
                        reportCount = reportCount.add(new BigDecimal(value.get(i).getEndCaseDenominatorCount()));
                        endCaseRate = endCaseRate.add(new BigDecimal(value.get(i).getEndCaseRate()));
                        estimateDeviationAmount = estimateDeviationAmount.add(new BigDecimal(value.get(i).getEstimateDeviationAmount()));
                        estimateDeviationRate = estimateDeviationRate.add(new BigDecimal(value.get(i).getEstimateDeviationRate()));
                        newEstimate = newEstimate.add(new BigDecimal(value.get(i).getNewEstimate()));
                    }
                }
                //报案数
                reportCountMap.put(key, String.valueOf(reportCount));
                //结案率
                endCaseRateMap.put(key,String.valueOf(endCaseRate));
                //代数估损偏差金额 （单位元-》万元）
                estimateDeviationAmountMap.put(key,String.valueOf(estimateDeviationAmount));
                //代数估损偏差率
                estimateDeviationRateMap.put(key,String.valueOf(estimateDeviationRate));
                //新增损失
                newEstimateMap.put(key,String.valueOf(newEstimate));
            });
        }
        manageDashBoardResVo.setReportCountMap(reportCountMap);
        manageDashBoardResVo.setEndCaseRateMap(endCaseRateMap);
        manageDashBoardResVo.setEstimateDeviationAmountMap(estimateDeviationAmountMap);
        manageDashBoardResVo.setEstimateDeviationRateMap(estimateDeviationRateMap);
        manageDashBoardResVo.setNewEstimateMap(newEstimateMap);
    }

    //按产品名称分组获取对应的新增损失
    private Map<String, String> transForManageDataToProductMap(List<ClmsMngDashboardHis> clmsMngDashboardHisList) {
        Map<String,String> productMap = new HashMap<>();
        Map<String,List<ClmsMngDashboardHis>> listMap = clmsMngDashboardHisList.stream()
                .collect(Collectors.groupingBy(ClmsMngDashboardHis::getClassName));
        if(ObjectUtil.isNotEmpty(listMap)){
            listMap.forEach((key,value) -> {
                BigDecimal sum = BigDecimal.ZERO;
                if(value!=null && value.size()>0){
                    for (int i = 0; i < value.size(); i++) {
                        sum = sum.add(value.get(i).getNewEstimate());
                    }
                }
                if(sum.compareTo(BigDecimal.ZERO)!=0){
                    //返回前端数据处理
                    productMap.put(key,String.valueOf(sum.setScale(2,RoundingMode.HALF_UP)));
                }
            });
        }
        return productMap;
    }

    private ClmsMngDashboardHisVo countMngDashBoardHisData(List<ClmsMngDashboardHis> clmsMngDashboardHisList) {
        ClmsMngDashboardHisVo clmsMngDashboardHisVo = new ClmsMngDashboardHisVo();
        //出险报案周期-分子(单位：天)
        BigDecimal reportPeroidNumerator = BigDecimal.ZERO;
        //出险报案周期-分母(单位：件)
        Integer reportPeroidDenominator = 0;
        //报案估损周期-分子(单位：天)
        BigDecimal estimatePeroidNumerator = BigDecimal.ZERO;
        //报案估损周期-分母(单位：件)
        Integer estimatePeroidDenominator = 0;
        //报案结案周期-分子(单位：天)
        BigDecimal endcasePeroidNumerator = BigDecimal.ZERO;
        //报案结案周期-分母(单位：件)
        Integer endcasePeroidDenominator = 0;
        //重开次数(单位：次)
        Integer reopenTimes = 0;
        //报案结案率-分子值(单位：件)
        Integer endCaseNumerator = 0;
        //报案结案率-分母值(单位：件)
        Integer endCaseDenominator = 0;
        //代数估损偏差金额(单位：元)
        BigDecimal estimateDeviationAmount = BigDecimal.ZERO;
        //代数估损偏差率-分子值(单位：元)
        BigDecimal estimateDeviationNumerator = BigDecimal.ZERO;
        //代数估损偏差率-分母值(单位：元)
        BigDecimal estimateDeviationDenominator = BigDecimal.ZERO;
        //新增损失(单位：元)
        BigDecimal newEstimate = BigDecimal.ZERO;
        //报案件数
        Integer endCaseDenominatorCount = 0;
        String dateTime = clmsMngDashboardHisList.get(0).getMetricsDay();

        BigDecimal length = BigDecimal.valueOf(clmsMngDashboardHisList.size());
        Integer size = clmsMngDashboardHisList.size();
        //数据汇总
        for(ClmsMngDashboardHis mngDashboardHis:clmsMngDashboardHisList){
            reportPeroidNumerator = reportPeroidNumerator.add(mngDashboardHis.getReportPeroidNumerator()!=null ? mngDashboardHis.getReportPeroidNumerator() : BigDecimal.ZERO);
            reportPeroidDenominator = reportPeroidDenominator+ (ObjectUtil.isNotNull(mngDashboardHis.getReportPeroidDenominator()) ? mngDashboardHis.getReportPeroidDenominator() : 0) ;
            estimatePeroidNumerator = estimatePeroidNumerator.add(mngDashboardHis.getEstimatePeroidNumerator()!=null ? mngDashboardHis.getEstimatePeroidNumerator() : BigDecimal.ZERO);
            estimatePeroidDenominator = estimatePeroidDenominator+(ObjectUtil.isNotNull(mngDashboardHis.getEstimatePeroidDenominator()) ? mngDashboardHis.getEstimatePeroidDenominator() : 0);
            endcasePeroidNumerator = endcasePeroidNumerator.add(mngDashboardHis.getEndcasePeroidNumerator()!=null ? mngDashboardHis.getEndcasePeroidNumerator() : BigDecimal.ZERO);
            endcasePeroidDenominator = endcasePeroidDenominator+(ObjectUtil.isNotNull(mngDashboardHis.getEndcasePeroidDenominator()) ? mngDashboardHis.getEndcasePeroidDenominator() : 0);
            reopenTimes = reopenTimes+(ObjectUtil.isNotNull(mngDashboardHis.getReopenTimes()) ? mngDashboardHis.getReopenTimes() : 0);
            endCaseNumerator = endCaseNumerator+(ObjectUtil.isNotNull(mngDashboardHis.getEndCaseNumerator()) ? mngDashboardHis.getEndCaseNumerator() : 0);
            endCaseDenominator = endCaseDenominator+(ObjectUtil.isNotNull(mngDashboardHis.getEndCaseDenominator()) ? mngDashboardHis.getEndCaseDenominator() : 0);
            estimateDeviationAmount = estimateDeviationAmount.add(mngDashboardHis.getEstimateDeviationAmount()!=null ? mngDashboardHis.getEstimateDeviationAmount() : BigDecimal.ZERO);
            estimateDeviationNumerator = estimateDeviationNumerator.add(mngDashboardHis.getEstimateDeviationNumerator()!=null ? mngDashboardHis.getEstimateDeviationNumerator() : BigDecimal.ZERO);
            estimateDeviationDenominator = estimateDeviationDenominator.add(mngDashboardHis.getEstimateDeviationDenominator()!=null ? mngDashboardHis.getEstimateDeviationDenominator() : BigDecimal.ZERO);
            newEstimate = newEstimate.add(mngDashboardHis.getNewEstimate()!=null ? mngDashboardHis.getNewEstimate() : BigDecimal.ZERO);
            endCaseDenominatorCount =  endCaseDenominatorCount + (ObjectUtil.isNotNull(mngDashboardHis.getEndCaseDenominator()) ? mngDashboardHis.getEndCaseDenominator() : 0);
        }
        clmsMngDashboardHisVo.setReportPeroidNumerator(reportPeroidNumerator);
        clmsMngDashboardHisVo.setReportPeroidDenominator(reportPeroidDenominator);
        clmsMngDashboardHisVo.setEstimatePeroidNumerator(estimatePeroidNumerator);
        clmsMngDashboardHisVo.setEstimatePeroidDenominator(estimatePeroidDenominator);
        clmsMngDashboardHisVo.setEndcasePeroidNumerator(endcasePeroidNumerator);
        clmsMngDashboardHisVo.setEndcasePeroidDenominator(endcasePeroidDenominator);
        clmsMngDashboardHisVo.setReopenTimes(reopenTimes);
        clmsMngDashboardHisVo.setEndCaseNumerator(endCaseNumerator);
        clmsMngDashboardHisVo.setEndCaseDenominator(endCaseDenominator);
        clmsMngDashboardHisVo.setEstimateDeviationAmount(estimateDeviationAmount);
        clmsMngDashboardHisVo.setEstimateDeviationNumerator(estimateDeviationNumerator);
        clmsMngDashboardHisVo.setEstimateDeviationDenominator(estimateDeviationDenominator);
        clmsMngDashboardHisVo.setNewEstimate(newEstimate);
        clmsMngDashboardHisVo.setEndCaseDenominatorCount(endCaseDenominatorCount);
        clmsMngDashboardHisVo.setStatisticCaliber(dateTime);

        return clmsMngDashboardHisVo;
    }

    private ManageDashBoardVo transForManageData(ClmsMngDashboardHisVo clmsMngDashboardHisvo) {
        ManageDashBoardVo manageDashBoardVo = new ManageDashBoardVo();
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        //新增损失 (单位元)
        manageDashBoardVo.setNewEstimate(String.valueOf(clmsMngDashboardHisvo.getNewEstimate()!=null ? clmsMngDashboardHisvo.getNewEstimate().setScale(2,RoundingMode.HALF_UP) : "0"));
        //代数估损偏差金额 (单位元)
        manageDashBoardVo.setEstimateDeviationAmount(String.valueOf(clmsMngDashboardHisvo.getEstimateDeviationAmount() != null ? clmsMngDashboardHisvo.getEstimateDeviationAmount().setScale(2,RoundingMode.HALF_UP) : "0"));
        //出险报案周期
        String accidentReportPeroid = stringTransForFraction(clmsMngDashboardHisvo.getReportPeroidNumerator(),BigDecimal.valueOf(clmsMngDashboardHisvo.getReportPeroidDenominator()));
        manageDashBoardVo.setAccidentReportPeroid(accidentReportPeroid);
        //报案估损周期
        String reportEstimatePeroid = stringTransForFraction(clmsMngDashboardHisvo.getEstimatePeroidNumerator(), BigDecimal.valueOf(clmsMngDashboardHisvo.getEstimatePeroidDenominator()));
        manageDashBoardVo.setReportEstimatePeroid(reportEstimatePeroid);
        //报案结案周期
        String reportEndCasePeroid = stringTransForFraction(clmsMngDashboardHisvo.getEndcasePeroidNumerator(), BigDecimal.valueOf(clmsMngDashboardHisvo.getEndcasePeroidDenominator()));
        manageDashBoardVo.setReportEndCasePeroid(reportEndCasePeroid);
        //报案结案率
        String endCaseRate = stringTransForFraction(BigDecimal.valueOf(clmsMngDashboardHisvo.getEndCaseNumerator()), BigDecimal.valueOf(clmsMngDashboardHisvo.getEndCaseDenominator()));
        manageDashBoardVo.setEndCaseRate(endCaseRate);
        //代数估损偏差率
        String estimateDeviationRate = stringTransForFraction(clmsMngDashboardHisvo.getEstimateDeviationNumerator(),clmsMngDashboardHisvo.getEstimateDeviationDenominator());
        manageDashBoardVo.setEstimateDeviationRate(estimateDeviationRate);
        //重开件数
        manageDashBoardVo.setReopenTimes(String.valueOf(clmsMngDashboardHisvo.getReopenTimes()));
        //报案件数
        manageDashBoardVo.setEndCaseDenominatorCount(String.valueOf(clmsMngDashboardHisvo.getEndCaseDenominatorCount()));
        manageDashBoardVo.setDateTime(clmsMngDashboardHisvo.getStatisticCaliber().replace("M-","").replace("0",""));
        return manageDashBoardVo;
    }

    //通用方法，将分子分母转换为两位小数的字符串
    private String stringTransForFraction(BigDecimal sor,BigDecimal end) {
        String result = "0";
        try{
            result = sor.divide(end,2,RoundingMode.HALF_UP).toString();
        }catch (Exception e){
            log.info(e.getMessage());
        }
       return result;
    }

//    private void checkUserPower(String userCode, String userName) {
//
//    }

    private void getDateTime(ManageDashBoardReqVo manageDashBoardReqVo) {
        String dashBoardPeriod = manageDashBoardReqVo.getDashBoardPeriod();
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        String yesterdayStr =  String.valueOf(yesterday).replace("-","");
        String curryMouth = yesterdayStr.substring(4,6);
        if (last_month.equals(dashBoardPeriod)){
            //当时间为“上月”时取上个月最后一天的年月日
            YearMonth lastMonth = YearMonth.now().minusMonths(1);
            LocalDate dateTime = lastMonth.atEndOfMonth();
            //将yyyy-MM-dd的LocalDate日期格式转为yyyyMMdd的字符串格式
            String dateStr = String.valueOf(dateTime).replace("-","");
            String lastMouth = dateStr.substring(4,6);
            manageDashBoardReqVo.setDateTime(yesterdayStr);
            //统计口径(m-本月 y-本年)
            manageDashBoardReqVo.setStatisticCaliber("M-"+lastMouth);
        }else if(current_year.equals(dashBoardPeriod)){
            //统计口径(m-本月 y-本年)
            manageDashBoardReqVo.setStatisticCaliber("Y");
            manageDashBoardReqVo.setDateTime(yesterdayStr);
        }else if(current_month.equals(dashBoardPeriod)){
            //统计口径(m-本月 y-本年)
            manageDashBoardReqVo.setStatisticCaliber("M-"+curryMouth);
            manageDashBoardReqVo.setDateTime(yesterdayStr);
        }
    }

    //定时任务 每天凌晨两点自动执行
//    @Scheduled(cron = "0 0 2 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void timeDataToHis(){
        try {
            //1.将数仓接口数据表同步到历史轨迹表中
            clmsMngDashboardHisMapper.insertByDay();
            //重复数据处理
            clmsMngDashboardHisMapper.deleteByRepeat();
            //2.清空数仓接口表数据
//            clmsMngDashboardIntfMapper.deleteAll();
        } catch (Exception e){
            throw new RuntimeException("定时同步数仓接口数据失败,请重试");
        }
    }
}
