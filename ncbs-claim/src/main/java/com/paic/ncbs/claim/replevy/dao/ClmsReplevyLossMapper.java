package com.paic.ncbs.claim.replevy.dao;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.replevy.dto.DutyPlanAmountDTO;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyLoss;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyLossVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 *
 * 表clms_replevy_loss对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface ClmsReplevyLossMapper extends BaseDao<ClmsReplevyLoss> {
    /**
     * 根据明细Id查询责任信息
     * @param replevyDetailId
     * @return
     */
   public List<ClmsReplevyLoss> selectByReplevyDetailId(String replevyDetailId);

    /**
     * 更新数据
     * @param clmsReplevyLoss
     * @return
     */
    int updateSelectiveByPrimaryKey(ClmsReplevyLoss clmsReplevyLoss);

    /**
     * 根据明细Id删除数据
     * @param replevyDetailId
     * @return
     */
    int deleteByReplevyDetailId(String replevyDetailId);

    /**
     * 根据追偿明细ID查询总追偿收入金额
     * @param replevyNo 追偿案件号
     * @return 总追偿收入金额
     */
    BigDecimal getTotalRepleviedMoney(@Param("replevyNo") String replevyNo);
    //根据追偿号查询条款
    List<ClmsReplevyLoss> selectPlanByReplevyNo(String replevyNo);
    List<ClmsReplevyLoss> selectByReplevyNoAndPlanCode(String replevyNo, String planCode);

    /**
     * 查询历史追偿金额总和（按责任代码和险种分组）
     * @param reportNo 报案号
     * @return 责任险种金额统计列表
     */
    List<DutyPlanAmountDTO> getHistoryRepleviedAmountByReplevyNo(@Param("reportNo") String reportNo, @Param("replevyDetailId") String replevyDetailId);

    /**
     * 根据追偿明细ID查询实际追回金额总和
     * @param replevyDetailId 追偿明细ID
     * @return 实际追回金额总和
     */
    BigDecimal getTotalRepleviedMoneyByDetailId(@Param("replevyDetailId") String replevyDetailId);
}
