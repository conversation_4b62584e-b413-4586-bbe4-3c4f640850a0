package com.paic.ncbs.claim.service.settle.factor.impl.bill;

import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.service.settle.factor.abstracts.settle.BillFilterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BillFilterServiceImpl implements BillFilterService {
    @Override
    public ClaimCaseDTO billFilter(ClaimCaseDTO bo) {
        log.info("执行发票规则实现");
        return bo;
    }
}
