package com.paic.ncbs.claim.controller.common;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.sao.OrderPaymentNoticeService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 指令支付超过时间未处理 自动发起通知支付批处理
 */
@RestController
@RequestMapping("/public/app/batchOrderPayment")
@Api(tags = {"批量通知收付指令支付批处理"})
@Slf4j
@RefreshScope
public class BatchOrderPaymentNoticeController {
    /**
     * 15天没有处理的任务 自动关闭
     * 默认值：15  支持配置
     */
    @Value("${batch.orderpaywaitdays:9}")
    private Integer orderPayWaitDays;
    @Autowired
    public OrderPaymentNoticeService orderPaymentNoticeService;
    @PostMapping("/batchOrderPaymentNotice")
    public ResponseResult batchOrderPaymentNotice() {
        orderPaymentNoticeService.batchOrderPaymentNotice(orderPayWaitDays);
        return ResponseResult.success();
    }
}
