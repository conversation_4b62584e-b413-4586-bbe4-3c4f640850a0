package com.paic.ncbs.claim.replevy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 估损责任记录查询DTO
 * 用于查询CLMS_ESTIMATE_DUTY_RECORD表数据
 */
@Data
@ApiModel("估损责任记录查询DTO")
public class EstimateDutyRecordQueryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty("责任代码")
    private String dutyCode;
    
    @ApiModelProperty("责任名称")
    private String dutyName;
    
    @ApiModelProperty("险种代码")
    private String planCode;
}
