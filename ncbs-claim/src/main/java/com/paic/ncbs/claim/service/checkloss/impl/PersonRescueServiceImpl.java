package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.model.dto.duty.PersonRescueDTO;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonRescueMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyDutyDetailMapper;
import com.paic.ncbs.claim.model.vo.duty.PersonRescueVO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.checkloss.PersonRescueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class PersonRescueServiceImpl implements PersonRescueService {

	@Autowired
    PersonRescueMapper personRescueMapper;
		
	@Autowired
    PolicyDutyDetailMapper policyDutyDetailMapper;

	@Autowired
	private AhcsCommonService ahcsCommonService;


	@Override
	public List<PersonRescueDTO> getPersonRescue(String reportNo, Integer caseTimes, String status, String taskId) {
		return personRescueMapper.getPersonRescue(reportNo, caseTimes, status, taskId);
	}
	
	@Override
	public PersonRescueVO getPersonRescueVO(String reportNo, Integer caseTimes, String status, String taskId) {

		BigDecimal totalAmount = new BigDecimal(0);
		PersonRescueVO personRescueVO = new PersonRescueVO();
		Map<String,PersonRescueDTO> initMap = new HashMap<>();
		List<PersonRescueDTO> personRescueList = this.getPersonRescue(reportNo, caseTimes, status, taskId);		
		if(ListUtils.isEmptyList(personRescueList)){
			personRescueList = new ArrayList<>();
		}		
		for(PersonRescueDTO personRescueDTO: personRescueList){
			PersonRescueDTO tmpDto = initMap.get(personRescueDTO.getRescueType());
			if(tmpDto != null){
				tmpDto.setAmount(personRescueDTO.getAmount());				
			}
		}
		List<PersonRescueDTO> initList = new ArrayList<>(initMap.values());
		for(PersonRescueDTO personRescueDTO : personRescueList){
			BigDecimal amount = personRescueDTO.getAmount() == null? new BigDecimal(0):personRescueDTO.getAmount();
			totalAmount = totalAmount.add(amount);
		}		
		personRescueVO.setTotalAmount(totalAmount);
		personRescueVO.setPersonRescueList(initList);
		personRescueVO.setModuleCode(ChecklossConst.PERSON_RESCUE);
		return personRescueVO;
	}

	@Override
	public void savePersonRescue(List<PersonRescueDTO> personRescueList, String reportNo, Integer caseTimes, String taskId) {
		List<PersonRescueDTO> existPersonRescueList = personRescueMapper.getPersonRescue(reportNo, caseTimes, null, taskId);
		if (ListUtils.isNotEmpty(existPersonRescueList)) {
			this.removePersonRescue(reportNo, caseTimes, taskId);
		}
		if (ListUtils.isNotEmpty(personRescueList)) {
			ahcsCommonService.batchHandlerTransactionalWithArgs(PersonRescueMapper.class, personRescueList, ListUtils.GROUP_NUM, "addPersonRescue");
		}
	}

	@Override
	public void removePersonRescue(String reportNo, Integer caseTimes, String taskId) {
		personRescueMapper.removePersonRescue(reportNo, caseTimes, taskId);
	}

}
