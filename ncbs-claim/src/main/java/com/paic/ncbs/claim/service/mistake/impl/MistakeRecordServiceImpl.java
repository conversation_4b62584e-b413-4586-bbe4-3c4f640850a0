package com.paic.ncbs.claim.service.mistake.impl;


import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.DutyBillLimitInfoService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle.ClmsDutyDetailBillSettleService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.mistake.MistakeRecordMapper;
import com.paic.ncbs.claim.model.dto.mistake.MistakeRecordDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.service.mistake.MistakeRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service("mistakeRecordService")
public class MistakeRecordServiceImpl implements MistakeRecordService {

	private static final String MISTAKE_STATUS_CONFIRMED="2" ;

	@Autowired
	private MistakeRecordMapper mistakeRecordMapper;

	@Autowired
	private TaskInfoService taskInfoService;
	@Autowired
	private DutyBillLimitInfoService dutyBillLimitInfoService;

	@Autowired
	private ClmsDutyDetailBillSettleService clmsDutyDetailBillSettleService;

	@Autowired
	private IOperationRecordService operationRecordService;
	@Override
	@Transactional
	public void addMistakeRecord(MistakeRecordDTO mistakeRecordDTO,String taskDefinitionBpmKey) {
		List<String> mistakeCodeList = mistakeRecordDTO.getMistakeCodeList();
		String reportNo = mistakeRecordDTO.getReportNo();
		Integer caseTimes = mistakeRecordDTO.getCaseTimes();
//		mistakeRecordMapper.deleteByReportNoAndBpmKey(reportNo,caseTimes,taskDefinitionBpmKey);
		TaskInfoDTO taskInfoDTO = taskInfoService.findLatestByReportNoAndBpmKey(reportNo, caseTimes, taskDefinitionBpmKey);
		// 用一个随机数来记录到 DeptCode 来区分每次不同退回
		String randomStr = StringUtils.getRandomStr(10);
		if (CollectionUtils.isNotEmpty(mistakeCodeList)){
			for (String mistakeCode : mistakeCodeList) {
				MistakeRecordDTO recordDTO = new MistakeRecordDTO();
				recordDTO.setIdAhcsMistakeRecord(UuidUtil.getUUID());
				recordDTO.setCaseTimes(caseTimes);
				// 不存taskid 存 BpmKey
				recordDTO.setRecordTaskId(taskDefinitionBpmKey);
				recordDTO.setReportNo(reportNo);
				recordDTO.setMistakeRemark(mistakeRecordDTO.getMistakeRemark());
				recordDTO.setStatus(MISTAKE_STATUS_CONFIRMED);
				recordDTO.setCreatedBy(WebServletContext.getUserId());
				recordDTO.setUpdatedBy(WebServletContext.getUserId());
				recordDTO.setMistakeCode(mistakeCode);
				recordDTO.setDeptCode(randomStr);
				recordDTO.setSubmitterUm(WebServletContext.getUserName());
				recordDTO.setUserInCharge(taskInfoDTO==null?"":taskInfoDTO.getAssigneeName());
				mistakeRecordMapper.addMistakeRecords(recordDTO);
			}
		}

	}

	@Override
	public List<MistakeRecordDTO> getSimpleMistakeRecord(MistakeRecordDTO mistakeRecordDTO, String taskDefinitionBpmKey) {
		String reportNo = mistakeRecordDTO.getReportNo();
		Integer caseTimes = mistakeRecordDTO.getCaseTimes();
		return mistakeRecordMapper.getSimpleMistakeRecord(reportNo,caseTimes,taskDefinitionBpmKey);
	}
}
