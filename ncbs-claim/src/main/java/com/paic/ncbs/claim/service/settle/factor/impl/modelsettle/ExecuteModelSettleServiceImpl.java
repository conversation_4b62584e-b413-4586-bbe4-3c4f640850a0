package com.paic.ncbs.claim.service.settle.factor.impl.modelsettle;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.enums.CalculationFactorEnum;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.CalculateFormulaDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.RemitAmountDTO;
import com.paic.ncbs.claim.service.settle.factor.impl.common.ClaimApplicationAwareUtil;
import com.paic.ncbs.claim.service.settle.factor.interfaces.base.BaseSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.formula.FormulaService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.modelsettle.ExecuteModelSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.reason.DutySettleReasonTemplateService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.CumulativeLimitService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.remit.RemitAmountInitService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 理算实现
 */
@Slf4j
@RefreshScope
@Service
public class ExecuteModelSettleServiceImpl implements ExecuteModelSettleService {
    @Autowired
    private ClaimApplicationAwareUtil claimApplicationAwareUtil;
    @Autowired
    private Map<String, BaseSettleService> serviceImpMaps;
    @Autowired
    private DutySettleReasonTemplateService dutySettleReasonTemplateService;

    /**
     * 生产环境和dev产品，方案编码都不一样
     * dev C00094,生产是C00034
     */
    @Value("${planCode.outpatientCode:C00094}")
    private String configDutyCode;
    @Value("${project.projectCode:N}")
    private List<String> configProjectCodeList;
    @Autowired
    private CumulativeLimitService cumulativeLimitService;
    @Override
    public ClaimCaseDTO modelSettle(ClaimCaseDTO copyClaimCaseDTO) {
        log.info("模型理算开始报案号={},理算入参={}", copyClaimCaseDTO.getReportNo(), JsonUtils.toJsonString(copyClaimCaseDTO));
        if(CollectionUtil.isEmpty(copyClaimCaseDTO.getPolicyPayDTOList())){
            return copyClaimCaseDTO;
        }
        List<DutyPayDTO> reasonDatalList=new ArrayList<>();
        for (PolicyPayDTO policy : copyClaimCaseDTO.getPolicyPayDTOList()) {
            List<PlanPayDTO> planList = policy.getPlanPayArr();
            for (PlanPayDTO plan : planList) {
                List<DutyPayDTO> dutyList =  plan.getDutyPayArr();
                for (DutyPayDTO duty :dutyList) {
                    List<DutyDetailPayDTO>  detailPayDTOS = duty.getDutyDetailPayArr();
                    //计算公式因子解析:不能放在责任层级  责任下会有多个责任明细分类，每个责任明细分类的公式是不一样的
                    //CalculateFormulaDTO alculateFormulaDTO  = claimParseFormula(duty);
                    duty.setReportNo(copyClaimCaseDTO.getReportNo());
                    for (DutyDetailPayDTO detail : detailPayDTOS) {
                        BigDecimal maxAmountPay = detail.getMaxAmountPay();//责任明细剩余理赔金额
                        if (!isDutyMatchCaseClass(copyClaimCaseDTO.getCaseClassList(),detail.getDutyDetailType())){
                            continue;
                        }
                        if(BigDecimal.ZERO.compareTo(maxAmountPay) == 0){
                            LogUtil.audit("--重构当前责任明细剩余给付额为0，无需理算! 报案号：{}，责任明细类型：{}",copyClaimCaseDTO.getReportNo(),detail.getDutyDetailName());
                            detail.setAutoSettleAmount(BigDecimal.ZERO);
                            continue;
                        }
                        //计算公式因子解析
                        List<MedicalBillInfoDTO> medicalBillInfoDTOList = null;
                        if(Objects.nonNull(copyClaimCaseDTO.getMedicalInfoDTO())){
                            medicalBillInfoDTOList = copyClaimCaseDTO.getMedicalInfoDTO().getMedicalBillInfoDTOList();
                        }
                        CalculateFormulaDTO alculateFormulaDTO  = claimParseFormula(detail,duty, medicalBillInfoDTOList);
                        detail.setCalServiceImplMap(alculateFormulaDTO.getCalServiceImplMap());
                        detail.setReportNo(copyClaimCaseDTO.getReportNo());
                        detail.setCaseTimes(copyClaimCaseDTO.getCaseTimes());

                        //责任明细分类
                        String dutyDetailType = Optional.ofNullable(detail.getDutyDetailType()).orElse("");
                        String serviceImpl= Constants.SETTLE_IMPL_MAP.get(dutyDetailType);
                        log.info("新逻辑理算责任明细分类报案号={},计算服务名={}",copyClaimCaseDTO.getReportNo(),serviceImpl);
                        if(StringUtils.isEmptyStr(serviceImpl)){
                            continue;
                        }
                        if(Objects.equals("N",detail.getIsSettleFlag())){
                            detail.setAutoSettleAmount(BigDecimal.ZERO);
                            log.info("核则结果无需理算报案号={},责任险种代码={},责任明细代码={}",copyClaimCaseDTO.getReportNo(),detail.getPlanCode(),detail.getDutyDetailCode());
                            continue;
                        }
                        BaseSettleService baseSettleService =  serviceImpMaps.get(serviceImpl);
                        log.info("新逻辑理算责任明细分类报案号={},计算服务实例对象名={}",copyClaimCaseDTO.getReportNo(),baseSettleService);
                        if(Objects.isNull(baseSettleService)){
                           continue;
                        }
                        baseSettleService.getSettleAmount(copyClaimCaseDTO,
                                detail,
                                alculateFormulaDTO.getExpression());
                        log.info("责任明细理算结果报案号={},责任明细编码={},理算金额={}",copyClaimCaseDTO.getReportNo(),detail.getDutyDetailCode(),detail.getAutoSettleAmount());
                        if(maxAmountPay.compareTo(detail.getAutoSettleAmount())<0){
                            detail.setNotice("理算结果"+detail.getAutoSettleAmount()+"超过剩余赔付额！按剩余赔付额"+maxAmountPay+"赔付");
                            detail.setAutoSettleAmount(maxAmountPay);
                        }
                    }
                    //理算依据处理
                    reasonDatalList.add(duty);

                }
            }

        }
        //月限额，年限额 数据处理
        cumulativeLimitService.cumulativeLimit(copyClaimCaseDTO);
        log.info("月限额结束报案号={},计算结果={}",copyClaimCaseDTO.getReportNo(),JsonUtils.toJsonString(copyClaimCaseDTO));
        for (DutyPayDTO duty: reasonDatalList) {
            dutySettleReasonTemplateService.displaySettleReason(duty);
        }
        log.info("模型理算结束报案号={},理算结果={}", copyClaimCaseDTO.getReportNo(), JSONObject.toJSONString(copyClaimCaseDTO));

        return copyClaimCaseDTO;
    }

    /**
     * 公式因子解析
     * (合理费用-免赔额)*赔付比例
     *
     * @param detail                 00-合理费用默认实现
     *                               * 01-合理费用等于 发票金额-自费-第三方金额实现
     *                               * times次免赔
     *                               * day-日免赔
     *                               * year-年免赔
     *                               * payProportion-赔付比列
     * @param medicalBillInfoDTOList
     */
    private CalculateFormulaDTO claimParseFormula(DutyDetailPayDTO detail, DutyPayDTO duty, List<MedicalBillInfoDTO> medicalBillInfoDTOList) {

        //String formula ="(default-timesRemitAmount)*payProportion";
        ApplicationContext context = claimApplicationAwareUtil.getApplicationContext();
        String formulaImpl= Constants.DUTY_DETAIL_FORMULA_MAP.get(detail.getDutyDetailType());
        FormulaService formulaServiceImpl = (FormulaService) context.getBean(formulaImpl);
        String formula =formulaServiceImpl.getFormula(detail);
        log.info("获取到的理算公式：{}", formula);
        CalculateFormulaDTO calculateFormulaDTO= new CalculateFormulaDTO();
        if(StringUtils.isEmptyStr(formula)){
           //没有公式就不解析
            return calculateFormulaDTO;
        }
        Expression expression = AviatorEvaluator.compile(formula);
        List<String> configServiceNameList = expression.getVariableFullNames();
        if(CollectionUtil.isEmpty(configServiceNameList)){
            throw new GlobalBusinessException("公式解析异常");
        }

        Map<String, CalculateAmountService> calServiceImplMap = new LinkedHashMap<>();

        Map<String, String> serviceImpMap = getActServiceName(configServiceNameList);
        log.info("理算公式解析结果：{}", serviceImpMap.keySet());
        for (Map.Entry<String, String> en : serviceImpMap.entrySet()) {
            if(StringUtils.isNotEmpty(Constants.REMIT_INIT_MAP.get(en.getKey()))){
                String initServiceName=Constants.REMIT_INIT_MAP.get(en.getKey());
                RemitAmountInitService remitAmountInitService = (RemitAmountInitService) context.getBean(initServiceName);
                remitAmountInitService.initRemitData(en.getKey(),en.getValue(),duty,calServiceImplMap, medicalBillInfoDTOList);
            }else{
                CalculateAmountService service = (CalculateAmountService) context.getBean(en.getValue());
                calServiceImplMap.put(en.getKey(), service);
            }

        }

        calculateFormulaDTO.setCalServiceImplMap(calServiceImplMap);
        calculateFormulaDTO.setExpression(expression);
        return calculateFormulaDTO;
    }

    private RemitAmountDTO getRemitAmountDTO(DutyPayDTO duty) {
        RemitAmountDTO initDto=new RemitAmountDTO();
        BeanUtils.copyProperties(duty,initDto);
        initDto.setConfigRemitAmount(duty.getRemitAmount());
        initDto.setInsuranceBeginDate(DateUtils.dateFormat(duty.getInsuranceBeginDate(),DateUtils.FULL_DATE_STR));
        initDto.setInsuranceEndDate(DateUtils.dateFormat(duty.getInsuranceEndDate(),DateUtils.FULL_DATE_STR));
        return initDto;
    }

    /**
     * 根据配置的属性组装计算公式
     * @param duty
     * @return
     */
    private String getFromula(DutyPayDTO duty) {
        log.info("责任编码={},责任免赔额属性={}",duty.getDutyCode(),duty.getRemitAmountType());
        StringBuilder sb=new StringBuilder();
        String reasonableAmount=Constants.REASONABLE_AMOUNT_00;//默认
        if (configDutyCode.contains(duty.getDutyCode()) && configProjectCodeList.contains(duty.getProductPackage())) {
            reasonableAmount =Constants.REASONABLE_AMOUNT_01;//
        }
        //免赔额类型
        String  remitAmountType = Constants.REMIT_AMOUNT_TYPE_MAP.get(duty.getRemitAmountType());
        if(StringUtils.isEmptyStr(remitAmountType)){
            remitAmountType="999";//无免赔额
        }
        sb.append("(").append(reasonableAmount).append("-").append(remitAmountType).append(")*").append(Constants.PROPORTION);
        return sb.toString();
    }

    /**
     * 映射获取因子实现类
     * @param configServiceNameList
     * @return
     */
    private Map<String,String>  getActServiceName(List<String> configServiceNameList) {
        Map<String,String> serviceImplNameMap=new LinkedHashMap<>();
        for (String name : configServiceNameList) {
            String fucName = CalculationFactorEnum.getFucNameByCode(name);
            if(StringUtils.isEmptyStr(fucName)){
                throw new GlobalBusinessException(name+"因子对应的实现类没有配置");
            }
            serviceImplNameMap.put(name, fucName);
        }

        return serviceImplNameMap;
    }
    public boolean isDutyMatchCaseClass(List<String> caseClassList,String dutyDetailType){
        if(ListUtils.isNotEmpty(caseClassList) && !Collections.disjoint(caseClassList, SettleConst.NO_PERSONAL_CLASS)){
            return true;
        }
        List<String> detailTypeCaseClass =  SettleConst.DETAIL_TYPE_CASE_CLASS_MAP.get(dutyDetailType);
        LogUtil.audit("--校验责任类型和当前案件类别是否匹配，caseClass:{},dutyDetailType:{}",
                caseClassList.toString(),SettleConst.DETAIL_TYPE_NAME.get(dutyDetailType));
        if (caseClassList.isEmpty() || detailTypeCaseClass == null || detailTypeCaseClass.isEmpty()){
            LogUtil.audit("--匹配结果: 未匹配上");
            return false;
        }
        boolean result = !Collections.disjoint(caseClassList,detailTypeCaseClass);
        LogUtil.audit("--匹配结果:{}", result ? "匹配" : "不匹配");
        return result;

    }
}
