package com.paic.ncbs.claim.service.settle.factor.impl.common;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.common.BeforeBillInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 过滤药房发票
 */
@Order(2)
@RefreshScope
@Slf4j
@Service
public class DrugstoreBillInfoServiceImpl implements BeforeBillInfoService {

    /**
     * 药房指定的方案编码
     */
    @Value("${pharmacy.packageCode:null}")
    private List<String> packageCodeList;

    /**
     * 药房责任
     */
    @Value("${drugsBill.duty:C00369}")
    private String drugsDutyCode;

    /**
     * 非药房责任
     */
    @Value("${noDrugsBill.duty:C00094}")
    private String noDrugsDutyCode;
    @Override
    public List<MedicalBillInfoDTO> expansion(List<MedicalBillInfoDTO> medicalBillInfoDTOList, DutyDetailPayDTO detailPayDTO) {
        log.info("报案号={},药房发票过滤配置={}",detailPayDTO.getReportNo(), JsonUtils.toJsonString(packageCodeList));
        if(!packageCodeList.contains(detailPayDTO.getProductPackage())){
            return medicalBillInfoDTOList;
        }
        List<MedicalBillInfoDTO> returList=new ArrayList<>();
        if(drugsDutyCode.contains(detailPayDTO.getDutyCode())){
            //药房发票
            returList= medicalBillInfoDTOList.stream().filter(medicalBillInfoDTO ->Objects.equals("BT_36011",medicalBillInfoDTO.getBillType())).collect(Collectors.toList());
        }
        if(noDrugsDutyCode.contains(detailPayDTO.getDutyCode())){
            returList= medicalBillInfoDTOList.stream().filter(medicalBillInfoDTO ->!Objects.equals("BT_36011",medicalBillInfoDTO.getBillType())).collect(Collectors.toList());
        }
        medicalBillInfoDTOList=returList;
        log.info("报案号={},药房过滤后发票={}",detailPayDTO.getReportNo(),JsonUtils.toJsonString(medicalBillInfoDTOList));
        return medicalBillInfoDTOList;
    }
}
