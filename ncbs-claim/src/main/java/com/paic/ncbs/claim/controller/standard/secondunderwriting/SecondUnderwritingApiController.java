package com.paic.ncbs.claim.controller.standard.secondunderwriting;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity;
import com.paic.ncbs.claim.model.dto.api.StandardRequestDTO;
import com.paic.ncbs.claim.model.vo.casezero.ProblemCaseVO;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSecondUnderwritingService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "对外二核相关接口")
@RestController
@Validated
@RequestMapping("/public/secondUnderwriting")
public class SecondUnderwritingApiController extends BaseController {

    @Autowired
    private ClmsSecondUnderwritingService clmsSecondUnderwritingService;
    @Autowired
    private TaskInfoService taskInfoService;


    /**
     * 新增数据
     * {
     *     "accidentDate": "2023-09-09 00:00:00",
     *     "reportNo": "90011000000000012769",
     *     "caseTimes":1,
     *     "diseaseInfo": "code_name_date#code_name_date",
     *     "evidenceMaterialType": "1,3",
     *     "materialFileId": "123,456",
     *     "taskCode": "OC_MANUAL_SETTLE"
     * }
     * @param standardRequestDTO 实体
     * @return 新增结果
     */
    @PostMapping("/sendTask")
    public ResponseResult<Object> sendTask(@RequestBody StandardRequestDTO standardRequestDTO) {
        ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity = JSONObject.parseObject(JSONObject.toJSONString(standardRequestDTO.getRequestData()), ClmsSecondUnderwritingEntity.class);
        clmsSecondUnderwritingService.sendTask(clmsSecondUnderwritingEntity);
        //发起二核后 通知TPA中台 案件状态发生了变化（只有线上的会通知，线下的单子不会）
//        if(Objects.equals(BpmConstants.OC_REPORT_TRACK,clmsSecondUnderwritingEntity.getTaskCode())){
//            claimSendTpaMqInfoService.sendTpaMq(clmsSecondUnderwritingEntity.getReportNo(),clmsSecondUnderwritingEntity.getCaseTimes(), Transform.getUWCaseProcessStatus(clmsSecondUnderwritingEntity.getTaskCode()));
//        }
        //查询taskId并返回
        ProblemCaseVO problemCaseVO = new ProblemCaseVO();
        String taskId = taskInfoService.getTaskId(clmsSecondUnderwritingEntity.getReportNo(),clmsSecondUnderwritingEntity.getCaseTimes(), BpmConstants.OC_CLAIM_SECOND_UNDERWRITING);
        problemCaseVO.setProblemNo(taskId);
        return ResponseResult.success(problemCaseVO);
    }
}
