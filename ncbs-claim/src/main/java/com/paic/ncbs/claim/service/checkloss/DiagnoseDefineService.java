package com.paic.ncbs.claim.service.checkloss;



import com.paic.ncbs.claim.model.dto.checkloss.DiagnoseDefineDTO;
import com.paic.ncbs.claim.model.vo.checkloss.DiagnoseDefineVO;

import java.util.List;


public interface DiagnoseDefineService {

	 
	public List<DiagnoseDefineVO> getDiagnoseDefines(String searchStr,String reportNo);

	public List<DiagnoseDefineVO> getDiagnoseDefineList(DiagnoseDefineDTO diagnoseDefineDTO);

	/**
	 * 查询国际ICD编码以S开头的数据
	 * @param searchStr
	 * @param reportNo
	 * @return
	 */
	public List<DiagnoseDefineVO> getDiagnoseDefineVos(String searchStr,String reportNo);

}
