package com.paic.ncbs.claim.service.report;

import com.paic.ncbs.claim.model.dto.ahcs.AhcsDomainDTO;
import com.paic.ncbs.claim.model.dto.report.EstimateLossDTO;

import java.util.List;

public interface EstimateLossService {

    /**
     * 获取所有估损配置项
     * @return
     */
    List<EstimateLossDTO> getAllEstimatLossConfig();

    /**
     * 获取估损配置项
     * @return
     */
    void setAllEstimatLossConfig(List<EstimateLossDTO> estimateLossList);

    /**
     * 根据出险类型获取估损配置项
     * @return
     */
    EstimateLossDTO getEstimatLossConfig(String insuredApplyType);

    /**
     * 运行报即立规则
     * @param
     * @param insuredApplyType
     */
    void runReportEstimateRule(AhcsDomainDTO ahcsDomainDTO, String insuredApplyType, String userId);

}
