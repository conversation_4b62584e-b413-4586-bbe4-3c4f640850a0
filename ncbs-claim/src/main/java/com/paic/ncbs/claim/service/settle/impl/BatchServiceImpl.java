package com.paic.ncbs.claim.service.settle.impl;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.settle.BatchDTO;
import com.paic.ncbs.claim.dao.mapper.settle.BatchMapper;
import com.paic.ncbs.claim.service.settle.BatchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Service("batchService")
public class BatchServiceImpl implements BatchService {
	
	@Autowired
    BatchMapper batchMapper;

	@Override
	public BatchDTO getBatch(String reportNo, Integer caseTimes) {
		return batchMapper.getBatch(reportNo, caseTimes);
	}

    
	@Override
	public String getSettleUserUm(String reportNo, Integer caseTimes){
		return batchMapper.getSettleUserUm(reportNo, caseTimes);
	}
	

	@Override
	public void insertBath(BatchDTO batch) throws GlobalBusinessException {
		
		batchMapper.insertBatch(batch);
	}

	@Override
	public void updateSettleTime(BatchDTO batch) {
		batchMapper.updateSettleTime(batch);
	}

	@Override
	public void updateBatch(BatchDTO batch) {
		batchMapper.updateBatch(batch);
	}

}
