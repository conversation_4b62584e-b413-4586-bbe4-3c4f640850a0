package com.paic.ncbs.claim.dao.entity.blacklist;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 黑名单审批记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Getter
@Setter
@TableName("clms_black_list_audit")
public class ClmsBlackListAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审批表主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 关联黑名单ID
     */
    @TableField("black_list_id")
    private String blackListId;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 操作类型：1-新增；2-修改；3-启用；4-禁用
     */
    @TableField("operate_type")
    private String operateType;

    /**
     * 提交人
     */
    @TableField("submit_by")
    private String submitBy;

    /**
     * 提交时间
     */
    @TableField("submit_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime submitTime;

    /**
     * 审批状态：1-待审批；2-审批中；3-审批通过；4-审批不通过
     */
    @TableField("audit_status")
    private String auditStatus;

    /**
     * 审批人
     */
    @TableField("audit_by")
    private String auditBy;

    /**
     * 审批时间
     */
    @TableField("audit_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTime;

    /**
     * 审批意见：1-同意；2-不同意
     */
    @TableField("audit_result")
    private String auditResult;

    /**
     * 审批说明
     */
    @TableField("audit_desc")
    private String auditDesc;

    /**
     * 创建人员
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;
}
