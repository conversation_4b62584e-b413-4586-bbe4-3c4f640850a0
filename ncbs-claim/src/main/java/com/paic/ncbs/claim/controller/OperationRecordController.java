package com.paic.ncbs.claim.controller;

import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.policy.ClaimRecordDTO;
import com.paic.ncbs.claim.model.dto.report.CustomerDTO;
import com.paic.ncbs.claim.model.dto.report.RecordDutyInfo;
import com.paic.ncbs.claim.model.vo.record.OperationRecordVO;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.policy.ClaimRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 操作记录
 */
@RestController
@RequestMapping("/app/claimRecord")
public class OperationRecordController {

    @Autowired
    private IOperationRecordService operationRecordService;

    @RequestMapping(value = "/getOperationRecord",produces = {"application/json"},method = RequestMethod.GET)
    public ResponseResult<List<OperationRecordVO>> getOperationRecord(@RequestParam("reportNo") String reportNo){
        List<OperationRecordVO> operationRecordVOS = operationRecordService.queryRecord(reportNo);
        return ResponseResult.success(operationRecordVOS);
    }
}
