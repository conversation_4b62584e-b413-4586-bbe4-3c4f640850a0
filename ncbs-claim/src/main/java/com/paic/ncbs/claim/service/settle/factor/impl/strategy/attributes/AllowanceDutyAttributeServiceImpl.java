package com.paic.ncbs.claim.service.settle.factor.impl.strategy.attributes;

import com.paic.ncbs.claim.common.constant.DutyAttributeConst;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.attributes.DutyAttributeService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service
public class AllowanceDutyAttributeServiceImpl implements DutyAttributeService {
    @Override
    public void setDutyDetailAttribute(DutyDetailPayDTO detail, Map<String, Optional<String>> attributes, Map<String, Map<String, String>> attributesdetailMap) {
        LogUtil.audit("---获取津贴类型责任属性---");
        if (!attributes.isEmpty()) {
            if (attributes.containsKey(DutyAttributeConst.REMIT_DAYS)) {
                detail.setRemitDays(nvl(new BigDecimal(attributes.get(DutyAttributeConst.REMIT_DAYS)
                        .get()), 0));
            }
            if (attributes.containsKey(DutyAttributeConst.ALLOWANCE_AMOUNT)) {
                detail.setAllowanceAmount(nvl(new BigDecimal(attributes.get(DutyAttributeConst.ALLOWANCE_AMOUNT)
                        .get()), 0));
            }
        } else {
            LogUtil.audit("津贴类型责任属性为空,案件reportNo={},caseTimes={},保单号={}", detail.getReportNo(), detail.getCaseTimes(), detail.getPolicyNo());
        }
    }
}
