package com.paic.ncbs.claim.service.endcase;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.other.CustomerInfoDTO;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.vo.endcase.*;
import com.paic.ncbs.claim.model.vo.policy.PolicyHistoryRequestVO;
import com.paic.ncbs.claim.model.vo.policy.PolicyHistoryVO;

import java.util.List;

public interface WholeCaseService {

    WholeCasePageResult getHistoryCaseList(WholeCaseVO wholeCaseVO) throws GlobalBusinessException;

    void modifyDocumentStatus(String reportNo, Integer caseTimes, String status, String userId);

    WholeCaseTimeVO getWholeCaseTime(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    void getCaseWithCondition(WholeCaseVO wholeCaseVO) throws GlobalBusinessException;

    ReportBaseInfoResData getReportBaseInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    boolean isTurnToNormalCase(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    String getOnlineCaseType(String reportNo, Integer caseTimes);

    List<TrackInfoVO> getTrackInfoList(String reportNo, Integer caseTimes);

    String getCaseTypeByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    Integer getCustomerAccidentCount(String clientNo);

    PolicyHistoryVO getPolicyHistory(PolicyHistoryRequestVO requestVO) throws GlobalBusinessException;

    /**
     * 未决管理查询，不包含重开案件+未结案+已立案
     * @param queryVO
     * @return
     */
    ResponseResult<Object> getPendingManagementQueryList(WholeCaseVO queryVO);
    AiModelStatisticsVO getAiModelStatistics();
}
