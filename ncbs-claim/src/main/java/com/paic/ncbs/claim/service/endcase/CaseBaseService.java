package com.paic.ncbs.claim.service.endcase;

import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO;
import com.paic.ncbs.claim.service.base.BaseService;

import java.util.List;

public interface CaseBaseService extends BaseService<CaseBaseEntity> {

    List<CaseBaseEntity> getCaseBaseInfoByReportNo(String reportNo);

    List<CaseBaseEntity> getCaseBaseInfoByReportNoAndCasetimes(String reportNo, String casetimes);

    List<CaseBaseEntity> getCaseBaseInfoByReportNoAndPolicytNo(String reportNo, String policyNo);

    List<CaseBaseDTO> getCaseBaseDTOList(String reportNo, Integer caseTimes);

    CaseBaseEntity getCaseBaseInfo(String report, String policyNo, Integer caseTimes);

    void updateRiskGroup(String idClmCaseBase, String riskGroupNo, String riskGroupName);

    void updateEsUpdatedDate(String reportNo, Integer caseTimes);
}
