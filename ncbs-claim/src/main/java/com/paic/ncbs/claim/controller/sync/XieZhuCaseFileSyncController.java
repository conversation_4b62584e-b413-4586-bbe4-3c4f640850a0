package com.paic.ncbs.claim.controller.sync;

import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.policy.ClaimRecordDTO;
import com.paic.ncbs.claim.model.dto.report.CustomerDTO;
import com.paic.ncbs.claim.model.dto.report.RecordDutyInfo;
import com.paic.ncbs.claim.service.SftpDealService;
import com.paic.ncbs.claim.service.policy.ClaimRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * 协筑补充案件同步
 */
@RestController
@RequestMapping("/public/sync")
@Slf4j
public class XieZhuCaseFileSyncController {

    @Autowired
    private SftpDealService sftpDealService;

    @GetMapping(value = "/toXieZhuSftp.do")
    public ResponseResult getClaimRecordByPolicy(@RequestParam(value = "number", required = false) Integer number) throws IOException {
        log.info("同步协筑sftp开始");
        sftpDealService.execute(number);
        log.info("同步协筑sftp结束");
        return ResponseResult.success();
    }
}
