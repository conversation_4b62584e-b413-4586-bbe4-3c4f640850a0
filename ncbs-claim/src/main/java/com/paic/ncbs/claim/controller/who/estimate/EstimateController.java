package com.paic.ncbs.claim.controller.who.estimate;

import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.EstimateConstValues;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.EstimateUtil;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePolicyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.ClmsEstimateRecordService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "立案")
@RestController
@RequestMapping("/who/app/estimateAction")
@Component("whoEstimateAction")
public class EstimateController extends BaseController {

	@Autowired
	private EstimateService estimateService;

	@Autowired
	private WholeCaseBaseService wholeCaseBaseService;

	@Autowired
	private RegisterCaseService registerCaseService;

	@Autowired
	private UserInfoService userInfoService;
	@Autowired
	private ClmsEstimateRecordService clmsEstimateRecordService;

	@Autowired
	private EstimateChangeService estimateChangeService;


	@ApiOperation("获取报案跟踪信息-立案信息")
	@PostMapping("/getEstimateDataList")
	public ResponseResult<EstimatePolicyFormDTO> getEstimateDataList(@RequestBody EstimatePolicyFormDTO estimatePolicyFormDTO) throws Exception {
		estimatePolicyFormDTO.setUpdatedBy(WebServletContext.getUserId());
		String reportNo = estimatePolicyFormDTO.getReportNo();
		Integer caseTimes = estimatePolicyFormDTO.getCaseTimes();
		EstimatePolicyFormDTO result = null;
		LogUtil.audit("报案跟踪查询立案信息,reportNo={}, caseTimes={}", reportNo, caseTimes);
		result = estimateService.getEstimateDataByTache(reportNo, caseTimes, EstimateUtil.ESTIMATE_TYPE_REGISTRATION, estimatePolicyFormDTO.getScene());
		int policyListSize = ListUtils.isNotEmpty(result.getEstimatePolicyList()) ? result.getEstimatePolicyList().size() : 0;
		LogUtil.audit("报案跟踪环节做过修正金额, reportNo={}, caseTimes={}, policyList.size={}", reportNo, caseTimes, policyListSize);
		result.setOldEstimateAmountSum(EstimateUtil.getEstimateAmountSum(result.getEstimatePolicyList()));

		WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo,caseTimes);
		result.setRegisterUm(wholeCaseBaseDTO.getRegisterUm());
		if(ConstValues.YES.equals(wholeCaseBaseDTO.getIsRegister())){
			if(ConstValues.SYSTEM_UM.equalsIgnoreCase(wholeCaseBaseDTO.getRegisterUm())){
				result.setRegisterName(ConstValues.SYSTEM_NAME);
			}else{
				result.setRegisterName(userInfoService.getUserNameById(wholeCaseBaseDTO.getRegisterUm()));
			}
		}
		result.setRegisterDate(wholeCaseBaseDTO.getRegisterDate());

		ResponseResult resultVO = registerCaseService.registerCaseCheck(reportNo, caseTimes);
		boolean isAutoRegisterOrAuditing = (ConstValues.YES.equals(wholeCaseBaseDTO.getIsRegister()) &&
				ConstValues.SYSTEM_UM.equals(wholeCaseBaseDTO.getRegisterUm())) || !ErrorCode.RegisterCase.CAN_APPLY_REGISTER_CASE.equals(resultVO.getCode());
		if(isAutoRegisterOrAuditing){
			result.setIsAmend(EstimateConstValues.NO_AMEND);
		}else{
			result.setIsAmend(EstimateConstValues.YES_AMEND);
		}

		//报即立(报案估损)金额
		result.setEstimateLossAmount(clmsEstimateRecordService.getEstimateLossAmount(reportNo,caseTimes));

		//未决修正审批详情页增加申请数据返回
		if("01".equals(estimatePolicyFormDTO.getPageSource())){
			List<EstimatePolicyDTO> estimatePolicyList = result.getEstimatePolicyList();
			List<EstimateChangePolicyDTO> applyEstimatePolicyList = estimateChangeService.getApplyEstimatePolicyList(reportNo, caseTimes);
			if(!CollectionUtils.isEmpty(estimatePolicyList)){
				String departmentName = estimatePolicyList.get(0).getDepartmentName();
				for (EstimateChangePolicyDTO changePolicyDTO : applyEstimatePolicyList) {
					changePolicyDTO.setCaseDepartment(departmentName);
				}
			}
			result.setApplyEstimatePolicyList(applyEstimatePolicyList);
		}
		return ResponseResult.success(result);
	}


	//预赔赔款录入
	@RequestMapping(value = "/getEstimatePolicyList", method = RequestMethod.POST)
	public ResponseResult<List<EstimatePolicyDTO>> getEstimatePolicyList(@RequestBody EstimatePolicyDTO estimatePolicy) throws GlobalBusinessException {
		List<EstimatePolicyDTO> list = estimateService.getEstimatePolicies(estimatePolicy.getReportNo(), estimatePolicy.getCaseTimes(),
				estimatePolicy.getSubTimes());
		return ResponseResult.success(list);
	}
}
