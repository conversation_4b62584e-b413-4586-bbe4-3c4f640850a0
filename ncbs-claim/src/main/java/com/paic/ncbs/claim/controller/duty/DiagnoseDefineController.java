package com.paic.ncbs.claim.controller.duty;


import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.checkloss.DiagnoseDefineVO;
import com.paic.ncbs.claim.service.checkloss.DiagnoseDefineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

@Api(tags = "诊断信息")
@Controller
@RequestMapping("/duty/app/diagnoseDefineAction")
public class DiagnoseDefineController extends BaseController {

    @Resource(name = "diagnoseDefineService")
    private DiagnoseDefineService diagnoseDefineService;


    @ApiOperation("查询获取诊断信息列表")
    @ResponseBody
    @RequestMapping(value = "/getDiagnoseDefineList", method = RequestMethod.POST)
    public ResponseResult<List<DiagnoseDefineVO>> searchDiagnoseDefineList(@RequestParam("searchStr") String searchStr,@RequestParam("reportNo") String reportNo) throws GlobalBusinessException, UnsupportedEncodingException {
        searchStr = URLDecoder.decode(searchStr,"UTF-8");
        reportNo = URLDecoder.decode(reportNo,"UTF-8");
        LogUtil.audit("getDiagnoseDefineList根据输入模糊查询获取诊断信息列表。searchStr,reportNo ={" + searchStr +  "," + reportNo + "}");
        List<DiagnoseDefineVO> list = diagnoseDefineService.getDiagnoseDefines(searchStr,reportNo);
        return ResponseResult.success(list);
    }


    @ApiOperation("获取诊断信息列表")
    @ResponseBody
    @RequestMapping(value = "/getDiagnoseDefineList/{searchStr}", method = RequestMethod.GET)
    public ResponseResult<List<DiagnoseDefineVO>> getDiagnoseDefineList(@PathVariable("searchStr") String searchStr) throws GlobalBusinessException {
        LogUtil.audit("getDiagnoseDefineList根据输入模糊查询获取诊断信息列表。searchStr={" + searchStr + "}");
        List<DiagnoseDefineVO> list = diagnoseDefineService.getDiagnoseDefines(searchStr,null);
        return ResponseResult.success(list);
    }

}