package com.paic.ncbs.claim.service.checkloss;


import com.paic.ncbs.claim.model.dto.checkloss.DisabilityAppraisalDTO;

import java.util.List;

public interface DisabilityAppraisalService {


	public void addDisabilityAppraisal(DisabilityAppraisalDTO disabilityAppraisalDTO);


	public void modifyDisabilityAppraisal(DisabilityAppraisalDTO disabilityAppraisalDTO);


	public DisabilityAppraisalDTO getDisabilityAppraisal(DisabilityAppraisalDTO disabilityAppraisalDTO);


	public List<DisabilityAppraisalDTO> getIsAppraisal(String idAhcsChannelProcess);


	public DisabilityAppraisalDTO getDisabilityAppraisalByIdAhcsChannelProcess(String idAhcsChannelProcess);


	public void removeDisabilityAppraisalByIdAhcsChannelProcess(String idAhcsChannelProcess);
}
