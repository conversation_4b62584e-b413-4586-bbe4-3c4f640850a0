package com.paic.ncbs.claim.service.settle.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.settle.PolicyEnendorsementVO;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.settle.EndorsementMapper;
import com.paic.ncbs.claim.model.vo.duty.DutyEndorsementVO;
import com.paic.ncbs.claim.service.settle.EndorsementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Transactional
@Service("endorsementService")
public class EndorsementServiceImpl implements EndorsementService {

    @Autowired
    private EndorsementMapper endorsementMapper;

    private static final String SYSTEM = "system";

    @Override
    public void mergeEndorsementInfo(EndorsementDTO endorsement) {
        String reportNo = endorsement.getReportNo();
        Integer caseTimes = endorsement.getCaseTimes();
        EndorsementDTO oldEndorsement = endorsementMapper.selectByReportNoAndCaseTime(reportNo, caseTimes);
        if(null == oldEndorsement){
            endorsementMapper.insertEndorsementInfo(endorsement);
        }else{
            endorsementMapper.updateEndorsementInfo(endorsement);
        }
    }

    @Override
    public EndorsementDTO getByReportNoAndCaseTime(String reportNo, Integer caseTimes) {
        return endorsementMapper.selectByReportNoAndCaseTime(reportNo, caseTimes);
    }

    @Override
    public void deleteByPolicyPayId(String reportNo, Integer caseTimes) {
        endorsementMapper.deleteByPolicyPayId(reportNo, caseTimes);
    }

    @Override
    public void addEndorsementInfo(EndorsementDTO endorsementInfo) {
        LogUtil.audit("#保存批单信息,入参endorsementInfo=" + endorsementInfo);
        endorsementMapper.insertEndorsementInfo(endorsementInfo);
    }

    @Override
    public void modifyEndorsementInfo(EndorsementDTO endorsementInfo) {
        endorsementMapper.updateEndorsementInfo(endorsementInfo);
    }

    @Override
    public void autoGenerateEndorsement(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPays) {
        LogUtil.audit("---- 进入批单流程 ---");
        boolean isGenerateEndorsement = isGenerateEndorsement(policyPays);
//        if(!isGenerateEndorsement){
//            LogUtil.audit("报案号：{} 无理算金额，无法生成批单信息！",reportNo);
//            return;
//        }
        List<PolicyEnendorsementVO> policyEnendorsements = new ArrayList<>();
        for (PolicyPayDTO policy : policyPays) {
//            if(!hasAutoSettled(policy)){
//                continue;
//            }
            PolicyEnendorsementVO policyEnendorsement = new PolicyEnendorsementVO();
            policyEnendorsement.setPolicyNo(policy.getPolicyNo());
            List<DutyEndorsementVO> dutyEndorsements = getDutyEndorsement(policy);
            if(CollectionUtils.isEmpty(dutyEndorsements)){
                continue;
            }
            policyEnendorsement.setDetailArr(dutyEndorsements);
            policyEnendorsements.add(policyEnendorsement);
        }

        EndorsementDTO endorsement = endorsementMapper.selectByReportNoAndCaseTime(reportNo,caseTimes);
        if (endorsement != null){
            endorsement.setUpdatedBy(SYSTEM);
            endorsement.setCaseTimes(caseTimes);
            endorsement.setReportNo(reportNo);
            endorsement.setEndorsementRemark(JSON.toJSONString(policyEnendorsements));
            endorsementMapper.updateEndorsementInfo(endorsement);
            LogUtil.audit("报案号：{} 更新批单信息",reportNo);
        }else {
            endorsement = new EndorsementDTO();
            endorsement.setIdAhcsEndorsement(UuidUtil.getUUID());
            endorsement.setCreatedBy(SYSTEM);
            endorsement.setUpdatedBy(SYSTEM);
            endorsement.setCaseTimes(caseTimes);
            endorsement.setReportNo(reportNo);
            endorsement.setEndorsementRemark(JSON.toJSONString(policyEnendorsements));
            endorsementMapper.insertEndorsementInfo(endorsement);
            LogUtil.audit(" 保存批单信息，报案号：{}",reportNo);
        }


        LogUtil.audit("---- 批单流程结束 ---");
    }

    @Override
    public void autoGenerateEndorsement4TPA(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPays) {
        LogUtil.audit("---- 进入批单流程 ---");
        boolean isGenerateEndorsement = isGenerateEndorsement(policyPays);
//        if(!isGenerateEndorsement){
//            LogUtil.audit("报案号：{} 无理算金额，无法生成批单信息！",reportNo);
//            return;
//        }
        List<PolicyEnendorsementVO> policyEnendorsements = new ArrayList<>();
        for (PolicyPayDTO policy : policyPays) {
//            if(!hasAutoSettled(policy)){
//                continue;
//            }
            PolicyEnendorsementVO policyEnendorsement = new PolicyEnendorsementVO();
            policyEnendorsement.setPolicyNo(policy.getPolicyNo());
            List<DutyEndorsementVO> dutyEndorsements = getDutyEndorsement4TPA(policy);
            policyEnendorsement.setDetailArr(dutyEndorsements);
            policyEnendorsements.add(policyEnendorsement);
        }

        EndorsementDTO endorsement = endorsementMapper.selectByReportNoAndCaseTime(reportNo,caseTimes);
        if (endorsement != null){
            endorsement.setUpdatedBy(SYSTEM);
            endorsement.setCaseTimes(caseTimes);
            endorsement.setReportNo(reportNo);
            endorsement.setEndorsementRemark(JSON.toJSONString(policyEnendorsements));
            endorsementMapper.updateEndorsementInfo(endorsement);
            LogUtil.audit("报案号：{} 更新批单信息",reportNo);
        }else {
            endorsement = new EndorsementDTO();
            endorsement.setIdAhcsEndorsement(UuidUtil.getUUID());
            endorsement.setCreatedBy(SYSTEM);
            endorsement.setUpdatedBy(SYSTEM);
            endorsement.setCaseTimes(caseTimes);
            endorsement.setReportNo(reportNo);
            endorsement.setEndorsementRemark(JSON.toJSONString(policyEnendorsements));
            endorsementMapper.insertEndorsementInfo(endorsement);
            LogUtil.audit(" 保存批单信息，报案号：{}",reportNo);
        }

        LogUtil.audit("---- 批单流程结束 ---");
    }

    private boolean isGenerateEndorsement(List<PolicyPayDTO> policyPays){
        if(CollectionUtils.isEmpty(policyPays)){
            return false;
        }
        for (PolicyPayDTO policy : policyPays) {
            if(hasAutoSettled(policy)){
                return true;
            }
        }
        return false;
    }

    private boolean hasAutoSettled(PolicyPayDTO policy){
        for (PlanPayDTO plan : policy.getPlanPayArr()) {
            for (DutyPayDTO duty : plan.getDutyPayArr()) {
                if(hasAutoSettled(duty)){
                    return true;
                }
            }
        }
        LogUtil.audit("报案号：{} 没有自动理算金额！",policy.getReportNo());
        return false;
    }

    private boolean hasAutoSettled(DutyPayDTO duty){
        for (DutyDetailPayDTO detail : duty.getDutyDetailPayArr()) {
            BigDecimal autoSettleAmount  = detail.getAutoSettleAmount();
            if(autoSettleAmount != null && autoSettleAmount.compareTo(BigDecimal.ZERO) > 0){
                return true;
            }
        }
        return false;
    }

    private List<DutyEndorsementVO> getDutyEndorsement(PolicyPayDTO policy){
        List<DutyEndorsementVO> dutyEndorsements = new ArrayList<>();
        List<PlanPayDTO> plans = policy.getPlanPayArr();
        for (PlanPayDTO plan : plans) {
            for (DutyPayDTO duty : plan.getDutyPayArr()) {
                String settleReason = duty.getSettleReason();
                if(StringUtils.isEmptyStr(settleReason)){
                    continue;
                }
                BigDecimal dutyAmount = duty.getSettleAmount();
//                if(!hasAutoSettled(duty)){
//                    continue;
//                }
                DutyEndorsementVO dutyEndorsement = new DutyEndorsementVO();
                dutyEndorsement.setDutyAmount(dutyAmount);
                dutyEndorsement.setDutyCode(duty.getDutyCode());
                dutyEndorsement.setDutyName(duty.getDutyName());
                dutyEndorsement.setAdjustmentTextArea(settleReason);
                dutyEndorsements.add(dutyEndorsement);
            }
        }
        return dutyEndorsements;
    }


    private List<DutyEndorsementVO> getDutyEndorsement4TPA(PolicyPayDTO policy){
        List<DutyEndorsementVO> dutyEndorsements = new ArrayList<>();
        List<PlanPayDTO> plans = policy.getPlanPayArr();
        for (PlanPayDTO plan : plans) {
            for (DutyPayDTO duty : plan.getDutyPayArr()) {
                BigDecimal dutyAmount = duty.getSettleAmount();

                DutyEndorsementVO dutyEndorsement = new DutyEndorsementVO();
                dutyEndorsement.setDutyAmount(dutyAmount);
                dutyEndorsement.setDutyCode(duty.getDutyCode());
                dutyEndorsement.setDutyName(duty.getDutyName());
                dutyEndorsement.setAdjustmentTextArea(duty.getSettleReason());
                dutyEndorsements.add(dutyEndorsement);
            }
        }
        return dutyEndorsements;
    }
}
