package com.paic.ncbs.claim.controller.common;

import cn.wesure.cmq.CmqTemplate;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.mq.MqMessageRecordEntity;
import com.paic.ncbs.claim.service.mqcompensation.MqCompensationService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Description mq补发控制层
 * @date 2023-06-12 9:45
 */
@RestController
@RequestMapping("public/app/mq")
@Api(tags = {"公共mq补发控制器"})
@Slf4j
public class MqCompensationController {


    @Autowired
    private MqCompensationService mqCompensationService;

    @Autowired
    private CmqTemplate cmqTemplate;

    @PostMapping("sendMessage")
    public ResponseResult sendMessage() {
        //查询失败记录
        List<MqMessageRecordEntity> messageRecordEntityList = mqCompensationService.getFailureRecordList();
        log.info("MqCompensationController sendMessage start");
        //发送mq
        if (messageRecordEntityList == null || messageRecordEntityList.size() < 1) {
            log.info("MqCompensationController sendMessage messageRecordEntityList is empty");
            return ResponseResult.success();
        }
        log.info("MqCompensationController sendMessage messageRecordEntityList size = {}",messageRecordEntityList.size());
        for(MqMessageRecordEntity messageRecord: messageRecordEntityList){
            String mqResp = "";
            try {
                //重新发送mq消息
                mqResp = cmqTemplate.sendTopic(messageRecord.getTopic(), messageRecord.getMessageBody());
                log.info("MqCompensationController.sendMessage, mqResp={}", mqResp);
            }catch (Exception e){
                log.error("mq补发报错",e);
                mqCompensationService.updateMqSendStatus(messageRecord, false);
                continue;
            }
            //更新表格
            if(StringUtils.isEmptyStr(mqResp)) {
                mqCompensationService.updateMqSendStatus(messageRecord, false);
            }else {
                mqCompensationService.updateMqSendStatus(messageRecord, true);
            }
        }
        log.info("MqCompensationController sendMessage end");
        return ResponseResult.success();
    }
}
