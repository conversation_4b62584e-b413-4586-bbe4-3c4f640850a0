package com.paic.ncbs.claim.service.settle.factor.impl.formula;

import com.paic.ncbs.claim.common.enums.CalculationFactorEnum;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.formula.FormulaService;
import org.springframework.stereotype.Service;

/**
 * 伤残理算公式：任明细保额 * 伤残比例
 */
@Service
public class DisabilityFormulaServiceImpl implements FormulaService {
    @Override
    public String getFormula(DutyDetailPayDTO detail) {
        return CalculationFactorEnum.BASE_AMOUNT_PAY_IMPL.getCode() +
                "*" +
                CalculationFactorEnum.DISABILITY_RATE_IMPL.getCode();
    }
}
