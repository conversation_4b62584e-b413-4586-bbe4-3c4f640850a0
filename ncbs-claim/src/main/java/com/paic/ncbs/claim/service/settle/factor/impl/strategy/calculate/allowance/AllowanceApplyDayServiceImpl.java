package com.paic.ncbs.claim.service.settle.factor.impl.strategy.calculate.allowance;

import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * 津贴天数
 */
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service
public class AllowanceApplyDayServiceImpl extends CalculateAmountService {

    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {

        paramsDTO.getSettleFactor().setAllowanceApplyDay(nvl(paramsDTO.getPersonBenefitDTO().getHospitalDays(), 0));
        paramsDTO.getSettleFactor().setCalculateAmount(nvl(paramsDTO.getPersonBenefitDTO().getHospitalDays(), 0));

    }
}
