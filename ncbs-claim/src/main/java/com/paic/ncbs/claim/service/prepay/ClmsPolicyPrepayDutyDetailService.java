package com.paic.ncbs.claim.service.prepay;

import com.paic.ncbs.claim.model.dto.prepayinfo.ClmsPolicyPrepayDutyDetailDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO;
import com.paic.ncbs.claim.model.vo.ahcs.PrePayCaseVO;

import java.util.List;

/**
 * 预赔责任明细表(ClmsPolicyPrepayDutyDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-15 11:01:25
 */
public interface ClmsPolicyPrepayDutyDetailService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsPolicyPrepayDutyDetailDTO queryById(String id);

    /**
     * 新增数据
     *
     * @param dtoList   实例对象
     * @param preCaseVO
     * @return 实例对象
     */
    void saveDutyDetail(List<DutyPrepayInfoDTO> dtoList);

    /**
     * 修改数据
     *
     * @param clmsPolicyPrepayDutyDetail 实例对象
     * @return 实例对象
     */
    ClmsPolicyPrepayDutyDetailDTO update(ClmsPolicyPrepayDutyDetailDTO clmsPolicyPrepayDutyDetail);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

    /**
     * 根据报案号 赔付次数，预赔次数查询责任明细信息
     * @param reportNo
     * @param caseTimes
     * @param subTimes
     */
    List<ClmsPolicyPrepayDutyDetailDTO> getDutyDetailInfo(String reportNo, Integer caseTimes, Integer subTimes);
}
