package com.paic.ncbs.claim.controller.openapi;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.service.wzemployer.CaseWZEmployerListService;
import com.paic.ncbs.claim.service.wzemployer.WZListUploadRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 任务提醒接收接口
 * 接收任务提醒消息
 */
@Api(tags = "任务提醒接口")
@Slf4j
@RestController
@RequestMapping("/public/wzCase")
public class WZCaseInfoController {

    @Autowired
    private CaseWZEmployerListService caseWZEmployerListService;

    @Autowired
    private WZListUploadRecordService wzListUploadRecordService;

    /**
     * 生产微众雇责理赔清单
     * @return
     */
    @ApiOperation(value = "生产微众雇责理赔清单")
    @GetMapping(value = "/createWZEmployerList")
    public ResponseResult<Object> createWZEmployerList(@RequestParam("uploadDate") String uploadDate){
        caseWZEmployerListService.createWZEmployerList(uploadDate);
        return ResponseResult.success("");
    }

    /**
     * 获取微众雇责理赔清单上传记录
     * @return
     */
    @ApiOperation(value = "查询微众雇责理赔清单上传记录")
    @GetMapping(value = "/getUploadRecordUrl")
    public ResponseResult<String> getUploadRecordUrl(@RequestParam("uploadDate") String uploadDate){
        return ResponseResult.success(wzListUploadRecordService.getUploadRecordUrl(uploadDate));
    }
}