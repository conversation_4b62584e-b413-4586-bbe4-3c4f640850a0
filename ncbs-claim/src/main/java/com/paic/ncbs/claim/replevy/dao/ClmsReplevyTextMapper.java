package com.paic.ncbs.claim.replevy.dao;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyText;
import com.paic.ncbs.claim.replevy.vo.ClmsReplevyTextVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 追偿审核信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Mapper
public interface ClmsReplevyTextMapper extends BaseDao<ClmsReplevyText> {
    /**
     * 根据主键查询数据
     * @return
     */
   List<ClmsReplevyText> getReplevyTextList(@Param("replevyNo") String replevyNo,@Param("replevyChargeId") String replevyChargeId,@Param("opinionType") String opinionType);

    /**
     * 查询审批意见数据
     * @param clmsReplevyTextVo
     * @return
     */
    List<ClmsReplevyTextVo> selectClmsReplevyTextVo(ClmsReplevyTextVo clmsReplevyTextVo);
}
