package com.paic.ncbs.claim.replevy.dao;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.math.BigDecimal;

public interface ClmsRelatedActualReceiptMapper extends BaseDao<ClmsRelatedActualReceipt> {
    List<ClmsRelatedActualReceipt> getRelatedActualReceiptByEntity(ClmsRelatedActualReceipt clmsRelatedActualReceipt);
    List<ClmsRelatedActualReceipt> getListGroupByBankTransFlowNo(ClmsRelatedActualReceipt clmsRelatedActualReceipt);
    List<ClmsRelatedActualReceipt> getSumByBankTransFlowNo(@Param("replevyNo")String replevyNo);
   /**
    * 根据批次号更新冻结标志
    *
    * @param batchNo 批次号
    * @param flag    标志
    */
   void updateFreezeFlagByBatchNo(@Param("batchNo") String batchNo, @Param("flag") String flag);

    void updateSelectiveByPrimaryKey(ClmsRelatedActualReceipt clmsRelatedActualReceipt);

    List<ClmsRelatedActualReceipt> getListByBusinessId(String businessId);
    int updateBatchNo(String batchNo, String reportNo, Integer caseTimes, Integer subTimes);

    /**
     * 根据业务ID查询核销金额总和
     * @param businessId 业务ID（追偿明细ID）
     * @return 核销金额总和
     */
    BigDecimal getTotalWriteOffAmountByDetailId(@Param("businessId") String businessId);
    /**
     * 根据明细Id删除数据
     * @param replevyDetailId
     * @return
     */
    int deleteByBusinessId(String replevyDetailId);
    ClmsRelatedActualReceipt getFreezeData(String reportNo, Integer subTimes, String freezeFlag);
    void updateFreezeFlagByReportNo(String reportNo, Integer subTimes, String receiptType,String userId, String freezeFlag);
}
