package com.paic.ncbs.claim.controller.common;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.RapeCheckUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

//import com.paic.ncbs.claim.mq.RabbitProducer;


@Api(tags = "机构信息")
@RestController
@RequestMapping("/report")
@Slf4j
public class DepartmentController extends BaseController {

	@Autowired
	private DepartmentDefineService departmentDefineService;

	@RequestMapping(value = "/getDepartmentByDepartmentCode", method = RequestMethod.GET)
	@ApiOperation(value = "获取二级机构信息")
	public ResponseResult<List<DepartmentVO>> getDepartmentByDepartmentCode() {
		String departmentCode = "2";
		List<DepartmentDefineEntity> departmentList = departmentDefineService.getDepartmentByDepartmentCode(departmentCode);
		List<DepartmentVO> departments = new ArrayList<>();
		for (DepartmentDefineEntity d : departmentList) {
			departments.add(changeVO(d));
		}
		return ResponseResult.success(departments);
	}

	private DepartmentVO changeVO(DepartmentDefineEntity departmentDefine) {
		DepartmentVO department = new DepartmentVO();
		department.setDepartmentCode(departmentDefine.getDepartmentCode());
		department.setDepartmentChineseName(departmentDefine.getDepartmentChineseName());
		department.setDepartmentAbbrName(departmentDefine.getDepartmentAbbrName());
		department.setDepartmentLevel(departmentDefine.getDepartmentLevel());
		department.setUpperDepartmentCode(departmentDefine.getUpperDepartmentCode());
		return department;
	}

	@RequestMapping(value = "/getSubDepartment", method = RequestMethod.GET)
	@ApiOperation(value = "获取三级机构信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "departmentCode", value = "核责机构", required = true, dataType = "String", dataTypeClass=String.class, paramType = "Query")
	})
	public ResponseResult<List<DepartmentVO>> getSubDepartment(String departmentCode) {
		RapeCheckUtil.checkParamEmpty(departmentCode, "机构码");
		List<DepartmentDefineEntity> departmentDefines = departmentDefineService.getSubDepartment(departmentCode);
		List<DepartmentVO> departments = new ArrayList<DepartmentVO>();
		for (DepartmentDefineEntity d : departmentDefines) {
			departments.add(changeVO(d));
		}
		return ResponseResult.success(departments);

	}

	@GetMapping("/getPermissionDepartment")
	public ResponseResult<List<DepartmentDTO>> getPermissionDepartment(){
		return ResponseResult.success(departmentDefineService.getLevel2AndParent(WebServletContext.getDepartmentCode()));
	}


}
