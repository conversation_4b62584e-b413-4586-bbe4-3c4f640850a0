package com.paic.ncbs.claim.common.util;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ListUtils {

    public static final Integer GROUP_NUM = 100;

    public static boolean isEmptyList(List list) {
        if (null == list || list.size() == 0) {
            return true;
        } else {
            return false;
        }

    }

    public static boolean isNotEmpty(List list) {
        return !isEmptyList(list);
    }

    public static String getStringWithSeparator(List<String> list, String separator) {
        if (isEmptyList(list)) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (Iterator<String> iterator = list.iterator(); iterator.hasNext(); ) {
            sb.append(iterator.next());
            if (iterator.hasNext()) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }

    public static <T> List<List<T>> getListByGroup(List<T> list, int groupPerNum) {
        List<List<T>> result = new ArrayList<>();
        if (ListUtils.isNotEmpty(list)) {
            int listSize = list.size();
            int totalGroup = (int) Math.ceil((double) listSize / (double) groupPerNum);
            for (int i = 0; i < totalGroup; i++) {
                int startIndex = i * groupPerNum;
                int endIndex = (i + 1) * groupPerNum > listSize ? listSize : (i + 1) * groupPerNum;
                result.add(list.subList(startIndex, endIndex));
            }
        }
        return result;
    }

}
