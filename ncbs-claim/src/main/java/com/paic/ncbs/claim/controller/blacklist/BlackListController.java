package com.paic.ncbs.claim.controller.blacklist;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListMapper;
import com.paic.ncbs.claim.model.vo.blacklist.ClmsBlackListVO;
import com.paic.ncbs.claim.service.blacklist.ClmsBlackListService;
import com.paic.ncbs.claim.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2025-07-11
 */
@Api(tags = "黑名单")
@RestController
@RequestMapping("/blacklist/blackListAction")
public class BlackListController extends BaseController {

    @Autowired
    private ClmsBlackListService blackListService;
    @Autowired
    private ClmsBlackListMapper clmsBlackListMapper;

    @ApiOperation("黑名单-获取审批通过和无需审批黑名单信息")
    @PostMapping(value = "/getBlackList")
    public ResponseResult<PageResult<ClmsBlackListVO>> getBlackList(@RequestBody ClmsBlackListVO clmsBlackListVO) throws Exception {
        return ResponseResult.success(blackListService.getBlackListByCondition(clmsBlackListVO));
    }

    @ApiOperation("黑名单-根据id查询黑名单信息")
    @GetMapping(value = "/getBlackListById/{id}")
    public ResponseResult<ClmsBlackListVO> getBlackListById(@PathVariable("id") String id) throws Exception {
        return ResponseResult.success(blackListService.getBlackListById(id));
    }

    @ApiOperation("黑名单-根据条件查询黑名单信息")
    @PostMapping(value = "/getBlackListByCondition")
    public ResponseResult<PageResult<ClmsBlackListVO>> getBlackListByCondition(@RequestBody ClmsBlackListVO clmsBlackListVO) throws Exception {
        return ResponseResult.success(blackListService.getBlackListByCondition(clmsBlackListVO));
    }

    @ApiOperation("黑名单-保存黑名单信息")
    @PostMapping(value = "/saveBlackList")
    public ResponseResult<Object> saveBlackList(@RequestBody ClmsBlackListVO clmsBlackListVO) throws Exception {
        blackListService.saveBlackList(clmsBlackListVO);
        return ResponseResult.success();
    }

    @ApiOperation("黑名单-修改、启用、禁用黑名单信息")
    @PostMapping(value = "/updateBlackList")
    public ResponseResult<Object> updateBlackList(@RequestBody ClmsBlackListVO clmsBlackListVO) throws Exception {
        blackListService.updateBlackList(clmsBlackListVO);
        return ResponseResult.success();
    }

}
