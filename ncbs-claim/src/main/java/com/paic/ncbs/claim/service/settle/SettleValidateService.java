package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.settle.CoinsureInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;


import java.util.List;
import java.util.Map;

public interface SettleValidateService {


	void checkSettle(List<PolicyPayDTO> policyPays, List<PaymentItemComData> paymentItems, String reportNo, Integer caseTimes) throws GlobalBusinessException;

	void checkSettled(List<PolicyPayDTO> policyPays, List<PaymentItemComData> paymentItems) throws GlobalBusinessException;

	void checkEndorsement(List<PolicyPayDTO> policyPays, EndorsementDTO endorsement) throws GlobalBusinessException;

    void validDutyDetailNull(String reportNo, Integer caseTimes);

	boolean hasSettlePay(List<PolicyPayDTO> policyPays);

	void checkCoinsureRadio(CoinsureInfoDTO coinsureInfo) throws GlobalBusinessException;

	void checkPolicyPay(List<PolicyPayDTO> policyPays,String reportNo, Integer caseTimes);

	/**
	 * 理算-核赔批单-赔付信息 进行校验反洗钱信息接口
	 * @param reportNo
	 * @param caseTimes
	 * @param paymentItemVOList
	 * @param paymentInfoMap
	 */
	void checkAntiMoneyLaundering(String reportNo, Integer caseTimes, List<PaymentItemComData> paymentItemVOList, Map<String, PaymentInfoDTO> paymentInfoMap);

    void checkBankDetail(List<PaymentItemComData> paymentItems, Map<String, PaymentInfoDTO> paymentInfoMap);

	void checkLastPaymentItem(List<PaymentItemComData> paymentItems, String reportNo, Integer caseTimes);
}
