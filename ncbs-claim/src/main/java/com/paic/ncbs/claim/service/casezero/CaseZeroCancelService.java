package com.paic.ncbs.claim.service.casezero;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.vo.casezero.ZeroCancelAuditInfoVO;

import java.util.List;
import java.util.Map;

public interface CaseZeroCancelService {

    CaseZeroCancelDTO getZeroCancelApplyInfo(String reportNo, Integer caseTimes, String status) throws GlobalBusinessException;

    void addCaseZeroCancelApply(CaseZeroCancelDTO zeroCancelDTO) throws GlobalBusinessException;

    void saveCaseZeroCancelApply(CaseZeroCancelDTO zeroCancelDTO);

    void saveCaseZeroCancelAudit(CaseZeroCancelDTO zeroCancelDTO);

    boolean getIsContinueByReportNo(String reportNo, Integer caseTimes);

    void checkCaseZeroCancelApply(CaseZeroCancelDTO zeroCancelDTO)throws GlobalBusinessException;

    boolean isExistApplyTaskNotCompleted(CaseZeroCancelDTO zeroCancelDTO);

    List<ZeroCancelAuditInfoVO> getAuditInfoList(String reportNo, Integer caseTimes);

    CaseZeroCancelDTO getLastZeroCancelInfo(String reportNo, Integer caseTimes);

    Map<String, String> getReasonMap() throws GlobalBusinessException;


    /**
     * 自动案件零注流程处理（包含自动零注审核）
     *
     * @param reportNo
     * @param caseTimes
     * @param applyType
     * @param applyReasonCode
     * @param applyReasonDetails
     * @param verifyPass
     */
    void autoCaseZeroCancelProcess(String reportNo, Integer caseTimes, String applyType, String applyReasonCode,
                                   String applyReasonDetails, Boolean verifyPass);

}
