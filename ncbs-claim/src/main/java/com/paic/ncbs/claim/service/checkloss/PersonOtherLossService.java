package com.paic.ncbs.claim.service.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PersonOtherLossDTO;

import java.util.List;

public interface PersonOtherLossService {

    public void savePersonOtherLoss(List<PersonOtherLossDTO> personOtherLossList, String reportNo, Integer caseTimes, String taskId, String idAhcsChannelProcess);

    public List<PersonOtherLossDTO> getPersonOtherLoss(String reportNo, Integer caseTimes, String status, String taskId, String channelProcessId);

}
