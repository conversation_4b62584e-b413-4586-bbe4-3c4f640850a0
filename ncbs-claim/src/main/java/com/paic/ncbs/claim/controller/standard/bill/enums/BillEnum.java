package com.paic.ncbs.claim.controller.standard.bill.enums;

import cn.hutool.core.util.StrUtil;

public enum BillEnum {
    RISK_BILL_200("RISK_BILL_200","非承保药品"),
    RISK_BILL_201("RISK_BILL_201","非承保诊疗项目"),
    RISK_BILL_202("RISK_BILL_202","非医疗费用"),
    RISK_BILL_203("RISK_BILL_203","康复、疗养"),
    RISK_BILL_204("RISK_BILL_204","心理治疗"),
    RISK_BILL_205("RISK_BILL_205","皮肤及美容相关"),
    RISK_BILL_206("RISK_BILL_206","视力矫正"),
    RISK_BILL_207("RISK_BILL_207","体检"),
    RISK_BILL_208("RISK_BILL_208","牙科"),
    RISK_BILL_209("RISK_BILL_209","被保人不符"),
    RISK_BILL_210("RISK_BILL_210","发票日大于报案日"),
    RISK_BILL_211("RISK_BILL_211","慢性病相关费用"),
    RISK_BILL_212("RISK_BILL_212","生育费用"),
    RISK_BILL_213("RISK_BILL_213","精神疾病"),
    RISK_BILL_299("RISK_BILL_299","其他");
    private String code;
    private String name;

    BillEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public static String getName(String type) {
        if(StrUtil.isEmpty(type)){
            return null;
        }
        for (BillEnum caseStatus : BillEnum.values()) {
            if (type.equals(caseStatus.getCode())) {
                return caseStatus.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
