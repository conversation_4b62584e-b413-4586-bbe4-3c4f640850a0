package com.paic.ncbs.claim.service.checkloss;

import com.paic.ncbs.claim.model.dto.settle.FlightDelayDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyFilghtInfoVO;
import com.paic.ncbs.claim.model.vo.duty.FlightDelayVO;

/**
 *
 * @description 航班延误
 */
public interface FlightDelayService {

    public void saveFlighterDelay(FlightDelayDTO flightDelayDTO);

    /**
     *
     * @Description: 删除航班延误
     */
    public void removeFlightDelay(String reportNo, Integer caseTimes, String taskCode, String channelProcessId);

    /**
     *
     * @Description: 获取航班延误信息
     */
    public FlightDelayDTO getFlighterDelay(String reportNo, Integer caseTimes, String status, String taskCode, String channelProcessId);

    /**
     * @Description: 页面查询航班数据,查询不到取航联查询
     * @param flightDelayVO
     */
    public FlightDelayVO flightDelayQueryInfo(FlightDelayVO flightDelayVO);

    /**
     * 
     * @Description: 更新航延数据状态（航延自动使用）
     */
    public void updateAutoFlightDelayStatus(String reportNo, Integer caseTimes, String status, String taskCode);

    /**
     * 
     * @Description: 更新客票信息
     */
    public void updateFlightDelayTicketInfo(FlightDelayDTO flightDelayDTO);

    /**
     * @Description: 核责查询航班信息优化(2020.1.15号优化)
     */
    DutyFilghtInfoVO getFlightInfoByDuty(FlightDelayVO flightDelayVO);
    
    /**
     * 更新航班验证信息
     * @param flightDelay
     * @return
     */
    public void modifyFlighterTravelVerify(FlightDelayDTO flightDelay);
}
