package com.paic.ncbs.claim.service.policy.impl;

import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.model.dto.policy.ClaimRecordDTO;
import com.paic.ncbs.claim.model.dto.report.CustomerDTO;
import com.paic.ncbs.claim.dao.mapper.other.ClaimRecordMapper;
import com.paic.ncbs.claim.model.dto.report.RecordDutyInfo;
import com.paic.ncbs.claim.service.policy.ClaimRecordService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ClaimRecordServiceImpl implements ClaimRecordService {

    @Autowired
    private ClaimRecordMapper claimRecordMapper;
    @Autowired
    private RiskPropertyService riskPropertyService;

    @Override
    public List<CustomerDTO> getCustomerByPolicy(ClaimRecordDTO claimRecordDTO) {
        return claimRecordMapper.getCustomerByPolicy(claimRecordDTO);
    }

    @Override
    public List<CustomerDTO> getUnResolvedReportNo(ClaimRecordDTO claimRecordDTO) {
        return claimRecordMapper.getUnResolvedReportNo(claimRecordDTO);
    }

    @Override
    public List<RecordDutyInfo> getRecordDutyByPolicy(ClaimRecordDTO claimRecordDTO) {
        return claimRecordMapper.getRecordDutyByPolicy(claimRecordDTO);
    }

    @Override
    public void getRiskProperty(ClaimRecordDTO claimRecordDTO){
        if(!riskPropertyService.displayRiskProperty(null,claimRecordDTO.getPolicyNo())){
            return;
        }
        List<CustomerDTO> riskPropertyList = claimRecordMapper.getRiskPropertyByPolicy(claimRecordDTO);
        if(ListUtils.isNotEmpty(riskPropertyList)){
            claimRecordDTO.setExistsPay(true);
            claimRecordDTO.setRiskPropertyList(riskPropertyList);
        }
        
		List<CustomerDTO> unResolvedRiskPropertyList = claimRecordMapper
				.getUnResolvedRiskPropertyByPolicy(claimRecordDTO);
		if (ListUtils.isNotEmpty(unResolvedRiskPropertyList)) {
			claimRecordDTO.setUnResolved(true);
			claimRecordDTO.setUnResolvedRiskPropertyList(unResolvedRiskPropertyList);
		}
    }
}
