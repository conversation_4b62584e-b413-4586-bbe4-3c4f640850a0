package com.paic.ncbs.claim.service.settle.factor.impl.strategy.calculate.base;

import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * 责任剩余保额
 */
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service
public class BaseAmountPayServiceImpl extends CalculateAmountService {

    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {

        paramsDTO.getSettleFactor().setBaseAmountPay(nvl(paramsDTO.getDutyDetailPayDTO().getBaseAmountPay(),0));
        paramsDTO.getSettleFactor().setCalculateAmount(nvl(paramsDTO.getDutyDetailPayDTO().getBaseAmountPay(),0));

    }
}
