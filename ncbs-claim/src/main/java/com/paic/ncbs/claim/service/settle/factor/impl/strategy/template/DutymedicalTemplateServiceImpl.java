package com.paic.ncbs.claim.service.settle.factor.impl.strategy.template;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.template.AbstractTemplateService;
import org.springframework.stereotype.Service;

@Service
public class DutymedicalTemplateServiceImpl extends AbstractTemplateService {
    @Override
    public void setTemplate(DutyDetailPayDTO detailPayDTO) {

    }

    @Override
    public boolean isCustomized() {
        return true;
    }

    @Override
    public String customizeReason(DutyDetailPayDTO detailPayDTO) {
        return "";
    }
}
