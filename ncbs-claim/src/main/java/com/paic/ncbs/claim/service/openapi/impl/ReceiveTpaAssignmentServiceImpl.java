package com.paic.ncbs.claim.service.openapi.impl;

import cn.hutool.core.util.ObjectUtil;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.openapi.TpaAssignmentDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.service.bpm.impl.BpmServiceImpl;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.openapi.ReceiveTpaAssignmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


/**
 * 接收TPA案件分配任务信息
 */
@Slf4j
@Service
public class ReceiveTpaAssignmentServiceImpl implements ReceiveTpaAssignmentService {
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private BpmServiceImpl bpmServiceImpl;
    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;

    /**
     * 处理分配信息
     * @param tpaAssignmentDTO
     */
    @Override
    public void receive(TpaAssignmentDTO tpaAssignmentDTO) {
        //入参必填校验
        checkInputData(tpaAssignmentDTO);

        //更新案件处理人为TPA分配的公司的对应处理人:
        //查询核心是否已操作了案件
        Integer count = taskInfoMapper.getTpaTaskInfo(tpaAssignmentDTO.getReportNo(),tpaAssignmentDTO.getCaseTimes());
        if(count>1){
            throw new GlobalBusinessException("案件任务已被核心业务手动处理,请耐心等待！");
        }
        //把报案号，赔付次数，报案状态为0未处理且是报案跟踪节点的数据更新
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        //更新条件
        taskInfoDTO.setCaseTimes(tpaAssignmentDTO.getCaseTimes());
        taskInfoDTO.setReportNo(tpaAssignmentDTO.getReportNo());
        taskInfoDTO.setStatus(BpmConstants.TASK_STATUS_PENDING);
        taskInfoDTO.setTaskDefinitionBpmKey(BpmConstants.OC_REPORT_TRACK);
        //是否自动分发
        String isAutoDispatch = tpaAssignmentDTO.getIsAutoDispatch();
        String tpaCom = tpaAssignmentDTO.getTpaCom();
        ReportInfoExEntity reportInfoExEntity = reportInfoExMapper.getAcceptanceNoByReportNo(tpaAssignmentDTO.getReportNo());
        if (!StringUtils.isEmptyStr(isAutoDispatch) && isAutoDispatch.equals("Y")) {
            bpmServiceImpl.autoDispatchRuleToUser(taskInfoDTO);
            if (null != reportInfoExEntity) {
               reportInfoExEntity.setCompanyId("SYSTEM");
               reportInfoExEntity.setUpdatedDate(new Date());
               reportInfoExMapper.updateByPrimaryKeySelective(reportInfoExEntity);
            }
        } else {
            taskInfoDTO.setAssigner(tpaCom);
            taskInfoDTO.setAssigneeName(tpaAssignmentDTO.getTpaComName());
        }
        if (StringUtils.isEmptyStr(taskInfoDTO.getAssigner())) {
            taskInfoDTO.setUpdatedBy(ConstValues.SYSTEM_NAME);
        } else {
            taskInfoDTO.setUpdatedBy(taskInfoDTO.getAssigner());
        }
        taskInfoDTO.setUpdatedDate(new Date());
        taskInfoMapper.updateTaskAssigner(taskInfoDTO);
        if (!StringUtils.isEmptyStr(tpaCom) && ("ensurlink".equals(tpaCom) || "ensurlink01".equals(tpaCom) || "TPA-ensurlink".equals(tpaCom) || "TPAEL".equals(tpaCom))) {
            if (null != reportInfoExEntity) {
                reportInfoExEntity.setCompanyId("42300010");
                reportInfoExEntity.setUpdatedDate(new Date());
                reportInfoExMapper.updateByPrimaryKeySelective(reportInfoExEntity);
            }
        }
        operationRecordService.insertDispatchRecord(taskInfoDTO.getReportNo(), taskInfoDTO.getTaskDefinitionBpmKey(), true, taskInfoDTO.getAssigner(), ConstValues.SYSTEM_NAME);
    }

    /**
     * 入参必填校验
     * @param tpaAssignmentDTO
     */
    private void checkInputData(TpaAssignmentDTO tpaAssignmentDTO) {
        if(StringUtils.isEmptyStr(tpaAssignmentDTO.getTpaCom())){
            //throw new GlobalBusinessException("TpaCom不能为空！");
        }
        if(StringUtils.isEmptyStr(tpaAssignmentDTO.getTpaComName())){
            //throw new GlobalBusinessException("TapComName不能为空！");
        }
        if(StringUtils.isEmptyStr(tpaAssignmentDTO.getReportNo())){
            throw new GlobalBusinessException("报案号不能为空！");
        }
        if(ObjectUtil.isEmpty(tpaAssignmentDTO.getCaseTimes())){
            throw new GlobalBusinessException("赔付次数不能为空！");
        }

    }
}
