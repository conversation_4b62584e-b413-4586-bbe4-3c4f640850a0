package com.paic.ncbs.claim.service.settle.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.service.rule.AutoRuleService;
import com.paic.ncbs.claim.service.settle.DutySettleRuleFilterService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * P02P00109001 少儿门诊全能版
 */
@Slf4j
@Service
public class DutySettleRuleFilterServiceImpl implements DutySettleRuleFilterService {
    @Autowired
    private AutoRuleService autoRuleService;
    /**
     * 方案编码集合
     */
    @Autowired
    private CaseClassMapper caseClassMapper;
    @Override
    public void dutyRuleFilter(String reportNo, Integer caseTimes, List<PolicyPayDTO> copyPolicyPays) {
        log.info("报案号={},核责前保单信息={}",reportNo, JsonUtils.toJsonString(copyPolicyPays));
        try{
            //调用核责
            autoRuleService.execDutyConfirmRule(reportNo,caseTimes,copyPolicyPays);
            log.info("报案号={},规则引擎核责后保单信息={}",reportNo, JsonUtils.toJsonString(copyPolicyPays));
        }catch (Exception e){
            log.info("调用规则引擎异常"+e.getMessage());
            log.info("调用规则引擎异常了走默认兜底方法");
            defaultDeal(reportNo,caseTimes,copyPolicyPays);
            log.info("报案号={},默认核责后保单信息={}",reportNo, JsonUtils.toJsonString(copyPolicyPays));
        }

    }

    /**
     * 默认处理方法
     * @param reportNo
     * @param caseTimes
     * @param copyPolicyPays
     */
    private void defaultDeal(String reportNo, Integer caseTimes, List<PolicyPayDTO> copyPolicyPays){
        List<String> caseClassList = caseClassMapper.getCaseClassList(reportNo, caseTimes, BpmConstants.CHECK_DUTY);
        for (PolicyPayDTO policy : copyPolicyPays) {
            List<PlanPayDTO> planList = policy.getPlanPayArr();
            for (PlanPayDTO plan :planList) {
                List<DutyPayDTO>  dutyList=  plan.getDutyPayArr();
                for (DutyPayDTO duty : dutyList) {
                    //模拟规则 如果是方案P02P00109001，
                    List<DutyDetailPayDTO> detailList=  duty.getDutyDetailPayArr();
                    for (DutyDetailPayDTO detail :detailList) {
                        //默认处理方法
                        if(isDutyMatchCaseClass(caseClassList,detail.getDutyDetailType())){
                            detail.setIsSettleFlag("Y");//Y-表示需要理算出金额
                        }else{
                            detail.setIsSettleFlag("N");
                        }

                    }

                }
            }
        }
    }

    /**
     * 设置值
     * @param duty
     */
    private void setDetailSettleFlag(DutyPayDTO duty) {
        List<DutyDetailPayDTO> detailList=  duty.getDutyDetailPayArr();
        for (DutyDetailPayDTO detail :detailList) {
            detail.setIsSettleFlag(duty.getIsSettleFlag());
        }
    }
    public boolean isDutyMatchCaseClass(List<String> caseClassList,String dutyDetailType){
        if(ListUtils.isNotEmpty(caseClassList) && !Collections.disjoint(caseClassList, SettleConst.NO_PERSONAL_CLASS)){
            return true;
        }
        List<String> detailTypeCaseClass =  SettleConst.DETAIL_TYPE_CASE_CLASS_MAP.get(dutyDetailType);
        LogUtil.audit("--校验责任类型和当前案件类别是否匹配，caseClass:{},dutyDetailType:{}",
                caseClassList.toString(),SettleConst.DETAIL_TYPE_NAME.get(dutyDetailType));
        if (caseClassList.isEmpty() || detailTypeCaseClass == null || detailTypeCaseClass.isEmpty()){
            LogUtil.audit("--匹配结果: 未匹配上");
            return false;
        }
        boolean result = !Collections.disjoint(caseClassList,detailTypeCaseClass);
        LogUtil.audit("--匹配结果:{}", result ? "匹配" : "不匹配");
        return result;

    }
}
