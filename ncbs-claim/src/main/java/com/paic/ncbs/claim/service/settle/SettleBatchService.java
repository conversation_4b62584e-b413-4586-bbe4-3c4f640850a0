package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.model.dto.settle.SettleBatchInfoDTO;
import com.paic.ncbs.claim.model.vo.settle.SttleBatchInfoVO;

public interface SettleBatchService {

    SttleBatchInfoVO getSettleAmountsSum(String reportNo, Integer caseTimes);

    SettleBatchInfoDTO getPartSettleSum(String reportNo, Integer caseTimes, String caseNo);

    SettleBatchInfoDTO getPartAcmAndProSum(String reportNo,Integer caseTimes,String caseNo);


}
