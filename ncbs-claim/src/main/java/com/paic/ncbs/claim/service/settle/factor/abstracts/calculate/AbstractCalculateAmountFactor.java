package com.paic.ncbs.claim.service.settle.factor.abstracts.calculate;


import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EveryDayBillInfoDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;

import java.util.Objects;

/**
 * 因子抽象类
 */
public abstract class AbstractCalculateAmountFactor {
    private CalculateAmountService calculateAmountService;

    /**
     * 设置方法
     * @param calculateAmountService
     */
    public void setCalculateReasonAmountInterface(CalculateAmountService calculateAmountService) {
        this.calculateAmountService = calculateAmountService;
    }

    /**
     * 计算
     * @return
     */
    public final void getAmout(CalculateParamsDTO paramsDTO){
        if(Objects.isNull(calculateAmountService)){
            return ;
        }
        //前置钩子函数 可以扩展重写
        if(isBeforBoolean(paramsDTO)){
            //可以扩展重写
            isBefordeal(paramsDTO);
        }
        calculateAmountService.calculate(paramsDTO);
    }

    /**
     * 前置处理函数
     * @return
     */
    public  EveryDayBillInfoDTO isBefordeal(CalculateParamsDTO paramsDTO){
        return paramsDTO.getEveryDayBillInfoDTO();
    }

    /**
     * 前置置钩子函数
     * @return
     */
    public  boolean isBeforBoolean(CalculateParamsDTO paramsDTO){
        return false;
    }

}
