package com.paic.ncbs.claim.service.taskdeal.impl;

import com.paic.ncbs.claim.dao.mapper.taskdeal.TechnicProductInfoMapper;
import com.paic.ncbs.claim.service.taskdeal.TechnicProductInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TechnicProductInfoServiceImpl implements TechnicProductInfoService {

	@Autowired
	private TechnicProductInfoMapper technicProductInfoMapper;
	
	@Override
	public String getTechnicProductCode(String productCode) {
		return technicProductInfoMapper.getTechnicProductCode(productCode);
	}
}
