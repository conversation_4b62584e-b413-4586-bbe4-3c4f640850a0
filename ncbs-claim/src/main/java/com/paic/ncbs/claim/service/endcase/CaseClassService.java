package com.paic.ncbs.claim.service.endcase;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.duty.DutySurveyVO;
import com.paic.ncbs.claim.model.vo.endcase.CaseClassDefineVO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDefineDTO;

import java.util.List;

public interface CaseClassService {


	List<CaseClassDefineVO> getCaseClassDefines(String reportNo, int caseTimes);

	List<CaseClassDefineDTO> getCaseClassDefineList() throws GlobalBusinessException;

	List<String> getCaseClassCodeList(String reportNo, Integer caseTimes, String taskId);

    boolean importantCase(String reportNo, int caseTimes);

	List<String> getCaseClassList(String reportNo, int caseTimes,String taskId);

	/**
	 * 保存案件类别信息
	 */
	List<String>  saveClassData(DutySurveyVO dutySurveyVO);

	/**
	 * TPA保存案件类别信息：功能跟立案按钮对应的是一样的，就值多设置了下创建时间
	 */
	List<String>  saveTPAClassData(DutySurveyVO dutySurveyVO);

}
