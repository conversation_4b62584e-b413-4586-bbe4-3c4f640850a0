package com.paic.ncbs.claim.replevy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.math.BigDecimal;

/**
 * 险种和责任信息查询结果DTO
 * 只包含SQL查询出来的字段
 */
@Data
@ApiModel("险种和责任信息查询结果DTO")
public class PlanAndDutyQueryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty("报案号")
    private String reportNo;
    
    @ApiModelProperty("初始化标识")
    private String initFlag;
    
    @ApiModelProperty("险种信息列表")
    private List<PlanInfoDTO> planPayList;
    
    @ApiModelProperty("责任信息列表")
    private List<DutyInfoDTO> dutyPayList;
    
    @ApiModelProperty("估损责任记录列表")
    private List<EstimateDutyInfoDTO> estimateDutyRecordList;
    
    /**
     * 险种信息DTO
     */
    @Data
    @ApiModel("险种信息DTO")
    public static class PlanInfoDTO implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty("险种代码")
        private String planCode;
        
        @ApiModelProperty("险种名称")
        private String planName;
        
        @ApiModelProperty("责任信息列表")
        private List<DutyInfoDTO> dutyList;
    }
    
    /**
     * 责任信息DTO
     */
    @Data
    @ApiModel("责任信息DTO")
    public static class DutyInfoDTO implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty("责任代码")
        private String dutyCode;
        
        @ApiModelProperty("责任名称")
        private String dutyName;
        
        @ApiModelProperty("剩余金额")
        private BigDecimal remainingAmount;
    }
    
    /**
     * 估损责任信息DTO
     */
    @Data
    @ApiModel("估损责任信息DTO")
    public static class EstimateDutyInfoDTO implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty("责任代码")
        private String dutyCode;
        
        @ApiModelProperty("责任名称")
        private String dutyName;
        
        @ApiModelProperty("险种代码")
        private String planCode;
    }
}
