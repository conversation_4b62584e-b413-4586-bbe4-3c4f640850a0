package com.paic.ncbs.claim.replevy.vo;

import lombok.Data;

@Data
public class CashFlowSearchVo {

    /** 对应字段：ourBank_account,备注：我方银行卡号 */
    private String ourBankAccount;
    //交易开始日期
    private String transFromDate;
    //交易结束日期
    private String transEndDate;
    //客户打款银行卡号，与客户打款银行账号名称二选一必传
    private String partnerBankAccount;
    //客户打款银行账号名称，与客户打款银行卡号二选一必传
    private String partnerBankAccountName;
    //银行交易流水号
    private String bankTransFlowNo;
}
