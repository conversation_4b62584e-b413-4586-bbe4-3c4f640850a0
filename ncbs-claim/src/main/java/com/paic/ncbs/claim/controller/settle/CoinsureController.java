package com.paic.ncbs.claim.controller.settle;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ahcs/do/app/CoinsureAction")
@Api(tags = {"人工理算页共保信息"})
public class CoinsureController extends BaseController {

    @Autowired
    private CoinsureService coinsureService;

    @GetMapping("/getCoinsureByPolicyNo")
    public ResponseResult<List<CoinsureDTO>> getCoinsureByPolicyNo(@RequestParam("policyNo") String policyNo) {

        return ResponseResult.success(coinsureService.getCoinsureByPolicyNo(policyNo));
    }


}
 