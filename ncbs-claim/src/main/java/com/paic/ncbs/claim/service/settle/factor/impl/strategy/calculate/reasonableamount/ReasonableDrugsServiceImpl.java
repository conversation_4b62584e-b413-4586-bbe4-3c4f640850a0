package com.paic.ncbs.claim.service.settle.factor.impl.strategy.calculate.reasonableamount;

import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EveryDayBillInfoDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * 药房责任合理费用计算
 */
@Service("reasonableDrugsServiceImpl")
public class ReasonableDrugsServiceImpl extends CalculateAmountService {
    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {
        if(Objects.isNull(paramsDTO.getEveryDayBillInfoDTO())){
            return;
        }
        EveryDayBillInfoDTO dto=paramsDTO.getEveryDayBillInfoDTO();
        BigDecimal reasonableAmount =nvl(dto.getBillAmount(), 0);
        if(reasonableAmount.compareTo(BigDecimal.ZERO) < 0){
            reasonableAmount = BigDecimal.ZERO;
        }
        paramsDTO.getSettleFactor().setReasonableAmount(reasonableAmount);
        paramsDTO.getSettleFactor().setCalculateAmount(reasonableAmount);
    }
}
