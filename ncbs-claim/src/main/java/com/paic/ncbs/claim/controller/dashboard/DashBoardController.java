package com.paic.ncbs.claim.controller.dashboard;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.dashboard.JobDashBoardVo;
import com.paic.ncbs.claim.model.vo.dashboard.ManageDashBoardReqVo;
import com.paic.ncbs.claim.model.vo.dashboard.ManageDashBoardResVo;
import com.paic.ncbs.claim.model.vo.dashboard.ManageDashBoardVo;
import com.paic.ncbs.claim.service.dashboard.JobDashBoardService;
import com.paic.ncbs.claim.service.dashboard.ManageDashBoardService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/dashboard")
@Api(tags = {"理赔看板"})
@Slf4j
public class DashBoardController {

    @Resource
    private JobDashBoardService jobDashBoardService;
    @Autowired
    private ManageDashBoardService manageDashBoardService;

    @Operation(summary = "job-part", description = "作业看板")
    @GetMapping("/jobPart/{dashBoardPeriod}")
    public ResponseResult<JobDashBoardVo> jobPart(@PathVariable(value = "dashBoardPeriod") String dashBoardPeriod){
        UserInfoDTO user = WebServletContext.getUser();
        JobDashBoardVo response = jobDashBoardService.query(user.getUserCode(), user.getUserName(), dashBoardPeriod);
        return ResponseResult.success(response);
    }

    @Operation(summary = "manage-part", description = "管理看板")
    @PostMapping(value = "/managePart")
    public ResponseResult<Object> mngPart(@RequestBody ManageDashBoardReqVo manageDashBoardReqVo){
        UserInfoDTO user = WebServletContext.getUser();
        ManageDashBoardResVo response = manageDashBoardService.query(user.getUserCode(), user.getUserName(), manageDashBoardReqVo);
        return ResponseResult.success(response);
    }

}
