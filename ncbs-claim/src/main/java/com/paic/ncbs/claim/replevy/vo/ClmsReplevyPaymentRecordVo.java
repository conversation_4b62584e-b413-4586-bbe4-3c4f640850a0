package com.paic.ncbs.claim.replevy.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，表clms_replevy_payment_record的VO对象<br/>
 * 对应表名：clms_replevy_payment_record,备注：追偿与收付交互表
 *
 */
@Data
public class ClmsReplevyPaymentRecordVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键id */
	private Integer id;
	/** 对应字段：report_no,备注：报案号 */
	private String reportNo;
	/** 对应字段：replevy_no,备注：追偿案件号 */
	private String replevyNo;
	/** 对应字段：replevy_times,备注：追偿次数 */
	private Integer replevyTimes;
	/** 对应字段：case_times,备注：赔付次数 */
	private Integer caseTimes;
	/** 对应字段：request_type,备注：请求类型--C01流水查询,C02流水使用,C03收费确认上报 */
	private String requestType;
	/** 对应字段：business_no,备注：业务编码 */
	private String businessNo;
	/** 对应字段：trans_date,备注：交易日期时间戳 */
	private Date transDate;
	/** 对应字段：direction_type,备注：收款付款--collection收款 payment付款 */
	private String directionType;
	/** 对应字段：trans_amount,备注：交易金额 */
	private BigDecimal transAmount;
	/** 对应字段：trans_purpose,备注：用途 */
	private String transPurpose;
	/** 对应字段：trans_abstract,备注：摘要 */
	private String transAbstract;
	/** 对应字段：payment_flowNo,备注：银企直联系统支付流水号 */
	private String paymentFlowno;
	/** 对应字段：banktrans_flow_no,备注：银行流水号 */
	private String banktransFlowNo;
	/** 对应字段：ourBank_account,备注：我方银行卡号 */
	private String ourbankAccount;
	/** 对应字段：partner_bank_account,备注：他方银行卡号 */
	private String partnerBankAccount;
	/** 对应字段：partner_bank_name,备注：他方银行名称 */
	private String partnerBankName;
	/** 对应字段：partner_bank_branch_name,备注：他方网点名称 */
	private String partnerBankBranchName;
	/** 对应字段：partner_bank_account_name,备注：他方账户名称 */
	private String partnerBankAccountName;
	/** 对应字段：receipt_file_cosurl,备注：银行回单在cos中的url */
	private String receiptFileCosurl;
	/** 对应字段：receipt_file_cosid,备注：银行回单在cos中的id */
	private String receiptFileCosid;
	/** 对应字段：post_script,备注：附言 */
	private String postScript;
	/** 对应字段：write_off_remain_amount,备注：核销余额 */
	private BigDecimal writeOffRemainAmount;
	/*** 冻结/解冻标志***/
	private String freezeFlag;
	/** 对应字段：valid_flag,备注：有效标志 */
	private String validFlag;
	/** 对应字段：flag,备注：标志字段 */
	private String flag;
	/** 对应字段：request_param,备注：请求报文 */
	private String requestParam;
	/** 对应字段：response_param,备注：响应报文 */
	private String responseParam;
	/** 对应字段：response_code,备注：响应代码--0000代表成功，9999代表失败 */
	private String responseCode;
	/** 对应字段：created_by,备注：创建人 */
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	private Date sysCtime;
	/** 对应字段：updated_by,备注：修改人员 */
	private String updatedBy;
	/** 对应字段：sys_utime,备注：修改时间 */
	private Date sysUtime;

}
