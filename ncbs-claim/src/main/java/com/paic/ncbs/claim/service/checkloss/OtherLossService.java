package com.paic.ncbs.claim.service.checkloss;


import com.paic.ncbs.claim.model.dto.duty.OtherLossDTO;

import java.util.List;

public interface OtherLossService {


    public void saveOtherLoss(OtherLossDTO otherLossDTO);


    public void removeOtherLoss(String reportNo, Integer caseTimes, String taskCode, String channelProcessId);

    void updateEffective(OtherLossDTO otherLossDTO);


    public List<OtherLossDTO> getOtherLoss(String reportNo, Integer caseTimes);

}
