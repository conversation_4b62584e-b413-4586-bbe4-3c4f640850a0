package com.paic.ncbs.claim.service.checkloss.impl;

import com.paic.ncbs.claim.dao.entity.checkloss.BaseRimsOpencoverTreatyEntity;
import com.paic.ncbs.claim.dao.mapper.checkloss.BaseRimsOpencoverTreatyEntityMapper;
import com.paic.ncbs.claim.service.checkloss.BaseRimsOpencoverTreatyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BaseRimsOpencoverTreatyServiceImpl implements BaseRimsOpencoverTreatyService {

	@Autowired
	private BaseRimsOpencoverTreatyEntityMapper baseRimsOpencoverTreatyMapper;

	public BaseRimsOpencoverTreatyEntity getInfoByTreatyNo(String treatyNo) {

		return baseRimsOpencoverTreatyMapper.getInfoByTreatyNo(treatyNo);
	}
}
