package com.paic.ncbs.claim.service.taskdeal.impl;


import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TravelAlertInvoiceMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TravelAlertMapper;
import com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertDTO;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.taskdeal.TravelAlertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class TravelAlertServiceImpl implements TravelAlertService {

    @Autowired
    TravelAlertMapper travelAlertDao;

    @Autowired
    TravelAlertInvoiceMapper travelAlertInvoiceDao;

    @Autowired
    private AhcsCommonService ahcsCommonService;

    @Override
    @Transactional
    public void saveTravelAlert(List<TravelAlertDTO> travelAlertList, String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
        List<TravelAlertDTO> existTrAlertDTOList = travelAlertDao.getTravelAlert(reportNo, caseTimes, null, taskCode, channelProcessId);
        if (ListUtils.isNotEmpty(existTrAlertDTOList)) {
            travelAlertDao.removeTravelAlert(reportNo, caseTimes, taskCode, channelProcessId);
        }
        ahcsCommonService.batchHandlerTransactionalWithArgs(TravelAlertMapper.class, travelAlertList, ListUtils.GROUP_NUM, "addTravelAlert");
    }

    @Override
    public void removeTravelAlert(String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
        travelAlertDao.removeTravelAlert(reportNo, caseTimes, taskCode, channelProcessId);
    }

    @Override
    public List<TravelAlertDTO> getTravelAlert(String reportNo, Integer caseTimes, String status, String taskCode, String channelProcessId) {
        return travelAlertDao.getTravelAlert(reportNo, caseTimes, status, taskCode, channelProcessId);
    }

    @Override
    public void insertTravelAlert(TravelAlertDTO travelAlertDTO) {
        String reportNo = travelAlertDTO.getReportNo();
        Integer caseTimes = travelAlertDTO.getCaseTimes();
        String taskCode = travelAlertDTO.getTaskCode();
        List<TravelAlertDTO> existTrAlertDTOList = travelAlertDao.getTravelAlert(reportNo, caseTimes, null, taskCode, null);
        if (ListUtils.isNotEmpty(existTrAlertDTOList)) {
            travelAlertDao.removeTravelAlert(reportNo, caseTimes, taskCode, null);
        }
        List<TravelAlertDTO> travelAlertList = new ArrayList<>();
        travelAlertList.add(travelAlertDTO);
        ahcsCommonService.batchHandlerTransactionalWithArgs(TravelAlertMapper.class, travelAlertList, ListUtils.GROUP_NUM, "addTravelAlert");
    }

    @Override
    public void deleteTravelAlertInvoice(String idAhcsTravelAlertInvoice) {
        travelAlertInvoiceDao.deleteTravelAlertInvoice(idAhcsTravelAlertInvoice);
    }

}
