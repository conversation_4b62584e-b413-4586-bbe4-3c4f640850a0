package com.paic.ncbs.claim.service.endcase.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseExMapper;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseExService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WholeCaseBaseExServiceImpl extends BaseServiceImpl<WholeCaseBaseExEntity> implements WholeCaseBaseExService {

	@Autowired
    private WholeCaseBaseExMapper wholeCaseBaseExMapper;

    @Override
    public List<WholeCaseBaseExEntity> getWholeCaseBaseEx(String reportNo) {
        return wholeCaseBaseExMapper.selectByReportNo(reportNo);
    }

    @Override
    public BaseDao<WholeCaseBaseExEntity> getDao() {
        return wholeCaseBaseExMapper;
    }
}
