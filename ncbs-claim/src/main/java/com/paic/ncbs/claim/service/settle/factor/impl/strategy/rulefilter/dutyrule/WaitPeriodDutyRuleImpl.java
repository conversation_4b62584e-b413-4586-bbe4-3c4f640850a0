package com.paic.ncbs.claim.service.settle.factor.impl.strategy.rulefilter.dutyrule;

import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.rulefilter.dutyrule.DutyRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;



/**
 * 特殊产品 等待期规则实现
 */
@Service
public class WaitPeriodDutyRuleImpl implements DutyRuleService {
    /**
     * 产品编码集合
     */
    @Value("${product.productCodeList:02P00040}")
    private List<String> productCodeList;
    @Autowired
    private MedicalBillInfoMapper medicalBillInfoMapper;
    @Override
    @Transactional
    public void  exeDutyRule(DutyDetailPayDTO detail, ClaimCaseDTO bo) {

    }
}
