package com.paic.ncbs.claim.service.settle.factor.impl.bill;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.service.settle.factor.abstracts.settle.BillFilterService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.bill.ExecuteBillFilterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ExecuteBillFilterServiceImpl implements ExecuteBillFilterService {
    @Autowired
    private List<BillFilterService> billFilterServices;
    @Override
    public ClaimCaseDTO billFilter(ClaimCaseDTO claimCaseDTO) {
        if(CollectionUtil.isNotEmpty(billFilterServices)){
            billFilterServices.stream().forEach(billFilterService -> billFilterService.billFilter(claimCaseDTO));
        }
        log.info("执行发票规则");
        return claimCaseDTO;
    }
}
