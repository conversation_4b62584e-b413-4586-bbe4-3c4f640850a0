package com.paic.ncbs.claim.service.settle.factor.impl.strategy.attributes;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.DutyAttributeConst;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.attributes.DutyAttributeService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service
public class MedicalDutyAttributeServiceImpl implements DutyAttributeService {
    @Override
    public void setDutyDetailAttribute(DutyDetailPayDTO detail, Map<String, Optional<String>> attributes, Map<String, Map<String, String>> attributesdetailMap) {
        detail.setWaitDays(0);//初始化等待期为0
        detail.setPayProportion(BigDecimal.ONE);
        if (Objects.equals(detail.getBeInHospitalFlag(), BaseConstant.BEIN_HOSPITAL_FLAG)) {
            detail.setReasonableAmountType(BaseConstant.STRING_2);//只有门诊发票
        }
        if(CollectionUtil.isEmpty(attributes)){
            return;
        }
        if(attributes.containsKey(DutyAttributeConst.WAIT_DAYS)){
            String configDays = attributes.get(DutyAttributeConst.WAIT_DAYS).get();
            detail.setWaitDays(Integer.valueOf(configDays));
        }
        //每月赔付天数
        if (attributes.containsKey(DutyAttributeConst.EVERY_DAY_PAY_DAYS)) {
            String payDays= attributes.get(DutyAttributeConst.EVERY_DAY_PAY_DAYS).get();
            if(StringUtils.isNotEmpty(payDays)){
                detail.setConfigPayDays(Integer.valueOf(payDays));
            }

        }
        //年度赔付天数
        if (attributes.containsKey(DutyAttributeConst.YEARLY_PAY_DAYS)) {
            String payDays= attributes.get(DutyAttributeConst.YEARLY_PAY_DAYS).get();
            if(StringUtils.isNotEmpty(payDays)){
                detail.setConfiYearlyPayDays(Integer.valueOf(payDays));
            }

        }
        //360-是否区分社保：属性没有配置360肯定不需要区分社保，配置了360需要看对应的value值，值为0表示区分，否则就不区分（跟产品邹俊确认）
        if (attributes.containsKey(DutyAttributeConst.IS_DISTINGUISH_SOCIA_360)) {
            String value =attributes.get(DutyAttributeConst.IS_DISTINGUISH_SOCIA_360).get();
            if (Objects.equals("0", attributes.get(DutyAttributeConst.IS_DISTINGUISH_SOCIA_360).get())) {
                detail.setIsDistinguishSocia("Y");
                //如果是需要区分社保表示，取是否经医保结算属性
                if (!attributesdetailMap.isEmpty()) {
                    LogUtil.info("责任" + detail.getDutyCode() + "需要区分社保属性");
                    setAttributeValue(attributesdetailMap, detail);
                }

            } else {
                LogUtil.info("对应的360属性值不为0 按不区分社保处理");
                detail.setIsDistinguishSocia("N");
                //不区分社保 但有可能区分是否经医保结算，但目前没有这种业务数据
                if (!attributesdetailMap.isEmpty()) {
                    LogUtil.info("责任" + detail.getDutyCode() + "需要区分社保属性");
                    setAttributeValue(attributesdetailMap, detail);
                }
            }
        } else {
            LogUtil.info("不会存在这种业务数据和业务leao会议确认过");
            detail.setIsDistinguishSocia("N");//是否需要区分社保 ：N-不区分：不区分社保的赔付比列取外层配置的
        }
        if (attributes.containsKey(DutyAttributeConst.REMIT_AMOUNT)) {
            detail.setRemitAmount(nvl(new BigDecimal(attributes.get(DutyAttributeConst.REMIT_AMOUNT).get()), 0));
            //免赔额类型
            if (attributes.containsKey(DutyAttributeConst.REMIT_AMOUNT_TYPE)) {
                //免赔额类型
                detail.setRemitAmountType(attributes.get(DutyAttributeConst.REMIT_AMOUNT_TYPE).get());
            } else {
                LogUtil.info("责任:" + detail.getDutyCode() + "配置了免赔额属性但没有配置免赔额类型,默认为按次");
                detail.setRemitAmountType("0");
            }

        }
        if (attributes.containsKey(DutyAttributeConst.PAY_LIMIT_TYPE)) {
            detail.setPayLimitType(attributes.get(DutyAttributeConst.PAY_LIMIT_TYPE).get());
            if (attributes.containsKey(DutyAttributeConst.PAY_LIMIT_6)) {
                String payLimit = attributes.get(DutyAttributeConst.PAY_LIMIT_6).get();
                detail.setPayLimit(BigDecimalUtils.getBigDecimal(payLimit));
            } else {
                LogUtil.info("责任" + detail.getDutyCode() + "配置了赔付限额类型属性，但没有配置限额，默认不处理限额");
            }
        } else {
            LogUtil.info("责任" + detail.getDutyCode() + "没配置赔付限额类型，就不存在处理限额");
        }
        //若为固定赔付比例，则直接取
        if (attributes.containsKey(DutyAttributeConst.PAY_PROPORTION)) {
            detail.setPayProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributes.get(DutyAttributeConst.PAY_PROPORTION).get())), 1));
        } else if (attributes.containsKey(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD) || (attributes.containsKey(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD_364) && Objects.equals("2", attributes.get(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD_364)))) {
            detail.setPayProportionType("2");
        } else if (!attributes.containsKey(DutyAttributeConst.PAY_PROPORTION_NONSTANDARD_364)) {
            //没有配置赔付比例类型那就默认按固定比例100%
            detail.setPayProportion(BigDecimalUtils.toPercent(new BigDecimal(100)));
        }
    }
    /**
     * 组装属性
     *
     * @param attributesdetailMap
     * @param detail
     */
    private void setAttributeValue(Map<String, Map<String, String>> attributesdetailMap, DutyDetailPayDTO detail) {
        //经医保结算赔付比例
        if (attributesdetailMap.containsKey(DutyAttributeConst.IS_SOCIA_361_4000)) {
            //有社保经医保结算赔付比例
            detail.setIsSocMedicalProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.IS_SOCIA_361_4000)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.IS_SOCIA_361_4001)) {
            //有社保未经医保结算的赔付比列
            detail.setIsSocNoMedicalProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.IS_SOCIA_361_4001)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.IS_SOCIA_361_4002)) {
            //有社保没有经医保结算的赔付罚则
            detail.setIsSocPenaltyProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.IS_SOCIA_361_4002)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        //无社保的经医保结算的比列
        if (attributesdetailMap.containsKey(DutyAttributeConst.NO_SOCIA_362_4000)) {
            //无社保的 经医保结算的赔付比列
            detail.setNoSocMedicalProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.NO_SOCIA_362_4000)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.NO_SOCIA_362_4001)) {
            //无社保 未经医保结算的赔付比列
            detail.setNoSocNoMedicalProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.NO_SOCIA_362_4001)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.NO_SOCIA_362_4002)) {
            //无社保 未经医保结算的赔付罚则
            detail.setNoSocPenaltyProportion(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(DutyAttributeConst.NO_SOCIA_362_4002)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 1));
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.MEDICAL_361_1003)) {
            detail.setIsSocialMedicalSettle(attributesdetailMap.get(DutyAttributeConst.MEDICAL_361_1003)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE));
            LogUtil.info("责任" + detail.getDutyCode() + "不需要否区分是否经医保结算");
        }
        if (attributesdetailMap.containsKey(DutyAttributeConst.MEDICAL_362_1003)) {
            detail.setNoSocialMedicalSettle(attributesdetailMap.get(DutyAttributeConst.MEDICAL_362_1003)
                    .get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE));
            LogUtil.info("责任" + detail.getDutyCode() + "不需要否区分是否经医保结算");
        }

        LogUtil.info("责任={},责任属性={}", detail.getDutyCode(), JsonUtils.toJsonString(detail));
    }
}
