package com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

@Service
public class ExecuteSaveSettleServiceImpl implements ExecuteSaveSettleService{
    @Autowired
    private SaveSettleService saveSettleService;
    @Override
    @Transactional
    public void saveResult(ClaimCaseDTO claimCaseDTO) {
        if(Objects.equals("0",claimCaseDTO.getFlag())){
            saveSettleService.saveResult(claimCaseDTO);
        }else{
            LogUtil.info("重构推推新单重新理算={}",claimCaseDTO.getReportNo());
            saveSettleService.updateResutl(claimCaseDTO);
        }

    }
}
