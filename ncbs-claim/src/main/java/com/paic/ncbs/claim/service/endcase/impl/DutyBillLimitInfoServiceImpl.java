package com.paic.ncbs.claim.service.endcase.impl;

import com.paic.ncbs.claim.common.constant.DutyAttributeConst;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.common.PolicyDto;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.settle.DutyAttributeValueDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyPayInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.service.common.ClmsGetPolicyMonthInfoService;
import com.paic.ncbs.claim.service.endcase.DutyBillLimitInfoService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 *
 */
@Service
public class DutyBillLimitInfoServiceImpl implements DutyBillLimitInfoService {
    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;

    @Autowired
    private MedicalBillInfoMapper medicalBillInfoMapper;

    @Autowired
    private DutyAttributeMapper dutyAttributeMapper;

    @Autowired
    private DutyPayMapper dutyPayMapper;

    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    @Autowired
    private PolicyInfoMapper policyInfoMapper;

    @Value("#{${orgLimit.day}}")
    private Map<String,BigDecimal> orgDayLimitMap;

    @Value("${special.productPackage}")
    private String specialProductPackage;

    @Autowired
    private ClmsGetPolicyMonthInfoService clmsGetPolicyMonthInfoService;

    @Override
    @Transactional
    public void dealBIllLimtData(String reportNo, Integer caseTimes){
        if(!updateBillLimt(reportNo,caseTimes)){
            if(caseTimes>1){
                //重开的数据 需要把caseTimes-1次（也就是上一次生成的每日限额记录approval_status值为3：3-重开后失效）
                dutyBillLimitInfoMapper.updateUpCaseTimesHistory(reportNo,caseTimes-1);
            }
            LogUtil.info("更新日限额报案号={},赔付次数={}",reportNo,caseTimes);
            dutyBillLimitInfoMapper.updateByReportNo(reportNo,caseTimes);
            LogUtil.info("更新日限额完成报案号={},赔付次数={}",reportNo,caseTimes);
        }
        //更新通用赔付天数的数据为核赔通过
        dutyBillLimitInfoMapper.updateNoLimitInfo(reportNo,caseTimes);

    }

    public boolean updateBillLimt(String reportNo, Integer caseTimes) {
        //查询险种信息是否有配置了限额
        List<DutyAttributeValueDTO> dutyAttributeValueDTOList = dutyAttributeMapper.getDutyAttribute(reportNo);
        LogUtil.info("报案号={}下的责任属性配置信息={}",reportNo,JsonUtils.toJsonString(dutyAttributeValueDTOList));
        if(CollectionUtils.isEmpty(dutyAttributeValueDTOList)){
            return false ;
        }

        //获取当前报案号的最终理算金额
        List<DutyPayInfoDTO> dutyPayInfoDTOList = dutyPayMapper.getDutyPayAmount(reportNo,caseTimes);
        if(CollectionUtils.isEmpty(dutyPayInfoDTOList)){
           return false;
        }

        //根据报案号，赔付次数查询账单信息
        List<String> dateList = dutyBillLimitInfoMapper.getAllDate(reportNo,caseTimes);
        if(CollectionUtils.isEmpty(dateList)){
            return false;
        }

        //整案信息
        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase2(reportNo, caseTimes);
        String indemnityModel = "";
        if (null != wholeCaseBaseDTO) {
            indemnityModel = wholeCaseBaseDTO.getIndemnityModel();
        }

        List<PolicyInfoDTO> policyInfoListByReportNo = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
        String productPackageType = "";
        if (CollectionUtils.isNotEmpty(policyInfoListByReportNo)) {
            productPackageType = policyInfoListByReportNo.get(0).getProductPackageType();
        }

        LogUtil.info("报案号={}下的发票日期信息={}",reportNo,JsonUtils.toJsonString(dateList));
        LogUtil.info("报案号={}，的最终理算金额责任层级信息={}",reportNo,JsonUtils.toJsonString(dutyPayInfoDTOList));
        for (DutyPayInfoDTO dutyPay : dutyPayInfoDTOList) {
             //判断责任是否配置了限额信息：只有配置了日限额才做一下的校验逻辑
            List<DutyAttributeValueDTO> dutyAttList = dealDuty(dutyPay,dutyAttributeValueDTOList);
             if(CollectionUtils.isEmpty(dutyAttList)){
                LogUtil.info("责任编码={}没有配置每日限额属性信息",dutyPay.getDutyCode());
                continue;
             }


            //判断理算金额是否超出了每日限额
            BigDecimal dutyConfiLimit = BigDecimalUtils.getBigDecimal(dutyAttList.get(0).getAttributeValue());//属性配置的每日限额
            BigDecimal dutySettleAmount =BigDecimalUtils.getBigDecimal(dutyPay.getDutyPayAmount());//本次责任理算金额
            BigDecimal sumdutyConfiLimit=dutyConfiLimit.multiply(new BigDecimal(dateList.size()));
            //责任的本次理算金额 如果大于所有发票日期的限额就阻断,通融不校验此项
            Map<Date,BigDecimal> paymap=new HashMap<>();
            if(dutySettleAmount.compareTo(sumdutyConfiLimit)>0){
                if (!"6".equals(indemnityModel)) {
                    throw new GlobalBusinessException("责任编码"+dutyPay.getDutyCode()+"理算金额"+dutySettleAmount+"超出了"+dateList.size()+"天的总限额"+sumdutyConfiLimit);
                }
            }else{
                //本次理算金额没有超过配置的日限额
                paymap =  checkSettleMount(dateList,dutyConfiLimit,dutySettleAmount,dutyPay,reportNo,caseTimes,indemnityModel);
                LogUtil.info("报案号={}，判断本次责任在本次所有发票日的已赔付记录的金额paymap={}",reportNo,JsonUtils.toJsonString(paymap));
            }
            LogUtil.info("本次报案号={}查看是否分摊手动理算金额",reportNo);
            //把最终理算金额更新
            DutyBillLimitInfoDTO paramsDto= new DutyBillLimitInfoDTO();
            paramsDto.setPolicyNo(dutyAttList.get(0).getPolicyNo());
            paramsDto.setPlanCode(dutyAttList.get(0).getPlanCode());
            paramsDto.setDutyCode(dutyAttList.get(0).getDutyCode());
            paramsDto.setReportNo(reportNo);
            paramsDto.setCaseTimes(caseTimes);
            paramsDto.setBillDateList(dateList);
            List<DutyBillLimitInfoDTO> oldBillLimtInfoList = dutyBillLimitInfoMapper.getDutyBillDateInfo(paramsDto);
            BigDecimal sum =sumPayAmount(oldBillLimtInfoList);
            LogUtil.info("本次报案号={}下的责任编码={}下的发票赔付信息={}",reportNo,dutyAttList.get(0).getDutyCode(),JsonUtils.toJsonString(oldBillLimtInfoList));
            BigDecimal settle =BigDecimalUtils.getBigDecimal(dutyPay.getDutyPayAmount());//本次理算自动理算的金额
            BigDecimal residueSettle=settle;//责任的理算金额
            List<DutyBillLimitInfoDTO> updateList =new ArrayList<>();
            Date date=new Date();
            for (DutyBillLimitInfoDTO dto : oldBillLimtInfoList) {
                BigDecimal payAmount=BigDecimal.ZERO;
                if(!Objects.isNull(paymap)){
                    //paymap不为空
                    if(paymap.containsKey(dto.getBillDate())){
                        //如果已赔付的数据中有相同发票日期的数据，取出相同发票日的已赔付金额，需要计算发票日剩余赔付金额
                        payAmount = paymap.get(dto.getBillDate());//发票日期当天已支付的理算金额
                        LogUtil.info("报案号={}，发票日期当天已支付的理算金额dto={},paymap={}",reportNo,JsonUtils.toJsonString(dto),JsonUtils.toJsonString(paymap));
                    }
                }
                BigDecimal limitAmount;
                if(null != orgDayLimitMap && orgDayLimitMap.containsKey(productPackageType) && !"公立".equals(dto.getHospitalPropertyDes())){
                    limitAmount = orgDayLimitMap.get(productPackageType);
                }else {
                    String limit=dutyAttList.get(0).getAttributeValue();
                    limitAmount = new BigDecimal(limit);//责任属性配置的日限额
                }
                if(specialProductPackage.contains(productPackageType) && CollectionUtils.isNotEmpty(policyInfoListByReportNo)){
                    // 根据起止日期计算月数
                    List<PolicyMonthDto> monthDtoList =
                            clmsGetPolicyMonthInfoService.getPolicyMonthInfo(policyInfoListByReportNo.get(0).getInsuranceBeginTime(),
                                    policyInfoListByReportNo.get(0).getInsuranceEndTime());
                    PolicyMonthDto policyMonthDto =  getStartEndDate(dto.getBillDate(),monthDtoList);
                    if(null != policyMonthDto && 0 == policyMonthDto.getMonth()){
                        limitAmount = orgDayLimitMap.get(productPackageType);
                    }
                }
                LogUtil.info("报案号={}，dutyAttList={},配置的限额={}，支付金额={}",reportNo,JsonUtils.toJsonString(dutyAttList),BigDecimalUtils.toString(limitAmount),BigDecimalUtils.toString(payAmount));

                BigDecimal dutyResidueLimt=limitAmount.subtract(nvl(payAmount,0));

                if(dto.getSettleClaimAmount().compareTo(dutyResidueLimt)<=0){
                    if(residueSettle.compareTo(BigDecimal.ZERO)<=0){
                        dto.setSettleClaimAmount(BigDecimal.ZERO);
                        dto.setApprovalStatus("0");
                        dto.setIsDeleted("1");
                    }else{
                        //当前责任在发票日的理算金额小于发票日的剩余限额，那么发票日数据不做变动
                        if(residueSettle.compareTo(dutyResidueLimt)<=0){
                            if(settle.compareTo(sum)==0){
                                //自动理算金额和手动理算金额不变的情况
                                residueSettle=residueSettle.subtract(dto.getSettleClaimAmount());
                            }else{
                               //变化
                                dto.setSettleClaimAmount(residueSettle);
                                residueSettle=residueSettle.subtract(residueSettle);
                            }


                        }else{
                            if(sum.compareTo(settle)==0){
                                dto.setSettleClaimAmount(dto.getSettleClaimAmount());
                                residueSettle=residueSettle.subtract(dto.getSettleClaimAmount());
                            }else{
                                dto.setSettleClaimAmount(dutyResidueLimt);
                                residueSettle=residueSettle.subtract(dutyResidueLimt);
                            }

                        }
                        dto.setApprovalStatus("1");
                        dto.setIsDeleted("0");

                    }
                }else{
                   //当前发票日的理算金额大于发票日的剩余限额的情况
                    //如果剩余理算金额小于了剩下的日限额
                    if(residueSettle.compareTo(dutyResidueLimt)<=0){
                        dto.setSettleClaimAmount(residueSettle);
                        if(residueSettle.compareTo(BigDecimal.ZERO)==0){
                            dto.setApprovalStatus("0");
                            dto.setIsDeleted("1");  //为0的数据删除

                        }else{
                            dto.setApprovalStatus("1");
                            dto.setIsDeleted("0");
                            residueSettle=residueSettle.subtract(residueSettle);
                        }


                    }else {
                        dto.setSettleClaimAmount(dutyResidueLimt);
                        residueSettle=residueSettle.subtract(dutyResidueLimt);
                        dto.setApprovalStatus("1");
                        dto.setIsDeleted("0"); //为0表示不能删除

                    }
                }
                dto.setUpdatedDate(date);
                updateList.add(dto);
            }
            //还有剩余需要阻断提示,通融不校验此项
            if (!"6".equals(indemnityModel)) {
                if(residueSettle.compareTo(BigDecimal.ZERO)>0){
                    throw new GlobalBusinessException("手动理算金额超剩余日限额"+residueSettle+"元请重新理算");
                }
            }
            if(CollectionUtils.isNotEmpty(updateList)){
                dutyBillLimitInfoMapper.updateBatchDutyBillLimit(updateList);
            }

        }
        if(caseTimes>1){
            //重开的数据 需要把caseTimes-1次（也就是上一次生成的每日限额记录approval_status值为3：3-重开后失效）
            dutyBillLimitInfoMapper.updateUpCaseTimesHistory(reportNo,caseTimes-1);
        }

        return true;

    }

    private PolicyMonthDto getStartEndDate(Date billDate, List<PolicyMonthDto> monthDtoList) {

        for (PolicyMonthDto monthDto : monthDtoList) {
            if(billDate.compareTo(monthDto.getStartDate())>=0 && billDate.compareTo(monthDto.getEndDate())<=0){
                return monthDto;
            }
        }
        return null;
    }

    /**
     * 统计自动理算的总赔付金额
     * @param oldBillLimtInfoList
     */
    private BigDecimal sumPayAmount(List<DutyBillLimitInfoDTO> oldBillLimtInfoList) {
        BigDecimal sum=BigDecimal.ZERO;
        for (DutyBillLimitInfoDTO dto : oldBillLimtInfoList) {
            sum=sum.add(dto.getSettleClaimAmount());
        }
        return sum;
    }

    /**
     * 每日的金额上限校验
     *
     * @param dutyAttList
     * @param billDateList
     * @param reportNo
     * @param caseTimes
     * @param paymap
     */
    private void everyDayLimitAmountCheck(List<DutyAttributeValueDTO> dutyAttList, List<String> billDateList, String reportNo, Integer caseTimes, Map<Date, BigDecimal> paymap) {
        DutyBillLimitInfoDTO paramsDto= new DutyBillLimitInfoDTO();
        paramsDto.setPolicyNo(dutyAttList.get(0).getPolicyNo());
        paramsDto.setPlanCode(dutyAttList.get(0).getPlanCode());
        paramsDto.setDutyCode(dutyAttList.get(0).getDutyCode());
        paramsDto.setReportNo(reportNo);
        paramsDto.setCaseTimes(caseTimes);
        paramsDto.setBillDateList(billDateList);
        List<DutyBillLimitInfoDTO> list = dutyBillLimitInfoMapper.getDutyBillDateInfo(paramsDto);
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        BigDecimal configLimitAmount = BigDecimalUtils.getBigDecimal(dutyAttList.get(0).getAttributeValue());
        for (DutyBillLimitInfoDTO dto : list) {
           BigDecimal daySettleAmount=dto.getSettleClaimAmount();//本次发票日产生的理算金额
           if(Objects.nonNull(paymap)){
               if(paymap.containsKey(dto.getBillDate())){
                   BigDecimal dayPayMount = paymap.get(dto.getBillDate());//发票日已经结案支付的金额
                   BigDecimal dayresidueLimt=configLimitAmount.subtract(dayPayMount);//剩余日限额
                   //发票日已支付的金额
                   if(dayPayMount.compareTo(configLimitAmount)>0){
                       throw new GlobalBusinessException("责任编码"+dutyAttList.get(0).getDutyCode()+"在"+dto.getBillDate()+"日已赔付"+dayPayMount+"元，责任日限额为"+configLimitAmount+"元,本次理算金额为"+daySettleAmount+"元本日累计已超日限额");
                   }else{
                       if(daySettleAmount.compareTo(dayresidueLimt)>0){
                           throw new GlobalBusinessException("责任编码"+dutyAttList.get(0).getDutyCode()+"在"+dto.getBillDate()+"日已赔付"+dayPayMount+"元,本次理算金额为"+daySettleAmount+"元，本日合计理算金额已超日限额"+configLimitAmount+"元");
                       }
                   }
               }else{
                   if(daySettleAmount.compareTo(configLimitAmount)>0){
                       throw new GlobalBusinessException("责任编码"+dutyAttList.get(0).getDutyCode()+"在"+dto.getBillDate()+"日理算金额为"+daySettleAmount+"元已超日限额"+configLimitAmount+"元");
                   }
               }
           }else{
               if(daySettleAmount.compareTo(configLimitAmount)>0){
                   throw new GlobalBusinessException("责任编码"+dutyAttList.get(0).getDutyCode()+"在"+dto.getBillDate()+"日理算金额为"+daySettleAmount+"元已超日限额"+configLimitAmount+"元");
               }
           }

        }

    }



    /**
     * 查询本次报案号自动理算产生的费用
     * @param reportNo
     * @param caseTimes
     */
    private List<DutyBillLimitInfoDTO> getAutoSettleAmountByReportNo(String reportNo, Integer caseTimes) {
        List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOS = dutyBillLimitInfoMapper.getAutoSettleAmountByReportNo(reportNo,caseTimes);
       return dutyBillLimitInfoDTOS;
    }

    /**
     * 判断理算金额是否超日限额
     * 本次理算金额没有超过配置的日限额 ，需要在判断本次责任在本次所有发票日的已赔付记录的金额 加上本次理算金额有没有超过日限额
     *
     * @param billDateList
     * @param dutyConfiLimit
     * @param dutySettleAmount
     * @param dutyPay
     * @param reportNo
     * @param caseTimes
     */
    private  Map<Date,BigDecimal> checkSettleMount(List<String> billDateList, BigDecimal dutyConfiLimit, BigDecimal dutySettleAmount, DutyPayInfoDTO dutyPay, String reportNo, Integer caseTimes,String indemnityModel) {
        DutyBillLimitInfoDTO paramDto=new DutyBillLimitInfoDTO();
        paramDto.setPolicyNo(dutyPay.getPolicyNo());
        paramDto.setPlanCode(dutyPay.getPlanCode());
        paramDto.setDutyCode(dutyPay.getDutyCode());
        paramDto.setBillDateList(billDateList);
        List<DutyBillLimitInfoDTO>  dutyBillLimitInfoDTOS = dutyBillLimitInfoMapper.getAllPayDutyLimitDate(paramDto);//当前报案号下所有发票日已赔付的金额总和
        LogUtil.info("查询责任已赔付的金额入参={}，返回查询结果={}",JsonUtils.toJsonString(paramDto),JsonUtils.toJsonString(dutyBillLimitInfoDTOS));
        if(CollectionUtils.isEmpty(dutyBillLimitInfoDTOS)){
           return null;
        }
        //重开案件的已支付金额需要排除当前案件的已支付金额
        if(caseTimes>1){
            dutyBillLimitInfoDTOS= dutyBillLimitInfoDTOS.stream().filter(dutyBillLimitInfoDTO -> !Objects.equals(reportNo,dutyBillLimitInfoDTO.getReportNo())).collect(Collectors.toList());
        }
        Map<Date,BigDecimal> payMap=new HashMap<>();//记录每个发票日期已赔付的金额
        BigDecimal sumAmount=BigDecimal.ZERO;
        //按日期分组
        Map<Date,List<DutyBillLimitInfoDTO>> dutyMap = dutyBillLimitInfoDTOS.stream().collect(Collectors.groupingBy(DutyBillLimitInfoDTO:: getBillDate));
        for (Map.Entry<Date,List<DutyBillLimitInfoDTO>>  entry: dutyMap.entrySet()){
           Date key = entry.getKey();
           List<DutyBillLimitInfoDTO> dtos = entry.getValue();
           BigDecimal settleAmount=BigDecimal.ZERO;
            for (DutyBillLimitInfoDTO dto :dtos) {
                sumAmount=sumAmount.add(dto.getSettleClaimAmount());
                settleAmount=settleAmount.add(dto.getSettleClaimAmount());
            }
            payMap.put(key,settleAmount);

        }

        BigDecimal sumConfiLimit =dutyConfiLimit.multiply(new BigDecimal(billDateList.size()));
        BigDecimal residueLimt = sumConfiLimit.subtract(sumAmount);//总的限额减去已赔付的限额
        //判断总的理算金额不能超过总的限额：剩余日限额,通融不校验此项
        if (!"6".equals(indemnityModel)) {
            if(dutySettleAmount.compareTo(residueLimt)>0){
                throw new GlobalBusinessException("责任编码"+dutyPay.getDutyCode()+"在"+JsonUtils.toJsonString(billDateList)+"日，理算总金额"+dutySettleAmount+"大于总的剩余赔付限额"+residueLimt+",在"+JsonUtils.toJsonString(billDateList)+"日已赔付总金额"+sumAmount+"总赔付限额"+sumConfiLimit);
            }
        }
        return payMap;

    }



    /**
     * 判断责任是否配置了限额,并获取到限额那条数据并返回
     * @param dutyPay
     * @param dutyAttributeValueDTOList
     */
    private List<DutyAttributeValueDTO> dealDuty(DutyPayInfoDTO dutyPay, List<DutyAttributeValueDTO> dutyAttributeValueDTOList) {
        List<DutyAttributeValueDTO>  dtoList= dutyAttributeValueDTOList.stream().filter(dutyDto->dutyPay.getDutyCode().equals(dutyDto.getDutyCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(dtoList)){
            return null;
        }
        Map<String,String> attribueMap= getattribueMap(dtoList);
        if(attribueMap.containsKey(DutyAttributeConst.PAY_LIMIT_TYPE) && Objects.equals("2",attribueMap.get(DutyAttributeConst.PAY_LIMIT_TYPE)) && attribueMap.containsKey(DutyAttributeConst.PAY_LIMIT_6)){
            LogUtil.info("责任编码={}配置了每日限额,详细属性配置={}",dutyPay.getDutyCode(), JsonUtils.toJsonString(dtoList));
            return dtoList.stream().filter(dutyDo->Objects.equals(DutyAttributeConst.PAY_LIMIT_6,dutyDo.getAttributeCode())).collect(Collectors.toList());
        }
        return  null;
    }

    /**
     * 判断责任是否配置了日限额
     * @param dtoList
     * @return
     */
    private Map<String,String> getattribueMap(List<DutyAttributeValueDTO> dtoList) {
        Map<String,String> attribueMap=new HashMap<>();
        for (DutyAttributeValueDTO dto : dtoList) {
            attribueMap.put(dto.getAttributeCode(), dto.getAttributeValue());
        }
        return attribueMap;
    }

    @Override
    public void deleteByReportNoAndCaseTimes(String reportNo, Integer caseTimes) {
        dutyBillLimitInfoMapper.deleteByReportNoAndCaseTimes(reportNo, caseTimes);
    }

    @Override
    @Transactional
    public void saveList(List<DutyBillLimitInfoDTO> list) {
        LogUtil.info("保存限额数据入参={}，长度={}",JsonUtils.toJsonString(list),list.size());
        //先删再插入
        dutyBillLimitInfoMapper.deleteDutyBillByDutyCode(list.get(0));
        dutyBillLimitInfoMapper.saveList(list);

    }

    @Override
    @Transactional
    public void saveListBydutyDetailCode(List<DutyBillLimitInfoDTO> list) {
        LogUtil.info("保存限额数据入参={}，长度={}，明细编码：{}",JsonUtils.toJsonString(list),list.size(), list.get(0).getDutyDetailCode());
        //先删再插入
        dutyBillLimitInfoMapper.deleteDutyBillByDutyDetailCode(list.get(0));
        dutyBillLimitInfoMapper.saveList(list);

    }

    @Override
    @Transactional
    public void saveHistoryDutyLimit(Date billDate, DutyDetailPayDTO detail, BigDecimal settleAmount) {
        //大于0的才记录,
        if(settleAmount.compareTo(BigDecimal.ZERO)<=0){
            return;
        }
        DutyBillLimitInfoDTO dutyBillLimitInfoDTO = new DutyBillLimitInfoDTO();
        dutyBillLimitInfoDTO.setIdClmsDutyBillLimit(UuidUtil.getUUID());
        dutyBillLimitInfoDTO.setPolicyNo(detail.getPolicyNo());
        dutyBillLimitInfoDTO.setReportNo(detail.getReportNo());
        dutyBillLimitInfoDTO.setBillDate(billDate);
        dutyBillLimitInfoDTO.setBillAmount(settleAmount);
        dutyBillLimitInfoDTO.setSettleClaimAmount(settleAmount);
        dutyBillLimitInfoDTO.setCaseTimes(detail.getCaseTimes());
        dutyBillLimitInfoDTO.setDutyCode(detail.getDutyCode());
        dutyBillLimitInfoDTO.setPlanCode(detail.getPlanCode());
        //默认为0 核赔完结案更新为1
        dutyBillLimitInfoDTO.setApprovalStatus("0");
        dutyBillLimitInfoDTO.setIsDeleted("0");
        dutyBillLimitInfoDTO.setCreatedBy("system");
        dutyBillLimitInfoDTO.setUpdatedBy("system");
        dutyBillLimitInfoDTO.setCreatedDate(new Date());
        dutyBillLimitInfoDTO.setUpdatedDate(new Date());
        if(StringUtils.isEmptyStr(detail.getPayLimitType())){
            dutyBillLimitInfoDTO.setLimitType("N");//N-不限额
        }else{
            dutyBillLimitInfoDTO.setLimitType(detail.getPayLimitType());//-
        }
        dutyBillLimitInfoMapper.addDutyBillLimit(dutyBillLimitInfoDTO);
    }

    @Override
    public List<DutyBillLimitInfoDTO> getDutyLimitData(DutyBillLimitInfoDTO dto) {
        List<DutyBillLimitInfoDTO> list=  dutyBillLimitInfoMapper.getDutyLimitData(dto);
        return list;
    }

    @Override
    @Transactional
    public void updateDutyBillLimitAmount(List<DutyBillLimitInfoDTO> list) {
        dutyBillLimitInfoMapper.updateDutyBillLimitAmount(list);
    }

}
