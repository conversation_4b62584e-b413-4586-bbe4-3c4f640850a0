package com.paic.ncbs.claim.model.dto.investigate;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "调查任务查询条件")
public class InvestigateTaskQueryDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("是否包含下级机构，Y/N")
    private String isIncludeSubordinates;

    @ApiModelProperty("是否本人案件，Y/N")
    private String isMyCase;

    @ApiModelProperty("快赔")
    private String isQuickPay;

}