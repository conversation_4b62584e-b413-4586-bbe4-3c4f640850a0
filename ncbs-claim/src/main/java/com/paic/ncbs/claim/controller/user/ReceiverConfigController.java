package com.paic.ncbs.claim.controller.user;
import com.paic.ncbs.claim.model.dto.user.ReceiverConfigDTO;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.user.ReceiverConfigVO;
import com.paic.ncbs.claim.service.user.ReceiverConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/app/receiverConfigAction")
public class ReceiverConfigController {

	@Autowired
	private ReceiverConfigService receiverConfigService;

	@PostMapping("/getReceiverList")
	public ResponseResult<List<ReceiverConfigVO>> getReceiverList(@RequestBody ReceiverConfigDTO receiverConfigDTO) {

		return ResponseResult.success(receiverConfigService.getReceiverVoList(receiverConfigDTO));
	}

	@PostMapping("/updateReceiverList")
	public ResponseResult updateReceiverList(@RequestBody List<ReceiverConfigVO> receiverConfigList){

		receiverConfigService.updateReceiverList(receiverConfigList);

		return ResponseResult.success();
	}

	/**
	 * 测试方法
	 * @param deptCode
	 * @param node
	 * @param scope
	 * @return
	 */
	@GetMapping("/getMajorCaseReceiver")
	public ResponseResult<Map<String,String>> getMajorCaseReceiver(@RequestParam("deptCode") String deptCode,
																   @RequestParam("node") String node,
																   @RequestParam("scope") String scope) {

		return ResponseResult.success(receiverConfigService.getMajorCaseReceiverList(deptCode, node, scope));
	}

}