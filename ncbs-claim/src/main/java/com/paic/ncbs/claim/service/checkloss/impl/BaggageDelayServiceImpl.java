package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.dao.mapper.checkloss.BaggageDelayMapper;
import com.paic.ncbs.claim.model.dto.checkloss.BaggageDelayDTO;
import com.paic.ncbs.claim.service.checkloss.BaggageDelayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
public class BaggageDelayServiceImpl implements BaggageDelayService {

	@Autowired
    BaggageDelayMapper baggageDelayDao;

	@Override
	@Transactional
	public void saveBaggageDelay(BaggageDelayDTO baggageDelayDTO) {
		String reportNo = baggageDelayDTO.getReportNo();
		Integer caseTimes = baggageDelayDTO.getCaseTimes();
		String channelProcessId = baggageDelayDTO.getIdAhcsChannelProcess();
		BaggageDelayDTO existBaggageDelayDTO = baggageDelayDao.getBaggageDelay(reportNo, caseTimes, null, baggageDelayDTO.getTaskCode(), channelProcessId);
		if(existBaggageDelayDTO != null){
			baggageDelayDao.updateEffective(baggageDelayDTO);
		}
		baggageDelayDao.addBaggageDelay(baggageDelayDTO);
	}

	@Override
	public void removeBaggageDelay(String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
		baggageDelayDao.removeBaggageDelay(reportNo, caseTimes, taskCode, channelProcessId);
	}

	@Override
	public void updateEffective(BaggageDelayDTO baggageDelayDTO) {
		baggageDelayDao.updateEffective(baggageDelayDTO);
	}

	public BaggageDelayDTO getBaggageDelay(String reportNo, Integer caseTimes,String status,String taskCode, String channelProcessId) {
		return baggageDelayDao.getBaggageDelay(reportNo,caseTimes,status,taskCode, channelProcessId);
	}

	@Override
	public BaggageDelayDTO queryBaggageDelay(String flightNo,String taskCode) {
		return null;
	}

}
