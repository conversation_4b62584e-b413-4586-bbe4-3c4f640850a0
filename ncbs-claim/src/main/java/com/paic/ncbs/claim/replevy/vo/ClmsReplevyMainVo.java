package com.paic.ncbs.claim.replevy.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，表clms_replevy_main的VO对象<br/>
 * 对应表名：clms_replevy_main,备注：追偿信息主表
 *
 */
@Data
public class ClmsReplevyMainVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	private String id;
	/** 对应字段：report_no,备注：报案号 */
	private String reportNo;
	/** 对应字段：case_no,备注：赔案号 */
	private String caseNo;
	/** 对应字段：claim_no,备注：立案号 */
	private String claimNo;
	/** 对应字段：policy_no,备注：保单号 */
	private String policyNo;
	/** 对应字段：replevy_no,备注：追偿案件号 */
	private String replevyNo;
	/** 对应字段：risk_code,备注：险种 */
	private String riskCode;
	/** 对应字段：replevy_times,备注：追偿次数 */
	private Integer replevyTimes;
	/** 对应字段：case_times,备注：赔付次数 */
	private Integer caseTimes;
	/** 对应字段：replevy_currency,备注：币别 */
	private String replevyCurrency;
	/** 对应字段：sum_plan_replevy,备注：计划追偿费用 */
	private BigDecimal sumPlanReplevy;
	/** 对应字段：sum_real_replevy,备注：追偿总收入 */
	private BigDecimal sumRealReplevy;
	/** 对应字段：sum_replevy_fee,备注：追偿总费用 */
	private BigDecimal sumReplevyFee;
	/** 对应字段：replevy_opinion,备注：追偿意见 */
	private String replevyOpinion;
	/** 对应字段：opinion_text,备注：意见说明 */
	private String opinionText;
	/** 对应字段：replevy_text,备注：追偿描述 */
	private String replevyText;
	/** 对应字段：cancel_date,备注：取消日期 */
	private Date cancelDate;
	/** 对应字段：cancel_reason,备注：取消原因 */
	private String cancelReason;
	/** 对应字段：status,备注：状态 */
	private String status;
	/** 对应字段：make_com,备注：归属机构 */
	private String makeCom;
	/** 对应字段：com_code,备注：机构代码 */
	private String comCode;
	/** 对应字段：valid_flag,备注：有效标志 */
	private String validFlag;
	/** 标志1-任务进行中，2-任务结束 */
	private String flag;
	/** 对应字段：handler_code,备注：处理人代码 */
	private String handlerCode;
	/** 对应字段：serial_no,备注：序号 */
	private Integer serialNo;
	/** 对应字段：compensate_no,备注：理算号 */
	private String compensateNo;
	/** 对应字段：approve_flag,备注：高级审核状态 */
	private String approveFlag;
	/** 对应字段：created_by,备注：创建人 */
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	private Date sysCtime;
	/** 对应字段：updated_by,备注：修改人员 */
	private String updatedBy;
	/** 对应字段：sys_utime,备注：修改时间 */
	private Date sysUtime;
	/** 任务完成时间 */
	private Date finishDate;

	/** 副表关联追偿主表id */
	private String replevyId;

}
