package com.paic.ncbs.claim.service.checkloss;


import com.paic.ncbs.claim.model.dto.checkloss.VehiclDelayOtherDTO;

public interface VehiclDelayOtherService {


	public void saveVehiclDelayOther(VehiclDelayOtherDTO vehiclDelayOtherDTO);


	public void removeVehiclDelay(String reportNo, Integer caseTime, String taskCode, String channelProcessId);

	void updateEffective(VehiclDelayOtherDTO vehiclDelayOtherDTO);


	public VehiclDelayOtherDTO getVehiclDelayOther(String reportNo, Integer caseTimes, String status, String taskCode, String channelProcessId);
}
