package com.paic.ncbs.claim.service.settle.factor.impl.formula;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.formula.FormulaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 医疗类型公式
 */
@Slf4j
@RefreshScope
@Service
public class MedicalFormulaServiceImpl implements FormulaService {
    /**
     * 生产环境和dev产品，方案编码都不一样
     * dev C00094,生产是C00034
     */
    @Value("${planCode.outpatientCode:C00094}")
    private String configDutyCode;
    @Value("${project.projectCode:N}")
    private List<String> configProjectCodeList;

    /**
     * 药房指定的方案编码
     */
    @Value("${pharmacy.packageCode:null}")
    private List<String> packageCodeList;
    /**
     * 药房责任
     */
    @Value("${drugsBill.duty:C00369}")
    private String drugsDutyCode;
    @Override
    public String getFormula(DutyDetailPayDTO detail) {
        log.info("责任编码={},责任免赔额属性={}",detail.getDutyCode(),detail.getRemitAmountType());
        StringBuilder sb=new StringBuilder();
        //合理费用----
        String reasonableAmount= Constants.REASONABLE_AMOUNT_00;//默认
        if (configDutyCode.contains(detail.getDutyCode()) && configProjectCodeList.contains(detail.getProductPackage())) {
            reasonableAmount =Constants.REASONABLE_AMOUNT_01;//
        }
        //药房责任的合理费用
        if(packageCodeList.contains(detail.getProductPackage()) && drugsDutyCode.contains(detail.getDutyCode())){
            reasonableAmount =Constants.REASONABLE_AMOUNT_02;//
        }

        //免赔额类型
        String  remitAmountType = Constants.REMIT_AMOUNT_TYPE_MAP.get(detail.getRemitAmountType());
        if(StringUtils.isEmptyStr(remitAmountType)){
            remitAmountType="noremit";//无免赔额
        }
        sb.append("(").append(reasonableAmount).append("-").append(remitAmountType).append(")*").append(Constants.PROPORTION);
        return sb.toString();
    }
}
