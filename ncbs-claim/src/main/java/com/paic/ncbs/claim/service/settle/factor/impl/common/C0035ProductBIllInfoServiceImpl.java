package com.paic.ncbs.claim.service.settle.factor.impl.common;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.common.BeforeBillInfoService;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;

@Order(3)
@Service("c0035ProductBIllInfoServiceImpl")
public class C0035ProductBIllInfoServiceImpl implements BeforeBillInfoService {
    @Override
    public List<MedicalBillInfoDTO> expansion(List<MedicalBillInfoDTO> medicalBillInfoDTOList, DutyDetailPayDTO detailPayDTO){
        return medicalBillInfoDTOList;
    }
}
