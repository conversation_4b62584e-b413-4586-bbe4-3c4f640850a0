package com.paic.ncbs.claim.controller.who.investigate;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditPublicDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskQueryDTO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskAuditPublicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "外部系统调查任务审核")
@RestController
@RequestMapping(value = "/public/app/investigateTaskAuditAction")
public class InvestigateTaskAuditPublicController {

    @Autowired
    private InvestigateTaskAuditPublicService investigateTaskAuditPublicService;

    @ApiOperation(value = "查询调查任务列表")
    @PostMapping(value = "/getInvestigateTaskList")
    public ResponseResult<List<WorkBenchTaskVO>> getInvestigateTaskList(@RequestBody InvestigateTaskQueryDTO queryDTO) {

        queryDTO.setIsIncludeSubordinates("Y");
        queryDTO.setIsMyCase("N");
        queryDTO.setIsQuickPay("N");
        
        List<WorkBenchTaskVO> taskList = investigateTaskAuditPublicService.getInvestigateTaskList(queryDTO);
        return ResponseResult.success(taskList);
    }

    @ApiOperation(value = "完成调查任务审核")
    @PostMapping(value = "/finishTaskAudit")
    public ResponseResult<Object> finishTaskAudit(@RequestBody InvestigateTaskAuditPublicDTO taskAuditDTO) {
        investigateTaskAuditPublicService.finishTaskAudit(taskAuditDTO);
        return ResponseResult.success();
    }

}