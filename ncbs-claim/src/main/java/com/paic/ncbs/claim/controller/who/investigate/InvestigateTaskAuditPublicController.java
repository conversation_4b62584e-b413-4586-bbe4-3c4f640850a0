package com.paic.ncbs.claim.controller.who.investigate;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.doc.DocAppFileUploadController;
import com.paic.ncbs.claim.controller.doc.DocumentController;
import com.paic.ncbs.claim.controller.doc.IOBSFileUploadController;
import com.paic.ncbs.claim.controller.report.QueryReportController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditPublicDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskQueryDTO;
import com.paic.ncbs.claim.model.vo.fileupolad.FileInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimInfoToESVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskQueryVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskAuditPublicService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "外部系统调查任务审核")
@RestController
@RequestMapping(value = "/public/app/investigateTaskAuditAction")
public class InvestigateTaskAuditPublicController {

    @Autowired
    private InvestigateTaskAuditPublicService investigateTaskAuditPublicService;

    @Autowired
    private QueryReportController queryReportController;

    @Autowired
    private DocAppFileUploadController docAppFileUploadController;

    @Autowired
    private IOBSFileUploadController iobsFileUploadController;

    @Autowired
    private DocumentController documentController;

    @Autowired
    private TaskListService taskListService;

    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;

    @ApiOperation(value = "查询调查任务列表")
    @PostMapping(value = "/getInvestigateTaskList")
    public ResponseResult<Map<String, List<WorkBenchTaskVO>>> getInvestigateTaskList(@RequestBody WorkBenchTaskQueryVO workBenchTaskQueryVO) throws Exception {
        // 固定参数设置
        workBenchTaskQueryVO.setIsIncludeSubordinates("Y");
        workBenchTaskQueryVO.setIsMyCase("N");
        workBenchTaskQueryVO.setIsQuickPay("N");

        // 设置用户信息
        UserInfoDTO user = WebServletContext.getUser();
        workBenchTaskQueryVO.setUserCode(user.getUserCode());

        // 包含下级机构
        List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(WebServletContext.getDepartmentCode());
        workBenchTaskQueryVO.setDepartmentCodes(departmentCodes);

        // 只处理调查审批任务
        workBenchTaskQueryVO.setTaskDefinitionBpmKey(BpmConstants.OC_INVESTIGATE_APPROVAL);

        // 获取工作台任务列表
        Map<String, List<WorkBenchTaskVO>> workBenchTaskList = taskListService.getWorkBenchTaskList(workBenchTaskQueryVO);

        // 过滤只保留调查审批任务
        Map<String, List<WorkBenchTaskVO>> filteredTaskList = workBenchTaskList.entrySet().stream()
            .filter(entry -> entry.getValue().stream()
                .anyMatch(vo -> BpmConstants.OC_INVESTIGATE_APPROVAL.equals(vo.getTaskDefinitionBpmKey())))
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().stream()
                    .filter(vo -> BpmConstants.OC_INVESTIGATE_APPROVAL.equals(vo.getTaskDefinitionBpmKey()))
                    .collect(Collectors.toList())
            ));

        return ResponseResult.success(filteredTaskList);
    }

    @ApiOperation(value = "完成调查任务审核")
    @PostMapping(value = "/finishTaskAudit")
    public ResponseResult<Object> finishTaskAudit(@RequestBody InvestigateTaskAuditPublicDTO taskAuditDTO) {
        investigateTaskAuditPublicService.finishTaskAudit(taskAuditDTO);
        return ResponseResult.success();
    }

    /**
     * 历史案件列表
     */
    @ApiOperation(value = "根据报案号.赔付次数被保人所有历史案件")
    @GetMapping(value = "/getHistoryCaseListNew")
    public ResponseResult<Map<String, Object>> getHistoryCaseListNew(
            @ApiParam("报案号") @RequestParam String reportNo,
            @ApiParam("赔付次数") @RequestParam Integer caseTimes,
            @RequestParam(defaultValue = "1") Integer pageIndex,
            @RequestParam(defaultValue = "10") Integer pageRows) {

        LogUtil.audit("#外部系统调查#历史案件列表查询#reportNo={}, caseTimes={}", reportNo, caseTimes);

        // 创建分页对象
        Pager pager = new Pager();
        pager.setPageIndex(pageIndex);
        pager.setPageRows(pageRows);

        // 直接调用原有方法
        List<ClaimInfoToESVO> claimInfoToESVOs = reportCustomerInfoService.getHistoryCaseListNew(reportNo, caseTimes, pager);
        return ResponseResult.success(claimInfoToESVOs, pager);
    }

    /**
     * 查看单证
     */
    @ApiOperation("查看单证需要获取文件组列表")
    @PostMapping(value = "/getDocumentList")
    public ResponseResult<List<FileInfoVO>> getDocumentList(@RequestBody FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        LogUtil.audit("#外部系统调查#查看单证#reportNo={}, caseTimes={}", fileInfoDTO.getReportNo(), fileInfoDTO.getCaseTimes());

        // 直接调用原有方法
        return docAppFileUploadController.getDocumentList(fileInfoDTO);
    }

    /**
     * 获取单证地址
     */
    @ApiOperation("获取单证真实访问地址")
    @GetMapping(value = "/getIntranetIOBSDownloadUrl")
    public ResponseResult<Object> getIntranetIOBSDownloadUrl(
            @ApiParam("文件ID") @RequestParam("fileId") String fileId,
            @ApiParam("文件名") @RequestParam("fileName") String fileName) {

        LogUtil.audit("#外部系统调查#获取单证地址#fileId={}, fileName={}", fileId, fileName);

        // 直接调用原有方法
        return iobsFileUploadController.getIntranetIOBSDownloadUrl(fileId, fileName);
    }

    /**
     * 单证大类列表
     */
    @ApiOperation("获取单证类型")
    @PostMapping(value = "/getAllDocumentTypeList")
    public ResponseResult<Object> getAllDocumentTypeList(@RequestBody FileInfoVO fileInfoVO) throws GlobalBusinessException {
        LogUtil.audit("#外部系统调查#获取单证类型#");

        // 直接调用原有方法
        return documentController.getAllDocumentTypeList(fileInfoVO);
    }

}