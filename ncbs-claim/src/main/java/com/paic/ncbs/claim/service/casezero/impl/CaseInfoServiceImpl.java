package com.paic.ncbs.claim.service.casezero.impl;

import com.paic.ncbs.claim.model.vo.estimate.CaseInfoVO;
import com.paic.ncbs.claim.dao.mapper.estimate.CaseInfoMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.service.casezero.CaseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class CaseInfoServiceImpl implements CaseInfoService {

    @Autowired
    private CaseInfoMapper caseInfoDao;

    @Autowired
    private EstimatePolicyMapper estimatePolicyMapper;

    @Override
    public List<CaseInfoVO> getCaseInfoList(String reportNo, Integer caseTimes) {
        List<CaseInfoVO> caseInfoVOList =  caseInfoDao.getCaseInfoList(reportNo, caseTimes);
        caseInfoVOList.forEach(caseInfo->{
            BigDecimal registerAmount = estimatePolicyMapper.getRegisterAmountByPolicyNo(reportNo,caseTimes,caseInfo.getPolicyNo());
            caseInfo.setRegisterAmount(registerAmount);
        });
        return caseInfoVOList;
    }
}
