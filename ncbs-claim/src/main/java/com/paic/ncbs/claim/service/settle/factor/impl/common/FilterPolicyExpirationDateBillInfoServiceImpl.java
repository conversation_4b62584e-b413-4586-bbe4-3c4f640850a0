package com.paic.ncbs.claim.service.settle.factor.impl.common;

import cn.hutool.core.date.DateUtil;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.common.BeforeBillInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 通用规则 过滤保单有效期等待期发票
 */
@Slf4j
@Order(4)
@Service
public class FilterPolicyExpirationDateBillInfoServiceImpl implements BeforeBillInfoService {
    @Override
    public List<MedicalBillInfoDTO> expansion(List<MedicalBillInfoDTO> medicalBillInfoDTOList, DutyDetailPayDTO detailPayDTO) {
        //查询保单有效期
        String insuranceBeginTime = DateUtils.parseToFormatStr(detailPayDTO.getInsuranceBeginTime(),DateUtils.SIMPLE_DATE_STR);
        Date insuranceBeginDate =DateUtils.formatStringToDate(insuranceBeginTime,DateUtils.SIMPLE_DATE_STR);
        //转格式年月日 保单终止日
        String insuranceEndDate = DateUtils.parseToFormatStr(detailPayDTO.getInsuranceEndTime(),DateUtils.SIMPLE_DATE_STR);
        //在转为日期类型
        Date insendDate=DateUtils.formatStringToDate(insuranceEndDate,DateUtils.SIMPLE_DATE_STR);
        String reportNo=detailPayDTO.getReportNo();
        //筛选出在保单有效期内的发票
        Date endWaitDate = null;//等待期结束日
        if(Objects.nonNull(detailPayDTO.getWaitDays()) && detailPayDTO.getWaitDays()>0){
            Date endWaitTime=DateUtils.addDate(insuranceBeginDate,detailPayDTO.getWaitDays());
            endWaitDate= DateUtil.offsetMillisecond(endWaitTime,-1);//处理等待期结束日期
            log.info("报案号={},等待期到={}",detailPayDTO.getReportNo(),DateUtils.parseToFormatStr(endWaitDate,DateUtils.FULL_DATE_STR));
        }
        for (MedicalBillInfoDTO medicalBillInfoDTO : medicalBillInfoDTOList) {
            String sbilldate = DateUtils.dateFormat(medicalBillInfoDTO.getStartDate(),DateUtils.SIMPLE_DATE_STR);
            Date billdate =DateUtils.formatStringToDate(sbilldate,DateUtils.SIMPLE_DATE_STR);
            //保单有效期内判断 billdate<insendDate 返回-1
            log.info("报案号={},发票日期={},保单起期={}，保单终止日期={}",detailPayDTO.getReportNo(),DateUtils.parseToFormatStr(billdate,DateUtils.FULL_DATE_STR),DateUtils.parseToFormatStr(insuranceBeginDate,DateUtils.FULL_DATE_STR),DateUtils.parseToFormatStr(insendDate,DateUtils.FULL_DATE_STR));
            if(billdate.compareTo(insuranceBeginDate)<0 || billdate.compareTo(insendDate)>0){
                log.info("发票日期不在保单有效期内");
                medicalBillInfoDTO.setEffectiveFlag("N");
                continue;
            }
            if(Objects.nonNull(endWaitDate)){
                log.info("报案号={},等待期到={}",reportNo,DateUtils.parseToFormatStr(endWaitDate,DateUtils.FULL_DATE_STR));
                if(billdate.compareTo(insuranceBeginDate)>=0 && billdate.compareTo(endWaitDate)<=0){
                    medicalBillInfoDTO.setWaitFlag("Y");
                    log.info("报案号={},等待期发票={}",reportNo, JsonUtils.toJsonString(medicalBillInfoDTO));
                }else{
                    medicalBillInfoDTO.setWaitFlag("N");
                    log.info("报案号={},过滤后的发票={}",reportNo,JsonUtils.toJsonString(medicalBillInfoDTO));
                }

            }else{
                log.info("报案号={},没等待期直接返回发票={}",reportNo,JsonUtils.toJsonString(medicalBillInfoDTO));
                medicalBillInfoDTO.setWaitFlag("N");
            }

        }
        return medicalBillInfoDTOList;
    }
}
