package com.paic.ncbs.claim.service.settle.factor.impl.base;

import com.googlecode.aviator.Expression;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonBenefitDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.*;
import com.paic.ncbs.claim.service.settle.factor.abstracts.calculate.AbstractCalculateAmountFactor;
import com.paic.ncbs.claim.service.settle.factor.interfaces.base.BaseSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * 津贴类理算逻辑
 */
@Slf4j
@Service
public class AllowanceSettleServiceImpl implements BaseSettleService {

    @Autowired
    private AbstractCalculateAmountFactor abstractCalculateAmountFactor;

    @Override
    public void getSettleAmount(ClaimCaseDTO claimCaseDTO, DutyDetailPayDTO detailPayDTO, Expression expression) {
        List<PersonBenefitDTO> personBenefitDTOList = claimCaseDTO.getPersonBenefitDTOList();
        BigDecimal autoSettleAmount = BigDecimal.ZERO;
        List<PersonBenefitSettleReasonTemplateDTO> reasonTemplateDTOList = new ArrayList<>();
        for (PersonBenefitDTO personBenefitDTO : personBenefitDTOList) {
            Map<String, Object> calparamsMap = new HashMap<>();
            CalculateParamsDTO paramsDTO = new CalculateParamsDTO();
            paramsDTO.setDutyDetailPayDTO(detailPayDTO);
            paramsDTO.setPersonBenefitDTO(personBenefitDTO);
            paramsDTO.setSettleFactor(new SettleFactor());
            for (Map.Entry<String, CalculateAmountService> en : detailPayDTO.getCalServiceImplMap().entrySet()) {
                CalculateAmountService service = en.getValue();
                if (Objects.isNull(service)) {
                    throw new GlobalBusinessException("获取实现类为null名称" + service);
                }
                abstractCalculateAmountFactor.setCalculateReasonAmountInterface(service);
                abstractCalculateAmountFactor.getAmout(paramsDTO);
                calparamsMap.put(en.getKey(), paramsDTO.getSettleFactor().getCalculateAmount());
            }

            BigDecimal result = (BigDecimal) expression.execute(calparamsMap);//理算金额
            if(result.compareTo(BigDecimal.ZERO) < 0){
                result = BigDecimal.ZERO;
            }
            autoSettleAmount = autoSettleAmount.add(result);

            //理算依据
            PersonBenefitSettleReasonTemplateDTO personBenefitSettleReasonTemplateDTO = new PersonBenefitSettleReasonTemplateDTO();
            personBenefitSettleReasonTemplateDTO.setBenefitTypeName(personBenefitDTO.getBenefitTypeName());
            personBenefitSettleReasonTemplateDTO.setAllowanceApplyDay(personBenefitDTO.getHospitalDays());
            personBenefitSettleReasonTemplateDTO.setAllowanceRemitDay(detailPayDTO.getRemitDays());
            personBenefitSettleReasonTemplateDTO.setAllowanceAmount(detailPayDTO.getAllowanceAmount());
            personBenefitSettleReasonTemplateDTO.setPayAmount(result);
            reasonTemplateDTOList.add(personBenefitSettleReasonTemplateDTO);
        }

        BigDecimal maxAmountPay = detailPayDTO.getMaxAmountPay();//责任剩余理赔金额
        if(autoSettleAmount.compareTo(maxAmountPay) <= 0){
            detailPayDTO.setAutoSettleAmount(autoSettleAmount.setScale(2, RoundingMode.HALF_UP));
        }else {
            detailPayDTO.setAutoSettleAmount(maxAmountPay);
            detailPayDTO.setNotice("理算结果"+autoSettleAmount+"超过剩余赔付额！按剩余赔付额"+maxAmountPay+"赔付");
        }


        detailPayDTO.setAllowanceDays(personBenefitDTOList.stream().map(PersonBenefitDTO::getHospitalDays).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));

        DetailSettleReasonTemplateDTO detailSettleReasonTemplateDTO =new DetailSettleReasonTemplateDTO();
        detailSettleReasonTemplateDTO.setDutyDetailName(detailPayDTO.getDutyDetailName());//责任明细名称
        detailSettleReasonTemplateDTO.setAutoSettleAmount(BigDecimalUtils.toString(autoSettleAmount));//最终理算结果
        detailSettleReasonTemplateDTO.setPersonBenefitList(reasonTemplateDTOList);
        detailSettleReasonTemplateDTO.setNotice(detailPayDTO.getNotice());
        detailPayDTO.setDetailSettleReasonTemplateDTO(detailSettleReasonTemplateDTO);
    }
}
