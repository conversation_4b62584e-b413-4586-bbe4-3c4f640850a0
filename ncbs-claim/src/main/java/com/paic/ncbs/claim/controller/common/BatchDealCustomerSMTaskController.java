package com.paic.ncbs.claim.controller.common;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.service.supplements.BatchDealCustomerSMTaskService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/public/app/supplementsMaterial")
@Api(tags = {"客户补材批处理"})
@Slf4j
@RefreshScope
public class BatchDealCustomerSMTaskController {

    /**
     * 超期没有处理的任务 自动关闭
     * 默认值：15  支持配置
     */
    @Value("${batch.supplements.days:15}")
    private Integer confiDays;
    @Autowired
    private BatchDealCustomerSMTaskService batchDealCustomerSMTaskService;

    /**
     * 超期没有处理的补材任务 自动关闭并恢复原来挂起的任务
     *
     * @return
     */
    @PostMapping("/batchDealServiceData")
    public ResponseResult batchDealServiceData() {
        log.info("客户补材批处理开始执行");
        batchDealCustomerSMTaskService.batchDealServiceData(confiDays);
        return ResponseResult.success();
    }
}
