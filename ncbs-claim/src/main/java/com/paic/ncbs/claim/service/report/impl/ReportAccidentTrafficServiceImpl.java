package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentTrafficEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentTrafficMapper;
import com.paic.ncbs.claim.service.report.ReportAccidentTrafficService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportAccidentTrafficServiceImpl extends BaseServiceImpl<ReportAccidentTrafficEntity> implements ReportAccidentTrafficService {

    @Autowired
    private ReportAccidentTrafficMapper reportAccidentTrafficMapper;

    @Override
    public BaseDao<ReportAccidentTrafficEntity> getDao() {
        return reportAccidentTrafficMapper;
    }

    @Override
    public ReportAccidentTrafficEntity getReportAccidentTrafficByReportNo(String reportNo) {
        return reportAccidentTrafficMapper.getReportAccidentTrafficByReportNo(reportNo);
    }
}
