package com.paic.ncbs.claim.service.report;


import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDomainDTO;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;


import java.util.List;
import java.util.Map;

public interface ParallelPolicyService {

    @Deprecated
    List<AhcsPolicyDomainDTO> getPolicyDomainByParallel(List<Map<String, String>> paramList);

    List<AhcsPolicyDomainDTO> queryPolicyDomainByParallel(List<CopyPolicyQueryVO> paramList);

    List<String> getNoByParallel(String noType, VoucherTypeEnum voucherTypeEnum, List<String> departmentList);
}
