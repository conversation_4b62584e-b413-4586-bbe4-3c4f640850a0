package com.paic.ncbs.claim.controller.standard.accept;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.api.StandardRequestDTO;
import com.paic.ncbs.claim.model.dto.oneStepEndCase.OneStepEndCaseDto;
import com.paic.ncbs.claim.model.vo.oneStepEndCase.OneStepEndCaseResponse;
import com.paic.ncbs.claim.model.vo.report.OnlineReportResponseVO;
import com.paic.ncbs.claim.model.vo.report.OnlineReportVO;
import com.paic.ncbs.claim.service.oneStepEndCase.OneStepEndCaseService;
import com.paic.ncbs.claim.service.report.OnlineReportService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Api(tags = "对外报案相关接口")
@RestController
@Validated
@RequestMapping("/public/report")
public class AcceptApiController {

    @Autowired
    private OnlineReportService onlineReportService;
    @Autowired
    private OneStepEndCaseService oneStepEndCaseService;

    @PostMapping(value = "/saveReport",produces = {"application/json;charset=utf-8"})
    public ResponseResult<Object> saveReport(@RequestBody StandardRequestDTO reportDto) {
        LogUtil.info("TPA线上报案-入参={}", JSON.toJSONString(reportDto));
        OnlineReportResponseVO onlineReportResponseVO;
        try {
            String jsonStr = JSONObject.toJSONString(reportDto.getRequestData());
            OnlineReportVO onlineReportVO = JSONObject.parseObject(jsonStr, OnlineReportVO.class);
            onlineReportVO.setCompanyId(reportDto.getCompanyId());
            onlineReportResponseVO = onlineReportService.saveReport(onlineReportVO);
            LogUtil.info("TPA线上报案成功报案号={}，返回数据={}",onlineReportResponseVO.getReportNo(), JSON.toJSONString(onlineReportResponseVO));
        } catch (GlobalBusinessException e) {
            LogUtil.info("TPA线上报案参数校验失败={}", e);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"线上报案参数校验失败" + e.getMessage());
        }catch (Exception exception) {
            LogUtil.error("TPA线上报案失败={}", exception);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"线上报案失败" + exception.getMessage());
        }
        return ResponseResult.success(onlineReportResponseVO);
    }

    @PostMapping(value = "/oneStepEndCase",produces = {"application/json;charset=utf-8"})
    public ResponseResult<Object> oneStepEndCase(@RequestBody @Valid @NotNull(message ="入参不能为空") StandardRequestDTO endCaseDto) {
        LogUtil.info("一步结案-入参={}", JSON.toJSONString(endCaseDto));
        String jsonStr = JSONObject.toJSONString(endCaseDto.getRequestData());
        OneStepEndCaseDto oneStepEndCaseDto = JSONObject.parseObject(jsonStr, OneStepEndCaseDto.class);
        OneStepEndCaseResponse oneStepEndCaseResponse;
        try {
            oneStepEndCaseResponse = oneStepEndCaseService.oneStepEndCase(oneStepEndCaseDto);
            LogUtil.info("一步结案-出参={}", JSON.toJSONString(oneStepEndCaseResponse));
        } catch (GlobalBusinessException businessException) {
            LogUtil.info("受理号：{}，一步结案结案参数校验失败={}", oneStepEndCaseDto.getAcceptanceNumber(),businessException.getMessage());
            return ResponseResult.fail(businessException.getCode(),"受理号："+oneStepEndCaseDto.getAcceptanceNumber()+"，一步结案结案参数校验失败，失败原因：" + businessException.getMessage());
        }catch (Exception exception) {
            LogUtil.error("受理号：{}，一步结案失败!",oneStepEndCaseDto.getAcceptanceNumber(), exception);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"受理号："+oneStepEndCaseDto.getAcceptanceNumber()+"一步结案结案失败：" + exception.getMessage());
        }
        return ResponseResult.success(oneStepEndCaseResponse);
    }
}
