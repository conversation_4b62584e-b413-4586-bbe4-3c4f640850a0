package com.paic.ncbs.claim.service.report;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;

import java.util.List;

public interface AutoEstimateAmountService {

    void autoEstimateAmount(String reportNo, Integer caseTimes);

    void addEstimateRecord(List<EstimatePolicyDTO> policyCopyData);

    void estimateAmountByBatch(String reportNo, Integer caseTimes, String isMerge) throws GlobalBusinessException;
}
