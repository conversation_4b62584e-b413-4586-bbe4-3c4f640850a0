package com.paic.ncbs.claim.service.settle.factor.impl.proportion;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeDetailEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeDetailMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.settle.factor.*;

import com.paic.ncbs.claim.service.settle.factor.abstracts.calculate.AbstractCalculateAmountFactor;
import com.paic.ncbs.claim.service.settle.factor.interfaces.bill.BIllSettleBuildService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.proportion.NoStandardProportionService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service
public class NoStandardProportionServiceImpl implements NoStandardProportionService {
    @Autowired
    private DutyAttributeDetailMapper dutyAttributeDetailMapper;
    @Autowired
    private AbstractCalculateAmountFactor abstractCalculateAmountFactor;
    @Autowired
    private BIllSettleBuildService bIllSettleBuildService;

    @Override
    public void noStardardSettle(DutyDetailPayDTO detail,List<EveryDayBillInfoDTO> everyBillLists) {
        if(Objects.isNull(everyBillLists)){
            return;
        }
        BigDecimal reasonableAmount=getReasonable(everyBillLists,detail);
        List<AhcsDutyAttributeDetailEntity> attributeDetailList = dutyAttributeDetailMapper.getInfoByDutyAttributeDutyId(detail.getIdPolicyDuty());
        if (!attributeDetailList.isEmpty()) {
            detail.setPayProportionType("2");
            detail.setPayProportion(BigDecimalUtils.NEGATIVE_ONE);
            BigDecimal result = BigDecimal.ZERO;
            StringBuilder settleReason = new StringBuilder();
            // 合理费用
            BigDecimal amount = nvl(reasonableAmount, 0);
            // 免赔额
            BigDecimal remitAmount = nvl(detail.getRemitAmount(), 0);
            // 实际赔付金额
            BigDecimal actualAmount = amount.subtract(remitAmount);
            // 如果合理费用小于免赔 则不用计算
            if (actualAmount.compareTo(BigDecimal.ZERO) <= 0) {
                detail.setTempSettleAmount(result);
                detail.setDetailSettleReason(settleReason.append("不足免赔额"));
                return;
            }

            Map<String, List<AhcsDutyAttributeDetailEntity>> a1 = attributeDetailList.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeColumnNo))
                    .collect(Collectors.groupingBy(AhcsDutyAttributeDetailEntity::getAttributeColumnNo, LinkedHashMap::new, Collectors.toList()));
            Set<String> interval = new HashSet();
            a1.forEach((k, v) -> {
                v = v.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeRowNo)).collect(Collectors.toList());
                BigDecimal left = new BigDecimal(v.get(0).getAttributeDetailValue());
                BigDecimal right = new BigDecimal(v.get(1).getAttributeDetailValue());
                if (actualAmount.compareTo(left) > 0 && actualAmount.compareTo(right) <= 0) {
                    interval.add(k);
                }
            });
            Iterator iterator = a1.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry entry = (Map.Entry) iterator.next();
                String k = (String) entry.getKey();
                List<AhcsDutyAttributeDetailEntity> v = (List<AhcsDutyAttributeDetailEntity>) entry.getValue();
                v = v.stream().sorted(Comparator.comparing(AhcsDutyAttributeDetailEntity::getAttributeRowNo)).collect(Collectors.toList());
                BigDecimal left = new BigDecimal(v.get(0).getAttributeDetailValue());
                BigDecimal right = new BigDecimal(v.get(1).getAttributeDetailValue());
                BigDecimal percent = new BigDecimal(v.get(2).getAttributeDetailValue());
                BigDecimal rate = BigDecimalUtils.toPercent(percent);
                if (interval.contains(k)) {
                    //判断 actualAmount
                    result = result.add(BigDecimalUtils.getPositive((actualAmount.subtract(left)).multiply(rate)));
                    setNoStardardSettleValue(settleReason, amount, remitAmount, left, percent);

                    break;
                } else {
                    result = result.add((right.subtract(left)).multiply(rate));
                    setValue(settleReason, right, left, percent);
                }

            }
            DetailSettleReasonTemplateDTO detailSettleReasonTemplateDTO=new DetailSettleReasonTemplateDTO();
            if(result.compareTo(detail.getMaxAmountPay())>0){
                detail.setAutoSettleAmount(detail.getMaxAmountPay());
                detailSettleReasonTemplateDTO.setAutoSettleAmount(BigDecimalUtils.toString(detail.getMaxAmountPay()));
            }else{
                detail.setAutoSettleAmount(result);
                detailSettleReasonTemplateDTO.setAutoSettleAmount(BigDecimalUtils.toString(result));
            }
            detailSettleReasonTemplateDTO.setDutyDetailName(detail.getDutyDetailName());
            detailSettleReasonTemplateDTO.setCalReason(settleReason.toString());
            detailSettleReasonTemplateDTO.setCalFormulaData(SettleConst.SETTLE_REASON.get(detail.getDutyDetailType()));
            detail.setDetailSettleReasonTemplateDTO(detailSettleReasonTemplateDTO);

        }
    }


    private void setNoStardardSettleValue(StringBuilder settleReason, BigDecimal amount, BigDecimal remitAmount, BigDecimal left, BigDecimal percent) {
        if (remitAmount.compareTo(BigDecimal.ZERO) == 0 && left.compareTo(BigDecimal.ZERO) == 0) {
            settleReason.append(amount).append("×").append(percent).append("%");
        } else if (remitAmount.compareTo(BigDecimal.ZERO) == 0 && left.compareTo(BigDecimal.ZERO) != 0) {
            settleReason.append("(").append(amount).append("-").append(left).append(")").append("×").append(percent).append("%");
        } else if (remitAmount.compareTo(BigDecimal.ZERO) != 0 && left.compareTo(BigDecimal.ZERO) == 0) {
            settleReason.append("(").append(amount).append("-").append(remitAmount).append(")").append("×").append(percent).append("%");
        } else {
            settleReason.append("(").append(amount).append("-").append(remitAmount).append("-").append(left).append(")").append("×").append(percent).append("%");
        }

    }
    private void setValue(StringBuilder settleReason, BigDecimal right, BigDecimal left, BigDecimal percent) {
        if (left.compareTo(BigDecimal.ZERO) == 0) {
            settleReason.append(right).append("×").append(percent).append("% +");

        } else {
            settleReason.append("(").append(right).append("-").append(left).append(")").append("×").append(percent).append("% +");
        }

    }
    private BigDecimal getReasonable(List<EveryDayBillInfoDTO> everyBillLists, DutyDetailPayDTO detail) {
        BigDecimal sum=BigDecimal.ZERO;
        Map<String, CalculateAmountService> calServiceImplMap =detail.getCalServiceImplMap();
        CalculateAmountService service=null;
        SettleFactor factor=new SettleFactor();
        CalculateParamsDTO paramsDTO=new CalculateParamsDTO();
        //存每张发票计算要素和结果
        List<BIllSettleResultDTO> billSettleResultDTOList=new ArrayList<>();
        for (EveryDayBillInfoDTO eDto :everyBillLists) {

            for (Map.Entry<String, CalculateAmountService> en : calServiceImplMap.entrySet()) {
                if(Objects.isNull(service)){
                    service =en.getValue();
                    break;
                }

            }
            if (Objects.isNull(service)) {
                throw new GlobalBusinessException("获取实现类为null名称" + service);
            }
            paramsDTO.setSettleFactor(factor);
            paramsDTO.setEveryDayBillInfoDTO(eDto);
            abstractCalculateAmountFactor.setCalculateReasonAmountInterface(service);
            abstractCalculateAmountFactor.getAmout(paramsDTO);
            BigDecimal reasonableAmount = paramsDTO.getSettleFactor().getReasonableAmount();
            sum=sum.add(reasonableAmount);

            //不在保单有效期，等待期内的发票，超每月赔付天数等发票的数据组装，理算金额都为0 ，
            BIllSettleResultDTO bIllSettleResultDTO =new BIllSettleResultDTO();
            bIllSettleResultDTO.setReasonableAmount(reasonableAmount);
            BIllSettleResultDTO bSRDTO= bIllSettleBuildService.dealBIllSettleResultDTO(eDto,detail,bIllSettleResultDTO);
            if(Objects.equals("1",bSRDTO.getFlag())){
                billSettleResultDTOList.add(bSRDTO);
                continue;
            }
            setBIllSettleResultDTOValue(bIllSettleResultDTO,billSettleResultDTOList,eDto,detail);
        }
        detail.setBillSettleResultDTOList(billSettleResultDTOList);
        return sum;
    }

    /**
     * 组装数据
     * @param bIllSettleResultDTO
     * @param bIllSettleResultDTOList
     * @param edto
     * @param detailPayDTO
     */
    private void setBIllSettleResultDTOValue(BIllSettleResultDTO bIllSettleResultDTO, List<BIllSettleResultDTO> bIllSettleResultDTOList, EveryDayBillInfoDTO edto, DutyDetailPayDTO detailPayDTO) {
        bIllSettleResultDTO.setReportNo(detailPayDTO.getReportNo());
        bIllSettleResultDTO.setPolicyNo(detailPayDTO.getPolicyNo());
        bIllSettleResultDTO.setCaseTimes(detailPayDTO.getCaseTimes());
        bIllSettleResultDTO.setProductCode(detailPayDTO.getProductCode());
        bIllSettleResultDTO.setPlanCode(detailPayDTO.getPlanCode());
        bIllSettleResultDTO.setDutyCode(detailPayDTO.getDutyCode());
        bIllSettleResultDTO.setDutyDetailCode(detailPayDTO.getDutyDetailCode());
        bIllSettleResultDTO.setMedicalSettleFlag(edto.getMedicalSettleFlag());
        bIllSettleResultDTO.setBillNo(edto.getBillNo());
        bIllSettleResultDTO.setBillDate(edto.getBillDate());
        bIllSettleResultDTO.setSettleType("0");
        bIllSettleResultDTO.setTimesLimit(Constants.FLAG_N);
        bIllSettleResultDTO.setDayLimit(Constants.FLAG_N);
        bIllSettleResultDTO.setMonthLimit(Constants.FLAG_N);
        bIllSettleResultDTO.setYearLimit(Constants.FLAG_N);
//        bIllSettleResultDTO.setId(edto.getId());
        bIllSettleResultDTO.setTherapyType(edto.getTherapyType());
        bIllSettleResultDTO.setBillAmount(edto.getBillAmount());
        PolicyMonthDto policyMonthDto = getStartEndDate(edto.getBillDate(),detailPayDTO.getMonthDtoList());
        if(Objects.isNull(policyMonthDto)){
            bIllSettleResultDTO.setMonth(-1);
        }else{
            bIllSettleResultDTO.setMonth(policyMonthDto.getMonth());
        }
        //组装责任明细发票理算信息组装
        bIllSettleResultDTOList.add(bIllSettleResultDTO);
    }

    private PolicyMonthDto getStartEndDate(Date billDate, List<PolicyMonthDto> monthDtoList) {

        for (PolicyMonthDto monthDto : monthDtoList) {
            if(billDate.compareTo(monthDto.getStartDate())>=0 && billDate.compareTo(monthDto.getEndDate())<=0){
                return monthDto;
            }
        }
        return null;
    }
}
