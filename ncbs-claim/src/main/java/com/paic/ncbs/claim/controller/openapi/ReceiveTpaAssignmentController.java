package com.paic.ncbs.claim.controller.openapi;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.openapi.TpaAssignmentRequestDTO;
import com.paic.ncbs.claim.service.openapi.ReceiveTpaAssignmentService;
import com.paic.ncbs.claim.utils.JsonUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 接收TPA任务分配信息
 */
@Api(tags = "TPA平台案件任务分配信息接入")
@Slf4j
@RestController
@RequestMapping("/public/receiveTpaAssignment")
public class ReceiveTpaAssignmentController {
    @Autowired
    private ReceiveTpaAssignmentService receiveTpaAssignmentService;
    /**
     * 接收TPA任务分配信息
     * @param request
     * @return
     */
    @PostMapping("/receiveTpaAssignment")
    public ResponseResult<Object> receiveTpaAssignment(@RequestBody TpaAssignmentRequestDTO request){
        log.info("接收TPA任务分配信息={}", JsonUtils.toJsonString(request));
        receiveTpaAssignmentService.receive(request.getRequestData());
        return  ResponseResult.success();
    }

}
