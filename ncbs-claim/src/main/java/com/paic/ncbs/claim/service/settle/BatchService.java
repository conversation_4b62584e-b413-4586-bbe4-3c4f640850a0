package com.paic.ncbs.claim.service.settle;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.settle.BatchDTO;

public interface BatchService {
	  
	BatchDTO getBatch(String reportNo, Integer caseTimes);

//	public AhcsBatchDTO getAhcsBatch(String reportNo,Integer caseTimes, String lossObjectNo);

	void insertBath(BatchDTO batch) throws GlobalBusinessException;

	String getSettleUserUm(String reportNo, Integer caseTimes);

	void updateSettleTime(BatchDTO batch);
	
	void updateBatch(BatchDTO batch);
}
