package com.paic.ncbs.claim.service.settle.factor.impl.strategy.calculate.remitamount;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.dao.mapper.settle.ClmsDutyDetailBillSettleMapper;
import com.paic.ncbs.claim.model.dto.settle.factor.CalculateParamsDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.RemitAmountDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.SettleFactor;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 年免赔 暂不支持
 */
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service
public class YearRemitAmountServiceImpl extends CalculateAmountService {
    @Autowired
    private ClmsDutyDetailBillSettleMapper clmsDutyDetailBillSettleMapper;

    /**
     * 剩余年免赔额
     */
    private BigDecimal yearRemitAmount;
    @Override
    public void calculate(CalculateParamsDTO paramsDTO) {
        SettleFactor factor =paramsDTO.getSettleFactor();
        //合理费用
        BigDecimal reasonableAmount  = factor.getReasonableAmount();
        if(Objects.isNull(yearRemitAmount)|| BigDecimal.ZERO.compareTo(yearRemitAmount)>=0){
            paramsDTO.getSettleFactor().setRemitAmount(BigDecimal.ZERO);
            paramsDTO.getSettleFactor().setCalculateAmount(BigDecimal.ZERO);
            yearRemitAmount=BigDecimal.ZERO;
            return;
        }
        if (Objects.equals("N", paramsDTO.getEveryDayBillInfoDTO().getEffectiveFlag())
                || Objects.equals("Y", paramsDTO.getEveryDayBillInfoDTO().getExceedMothPayDays())
                || Objects.equals("Y", paramsDTO.getEveryDayBillInfoDTO().getExceedYearlyPayDays())
                || Objects.equals("Y", paramsDTO.getEveryDayBillInfoDTO().getWaitFlag())) {
            paramsDTO.getSettleFactor().setRemitAmount(BigDecimal.ZERO);
            paramsDTO.getSettleFactor().setCalculateAmount(BigDecimal.ZERO);
            yearRemitAmount=BigDecimal.ZERO;
            return;
        }
        if(reasonableAmount.subtract(yearRemitAmount).compareTo(BigDecimal.ZERO)>=0){
            paramsDTO.getSettleFactor().setRemitAmount(yearRemitAmount);
            paramsDTO.getSettleFactor().setCalculateAmount(yearRemitAmount);
            yearRemitAmount=BigDecimal.ZERO;
        }else{
            paramsDTO.getSettleFactor().setRemitAmount(reasonableAmount);
            paramsDTO.getSettleFactor().setCalculateAmount(reasonableAmount);
            yearRemitAmount=yearRemitAmount.subtract(reasonableAmount);
        }


    }

    /**
     * 初始化构建 年免赔额
     */
    @Override
    public void initBuild(RemitAmountDTO remitAmountDTO){
        //
        BigDecimal configRemitAmount=remitAmountDTO.getConfigRemitAmount();
        BigDecimal usedRemitAmount=BigDecimal.ZERO;
        List<ClmsDutyDetailBillSettleDTO> usedList =clmsDutyDetailBillSettleMapper.getDutyUsedRemitAmount(remitAmountDTO);
        if(CollectionUtil.isNotEmpty(usedList)){
            usedRemitAmount=getUsedRemiamount(usedList,remitAmountDTO);
        }

        if(configRemitAmount.compareTo(usedRemitAmount)<=0){
            yearRemitAmount=BigDecimal.ZERO;
        }else{
            yearRemitAmount=configRemitAmount.subtract(usedRemitAmount);
        }

    }


    private BigDecimal getUsedRemiamount(List<ClmsDutyDetailBillSettleDTO> usedList, RemitAmountDTO remitAmountDTO) {
        Map<String,List<ClmsDutyDetailBillSettleDTO>> mapList = usedList.stream().collect(Collectors.groupingBy(ClmsDutyDetailBillSettleDTO::getReportNo));
        BigDecimal used=BigDecimal.ZERO;
        for (Map.Entry<String,List<ClmsDutyDetailBillSettleDTO>> entry :mapList.entrySet()) {
            if(remitAmountDTO.getCaseTimes()>1){
                if(Objects.equals(entry.getKey(),remitAmountDTO.getReportNo())){
                   continue;
                }
            }
            List<ClmsDutyDetailBillSettleDTO> oneReportList = entry.getValue();
            List<ClmsDutyDetailBillSettleDTO> soredList = oneReportList.stream().sorted(Comparator.comparing(ClmsDutyDetailBillSettleDTO::getCaseTimes).reversed()).collect(Collectors.toList());
            used=used.add(soredList.get(0).getRemitAmount());
        }

        return used;
    }
}
