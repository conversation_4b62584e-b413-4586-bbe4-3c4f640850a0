package com.paic.ncbs.claim.service.settle.factor.interfaces.base;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.DutyDetailSettleReasonDTO;

import java.util.List;

/**
 * 每日发票计算结果统计
 */
public interface EveryBillTotalService {
    void everyBillTotalAmount(DutyDetailSettleReasonDTO ddsrDto, List<BIllSettleResultDTO> billSettleResultDTOList, DutyDetailPayDTO detailPayDTO);
}
