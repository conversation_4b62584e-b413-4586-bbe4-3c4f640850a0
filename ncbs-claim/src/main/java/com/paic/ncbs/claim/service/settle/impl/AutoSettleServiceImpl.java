package com.paic.ncbs.claim.service.settle.impl;

import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonBenefitMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonOtherLossMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsAllowanceInfoMapper;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsCashLossInfoMapper;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsLegalLiabilityClassInfoMapper;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsPersonalInjuryDeathInfoMapper;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsPropertyLossInfoMapper;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsRescueInfoMapper;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsSubstanceLossInfoMapper;
import com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsTravelDelayInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.*;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.service.duty.DutyAttrAsyncInitService;
import com.paic.ncbs.claim.service.endcase.DutyBillLimitInfoService;
import com.paic.ncbs.claim.service.settle.AutoSettleService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service("autoSettleService")
public class AutoSettleServiceImpl implements AutoSettleService {

    @Autowired
    private PersonBenefitMapper personBenefitMapper;

    @Autowired
    private CaseClassMapper caseClassDao;

    @Autowired
    private PersonOtherLossMapper personOtherLossMapper;

    @Autowired
    private ClmsAllowanceInfoMapper allowanceInfoMapper;
    @Autowired
    private ClmsCashLossInfoMapper cashLossInfoMapper;
    @Autowired
    private ClmsLegalLiabilityClassInfoMapper legalLiabilityClassInfoMapper;
    @Autowired
    private ClmsPersonalInjuryDeathInfoMapper personalInjuryDeathInfoMapper;
    @Autowired
    private ClmsPropertyLossInfoMapper propertyLossInfoMapper;
    @Autowired
    private ClmsRescueInfoMapper rescueInfoMapper;
    @Autowired
    private ClmsSubstanceLossInfoMapper substanceLossInfoMapper;
    @Autowired
    private ClmsTravelDelayInfoMapper travelDelayInfoMapper;

    @Autowired
    private DutyAttrAsyncInitService dutyAttrAsyncInitService;

    @Autowired
    private DutyDetailPayMapper dutyDetailPayMapper;

    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;

    @Autowired
    private DutyBillLimitInfoService dutyBillLimitInfoService;



    @Override
    public void getSettleAmount(String reportNo, Integer caseTimes, List<PolicyPayDTO> copyPolicyPays) throws GlobalBusinessException {
        //清空之前的明细理算金额
        SettleHelper.clearPay(copyPolicyPays);
        //根据责任明细分类，映射对应的理算公式，给相应的自动理算金额赋值
        List<String> caseClassList = caseClassDao.getCaseClassList(reportNo, caseTimes, BpmConstants.CHECK_DUTY);
        copyPolicyPays.forEach(policy->{
            List<PlanPayDTO> planPayList = policy.getPlanPayArr();
            planPayList.forEach(plan->{
                List<DutyPayDTO> dutyPayList = plan.getDutyPayArr();
                dutyPayList.forEach(duty->{
                    final BigDecimal[] settleAmount = {BigDecimal.ZERO};
                    //保单责任和案件类别匹配
                    List<DutyDetailPayDTO> detailPayList = duty.getDutyDetailPayArr();
                    detailPayList.forEach(detail->{
                        String dutyDetailType = detail.getDutyDetailType();
                        detail.setIsDisplay(BaseConstant.TRUE);
                        /**
                         * 2024-07-10 思佳要求所有责任不论案件类型是否与责任明细责任分类匹配，一律放开 不用置灰
                         */
                        if (isDutyMatchCaseClass(caseClassList,dutyDetailType)){
                            settleAmount[0] = settleAmount[0].add(nvl(this.calculateSettleAmount(detail),0));
                            //detail.setIsDisplay(BaseConstant.TRUE);
                        }
                    });
                    duty.setSettleAmount(settleAmount[0]);
                    //理算依据
                    setSettleReason(duty);
                });
            });
        });
    }

    @Override
    public boolean isDutyMatchCaseClass(List<String> caseClassList,String dutyDetailType){
        if(ListUtils.isNotEmpty(caseClassList) && !Collections.disjoint(caseClassList,SettleConst.NO_PERSONAL_CLASS)){
            return true;
        }
        List<String> detailTypeCaseClass =  SettleConst.DETAIL_TYPE_CASE_CLASS_MAP.get(dutyDetailType);
        LogUtil.audit("--校验责任类型和当前案件类别是否匹配，caseClass:{},dutyDetailType:{}",
                caseClassList.toString(),SettleConst.DETAIL_TYPE_NAME.get(dutyDetailType));
        if (caseClassList.isEmpty() || detailTypeCaseClass == null || detailTypeCaseClass.isEmpty()){
            LogUtil.audit("--匹配结果: 未匹配上");
            return false;
        }
        boolean result = !Collections.disjoint(caseClassList,detailTypeCaseClass);
        LogUtil.audit("--匹配结果:{}", result ? "匹配" : "不匹配");
        return result;

    }

    @Override
    public void autoSettle(List<DutyDetailPayDTO> detailPayDTOS){
        if (detailPayDTOS.isEmpty()){
            throw new GlobalBusinessException(ErrorCode.NOT_NULL_ERROR,"责任明细信息");
        }
//        detailPayDTOS.forEach(this::calculateSettleAmount);
        for (DutyDetailPayDTO detailPayDTO : detailPayDTOS) {
            if(Objects.nonNull(detailPayDTO.getSettleAmount())){
                detailPayDTO.setAutoSettleAmount(detailPayDTO.getSettleAmount());
            }
        }

    }

    private BigDecimal calculateSettleAmount(DutyDetailPayDTO detail){

        String reportNo = detail.getReportNo();
        int caseTimes = detail.getCaseTimes();
        String dutyDetailType = detail.getDutyDetailType();
        BigDecimal autoSettleAmount = BigDecimal.ZERO;
        BigDecimal dutyBaseAmountPay = nvl(detail.getBaseAmountPay(),0);
        BigDecimal maxAmountPay = detail.getMaxAmountPay();//责任剩余理赔金额
        if(Objects.equals("N",detail.getIsSettleFlag())){
            detail.setAutoSettleAmount(autoSettleAmount);
            return autoSettleAmount;
        }
        // 若有赔偿限额，取较低值
        BigDecimal payLimit = detail.getPayLimit();//产品责任属性配置的限额
        if(Objects.equals("2",detail.getPayLimitType())){
            if(Objects.isNull(detail.getPayLimit())){
                throw new GlobalBusinessException("配置了限额类型且是每日限额 请配置限额金额！");
            }
            int count= detail.getDutyAttributeDTOList().size();
            if(count>=1){
                BigDecimal counts=new BigDecimal(count);
                BigDecimal sumPaylimt=payLimit.multiply(counts);
                //detail.setPayLimit(sumPaylimt);
                if (sumPaylimt.compareTo(maxAmountPay) < 0) {
                    maxAmountPay = sumPaylimt;
                }
            }

        }else {
            if (Objects.nonNull(payLimit) && payLimit.compareTo(maxAmountPay) < 0) {
                maxAmountPay = payLimit;
            }
        }


        if (dutyDetailType != null ){
            String dutyDetailTypeName = SettleConst.DETAIL_TYPE_NAME.get(dutyDetailType);
            if(BigDecimal.ZERO.compareTo(maxAmountPay) == 0){
                LogUtil.audit("--计算理算金额校验-- 当前责任明细剩余给付额为0，无需理算! 报案号：{}，责任明细类型：{}",reportNo,dutyDetailTypeName);
                return autoSettleAmount;
            }
            switch(dutyDetailType){
                case SettleConst.DETAIL_TYPE_DEATH:
                case SettleConst.DETAIL_TYPE_MAJOR_DISEASE:
                case SettleConst.DETAIL_TYPE_QUOTA:
                    LogUtil.audit("--计算理算金额参数-- 报案号：{}，责任明细类型：{}，责任明细保额：{}",reportNo,dutyDetailTypeName,dutyBaseAmountPay);
                    autoSettleAmount = dutyBaseAmountPay;
                    break;
                case SettleConst.DETAIL_TYPE_DISABILITY:
                    LogUtil.audit("--计算理算金额参数-- 报案号：{}，责任明细类型：{}，责任明细保额：{}，伤残比例：{}",reportNo,dutyDetailTypeName,dutyBaseAmountPay,detail.getDisabilityRate());
                    autoSettleAmount = dutyBaseAmountPay.multiply(nvl(detail.getDisabilityRate(),0));
                    break;
                case SettleConst.DETAIL_TYPE_MEDICAL:
                    BigDecimal sumReasonableAmount=BigDecimal.ZERO;//总的合理费用
                    if (BigDecimalUtils.NEGATIVE_ONE.equals(detail.getPayProportion())||Objects.equals("2",detail.getPayProportionType())){
                        dutyAttrAsyncInitService.getMedicalAttrAndCalculate(detail,reportNo,caseTimes,detail.getIdPolicyDuty());
                        autoSettleAmount = detail.getTempSettleAmount();
                        //autoSettleAmount= dealIsSocialMedicl(detail.getTempSettleAmount(),detail);
                        sumReasonableAmount=detail.getReasonableAmount();
                    }else {
                        List<DutyAttributeDTO>  dutyAttributeDTOList =detail.getDutyAttributeDTOList();
                        BigDecimal sumDateSettleAmount=BigDecimal.ZERO;
                        BigDecimal noLimtsettleAmount = BigDecimal.ZERO;//
                        BigDecimal sumDayhistoryAmount=BigDecimal.ZERO;//责任本次报案之前累计的账单日限额
                        //责任理算依据集合 存责任下的所有理算依据
                        List<ClmsDutySettleReasonDTO>  settlelist = new ArrayList<>();
                        LogUtil.info("责任的发票日期计算后的合理费用信息={}",JsonUtils.toJsonString(dutyAttributeDTOList));
                        if(!CollectionUtils.isEmpty(dutyAttributeDTOList)){
                            //按发票日期分组
                            //Map<Date,List<DutyAttributeDTO>>  dateListMap=  dutyAttributeDTOList.stream().collect(Collectors.groupingBy(DutyAttributeDTO::getBillDate));
                            //按日期排序后分组
                            Map<Date,List<DutyAttributeDTO>>  sortDateListMap=dutyAttributeDTOList.stream().sorted(Comparator.comparing(DutyAttributeDTO::getBillDate)).collect(Collectors.groupingBy(DutyAttributeDTO::getBillDate,LinkedHashMap::new,Collectors.toList()));
                            if(Objects.equals("Y",detail.getIsDistinguishSocia())){
                                //需要区分社保:区分社保的 要根据被保险人社保标志 发票是否经医保结算来判断，发票没有经医保结算的还要乘以罚则
                                for (Map.Entry<Date,List<DutyAttributeDTO>> entry : sortDateListMap.entrySet()) {
                                    ClmsDutySettleReasonDTO cdsrDto = new ClmsDutySettleReasonDTO();
                                    List<SettleReasonDTO> settleReasonList=new ArrayList<>();//存理算依据 存每天的
                                    List<DutyAttributeDTO> dtoList = entry.getValue();//每一个发票日期经医保结算和非经医保结算的数据集合列表
                                    if(Objects.equals("N",dtoList.get(0).getEffectiveFlag())){
                                        setReasonValue(dtoList,settleReasonList,cdsrDto,settlelist);
                                        continue;
                                    }
                                    if(Objects.equals("Y",dtoList.get(0).getExceedMothPayDays())){
                                        SettleReasonDTO reasonDTO =new SettleReasonDTO();
                                        reasonDTO.setSettleReason(Constants.EXCEEd_MONTH_PAY_DAY_NOTICE);
                                        reasonDTO.setSettleAmount(BigDecimal.ZERO);
                                        settleReasonList.add(reasonDTO);
                                        cdsrDto.setSettleReasonDTOList(settleReasonList);
                                        cdsrDto.setBillDate(DateUtils.dateFormat(dtoList.get(0).getBillDate(),DateUtils.SIMPLE_DATE_STR));
                                        settlelist.add(cdsrDto);
                                        continue;
                                    }
                                    if(Objects.equals("Y",dtoList.get(0).getExceedYearlyPayDays())){
                                        exceedYearlyPayDaysReason(settlelist,dtoList,settleReasonList,cdsrDto);
                                        continue;
                                    }
                                    //计算发票日当天的经医保和非经医保合理费用 根据公式计算出来的理算金额
                                    BigDecimal dateAmount = getMedicalSettleAmount(dtoList,detail,settleReasonList);
                                    //得到发票日当天的合理费用总和
                                    BigDecimal reasonableAmount =getDayReasonableAmount(dtoList);
                                    //统计总的合理费用
                                    sumReasonableAmount=sumReasonableAmount.add(reasonableAmount);
                                    noLimtsettleAmount=noLimtsettleAmount.add(dateAmount);
                                    //查询当前责任账单日期这天的赔付记录
                                    BigDecimal dayhistoryAmount = getDutyHistory(detail,entry.getKey());
                                    sumDayhistoryAmount=sumDayhistoryAmount.add(dayhistoryAmount);
                                    BigDecimal reportNoUpCaseTimeAmount=BigDecimal.ZERO;
                                    if(detail.getCaseTimes()>1){
                                        //重开案件 需要排查上一次(caseTimes-1)的赔付的金额
                                        //查询上一次责任赔付记录金额
                                        reportNoUpCaseTimeAmount= getUpCasetimesAmount(entry.getKey(),detail);
                                        dayhistoryAmount=dayhistoryAmount.subtract(reportNoUpCaseTimeAmount);
                                    }
                                    if(Objects.nonNull(detail.getPayLimitType())){
                                        //每日限额
                                        if(Objects.equals("2",detail.getPayLimitType())){
                                            //如果账单日期的历史金额已经大于了每日限额 则本次理算金额为0
                                            if(dayhistoryAmount.compareTo(payLimit)>=0){
                                                dateAmount=BigDecimal.ZERO;
                                                cdsrDto.setSettleZeroFlag("Y");
                                            }else{//账单日的历史总赔付金额小于每日限额
                                                //本次理算金额加上历史赔付的总金额比较 是否大于每日限额
                                                if(dateAmount.add(dayhistoryAmount).compareTo(payLimit)<0){
                                                    sumDateSettleAmount =sumDateSettleAmount.add(dateAmount);
                                                }else{
                                                    //本次计算的理算金额加上账单当日赔付历史总金额大于每日限额，那么本次账单日理算的金额等于 每日限额减去历史账单日赔付总金额
                                                    dateAmount= payLimit.subtract(dayhistoryAmount);
                                                    sumDateSettleAmount =sumDateSettleAmount.add(dateAmount) ;
                                                    cdsrDto.setPayAmount(dateAmount);
                                                    cdsrDto.setNoLimtsettleFlag("1");//打个标记 后面理算依据显示话术不一样根据这个判断

                                                }

                                            }
                                        } else if(Objects.equals("0",detail.getPayLimitType())){
                                            LogUtil.info("年限额：暂不处理");
                                            sumDateSettleAmount =sumDateSettleAmount.add(dateAmount);
                                            //计算年限额
                                        }else{
                                            LogUtil.info("不知名的限额类型 就不限额");
                                            sumDateSettleAmount =sumDateSettleAmount.add(dateAmount);
                                        }
                                    }else{
                                        LogUtil.info("没有配置限额类型 就不限限额");
                                        sumDateSettleAmount =sumDateSettleAmount.add(dateAmount);
                                    }

                                    //记录保单下的责任 每天的历史赔付金额 大于0的才记录,且是日限额类型的
                                    if(dateAmount.compareTo(BigDecimal.ZERO)>0){
                                        setDutyHistoryValue(entry.getKey(),detail,dateAmount);
                                    }
                                    cdsrDto.setSettleReasonDTOList(settleReasonList);
                                    cdsrDto.setBillDate(DateUtils.dateFormat(dtoList.get(0).getBillDate(),DateUtils.SIMPLE_DATE_STR));
                                    settlelist.add(cdsrDto);
                                }

                            }else{
                                //不区分社保身份：不区分社保的 把发票日期当天的经医保结算和非经医保结算的合理费用累加后再计算
                                for (Map.Entry<Date,List<DutyAttributeDTO>> entry : sortDateListMap.entrySet()) {
                                    ClmsDutySettleReasonDTO cdsrDto = new ClmsDutySettleReasonDTO();
                                    List<SettleReasonDTO> settleReasonList=new ArrayList<>();//存理算依据 存每天的
                                    List<DutyAttributeDTO> dtoList = entry.getValue();//每一个发票日期经医保结算和非经医保结算的数据集合列表
                                    if(Objects.equals("N",dtoList.get(0).getEffectiveFlag())){
                                        setReasonValue(dtoList,settleReasonList,cdsrDto,settlelist);
                                        continue;
                                    }
                                    if(Objects.equals("Y",dtoList.get(0).getExceedMothPayDays())){
                                        SettleReasonDTO reasonDTO =new SettleReasonDTO();
                                        reasonDTO.setSettleReason(Constants.EXCEEd_MONTH_PAY_DAY_NOTICE);
                                        reasonDTO.setSettleAmount(BigDecimal.ZERO);
                                        settleReasonList.add(reasonDTO);
                                        cdsrDto.setSettleReasonDTOList(settleReasonList);
                                        cdsrDto.setBillDate(DateUtils.dateFormat(dtoList.get(0).getBillDate(),DateUtils.SIMPLE_DATE_STR));
                                        settlelist.add(cdsrDto);
                                        continue;
                                    }

                                    if(Objects.equals("Y",dtoList.get(0).getExceedYearlyPayDays())){
                                        exceedYearlyPayDaysReason(settlelist,dtoList,settleReasonList,cdsrDto);
                                        continue;
                                    }
                                    BigDecimal dateAmount=getAutoSettlAmount(dtoList,detail,settleReasonList);
                                    //得到发票日当天的合理费用总和
                                    BigDecimal reasonableAmount =getDayReasonableAmount(dtoList);
                                    //统计总的合理费用
                                    sumReasonableAmount=sumReasonableAmount.add(reasonableAmount);

                                    noLimtsettleAmount=noLimtsettleAmount.add(dateAmount);
                                    //查询当前责任账单日期这天的赔付记录
                                    BigDecimal dayhistoryAmount = getDutyHistory(detail,entry.getKey());
                                    sumDayhistoryAmount=sumDayhistoryAmount.add(dayhistoryAmount);
                                    BigDecimal reportNoUpCaseTimeAmount=BigDecimal.ZERO;
                                    if(detail.getCaseTimes()>1){
                                        //重开案件 需要排查上一次(caseTimes-1)的赔付的金额
                                        //查询上一次责任赔付记录金额
                                        reportNoUpCaseTimeAmount= getUpCasetimesAmount(entry.getKey(),detail);
                                        dayhistoryAmount=dayhistoryAmount.subtract(reportNoUpCaseTimeAmount);
                                    }
                                    if(Objects.nonNull(detail.getPayLimitType())){
                                        //每日限额
                                        if(Objects.equals("2",detail.getPayLimitType())){
                                            //如果账单日期的历史金额已经大于了每日限额 则本次理算金额为0
                                            if(dayhistoryAmount.compareTo(payLimit)>=0){
                                                dateAmount=BigDecimal.ZERO;
                                                cdsrDto.setSettleZeroFlag("Y");
                                            }else{//账单日的历史总赔付金额小于每日限额
                                                //本次理算金额加上历史赔付的总金额比较 是否大于每日限额
                                                if(dateAmount.add(dayhistoryAmount).compareTo(payLimit)<0){
                                                    sumDateSettleAmount =sumDateSettleAmount.add(dateAmount);
                                                }else{
                                                    //本次计算的理算金额加上账单当日赔付历史总金额大于每日限额，那么本次账单日理算的金额等于 每日限额减去历史账单日赔付总金额
                                                    dateAmount= payLimit.subtract(dayhistoryAmount);
                                                    sumDateSettleAmount =sumDateSettleAmount.add(dateAmount) ;
                                                    cdsrDto.setPayAmount(dateAmount);
                                                    cdsrDto.setNoLimtsettleFlag("1");//打个标记 后面理算依据显示话术不一样根据这个判断

                                                }

                                            }
                                        } else if(Objects.equals("0",detail.getPayLimitType())){
                                            LogUtil.info("年限额：暂不处理");
                                            sumDateSettleAmount =sumDateSettleAmount.add(dateAmount);
                                            //计算年限额
                                        }else{
                                            LogUtil.info("不知名的限额类型 就不限额");
                                            sumDateSettleAmount =sumDateSettleAmount.add(dateAmount);
                                        }
                                    }else{
                                        LogUtil.info("没有配置限额类型 就不限限额");
                                        sumDateSettleAmount =sumDateSettleAmount.add(dateAmount);
                                    }

                                    //记录保单下的责任 每天的历史赔付金额 大于0的才记录,且是日限额类型的
                                    if(dateAmount.compareTo(BigDecimal.ZERO)>0){
                                        setDutyHistoryValue(entry.getKey(),detail,dateAmount);
                                    }
                                    cdsrDto.setBillDate(DateUtils.dateFormat(dtoList.get(0).getBillDate(),DateUtils.SIMPLE_DATE_STR));
                                    cdsrDto.setSettleReasonDTOList(settleReasonList);
                                    settlelist.add(cdsrDto);

                                }
                            }
                            detail.setNoLimtsettleAmount(noLimtsettleAmount);//这个值用于后面赔付依据展示用
                            detail.setDayhistoryAmount(sumDayhistoryAmount);
                            autoSettleAmount=sumDateSettleAmount;
                            //理算依据
                            detail.setClmsDutySettleReasonDTOList(settlelist);

                        }else{
                            LogUtil.info("没有录入账单或录入发票不符合计算条件");
                            autoSettleAmount=BigDecimal.ZERO;
                        }
                        //合理费用金额 如果有多张账单需要求和
                        detail.setReasonableAmount(sumReasonableAmount);

                    }
                    if(nvl(sumReasonableAmount,0).subtract(nvl(detail.getRemitAmount(),0)).compareTo(BigDecimal.ZERO) <= 0){
                        autoSettleAmount = BigDecimal.ZERO;
                    }


                    LogUtil.audit("--计算理算金额参数-- 报案号：{}，责任明细类型：{}，责任明细保额：{}，合理费用总额：{}，免赔额：{}，赔付比例：{}",
                            reportNo,dutyDetailTypeName,dutyBaseAmountPay,detail.getReasonableAmount(),
                            detail.getRemitAmount(),detail.getPayProportion());
                    break;
                case SettleConst.DETAIL_TYPE_ALLOWANCE:
                    List<PersonBenefitDTO> benefitList = personBenefitMapper.getPersonBenefit(reportNo,caseTimes, BpmConstants.CHECK_DUTY,null);
                    BigDecimal tempAmount;
                    StringBuilder settleReason = new StringBuilder();
                    if (!benefitList.isEmpty()){
                        for (PersonBenefitDTO personBenefitDTO:benefitList){
                            BigDecimal allowanceDays = personBenefitDTO.getHospitalDays();
                            if(allowanceDays == null){
                               continue;
                            }
                            allowanceDays = allowanceDays.setScale(0);
                            LogUtil.audit("--计算理算金额参数-- 报案号：{}，责任明细类型：{}，责任明细保额：{},津贴天数：{},免赔天数：{}，津贴日额：{}",
                                    reportNo,dutyDetailTypeName,dutyBaseAmountPay,allowanceDays,detail.getRemitDays(),detail.getAllowanceAmount());
                            if((nvl(allowanceDays,0).subtract(nvl(detail.getRemitDays(),0))).compareTo(BigDecimal.ZERO) > 0) {
                                tempAmount = nvl(allowanceDays, 0).subtract(nvl(detail.getRemitDays(), 0)).multiply(nvl(detail.getAllowanceAmount(), 0));
                            }else {
                                tempAmount = BigDecimal.ZERO;
                            }
                            settleReason.append(detail.getDutyDetailName()).append(SettleConst.SETTLE_REASON.get(dutyDetailType))
                                    .append(SettleConst.BENEFIT_MAPPING_MAP.get(personBenefitDTO.getBenefitType()))
                                    .append(" : (").append(nvl(allowanceDays,"0")).append(" - ")
                                    .append(nvl(detail.getRemitDays(),"0")).append(") × ")
                                    .append(nvl(detail.getAllowanceAmount(),"0"))
                                    .append("=").append(BigDecimalUtils.getScaleRoundHalfUp(nvl(tempAmount, "0"), 2)).append("<br/>");

                            autoSettleAmount = autoSettleAmount.add(tempAmount);
                        }
                    }
                    detail.setAllowanceDays(benefitList.stream().map(PersonBenefitDTO::getHospitalDays).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add));
                    detail.setDetailSettleReason(settleReason);
                    break;
                case SettleConst.DETAIL_TYPE_SERVICE:
                case SettleConst.DETAIL_TYPE_OTHERS:
                    LogUtil.audit("--计算理算金额参数-- 报案号：{}，责任明细类型：{}",reportNo,dutyDetailTypeName);
                    List<PersonOtherLossDTO> personOtherLossList = personOtherLossMapper.getPersonOtherLoss(reportNo, caseTimes, ChecklossConst.STATUS_TMP_SUBMIT, BpmConstants.CHECK_DUTY, null);
                    autoSettleAmount = personOtherLossList.stream().map(PersonOtherLossDTO::getLossAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                    break;
                // 损失类型
                case SettleConst.DETAIL_TYPE_LOSS:
                    autoSettleAmount = this.calculateLossAmount(reportNo, caseTimes);
                    break;
                default:break;
            }
        }else {
            LogUtil.info("未获取到责任明细类型，请检查！");
        }
        //
        if(maxAmountPay.compareTo(autoSettleAmount) < 0){
            LogUtil.audit("--计算理算金额结果-- 理算结果超过剩余赔付额！");
            detail.setNotice("理算结果"+autoSettleAmount+"超过剩余赔付额！按剩余赔付额"+maxAmountPay+"赔付");
            autoSettleAmount = maxAmountPay;
        }
        LogUtil.audit("--计算理算金额结果-- 自动理算金额：{}",autoSettleAmount);
        detail.setAutoSettleAmount(autoSettleAmount);
        return autoSettleAmount;
    }

    private void setReasonValue(List<DutyAttributeDTO> dtoList, List<SettleReasonDTO> settleReasonList, ClmsDutySettleReasonDTO cdsrDto, List<ClmsDutySettleReasonDTO> settlelist) {
        SettleReasonDTO reasonDTO =new SettleReasonDTO();
        reasonDTO.setSettleReason(Constants.POLICY_EFFECTIVE_OUT);
        reasonDTO.setSettleAmount(BigDecimal.ZERO);
        settleReasonList.add(reasonDTO);
        cdsrDto.setSettleReasonDTOList(settleReasonList);
        cdsrDto.setBillDate(DateUtils.dateFormat(dtoList.get(0).getBillDate(),DateUtils.SIMPLE_DATE_STR));
        settlelist.add(cdsrDto);
    }

    /**
     * 非级距区分是否有社保，是否有经医保结算
     * @param detail
     */
    private BigDecimal dealIsSocialMedicl(BigDecimal tempSettleAmount, DutyDetailPayDTO detail) {
        if(tempSettleAmount.compareTo(BigDecimal.ZERO)==0){
            return tempSettleAmount;
        }
        if(!Objects.equals("Y",detail.getIsDistinguishSocia())){
            LogUtil.info("不存在不区分社保的情况，需要产品配置正确的属性");
            return tempSettleAmount;
        }
        List<DutyAttributeDTO>  dutyAttributeDTOList =detail.getDutyAttributeDTOList();
        if(CollectionUtils.isEmpty(dutyAttributeDTOList)){
            return  tempSettleAmount;
        }
        String isSocialMedicalFlag="N";//默认都是未经医保结算的
        for (DutyAttributeDTO dto :dutyAttributeDTOList) {
            if(Objects.equals("Y",dto.getMedicalSettleFlag())){
                isSocialMedicalFlag="Y";
                break;
            }
        }
        if(Objects.equals("2",detail.getIsSociaSecurity())){//有社保
            if(!Objects.equals("3",detail.getIsSocialMedicalSettle())){
                //不区分是否经医保结算
                LogUtil.info("有社保不区分是否经医保结算直接返回金额");
               return  tempSettleAmount;
            }
            if(Objects.equals("N",isSocialMedicalFlag)){
                //未经医保结算 乘以罚则
                BigDecimal proportino= getIsSocNoStandardPenalty(detail);
                BigDecimal amount = tempSettleAmount.multiply(proportino);
                LogUtil.info("非级距有社保未经医保结算的最终理算金额乘以罚则"+tempSettleAmount+"*"+proportino+"="+amount);
                return amount;
            }

        }else{
            //无社保
            if(!Objects.equals("3",detail.getNoSocialMedicalSettle())){
                //不区分是否医保结算
                LogUtil.info("无社保不区分是否经医保结算直接返回金额");
                return  tempSettleAmount;
            }
            if(Objects.equals("N",isSocialMedicalFlag)){
                //未经医保结算 的乘以罚则
                BigDecimal noProportino= getNoSocNoStandardPenalty(detail);
                BigDecimal amount =tempSettleAmount.multiply(noProportino);
                LogUtil.info("非级距无社保区分是否经医保结算 按未经医保结算的最终理算金额乘以罚则"+tempSettleAmount+"*"+noProportino+"="+amount);
                return amount;
            }
        }

        return tempSettleAmount;
    }

    /**
     * 计算理算金额:看是否区分经医保结算
     *
     * @param dtoList
     * @param detail
     */
    private BigDecimal getMediclAutoSettleAmout(List<DutyAttributeDTO> dtoList, DutyDetailPayDTO detail,List<SettleReasonDTO> settleReasonList) {
        BigDecimal sumSettleAmount = BigDecimal.ZERO;

        //是否区分经医保结算
        if(Objects.equals("3",detail.getIsSocialMedicalSettle())){
            for (DutyAttributeDTO dto : dtoList) {
                BigDecimal  settleAmount=BigDecimal.ZERO;
                if(Objects.equals("Y",dto.getMedicalSettleFlag())){
                    BigDecimal proportion=BigDecimal.ZERO;
                    if(Objects.nonNull(detail.getIsSocMedicalProportion())){
                        proportion=nvl(detail.getIsSocMedicalProportion(),1);
                    }else {
                        proportion= nvl(detail.getPayProportion(),1);
                    }
                    //经医保结算的：（合理费用-免配额）* 有社保经医保结算比例
                    settleAmount = (nvl(dto.getReasonableAmount(),0).subtract(nvl(detail.getRemitAmount(),0)))
                            .multiply(proportion);
                    //判断 如果合理费用小于了免赔额 理算金额为o
                    if (settleAmount.compareTo(BigDecimal.ZERO)<0){
                        settleAmount=BigDecimal.ZERO;
                    }
                    //组装理算依据
                    setSettleReasonValue(settleAmount,dto.getReasonableAmount(),nvl(detail.getRemitAmount(),0),proportion,settleReasonList);
                    //计算抵扣免赔额
                    setRemitAmountValue(dto.getReasonableAmount(),detail);
                    LogUtil.info("被保险人有社保,区分是否经医保结算，经医保结算的报案号={},合理费用={}，公式理算金额={}，免赔额={}，经医保结算的赔付比列={}",detail.getReportNo(),BigDecimalUtils.toString(dto.getReasonableAmount()),BigDecimalUtils.toString(settleAmount),BigDecimalUtils.toString(detail.getRemitAmount()),BigDecimalUtils.toString(proportion));
                }else{
                   BigDecimal proportino= getNoMediclProportion(detail);
                    //未经医保结算的：(合理费用-免配额）* 有社保未经医保结算比例*罚则比例
                    settleAmount = (nvl(dto.getReasonableAmount(),0).subtract(nvl(detail.getRemitAmount(),0)))
                            .multiply(proportino);

                    //判断 如果合理费用小于了免赔额 理算金额为o
                    if (settleAmount.compareTo(BigDecimal.ZERO)<0){
                        settleAmount=BigDecimal.ZERO;
                    }
                    setSettleReasonValue(settleAmount,dto.getReasonableAmount(),nvl(detail.getRemitAmount(),0),proportino,settleReasonList);
                    //计算抵扣免赔额
                    setRemitAmountValue(dto.getReasonableAmount(),detail);
                    LogUtil.info("被保险人有社保,未经医保结算的报案号={},合理费用={},公式理算金额={}，免赔额={}，赔付比列={},有社保未经医保结算罚则={}",detail.getReportNo(),BigDecimalUtils.toString(dto.getReasonableAmount()),BigDecimalUtils.toString(settleAmount),BigDecimalUtils.toString(detail.getRemitAmount()),BigDecimalUtils.toString(detail.getPayProportion()),BigDecimalUtils.toString(detail.getPenalty()));

                }
                sumSettleAmount= sumSettleAmount.add(settleAmount);
            }
        }else{
            //不区分经医保结算
            sumSettleAmount=  getAutoSettlAmount(dtoList,detail,settleReasonList);

        }
        return sumSettleAmount;

    }

    /**
     * 免赔额抵扣 次免赔
     * @param reasonableAmount
     * @param detail
     */
    private void setRemitAmountValue(BigDecimal reasonableAmount, DutyDetailPayDTO detail) {
        if(nvl(detail.getRemitAmount(),0).compareTo(BigDecimal.ZERO)<=0){
            return;
        }
        if(!Objects.equals("0",detail.getRemitAmountType()) ){
            return;
        }
        //次免赔
        BigDecimal remitAmount=nvl(detail.getRemitAmount(),0).subtract(reasonableAmount);
        if(remitAmount.compareTo(BigDecimal.ZERO)<=0){
            detail.setRemitAmount(BigDecimal.ZERO);
        }else{
            detail.setRemitAmount(remitAmount);
        }




    }

    /**
     * 获取有社保未经医保结算的赔付比例
     * @param detail
     * @return
     */
    private BigDecimal getNoMediclProportion(DutyDetailPayDTO detail) {

        BigDecimal proporttion=BigDecimal.ZERO;
        if(Objects.nonNull(detail.getIsSocNoMedicalProportion())){
            proporttion=nvl(detail.getIsSocNoMedicalProportion(),1);
            LogUtil.info("责任编码={}，有社保配置了未经医保结算的赔付比例为 就用这个比例{}",detail.getDutyCode(),BigDecimalUtils.toString(proporttion));
        }else if(Objects.nonNull(detail.getIsSocPenaltyProportion())) {
            if(Objects.nonNull(detail.getIsSocMedicalProportion())){
                //有社保 经医保结算的赔付比例不为空的情况  赔付比例=经医保结算的比例*罚则
                proporttion=nvl(detail.getIsSocMedicalProportion(),1).multiply(nvl(detail.getIsSocPenaltyProportion(),1));
                LogUtil.info("责任编码={}，有社保配置了罚则，经医保结算的赔付比例不为空的情况  赔付比例=经医保结算的比例*罚则={}",detail.getDutyCode(),BigDecimalUtils.toString(proporttion));
            }else{
                //如果经医保结算的赔付比例为空了 就去拿最外层的固定赔付比例
                proporttion=nvl(detail.getPayProportion(),1).multiply(nvl(detail.getIsSocPenaltyProportion(),1));
                LogUtil.info("责任编码={}，经医保结算的赔付比例为空了 就去拿最外层的固定赔付比例{}",detail.getDutyCode(),BigDecimalUtils.toString(proporttion));

            }

        }else{
            proporttion=nvl(detail.getPayProportion(),1);
            LogUtil.info("责任编码={}，有社保没有配置未经医保结算比例和罚则默认拿最外层赔付比例={}",detail.getDutyCode(),BigDecimalUtils.toString(proporttion));

        }
        return proporttion;

    }

    /**
     * 计算理算金额 判断被保险人是否有社保
     * @param indtoList
     * @param detail
     * @return
     */
    private BigDecimal getMedicalSettleAmount(List<DutyAttributeDTO> indtoList, DutyDetailPayDTO detail, List<SettleReasonDTO> settleReasonList) {
        BigDecimal sumSettleAmount = BigDecimal.ZERO;
        //按是否经医保结算排序
        List<DutyAttributeDTO> dtoList =indtoList.stream().sorted(Comparator.comparing(DutyAttributeDTO::getSerialNo)).collect(Collectors.toList());
        if(Objects.equals("2",detail.getIsSociaSecurity())){
            //被保险人有社保
            sumSettleAmount =  getMediclAutoSettleAmout(dtoList,detail,settleReasonList);

        }else{
            //被保险人无社保
            sumSettleAmount= getNoSociAutoSettleAmout(dtoList,detail,settleReasonList);

        }

       return sumSettleAmount;
    }

    /**
     * 被保险人无社保的情况
     * @param dtoList
     * @param detail
     */
    private BigDecimal getNoSociAutoSettleAmout(List<DutyAttributeDTO> dtoList, DutyDetailPayDTO detail,List<SettleReasonDTO> settleReasonList) {
        BigDecimal sumSettleAmount = BigDecimal.ZERO;
        //是否区分经医保结算
        if(Objects.equals("3",detail.getNoSocialMedicalSettle())){
            for (DutyAttributeDTO dto : dtoList) {
                BigDecimal  settleAmount=BigDecimal.ZERO;
                if(Objects.equals("Y",dto.getMedicalSettleFlag())){
                    BigDecimal proportion=BigDecimal.ZERO;
                    if(Objects.nonNull(detail.getNoSocMedicalProportion())){
                        proportion=nvl(detail.getNoSocMedicalProportion(),1);
                    }else {
                        proportion= nvl(detail.getPayProportion(),1);
                    }
                    //无社保 经医保结算的：（合理费用-免配额）* 无社保经医保结算比例
                    settleAmount = (nvl(dto.getReasonableAmount(),0).subtract(nvl(detail.getRemitAmount(),0)))
                            .multiply(proportion);
                    //判断 如果合理费用小于了免赔额 理算金额为o
                    if (settleAmount.compareTo(BigDecimal.ZERO)<0){
                        settleAmount=BigDecimal.ZERO;
                    }

                    //理算依据
                    setSettleReasonValue(settleAmount,dto.getReasonableAmount(),nvl(detail.getRemitAmount(),0),proportion,settleReasonList);
                   //抵扣次免赔额
                    setRemitAmountValue(dto.getReasonableAmount(),detail);
                    LogUtil.info("被保险人无社保,区分是否经医保结算，经医保结算的报案号={},合理费用={}，公式理算金额={}，免赔额={}，经医保结算的赔付比列={}",detail.getReportNo(),BigDecimalUtils.toString(dto.getReasonableAmount()),BigDecimalUtils.toString(settleAmount),BigDecimalUtils.toString(detail.getRemitAmount()),BigDecimalUtils.toString(proportion));
                }else{
                    BigDecimal proportion= getNoSocilProportion(detail);
                    //未经医保结算的：(合理费用-免配额）* 有社保未经医保结算比例*罚则比例
                    settleAmount = (nvl(dto.getReasonableAmount(),0).subtract(nvl(detail.getRemitAmount(),0)))
                            .multiply(proportion);
                    //判断 如果合理费用小于了免赔额 理算金额为o
                    if (settleAmount.compareTo(BigDecimal.ZERO)<0){
                        settleAmount=BigDecimal.ZERO;
                    }

                    setSettleReasonValue(settleAmount,dto.getReasonableAmount(),nvl(detail.getRemitAmount(),0),proportion,settleReasonList);
                    //抵扣次免赔额
                    setRemitAmountValue(dto.getReasonableAmount(),detail);
                    LogUtil.info("被保险人有社保,未经医保结算的报案号={},合理费用={},公式理算金额={}，免赔额={}，赔付比列={},有社保未经医保结算罚则={}",detail.getReportNo(),BigDecimalUtils.toString(dto.getReasonableAmount()),BigDecimalUtils.toString(settleAmount),BigDecimalUtils.toString(detail.getRemitAmount()),BigDecimalUtils.toString(detail.getPayProportion()),BigDecimalUtils.toString(detail.getPenalty()));

                }
                sumSettleAmount= sumSettleAmount.add(settleAmount);
            }

        }else{
            //不区分经医保结算
            sumSettleAmount=  getAutoSettlAmount(dtoList,detail,settleReasonList);
        }
        return sumSettleAmount;
    }

    private BigDecimal getNoSocilProportion(DutyDetailPayDTO detail) {

        BigDecimal noProporttion=BigDecimal.ZERO;
        if(Objects.nonNull(detail.getNoSocNoMedicalProportion())){
            noProporttion=nvl(detail.getNoSocNoMedicalProportion(),1);
            LogUtil.info("责任编码={}，被保险人无社保配置了未经医保结算的赔付比例为 就用这个比例{}",detail.getDutyCode(),BigDecimalUtils.toString(noProporttion));
        }else if(Objects.nonNull(detail.getNoSocPenaltyProportion())) {
            if(Objects.nonNull(detail.getNoSocMedicalProportion())){
                //有社保 经医保结算的赔付比例不为空的情况  赔付比例=经医保结算的比例*罚则
                noProporttion=nvl(detail.getNoSocMedicalProportion(),1).multiply(nvl(detail.getNoSocPenaltyProportion(),1));
                LogUtil.info("责任编码={}，被保险人无社保配置了罚则，无社保经医保结算的赔付比例不为空的情况  赔付比例=无社保经医保结算的比例*罚则={}",detail.getDutyCode(),BigDecimalUtils.toString(noProporttion));
            }else{
                //如果经医保结算的赔付比例为空了 就去拿最外层的固定赔付比例
                noProporttion=nvl(detail.getPayProportion(),1).multiply(nvl(detail.getNoSocPenaltyProportion(),1));
                LogUtil.info("责任编码={}，被保险人无社保 经医保结算的赔付比例为空了 就去拿最外层的固定赔付比例{}",detail.getDutyCode(),BigDecimalUtils.toString(noProporttion));

            }

        }else{
            noProporttion=nvl(detail.getPayProportion(),1);
            LogUtil.info("责任编码={}，被保险人无社保没有配置未经医保结算比例和罚则默认拿最外层赔付比例={}",detail.getDutyCode(),BigDecimalUtils.toString(noProporttion));

        }
        return noProporttion;

    }


    /**
     * 计算金额
     *
     * @param dtoList
     * @param detail
     */
    private BigDecimal getAutoSettlAmount(List<DutyAttributeDTO> dtoList, DutyDetailPayDTO detail,List<SettleReasonDTO> settleReasonList) {
        BigDecimal reasonableAmount=getDayReasonableAmount(dtoList);
        BigDecimal  settleAmount=(nvl(reasonableAmount,0).subtract(nvl(detail.getRemitAmount(),0)))
                .multiply(nvl(detail.getPayProportion(),1));
        //判断 如果合理费用小于了免赔额 理算金额为o
        if (settleAmount.compareTo(BigDecimal.ZERO)<0){
            settleAmount=BigDecimal.ZERO;
        }
        setSettleReasonValue(settleAmount,reasonableAmount,nvl(detail.getRemitAmount(),0),detail.getPayProportion(),settleReasonList);

        //抵扣次免赔额
        setRemitAmountValue(reasonableAmount,detail);
        return settleAmount;
    }

    /**
     * 累计发票日当天的所有合理费用
     * @param dtoList
     */
    private BigDecimal getDayReasonableAmount(List<DutyAttributeDTO> dtoList) {
        BigDecimal amount=BigDecimal.ZERO;
        for (DutyAttributeDTO dto : dtoList) {
            amount=amount.add(dto.getReasonableAmount());
        }
        return amount;
    }

    @Override
    public void setSettleReason(DutyPayDTO duty){
        StringBuilder settleReason = new StringBuilder();
        List<DutyDetailPayDTO> detailPayArr = duty.getDutyDetailPayArr();
        for (DutyDetailPayDTO detail:detailPayArr){
            if(detail.getAutoSettleAmount() != null && BigDecimalUtils.compareBigDecimalMinus(detail.getAutoSettleAmount(),BigDecimal.ZERO)){
                detail.setAutoSettleAmount(BigDecimal.ZERO);
            }
            if(Objects.equals("N",detail.getIsSettleFlag())){
               continue;
            }
            if (detail.getIsDisplay()){
                String dutyDetailType = detail.getDutyDetailType();
                if(StringUtils.isEmptyStr(dutyDetailType)){
                    continue;
                }
                switch(dutyDetailType){
                    //伤残类型:责任明细保额*伤残比例
                    case SettleConst.DETAIL_TYPE_DISABILITY:
                        settleReason.append(detail.getDutyDetailName()).append(SettleConst.SETTLE_REASON.get(dutyDetailType));
                        settleReason.append(" = ").append(nvl(detail.getBaseAmountPay(),"0")).append(" × ")
                                .append(nvl(detail.getDisabilityRate(),"0").multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString()).append("%").append(" = ")
                                .append(BigDecimalUtils.getScaleRoundHalfUp(nvl(detail.getAutoSettleAmount(),"0"),2)).append("<br/>");
                        break;
                    //医疗类型:（合理费用-免赔额）*赔付比例
                    case SettleConst.DETAIL_TYPE_MEDICAL:
                        settleReason.append(detail.getDutyDetailName());
                        if (BigDecimalUtils.NEGATIVE_ONE.equals(detail.getPayProportion())){
                            settleReason.append(SettleConst.SETTLE_REASON.get(dutyDetailType));
                            settleReason.append(" = ").append(detail.getDetailSettleReason()).append("<br/>");
                        }else {
                            settleReason.append("：合计赔付").append(BigDecimalUtils.getScaleRoundHalfUp(nvl(detail.getAutoSettleAmount(),"0"),2)).append("元").append("<br/>");
                            setDutyReasonValue(detail,settleReason);
                        }
                        //超剩余理赔额的提示
                        if(Objects.nonNull(detail.getNotice())){
                            settleReason.append(detail.getNotice()).append("<br/>");
                        }
                        if(Objects.equals("Y",detail.getBeInHospitalFlag())){
                            settleReason.append(Constants.HOSTPITAL_NOTICE);
                        }
                        break;
                    //津贴类型:（津贴天数-免赔天数）*津贴日额
                    case SettleConst.DETAIL_TYPE_ALLOWANCE:
                        if (detail.getAllowanceDays().compareTo(BigDecimal.ZERO) > 0){
                            settleReason.append(detail.getDetailSettleReason());
                        }else {
                            settleReason.append(detail.getDutyDetailName()).append(SettleConst.SETTLE_REASON.get(dutyDetailType));
                            settleReason.append(" = ").append("(").append(nvl(detail.getAllowanceDays(),"0")).append(" - ")
                                    .append(nvl(detail.getRemitDays(),"0")).append(") × ")
                                    .append(nvl(detail.getAllowanceAmount(),"0")).append(" = ")
                                    .append(BigDecimalUtils.getScaleRoundHalfUp(nvl(detail.getAutoSettleAmount(),"0"),2)).append("<br/>");
                        }
                        break;
                    case SettleConst.DETAIL_TYPE_DEATH:
                    case SettleConst.DETAIL_TYPE_MAJOR_DISEASE:
                    case SettleConst.DETAIL_TYPE_QUOTA:
                        settleReason.append(detail.getDutyDetailName()).append(SettleConst.SETTLE_REASON.get(dutyDetailType));
                        settleReason.append(" = ").append(nvl(duty.getBaseAmountPay(),"0"));
                        break;
                    // 损失类型
                    case SettleConst.DETAIL_TYPE_LOSS:
                        settleReason.append(detail.getDutyDetailName()).append(SettleConst.SETTLE_REASON.get(dutyDetailType));
                        settleReason.append(" = ").append(BigDecimalUtils.getScaleRoundHalfUp(nvl(detail.getAutoSettleAmount(),"0"),2)).append("<br/>");
                        break;
                    default:
                        settleReason = new StringBuilder();
                        break;
                }
            }
        }
        LogUtil.info("责任设置理算依据责任编码={},理算依据={}",duty.getDutyCode(),settleReason.toString());
        duty.setSettleReason(settleReason.toString());
    }

    public static BigDecimal sum(BigDecimal... bds) {
        if (bds == null) {
            return new BigDecimal("0.00");
        }
        BigDecimal sum = new BigDecimal("0.00");
        for (BigDecimal bigDecimal : bds) {
            sum = sum.add(nvl(bigDecimal, 0));
        }
        return sum;
    }

    /**
     * 计算损失理算金额
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 损失理算金额
     */
    private BigDecimal calculateLossAmount(String reportNo, int caseTimes) {
        BigDecimal lossAmount = BigDecimal.ZERO;
        List<String> caseClassList = caseClassDao.getCaseClassList(reportNo, caseTimes, BpmConstants.CHECK_DUTY);
        if (CollectionUtils.isEmpty(caseClassList)) {
            return lossAmount;
        }

        // 人身伤亡
//        if (caseClassList.contains(InsuredApplyTypeEnum.DUTY_BODY_HURT_OR_DIED.getType())) {
//            BigDecimal settleAmout = personalInjuryDeathInfoMapper.getSettleAmout(reportNo, caseTimes);
//            lossAmount = lossAmount.add(settleAmout);
//        }

        // 财产损失
//        if (caseClassList.contains(InsuredApplyTypeEnum.DUTY_WEALTH_LOSS.getType())) {
//            BigDecimal settleAmout = propertyLossInfoMapper.getSettleAmout(reportNo, caseTimes);
//            lossAmount = lossAmount.add(settleAmout);
//        }

        // 法律责任及其他
//        if (caseClassList.contains(InsuredApplyTypeEnum.DUTY_LAW_OR_OTHER.getType())) {
//            BigDecimal settleAmout = legalLiabilityClassInfoMapper.getSettleAmout(reportNo, caseTimes);
//            lossAmount = lossAmount.add(settleAmout);
//        }

        // 物质损失
        if (caseClassList.contains(InsuredApplyTypeEnum.IDV_WEALTH_LOSS.getType())) {
            BigDecimal settleAmout = substanceLossInfoMapper.getSettleAmout(reportNo, caseTimes);
            lossAmount = lossAmount.add(settleAmout);
        }

        // 现金损失
        if (caseClassList.contains(InsuredApplyTypeEnum.IDV_WEALTH_CASH_LOSS.getType())) {
            BigDecimal settleAmout = cashLossInfoMapper.getSettleAmout(reportNo, caseTimes);
            lossAmount = lossAmount.add(settleAmout);
        }

        // 给付型津贴
        if (caseClassList.contains(InsuredApplyTypeEnum.OTHER_ALLOWANCE.getType())) {
            BigDecimal settleAmout = allowanceInfoMapper.getSettleAmout(reportNo, caseTimes);
            lossAmount = lossAmount.add(settleAmout);
        }

        // 延误类
        if (caseClassList.contains(InsuredApplyTypeEnum.DELAY_TYPE.getType())) {
            BigDecimal settleAmout = travelDelayInfoMapper.getSettleAmout(reportNo, caseTimes);
            lossAmount = lossAmount.add(settleAmout);
        }

        // 救援类
        if (caseClassList.contains(InsuredApplyTypeEnum.RESCUE_TYPE.getType())) {
            BigDecimal settleAmout = rescueInfoMapper.getSettleAmout(reportNo, caseTimes);
            lossAmount = lossAmount.add(settleAmout);
        }

        return lossAmount;
    }
    private BigDecimal getDutyHistory(DutyDetailPayDTO detail, Date billDate) {
        DutyBillLimitInfoDTO paramsDto= new DutyBillLimitInfoDTO();
        paramsDto.setPolicyNo(detail.getPolicyNo());
        paramsDto.setPlanCode(detail.getPlanCode());
        paramsDto.setDutyCode(detail.getDutyCode());
        paramsDto.setBillDate(billDate);
        BigDecimal sum = dutyBillLimitInfoMapper.getDaySum(paramsDto);
        return sum;
    }

    /**
     * 设置限额表数据
     * @param billDate
     * @param detail
     * @param dateAmount
     */
    private void setDutyHistoryValue(Date billDate, DutyDetailPayDTO detail, BigDecimal dateAmount) {
        DutyBillLimitInfoDTO dutyBillLimitInfoDTO = new DutyBillLimitInfoDTO();
        dutyBillLimitInfoDTO.setIdClmsDutyBillLimit(UuidUtil.getUUID());
        dutyBillLimitInfoDTO.setPolicyNo(detail.getPolicyNo());
        dutyBillLimitInfoDTO.setReportNo(detail.getReportNo());
        dutyBillLimitInfoDTO.setBillDate(billDate);
        dutyBillLimitInfoDTO.setBillAmount(dateAmount);
        dutyBillLimitInfoDTO.setSettleClaimAmount(dateAmount);
        dutyBillLimitInfoDTO.setCaseTimes(detail.getCaseTimes());
        dutyBillLimitInfoDTO.setDutyCode(detail.getDutyCode());
        dutyBillLimitInfoDTO.setPlanCode(detail.getPlanCode());
        dutyBillLimitInfoDTO.setDutyDetailCode(detail.getDutyDetailCode());
        //默认为0 核赔完结案更新为1
        dutyBillLimitInfoDTO.setApprovalStatus("0");
        dutyBillLimitInfoDTO.setIsDeleted("0");
        dutyBillLimitInfoDTO.setCreatedBy("system");
        dutyBillLimitInfoDTO.setUpdatedBy("system");
        dutyBillLimitInfoDTO.setCreatedDate(new Date());
        dutyBillLimitInfoDTO.setUpdatedDate(new Date());
        if(StringUtils.isEmptyStr(detail.getPayLimitType())){
            dutyBillLimitInfoDTO.setLimitType("N");//N-不限额
        }else{
            dutyBillLimitInfoDTO.setLimitType(detail.getPayLimitType());//-
        }
        if(Objects.isNull(detail.getDutyBillLimitInfoDTOList())){
            List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOList=new ArrayList<>();
            dutyBillLimitInfoDTOList.add(dutyBillLimitInfoDTO);
            detail.setDutyBillLimitInfoDTOList(dutyBillLimitInfoDTOList);
            LogUtil.info("新创建list，报案号={}，险种编码={}，责任编码={}，责任明细编码={},责任明细限额条数={}责任明细限额数据={}",detail.getReportNo(),detail.getPlanCode(),detail.getDutyCode(),detail.getDutyDetailCode(),detail.getDutyBillLimitInfoDTOList().size(),JsonUtils.toJsonString(detail.getDutyBillLimitInfoDTOList()));
        }else{
            detail.getDutyBillLimitInfoDTOList().add(dutyBillLimitInfoDTO);
            LogUtil.info("已有list，报案号={}，险种编码={}，责任编码={}，责任明细编码={},责任明细限额条数={}责任明细限额数据={}",detail.getReportNo(),detail.getPlanCode(),detail.getDutyCode(),detail.getDutyDetailCode(),detail.getDutyBillLimitInfoDTOList().size(),JsonUtils.toJsonString(detail.getDutyBillLimitInfoDTOList()));

        }



    }

    /**
     * 上一次赔付次数的赔付限额
     * @param billDate
     * @param detail
     * @return
     */
    private BigDecimal getUpCasetimesAmount(Date billDate, DutyDetailPayDTO detail) {
        DutyBillLimitInfoDTO paramsDto= new DutyBillLimitInfoDTO();
        paramsDto.setReportNo(detail.getReportNo());
        paramsDto.setPolicyNo(detail.getPolicyNo());
        paramsDto.setPlanCode(detail.getPlanCode());
        paramsDto.setDutyCode(detail.getDutyCode());
        paramsDto.setBillDate(billDate);
        paramsDto.setCaseTimes(detail.getCaseTimes()-1);
        BigDecimal upAmount=  dutyBillLimitInfoMapper.getUpCasetimesAmount(paramsDto);
        LogUtil.info("报案号={},金额={}",detail.getReportNo(),BigDecimalUtils.toString(upAmount), JsonUtils.toJsonString(paramsDto));
        return upAmount;
    }

    /**
     * 理算依据组装
     * @param settleAmount 理算金额
     * @param reasonableAmount 合理费用
     * @param remitAmount 免赔额
     * @param proportion 赔付比例
     * @param settleReasonList 理算依据集合
     */
    private void setSettleReasonValue(BigDecimal settleAmount,BigDecimal reasonableAmount, BigDecimal remitAmount, BigDecimal proportion,List<SettleReasonDTO> settleReasonList) {
        SettleReasonDTO dto = new SettleReasonDTO();
        StringBuilder settleReason = new StringBuilder();
        String  strProportion=nvl(proportion,1).multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString();
        if(reasonableAmount.subtract(nvl(remitAmount,0)).compareTo(BigDecimal.ZERO)<0){
            dto.setLessThanRemitAmountFlag("Y");
            //组装理算依据用  不足免赔额的情况下 改成(100.00-100.00) × 70%+(200.00-100.00) × 80%=80.00 这种样式，保持等号前后值相等
            remitAmount=reasonableAmount;
        }
        if(nvl(remitAmount,0).compareTo(BigDecimal.ZERO)==0){
            settleReason.append(reasonableAmount.setScale(2, RoundingMode.HALF_UP)).append(" × ").append(strProportion).append("%");
        }else{
            settleReason.append("(").append(reasonableAmount.setScale(2, RoundingMode.HALF_UP)).append("-").append(nvl(remitAmount.setScale(2, RoundingMode.HALF_UP),0)).append(") × ").append(strProportion).append("%");
        }
        dto.setSettleReason(settleReason.toString());
        dto.setSettleAmount(settleAmount);
        settleReasonList.add(dto);
    }

    /**
     * 设置责任最终理算依据
     * @param detail
     */
    private void setDutyReasonValue(DutyDetailPayDTO detail,StringBuilder settleReason) {
        List<ClmsDutySettleReasonDTO> settleReasonDTOList=detail.getClmsDutySettleReasonDTOList();
        if(Objects.isNull(settleReasonDTOList)){
           return;
        }
        if(Objects.nonNull(detail.getPayLimitType()) && Objects.equals("2",detail.getPayLimitType())){
            setLimitValues(detail,settleReason);//限额情况
        }else{
            //不限额的情况
            setNoLimitValues(detail,settleReason);
        }



    }

    private void setNoLimitValues(DutyDetailPayDTO detail, StringBuilder settleReason) {
        List<ClmsDutySettleReasonDTO> settleReasonDTOList=detail.getClmsDutySettleReasonDTOList();
        if(CollectionUtils.isEmpty(settleReasonDTOList)){
            String  strProportion=nvl(detail.getPayProportion(),1).multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString();

            if(nvl(detail.getRemitAmount(),0).compareTo(BigDecimal.ZERO)==0){
                settleReason.append(detail.getReasonableAmount().setScale(2,RoundingMode.HALF_UP)).append(" × ").append(strProportion).append("%=").append(detail.getAutoSettleAmount()).append("<br/>");
            }else{
                settleReason.append("(").append(detail.getReasonableAmount().setScale(2,RoundingMode.HALF_UP)).append("-").append(detail.getRemitAmount()).append(") × ").append(strProportion).append("%=").append(detail.getAutoSettleAmount()).append("<br/>");

            }
        }else{
            List<ClmsDutySettleReasonDTO> sortList= settleReasonDTOList.stream().sorted(Comparator.comparing(ClmsDutySettleReasonDTO::getBillDate)).collect(Collectors.toList());
            for (ClmsDutySettleReasonDTO settleReasonDTO: sortList) {
                List<SettleReasonDTO> settleList =  settleReasonDTO.getSettleReasonDTOList();
                settleReason.append(settleReasonDTO.getBillDate()).append("号理算金额：");
                if(settleList.size()==1){
                    settleReason.append(settleList.get(0).getSettleReason()).append("=").append(settleList.get(0).getSettleAmount().setScale(2, RoundingMode.HALF_UP));
                    if(Objects.equals("Y",settleList.get(0).getLessThanRemitAmountFlag())){
                        settleReason.append(Constants.LESS_THAN_REMIT_AMOUNT);
                    }
                    settleReason.append("<br/>");
                }else{
                    BigDecimal everyDaySumAmount=BigDecimal.ZERO;
                    for (SettleReasonDTO dto : settleList) {
                        settleReason.append(dto.getSettleReason());
                        if(Objects.equals("Y",dto.getLessThanRemitAmountFlag())){
                            settleReason.append(Constants.LESS_THAN_REMIT_AMOUNT);
                        }
                        settleReason.append("+");
                        everyDaySumAmount=everyDaySumAmount.add(dto.getSettleAmount());
                    }
                    LogUtil.info("报案号"+detail.getReportNo()+"理算依据"+settleReason.toString());
                    settleReason.deleteCharAt(settleReason.lastIndexOf("+"));
                    settleReason.append("=").append(everyDaySumAmount.setScale(2,RoundingMode.HALF_UP));
                    settleReason.append("<br/>");
                }
            }

        }

    }

    private void setLimitValues(DutyDetailPayDTO detail, StringBuilder settleReason) {
        List<ClmsDutySettleReasonDTO> settleReasonDTOList=detail.getClmsDutySettleReasonDTOList();
        List<ClmsDutySettleReasonDTO> sortList= settleReasonDTOList.stream().sorted(Comparator.comparing(ClmsDutySettleReasonDTO::getBillDate)).collect(Collectors.toList());
        for (ClmsDutySettleReasonDTO clmsDto : sortList) {
            List<SettleReasonDTO> settleDtoList =clmsDto.getSettleReasonDTOList();
            settleReason.append(clmsDto.getBillDate()).append("号理算金额：");
            if(settleDtoList.size()==1){
                settleReason.append(settleDtoList.get(0).getSettleReason()).append("=").append(settleDtoList.get(0).getSettleAmount().setScale(2, RoundingMode.HALF_UP));
                if(Objects.equals("Y",settleDtoList.get(0).getLessThanRemitAmountFlag())){
                    settleReason.append(Constants.LESS_THAN_REMIT_AMOUNT);
                }
            }else{
                BigDecimal everyDaySumAmount=BigDecimal.ZERO;
                for (SettleReasonDTO dto : settleDtoList) {
                    settleReason.append(dto.getSettleReason());
                    if(Objects.equals("Y",dto.getLessThanRemitAmountFlag())){
                        settleReason.append(Constants.LESS_THAN_REMIT_AMOUNT);
                    }
                    settleReason.append("+");
                    everyDaySumAmount=everyDaySumAmount.add(dto.getSettleAmount());
                }
                settleReason.deleteCharAt(settleReason.lastIndexOf("+"));
                settleReason.append("=").append(everyDaySumAmount.setScale(2,RoundingMode.HALF_UP));
            }

            if(Objects.nonNull(detail.getPayLimitType()) && Objects.equals("2",detail.getPayLimitType())){
                if(Objects.equals("1",clmsDto.getNoLimtsettleFlag())){
                    settleReason.append("(理算金额超日限额本日按照限额给付").append(BigDecimalUtils.getScaleRoundHalfUp(nvl(clmsDto.getPayAmount(),"0"),2)).append("元)");
                }
                if(Objects.equals("Y",clmsDto.getSettleZeroFlag())){
                    settleReason.append("(当日赔付金额已超限额,本次按0元赔付)");
                }
            }
            settleReason.append("<br/>");

        }
    }

    /**
     * 获取非级距的罚则
     * @param detail
     * @return
     */
    private BigDecimal getIsSocNoStandardPenalty(DutyDetailPayDTO detail) {

        BigDecimal penalty=BigDecimal.ZERO;
        if(Objects.nonNull(detail.getIsSocPenaltyProportion())) {
            penalty= nvl(detail.getIsSocPenaltyProportion(),1);
            LogUtil.info("责任编码={}，非标准级距有社保配置了未经医保结算的罚则={}",detail.getDutyCode(),BigDecimalUtils.toString(penalty));
        }else{
            penalty=new BigDecimal(1);
            LogUtil.info("责任编码={}，非标准级距有社保没有配置未经医保结算比例和罚则默认100%={}",detail.getDutyCode(),BigDecimalUtils.toString(penalty));

        }
        return penalty;

    }

    /**
     * 获取无社保情况下的未经医保结算比例
     * @param detail
     * @return
     */
    private BigDecimal getNoSocNoStandardPenalty(DutyDetailPayDTO detail) {

        BigDecimal penalty=BigDecimal.ZERO;
        if(Objects.nonNull(detail.getNoSocPenaltyProportion())) {
            penalty=nvl(detail.getNoSocPenaltyProportion(),1);
            LogUtil.info("责任编码={}，被保险人无社保未经医保结算罚则={}",detail.getDutyCode(),BigDecimalUtils.toString(penalty));

        }else{
            penalty=new BigDecimal(1);
            LogUtil.info("责任编码={}，被保险人无社保没有配置未经医保结算罚则默认100%",detail.getDutyCode());

        }
        return penalty;

    }
    private void exceedYearlyPayDaysReason(List<ClmsDutySettleReasonDTO> settlelist, List<DutyAttributeDTO> dtoList, List<SettleReasonDTO> settleReasonList, ClmsDutySettleReasonDTO cdsrDto) {
        SettleReasonDTO reasonDTO =new SettleReasonDTO();
        reasonDTO.setSettleReason(Constants.EXCEEd_YEARLY_PAY_DAY_NOTICE);
        reasonDTO.setSettleAmount(BigDecimal.ZERO);
        settleReasonList.add(reasonDTO);
        cdsrDto.setSettleReasonDTOList(settleReasonList);
        cdsrDto.setBillDate(DateUtils.dateFormat(dtoList.get(0).getBillDate(),DateUtils.SIMPLE_DATE_STR));
        settlelist.add(cdsrDto);
    }
}
