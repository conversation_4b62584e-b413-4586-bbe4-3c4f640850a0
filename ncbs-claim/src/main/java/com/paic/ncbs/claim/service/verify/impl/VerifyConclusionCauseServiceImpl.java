package com.paic.ncbs.claim.service.verify.impl;


import com.paic.ncbs.claim.dao.mapper.verify.VerifyConclusionCauseMapper;
import com.paic.ncbs.claim.model.dto.verify.VerifyConclusionCauseDTO;
import com.paic.ncbs.claim.service.verify.VerifyConclusionCauseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("verifyConclusionCauseService")
public class VerifyConclusionCauseServiceImpl implements VerifyConclusionCauseService {

	@Autowired
	private VerifyConclusionCauseMapper verifyConclusionCauseMapper;
	
	 
	@Override
	public List<VerifyConclusionCauseDTO> getVerifyConclusionCauseList() {
		return verifyConclusionCauseMapper.getVerifyConclusionCauseList();
	}

}
