package com.paic.ncbs.claim.replevy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 追偿主表查询结果包装DTO
 */
@Data
@ApiModel("追偿主表查询结果包装DTO")
public class ReplevyMainQueryResultDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty("追偿主表查询结果列表")
    private List<ReplevyMainQueryDTO> resultList;
    
    @ApiModelProperty("显示按钮标志：0-不显示 1-显示")
    private Integer displayButton;
}