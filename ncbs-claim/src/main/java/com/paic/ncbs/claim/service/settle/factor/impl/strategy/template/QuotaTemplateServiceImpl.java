package com.paic.ncbs.claim.service.settle.factor.impl.strategy.template;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.template.AbstractTemplateService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.print.util.PdfHelpUtils;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.util.Map;

/**
 * 一般定额
 */
@Slf4j
@Service
public class QuotaTemplateServiceImpl extends AbstractTemplateService {

    @Override
    public void setTemplate(DutyDetailPayDTO detailPayDTO) {
        templatePath=Constants.TEMPLATE_FTL_MAP.get(detailPayDTO.getDutyDetailType());
    }

    @Override
    public boolean isCustomized() {
        return false;
    }

    @Override
    public String customizeReason(DutyDetailPayDTO detailPayDTO) {
      return "";
    }
}
