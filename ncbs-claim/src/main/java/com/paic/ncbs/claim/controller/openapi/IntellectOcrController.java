package com.paic.ncbs.claim.controller.openapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.HttpClientUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.fileupload.FileInfoMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPlanDutyDTO;
import com.paic.ncbs.claim.model.dto.ocr.BillOcrRecordDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.ocr.OcrFileUrlVO;
import com.paic.ncbs.claim.model.vo.settle.PolicyInfoVO;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.claim.service.ocr.IBillOcrRecordService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/public/app/ocr")
@RefreshScope
public class IntellectOcrController extends BaseController {

    @Autowired
    private IOBSFileUploadService iobsFileUploadService;

    @Autowired
    private FileInfoMapper fileInfoMapper;

    @Autowired
    private IBillOcrRecordService billOcrRecordService;

    @Autowired
    private ReportInfoExMapper reportInfoExMapper;

    @Autowired
    private PolicyInfoMapper policyInfoMapper;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Value("${ocr.pushOcrUrl}")
    private String pushOcrUrl;

    @Value("${gift.productPackage}")
    private List<String> giftProductPackage;

    @PostMapping(value = "/getIntranetIOBSDownloadUrl")
    public ResponseResult<String> getIntranetIOBSDownloadUrl(@RequestBody OcrFileUrlVO ocrFileUrlVO) {
        String iobsFileDownloadUrl = iobsFileUploadService.getPerpetualDownloadAuditUrl(ocrFileUrlVO.getFilePath(), ocrFileUrlVO.getFileName());
        return ResponseResult.success(iobsFileDownloadUrl);
    }

    @GetMapping(value = "/pushOcr")
    public ResponseResult pushOcr(@RequestParam("reportNo") String reportNo, @RequestParam("ocrAuditFlag") Boolean ocrAuditFlag) {
        List<FileDocumentDTO> fileDocumentDTOS = fileInfoMapper.queryDocumentByGroupId(reportNo);
        List<PolicyInfoDTO> policyInfoListByReportNo = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
        fileDocumentDTOS = fileDocumentDTOS.stream().filter(i -> "005".equals(i.getDocumentClass())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fileDocumentDTOS)) {
            log.warn("未查询到需推送的图片,reportNo:{}", reportNo);
            return ResponseResult.success();
        }
        List<Map<String, String>> imageList = new ArrayList<>();
        for (FileDocumentDTO fileDocumentDTO : fileDocumentDTOS) {
            Map<String, String> imageMap = new HashMap<>(2);
            imageMap.put("fileName", fileDocumentDTO.getDocumentName());
            imageMap.put("imageUrl", fileDocumentDTO.getUploadPath());
            imageList.add(imageMap);
        }
        Map<String, Object> paramMap = new HashMap<>(6);
        paramMap.put("casNum", reportNo);
        paramMap.put("requestSource", "LM");
        paramMap.put("imageList", imageList);
        paramMap.put("productType", "01");
        if(!CollectionUtils.isEmpty(policyInfoListByReportNo)){
            paramMap.put("productCode", policyInfoListByReportNo.get(0).getProductCode());
            paramMap.put("productPackageType", policyInfoListByReportNo.get(0).getProductPackageType());
            if(giftProductPackage.contains(policyInfoListByReportNo.get(0).getProductPackageType())) {
                paramMap.put("productType", "02");
            }
        }

        log.info("调用ocr案件处理接口，url：{}，paramMap：{}", pushOcrUrl, JsonUtils.toJsonString(paramMap));
        String result = HttpClientUtil.doPost(pushOcrUrl, JsonUtils.toJsonString(paramMap));
        log.info("调用ocr案件处理接口，result：{}", result);
        if (StringUtils.isEmptyStr(result)) {
            log.error("调用ocr案件处理接口返回异常，reportNo：{}", reportNo);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(), GlobalResultStatus.FAIL.getMsg());
        }
        JSONObject jsonObject = JSON.parseObject(result);
        String code = jsonObject.getString("code");
        if (!"0000".equals(code)) {
            log.error("调用ocr案件处理接口返回失败，reportNo：{}，result：{}", reportNo, result);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(), GlobalResultStatus.FAIL.getMsg());
        }

        try {
            if (null != ocrAuditFlag && ocrAuditFlag) {
                reportInfoExMapper.updateCompanyIdByReportNo(reportNo, "AiModel");
                TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
                //更新条件
                taskInfoDTO.setCaseTimes(1);
                taskInfoDTO.setReportNo(reportNo);
                taskInfoDTO.setStatus(BpmConstants.TASK_STATUS_PENDING);
                taskInfoDTO.setTaskDefinitionBpmKey(BpmConstants.OC_REPORT_TRACK);

                taskInfoDTO.setAssigner(ConstValues.SYSTEM_NAME);
                taskInfoDTO.setAssigneeName(ConstValues.SYSTEM_NAME);
                taskInfoDTO.setUpdatedBy(ConstValues.SYSTEM_NAME);
                taskInfoDTO.setUpdatedDate(new Date());
                taskInfoMapper.updateTaskAssigner(taskInfoDTO);
            }
        } catch (Exception e) {
            log.error("下发模型后更新reportEx异常：reportNo:{},message:{}", reportNo, e.getMessage(), e);
        }


        return ResponseResult.success();
    }

    @PostMapping(value = "/saveOcrBillData")
    public ResponseResult saveOcrBillData(@RequestBody List<BillOcrRecordDTO> billOcrRecordDTOList) throws Exception {
        billOcrRecordService.saveRecordByCase(billOcrRecordDTOList);
        return ResponseResult.success();
    }

}
