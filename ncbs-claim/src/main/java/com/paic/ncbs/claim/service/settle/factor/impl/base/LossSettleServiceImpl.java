package com.paic.ncbs.claim.service.settle.factor.impl.base;

import com.googlecode.aviator.Expression;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.*;
import com.paic.ncbs.claim.service.settle.factor.abstracts.calculate.AbstractCalculateAmountFactor;
import com.paic.ncbs.claim.service.settle.factor.interfaces.base.BaseSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.calculate.CalculateAmountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

/**
 * 损失 理算公式：物质损失+现金损失+给付型津贴+延误类+救援类
 */
@Slf4j
@Service
public class LossSettleServiceImpl implements BaseSettleService {

    @Autowired
    private AbstractCalculateAmountFactor abstractCalculateAmountFactor;

    @Override
    public void getSettleAmount(ClaimCaseDTO claimCaseDTO, DutyDetailPayDTO detailPayDTO, Expression expression) {
        Map<String, Object> calparamsMap = new HashMap<>();
        CalculateParamsDTO paramsDTO = new CalculateParamsDTO();
        paramsDTO.setDutyDetailPayDTO(detailPayDTO);
        paramsDTO.setSettleFactor(new SettleFactor());
        paramsDTO.setCaseClassList(claimCaseDTO.getCaseClassList());
        for (Map.Entry<String, CalculateAmountService> en : detailPayDTO.getCalServiceImplMap().entrySet()) {
            CalculateAmountService service = en.getValue();
            if (Objects.isNull(service)) {
                throw new GlobalBusinessException("获取实现类为null名称" + service);
            }
            abstractCalculateAmountFactor.setCalculateReasonAmountInterface(service);
            abstractCalculateAmountFactor.getAmout(paramsDTO);
            calparamsMap.put(en.getKey(), paramsDTO.getSettleFactor().getCalculateAmount());
        }

        BigDecimal autoSettleAmount = (BigDecimal) expression.execute(calparamsMap);//理算金额
        if(autoSettleAmount.compareTo(BigDecimal.ZERO) < 0){
            autoSettleAmount = BigDecimal.ZERO;
        }

        BigDecimal maxAmountPay = detailPayDTO.getMaxAmountPay();//责任剩余理赔金额
        if(autoSettleAmount.compareTo(maxAmountPay) <= 0){
            detailPayDTO.setAutoSettleAmount(autoSettleAmount.setScale(2, RoundingMode.HALF_UP));
        }else {
            detailPayDTO.setAutoSettleAmount(maxAmountPay);
            detailPayDTO.setNotice("理算结果"+autoSettleAmount+"超过剩余赔付额！按剩余赔付额"+maxAmountPay+"赔付");
        }

        //理算依据参数设置
        DetailSettleReasonTemplateDTO detailSettleReasonTemplateDTO =new DetailSettleReasonTemplateDTO();
        detailSettleReasonTemplateDTO.setDutyDetailName(detailPayDTO.getDutyDetailName());//责任明细名称
        detailSettleReasonTemplateDTO.setAutoSettleAmount(BigDecimalUtils.toString(autoSettleAmount));//最终理算结果
        detailSettleReasonTemplateDTO.setNotice(detailPayDTO.getNotice());
        detailPayDTO.setDetailSettleReasonTemplateDTO(detailSettleReasonTemplateDTO);

    }
}
