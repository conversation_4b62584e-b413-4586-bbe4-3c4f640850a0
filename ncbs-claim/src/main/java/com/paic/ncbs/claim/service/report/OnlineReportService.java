package com.paic.ncbs.claim.service.report;

import com.paic.ncbs.claim.model.vo.report.OnlineReportCheckResponseVO;
import com.paic.ncbs.claim.model.vo.report.OnlineReportCheckVO;
import com.paic.ncbs.claim.model.vo.report.OnlineReportResponseVO;
import com.paic.ncbs.claim.model.vo.report.OnlineReportVO;

public interface OnlineReportService {

    OnlineReportResponseVO saveOnlineReport(OnlineReportVO onlineReportVO);

    /**
      *
      * @Description 报案前检查接口
      * <AUTHOR>
      * @Date 2023/11/28 10:36
      **/
    OnlineReportCheckResponseVO checkOnlineReport(OnlineReportCheckVO onlineReportCheckVO);

    /**
     * @Description TPA报案接口
     * @param onlineReportVO
     * @return
     */
    OnlineReportResponseVO saveReport(OnlineReportVO onlineReportVO);
}
