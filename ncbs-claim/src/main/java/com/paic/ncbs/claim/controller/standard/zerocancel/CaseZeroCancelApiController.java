package com.paic.ncbs.claim.controller.standard.zerocancel;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.api.StandardRequestDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.vo.casezero.ProblemCaseVO;
import com.paic.ncbs.claim.service.casezero.CaseZeroCancelService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Api(tags = "零结注销")
@RestController
@RequestMapping("/public/caseZeroCancel")
public class CaseZeroCancelApiController extends BaseController {

    @Autowired
    private CaseZeroCancelService caseZeroCancelService;

    @Autowired
    private TaskInfoService taskInfoService;

    @ResponseBody
    @ApiOperation(value = "零结注销申请提交")
    @PostMapping(value = "/saveCaseZeroCancelApply")
    public ResponseResult<Object> saveCaseZeroCancelApply(@RequestBody StandardRequestDTO caseZeroCancelAplyDTO) {
        String jsonStr = JSONObject.toJSONString(caseZeroCancelAplyDTO.getRequestData());
        CaseZeroCancelDTO caseZeroCancelDTO = JSONObject.parseObject(jsonStr, CaseZeroCancelDTO.class);
        LogUtil.audit("#TPA发送零结注销申请#  reportNo:{},caseTimes:{}", caseZeroCancelDTO.getReportNo(), caseZeroCancelDTO.getCaseTimes());
        // 保存零注申请信息
        caseZeroCancelDTO.setStatus("1");
        caseZeroCancelService.saveCaseZeroCancelApply(caseZeroCancelDTO);
        //查询taskId并返回
        ProblemCaseVO problemCaseVO = new ProblemCaseVO();
        String taskId = taskInfoService.getLatestTaskId(caseZeroCancelDTO.getReportNo(),caseZeroCancelDTO.getCaseTimes(), BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT);
        problemCaseVO.setProblemNo(taskId);
        return ResponseResult.success(problemCaseVO);
    }

    
}
