package com.paic.ncbs.claim.controller.user;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags="用户管理")
@RestController
@RequestMapping(value = "app/userInfoAction")
public class UserInfoController extends BaseController {

	@Autowired
	private UserInfoService userInfoService;

	@ApiOperation("获取所有二级机构")
	@GetMapping(value = "/getLevel2DeptListExcludeUser")
	public ResponseResult<List<DepartmentDTO>> getLevel2DeptListExcludeUser() throws GlobalBusinessException {
		List<DepartmentDTO> deptList = userInfoService.getLevel2DeptListExcludeUser(WebServletContext.getUserId());
		LogUtil.audit("#获取所有二级机构，排除当前用户所在二级机构#,传出参数deptList=" + JSON.toJSONString(deptList));
		return ResponseResult.success(deptList);
	}

	@GetMapping("/getUserInfoDTO")
	public ResponseResult<UserInfoDTO> getUserInfoDTO(@RequestParam ("userCode")String userCode){
		return ResponseResult.success(userInfoService.getUserInfoDTO(userCode));
	}


}