package com.paic.ncbs.claim.service.settle.impl;

import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.settle.BatchMapper;
import com.paic.ncbs.claim.dao.mapper.settle.EndorsementMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.SettleBatchMapper;
import com.paic.ncbs.claim.model.dto.settle.BatchDTO;
import com.paic.ncbs.claim.model.dto.settle.SettleBatchInfoDTO;
import com.paic.ncbs.claim.model.vo.settle.SttleBatchInfoVO;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.settle.SettleBatchService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service("settleBatchService")
public class SettleBatchServiceImpl implements SettleBatchService {

    @Autowired
    private SettleBatchMapper settleBatchDao;

    @Autowired
    private BatchMapper batchDao;

    @Autowired
    private EndorsementMapper endorsementMapper;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private PolicyPayService policyPayService;

    /**
     * 查询核赔批单信息，计算赔付金额合计信息
     */
    @Override
    public SttleBatchInfoVO getSettleAmountsSum(String reportNo, Integer caseTimes) {
        SettleBatchInfoDTO settleAmountsSum = settleBatchDao.getSettleAmountsSum(reportNo, caseTimes);
        SttleBatchInfoVO sttleBatchInfoVO = new SttleBatchInfoVO();
        if (settleAmountsSum == null) {
            settleAmountsSum = new SettleBatchInfoDTO();
            LogUtil.audit("报案号:{},赔付次数:{},查询赔付数据为空,无法计算赔付金额合计信息", reportNo, caseTimes);
            sttleBatchInfoVO.setIndemnityMode(settleAmountsSum.getIndemnityMode());
            sttleBatchInfoVO.setEndorseTemplate("");
            sttleBatchInfoVO.setSettleBatchInfoDTO(settleAmountsSum);
            return sttleBatchInfoVO;
        }
        BatchDTO batchDTO = batchDao.getBatch(reportNo, caseTimes);
        if(batchDTO != null){
            batchDTO.setSettleUserName(userInfoService.getUserNameById(batchDTO.getSettleUserUm()));
        }
        sttleBatchInfoVO.setBatchDTO(batchDTO);
        sttleBatchInfoVO.setPolicyPayTotal(BigDecimalUtils.sum(settleAmountsSum.getPolicyPayAmount(), settleAmountsSum.getPolicyFee()));
        sttleBatchInfoVO.setFinalPay(BigDecimalUtils.sum(settleAmountsSum.getFinalPayAmount(), settleAmountsSum.getFinalFee()));
        sttleBatchInfoVO.setPrePay(BigDecimalUtils.sum(settleAmountsSum.getPrePayAmount(), settleAmountsSum.getPreFeeAmount()));
        sttleBatchInfoVO.setPolicyPayTotalNoFee(settleAmountsSum.getPolicyPayAmount());
        sttleBatchInfoVO.setFinalPayNoFee(nvl(settleAmountsSum.getFinalPayAmount(),0).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : settleAmountsSum.getFinalPayAmount());
        sttleBatchInfoVO.setPrePayNoFee(settleAmountsSum.getPrePayAmount());
        SettleBatchInfoDTO proAndAccInfo = settleBatchDao.getAcmAndProAmountSum(reportNo, caseTimes);
        if (null == proAndAccInfo) {
            proAndAccInfo = new SettleBatchInfoDTO();
        }
        sttleBatchInfoVO.setProtocolAmount(proAndAccInfo.getProtocolAmount());
        sttleBatchInfoVO.setAccommodationAmount(proAndAccInfo.getAccommodationAmount());
        sttleBatchInfoVO.setIndemnityMode(settleAmountsSum.getIndemnityMode());
        BigDecimal originalAmount;
        if (SettleConst.INDEMNITY_MODE_PROTOCOL.equals(sttleBatchInfoVO.getIndemnityMode())) {
            BigDecimal protocolAmount = nvl(proAndAccInfo.getProtocolAmount(), 0);
            originalAmount = (settleAmountsSum.getPolicyPayAmount() == null ? BigDecimal.ZERO : settleAmountsSum.getPolicyPayAmount()).subtract(protocolAmount);
            settleAmountsSum.setProtocolAmount(protocolAmount);
        } else if(SettleConst.INDEMNITY_MODE_ACCOMMODATE.equals(sttleBatchInfoVO.getIndemnityMode())){
            BigDecimal accommodationAmount = nvl(proAndAccInfo.getAccommodationAmount(), 0);
            originalAmount = (settleAmountsSum.getPolicyPayAmount() == null ? BigDecimal.ZERO : settleAmountsSum.getPolicyPayAmount()).subtract(accommodationAmount);
            settleAmountsSum.setAccommodationAmount(accommodationAmount);
        }else {
            originalAmount = settleAmountsSum.getPolicyPayAmount();
        }
        sttleBatchInfoVO.setOriginalAmount(originalAmount);
        settleAmountsSum.setOriginalAmount(originalAmount);
        sttleBatchInfoVO.setSettleBatchInfoDTO(settleAmountsSum);
        if(ChecklossConst.CONCLUSION_PROTOCOL_VERIFY_PAY.equals(settleAmountsSum.getIndemnityMode())||ChecklossConst.CONCLUSION_ACCOMMODATION_VERIFY_PAY.equals(settleAmountsSum.getIndemnityMode())){
            sttleBatchInfoVO.setEndorseTemplate(endorsementMapper.getEndorseTemplate(settleAmountsSum.getIndemnityMode()));
        }

        // 计算重开应支付赔款金额
        if (caseTimes > 1) {
            BigDecimal lastPolicyPayAmount = policyPayService.getLastPolicyPayAmount(reportNo, caseTimes);
            settleAmountsSum.setLastPolicyPayAmount(lastPolicyPayAmount);

            // 重开时，本次应支付赔款金额需再减去前次已支付赔款金额
            if (settleAmountsSum.getFinalPayAmount() != null) {
                settleAmountsSum.setFinalPayAmount(settleAmountsSum.getFinalPayAmount().subtract(lastPolicyPayAmount));
            }
        }
        return sttleBatchInfoVO;
    }

    @Override
    public SettleBatchInfoDTO getPartSettleSum(String reportNo, Integer caseTimes, String caseNo) {
        SettleBatchInfoDTO partSettleSumVO = settleBatchDao.getPartSettleSum(reportNo, caseTimes, caseNo);
        if (null == partSettleSumVO) {
            partSettleSumVO = new SettleBatchInfoDTO();
        }
        return partSettleSumVO;
    }

    @Override
    public SettleBatchInfoDTO getPartAcmAndProSum(String reportNo, Integer caseTimes, String caseNo) {
        SettleBatchInfoDTO partAcmAndProSumVO = settleBatchDao.getPartAcmAndProSum(reportNo, caseTimes, caseNo);
        if (null == partAcmAndProSumVO) {
            partAcmAndProSumVO = new SettleBatchInfoDTO();
        }
        return partAcmAndProSumVO;
    }



}
