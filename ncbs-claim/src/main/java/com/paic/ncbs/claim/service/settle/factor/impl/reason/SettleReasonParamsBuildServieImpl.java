package com.paic.ncbs.claim.service.settle.factor.impl.reason;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EverySettleReasonParamsDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.reason.SettleReasonParamsBuildServie;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class SettleReasonParamsBuildServieImpl implements SettleReasonParamsBuildServie {
    /**
     * 处理每一天的发票集合，所以 整个集合中的数据要么全部不在有效期 要么全在，
     * 要么全都超每月赔付天数 要么都不超
     * @param everyBIllResults
     * @param billDate
     * @return
     */
    @Override
    public EverySettleReasonParamsDTO build(List<BIllSettleResultDTO> everyBIllResults, Date billDate) {
        EverySettleReasonParamsDTO dto =null;
        //是否是保单有效期内发票
        boolean effectiveFlag = everyBIllResults.stream().filter(bIllSettleResultDTO -> Objects.equals(Constants.NOT_FLAG,bIllSettleResultDTO.getEffectiveFlag())).findAny().isPresent();
        if(effectiveFlag){
            dto=new EverySettleReasonParamsDTO();
            setVaule(dto,billDate);
            dto.setEffectiveFlag(Constants.NOT_FLAG);
            return dto;
        }
        //等待期发票
        boolean waitFlag=everyBIllResults.stream().filter(bIllSettleResultDTO -> Objects.equals(Constants.YES_FLAG,bIllSettleResultDTO.getWaitFlag())).findAny().isPresent();
        if(waitFlag){
            dto=new EverySettleReasonParamsDTO();
            setVaule(dto,billDate);
            dto.setWaitFlag(Constants.YES_FLAG);
            return dto;
        }
        //是否超每月赔付天数
        boolean exceedMothPayDays = everyBIllResults.stream().filter(bIllSettleResultDTO -> Objects.equals(Constants.YES_FLAG,bIllSettleResultDTO.getExceedMothPayDays())).findAny().isPresent();
        if(exceedMothPayDays){
            dto=new EverySettleReasonParamsDTO();
            setVaule(dto,billDate);
            dto.setExceedMothPayDays(Constants.YES_FLAG);
            return dto;
        }
        //超年度赔付天数
        boolean exceedYearlyPayDays=everyBIllResults.stream().filter(bIllSettleResultDTO -> Objects.equals(Constants.YES_FLAG,bIllSettleResultDTO.getExceedYearlyPayDays())).findAny().isPresent();
        if(exceedYearlyPayDays){
            dto=new EverySettleReasonParamsDTO();
            setVaule(dto,billDate);
            dto.setExceedYearlyPayDays(Constants.YES_FLAG);
            return dto;
        }
        return null;
    }

    private void setVaule(EverySettleReasonParamsDTO dto,Date billDate) {
        dto.setBillDate(billDate);
        dto.setStrBillDate(DateUtils.dateFormat(billDate,DateUtils.SIMPLE_DATE_STR));
    }
}
