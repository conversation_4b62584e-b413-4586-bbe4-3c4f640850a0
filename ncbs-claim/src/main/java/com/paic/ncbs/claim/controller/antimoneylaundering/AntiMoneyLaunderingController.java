package com.paic.ncbs.claim.controller.antimoneylaundering;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.AmlCheckResultDTO;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.AmlSendRecordVO;
import com.paic.ncbs.claim.model.vo.settle.SettlesFormVO;
import com.paic.ncbs.claim.service.antimoneylaundering.ClmsAmlInfoService;
import com.paic.ncbs.claim.service.antimoneylaundering.ClmsSendAmlRecordService;
import com.paic.ncbs.claim.utils.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @author:QIANKEGONG513
 * date:2023/5/17
 * version:v0.0.1
 */
@Api(tags = {"反洗钱"})
@RestController
@Slf4j
@RequestMapping("/pay/antiMoneyLaunderingAction")
public class AntiMoneyLaunderingController extends BaseController {

    @Autowired
    private ClmsAmlInfoService clmsAmlInfoService;

    @Autowired
    private ClmsSendAmlRecordService sendAmlRecordService;

    /**
     * 保存反洗钱信息接口
     * @param dto 入参
     * @return 出参
     */
    @ApiOperation(value = "保存反洗钱信息接口")
    @PostMapping("/addClmsAntiMoneyLaunderingInfo")
    public ResponseResult<Object> addClmsAntiMoneyLaunderingInfo(@RequestBody ClmsAntiMoneyLaunderingInfoDto dto, HttpServletRequest request){
        log.info("addAntiMoneyLaundering入参：" + JsonUtils.toJsonString(dto));
        clmsAmlInfoService.saveAmlInfo(dto);
        return ResponseResult.success();
    }

    /**
     * 查询反洗钱信息接口
     * @param dto 入参
     * @return 出参
     */
    @ApiOperation(value = "查询反洗钱信息接口")
    @PostMapping("/getClmsAntiMoneyLaunderingInfo")
    public ResponseResult<Object> getClmsAntiMoneyLaunderingInfo(@RequestBody ClmsAntiMoneyLaunderingInfoDto dto){
        log.info("getAntiMoneyLaunderingInfo入参：" + JsonUtils.toJsonString(dto));
//        ClmsAntiMoneyLaunderingInfoDto  reultDto = clmsAmlInfoService.getClmsAntiMoneyLaunderingInfo(dto);
        ClmsAntiMoneyLaunderingInfoDto  reultDto = clmsAmlInfoService.getClmsAntiMoneyLaunderingInfoNew(dto);
        return ResponseResult.success(reultDto);
    }

    /**
     * 删除反洗钱信息接口
     * @param dto 入参
     * @return 出参
     */
    @ApiOperation(value = "删除反洗钱信息接口")
    @PostMapping("/deleteClmsAntiMoneyLaunderingInfo")
    public ResponseResult<Object> deleteClmsAntiMoneyLaunderingInfo(@RequestBody ClmsAntiMoneyLaunderingInfoDto dto){
        log.info("deleteAntiMoneyLaunderingInfo入参：" + JsonUtils.toJsonString(dto));
        clmsAmlInfoService.deleteClmsAntiMoneyLaunderingInfo(dto);
        return ResponseResult.success();
    }

    @ApiOperation(value = "反洗钱可疑数据校验")
    @PostMapping(value = "/checkPaymentItem")
    public ResponseResult<AmlCheckResultDTO> checkPaymentItem(@RequestBody SettlesFormVO dto) {
        AmlCheckResultDTO checkPaymentItemDTO = sendAmlRecordService.checkPaymentItem(dto);
        return ResponseResult.success(checkPaymentItemDTO);
    }

    @ApiOperation(value = "获取可疑交易反洗钱审批记录")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "reportNo", value = "报案号", dataType = "String", dataTypeClass = String.class),
        @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int", dataTypeClass = Integer.class)
    })
    @GetMapping(value = "/getRecordList/{reportNo}/{caseTimes}")
    public ResponseResult<List<AmlSendRecordVO>> getRecordList(@PathVariable String reportNo,
                                                               @PathVariable Integer caseTimes) {
        List<AmlSendRecordVO> voList = sendAmlRecordService.getRecordList(reportNo, caseTimes);
        return ResponseResult.success(voList);
    }
    /**
     * 根据姓名，证件类型，证件号 ，查询反洗钱信息
     * @param dto 入参
     * @return 出参
     */
    @ApiOperation(value = "查询反洗钱信息是否已存在接口，根据姓名，证件类型，证件号查询")
    @PostMapping("/getAmlByNameAndCardTypeAndCardNo")
    public ResponseResult<Object> getAmlByNameAndCardTypeAndCardNo(@RequestBody ClmsAntiMoneyLaunderingInfoDto dto){
        log.info("getAmlByNameAndCardTypeAndCardNo入参：" + JsonUtils.toJsonString(dto));
        ClmsAntiMoneyLaunderingInfoDto  reultDto = clmsAmlInfoService.getAmlByNameAndCardTypeAndCardNo(dto);
        return ResponseResult.success(reultDto);
    }

}
