package com.paic.ncbs.claim.service.customer;

import com.paic.ncbs.claim.exception.GlobalBusinessException;

import java.text.ParseException;

/**
 * 获取客户信息服务
 */
public interface CustomerInfoService {

    /**
     * 根据 客户姓名，证件类型，证件号查询客户号
     * @param customerName
     * @param bankAccountAttribute  帐号类型:个人帐号=1,公司帐号=0
     * @param cardType
     * @param idNo
     * @return
     */
    String getCustomerNo(String customerName,String bankAccountAttribute, String cardType, String idNo);

    /**
     * 定时任务，历史数据补加新客户号
     */
    void regenerateCustomerNo() throws GlobalBusinessException;
}
