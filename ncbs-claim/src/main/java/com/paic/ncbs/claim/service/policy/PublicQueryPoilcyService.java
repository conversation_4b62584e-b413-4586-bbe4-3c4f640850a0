package com.paic.ncbs.claim.service.policy;


import com.paic.ncbs.claim.model.dto.policy.QueryPolicyAbstractInfoListDTO;
import com.paic.ncbs.claim.model.dto.policy.QueryPolicyDetailInfoDTO;

import java.util.List;
import java.util.Map;

public interface PublicQueryPoilcyService {

	@SuppressWarnings("rawtypes")
	List queryPolicyAbstractInfoList(QueryPolicyAbstractInfoListDTO queryPolicyAbstractInfoListDTO)throws Exception;

	@SuppressWarnings("rawtypes")
	Map queryPolicyDetailByPolicyNo(QueryPolicyDetailInfoDTO queryPolicyDetailInfoDTO)throws Exception;

	@SuppressWarnings("rawtypes")
	Map queryPolicyDetailByDocumentNoAndClientMsg(QueryPolicyDetailInfoDTO queryPolicyDetailInfoDTO) throws Exception;
	

}
