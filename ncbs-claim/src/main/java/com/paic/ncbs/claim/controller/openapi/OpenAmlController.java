package com.paic.ncbs.claim.controller.openapi;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.AmlAuditResult;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.AmlResponse;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.AmlAuditResultList;
import com.paic.ncbs.claim.service.antimoneylaundering.ClmsSendAmlRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "反洗钱对外")
@Slf4j
@RestController
@RequestMapping("/public/aml")
public class OpenAmlController {

    @Autowired
    private ClmsSendAmlRecordService sendAmlRecordService;

    @ApiOperation("接收反洗钱可疑数据审核结果")
    @PostMapping(value = "/auditResult", produces = "application/xml;charset=UTF-8")
    public AmlResponse auditResult(@RequestBody AmlAuditResult amlAuditResult) {
        try {
            sendAmlRecordService.auditResult(amlAuditResult);
            return AmlResponse.success();
        } catch (GlobalBusinessException e) {
            return AmlResponse.fail(e.getMessage());
        } catch (Exception e) {
            log.error("OpenAmlController.auditResult error", e);
            return AmlResponse.fail(e.getMessage());
        }
    }
}
