package com.paic.ncbs.claim.service.investigate;

import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditPublicDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskQueryDTO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;

import java.util.List;

public interface InvestigateTaskAuditPublicService {

    /**
     * 查询调查任务列表
     * @param queryDTO 查询条件
     * @return 任务列表
     */
    List<WorkBenchTaskVO> getInvestigateTaskList(InvestigateTaskQueryDTO queryDTO);

    /**
     * 完成调查任务审核
     * @param taskAuditDTO 审核信息
     */
    void finishTaskAudit(InvestigateTaskAuditPublicDTO taskAuditDTO);

}