package com.paic.ncbs.claim.service.openapi.impl;

import com.paic.ncbs.claim.common.enums.IndemnityConclusionEnum;
import com.paic.ncbs.claim.common.enums.WholeCaseStatusEnum;
import com.paic.ncbs.claim.model.dto.endcase.CustomerReprotInfoDTO;
import com.paic.ncbs.claim.model.dto.openapi.IcdDto;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.model.dto.openapi.ClmsIcdDTO;
import com.paic.ncbs.claim.service.openapi.OpenQueryIcdInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OpenQueryIcdInfoServiceImpl implements OpenQueryIcdInfoService {
    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;

    @Override
    public ClmsIcdDTO getIcdInfo(String customerNo) {
        ClmsIcdDTO clmsIcdDTO = new ClmsIcdDTO();
        List<IcdDto> icdDtoList = reportCustomerInfoMapper.getCustomerIcdInfo(customerNo);
        clmsIcdDTO.setIcdDtoList(icdDtoList);
        return clmsIcdDTO;
    }
}
