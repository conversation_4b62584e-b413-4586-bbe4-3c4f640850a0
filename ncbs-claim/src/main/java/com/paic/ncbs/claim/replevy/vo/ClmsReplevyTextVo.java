package com.paic.ncbs.claim.replevy.vo;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，表clms_replevy_text的VO对象<br/>
 * 对应表名：clms_replevy_text,备注：追偿审核信息表
 *
 */
@Data
public class ClmsReplevyTextVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键id */
	private String id;
	/** 对应字段：replevy_id,备注：追偿主表id */
	private String replevyId;
	/** 对应字段：report_no,备注：报案号 */
	private String reportNo;
	/** 对应字段：replevy_no,备注：追偿案件号 */
	private String replevyNo;
	/** 对应字段：replevy_times,备注：追偿次数 */
	private Integer replevyTimes;
	/** 对应字段：case_times,备注：赔付次数 */
	private Integer caseTimes;
	/** 对应字段：replevy_role,备注：角色 */
	private String replevyRole;
	/** 业务动作 1-追偿审批,2-追偿费用审批*/
	private String opinionType;
	/** 申请人 */
	private String applyUm;
	/** 申请人名称*/
	private String applyName;
	/** 审批人 */
	private String approveUm;
	/** 审批人名称*/
	private String approveName;
	/** 对应字段：make_com,备注：操作机构 */
	private String makeCom;
	/** 对应字段：approve_opinion,备注：审核意见 1-同意，2-不同意，3-退回上一级，*/
	private String approveOpinion;
	/** 对应字段：handle_text,备注：意见说明 */
	private String handleText;
	/** 对应字段：valid_flag,备注：有效标志 */
	private String validFlag;
	/** 对应字段：flag,备注：标志字段 */
	private String flag;
	/** 对应字段：serial_no,备注：序号 */
	private Integer serialNo;
	/** 审批日期 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date handleDate;
	/** 对应字段：created_by,备注：创建人 */
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	/** 对应字段：sys_ctime,备注：创建时间 */
	private Date sysCtime;
	/** 对应字段：updated_by,备注：修改人员 */
	private String updatedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	/** 对应字段：sys_utime,备注：修改时间 */
	private Date sysUtime;
	//发起时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date applyDate;
	//申请意见
	private String applyText;
	//费用id
	private String replevyChargeId;
	@ApiModelProperty("任务id")
	private String taskId;
	/**
	 * 上级审批人
	 */
	private String selectedUserId;

}
