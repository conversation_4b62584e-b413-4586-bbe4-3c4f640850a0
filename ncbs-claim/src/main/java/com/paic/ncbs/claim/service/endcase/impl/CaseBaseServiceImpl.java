package com.paic.ncbs.claim.service.endcase.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseBaseMapper;
import com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CaseBaseServiceImpl extends BaseServiceImpl<CaseBaseEntity> implements CaseBaseService {

    @Autowired
    private CaseBaseMapper caseBaseMapper;

    public BaseDao<CaseBaseEntity> getDao() {
        return caseBaseMapper;
    }

    @Override
    public List<CaseBaseEntity> getCaseBaseInfoByReportNo(String reportNo) {
        return caseBaseMapper.getCaseBaseInfoByReportNo(reportNo);
    }

    @Override
    public List<CaseBaseEntity> getCaseBaseInfoByReportNoAndCasetimes(String reportNo, String casetimes) {
        return caseBaseMapper.getCaseBaseInfoByReportNoAndCasetimes(reportNo, casetimes);
    }

    @Override
    public List<CaseBaseEntity> getCaseBaseInfoByReportNoAndPolicytNo(String reportNo, String policyNo) {
        return caseBaseMapper.getCaseBaseInfoByReportNoAndPolicytNo(reportNo, policyNo);
    }

    @Override
    public List<CaseBaseDTO> getCaseBaseDTOList(String reportNo, Integer caseTimes) {
        return caseBaseMapper.getCaseBaseList(reportNo, caseTimes);
    }

	@Override
	public CaseBaseEntity getCaseBaseInfo(String report, String policyNo, Integer caseTimes) {
		return caseBaseMapper.getCaseBaseInfo(report, policyNo, caseTimes);
	}

	@Override
	public void updateRiskGroup(String idClmCaseBase, String riskGroupNo, String riskGroupName) {
		caseBaseMapper.updateRiskGroup(idClmCaseBase,riskGroupNo,riskGroupName);
	}

    @Override
    public void updateEsUpdatedDate(String reportNo, Integer caseTimes) {
        caseBaseMapper.updateEsUpdatedDate(reportNo,caseTimes);
    }


}
