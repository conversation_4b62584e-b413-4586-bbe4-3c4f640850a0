package com.paic.ncbs.claim.service.blacklist.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackList;
import com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListAudit;
import com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListRecord;
import com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListAuditMapper;
import com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListMapper;
import com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListRecordMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.blacklist.BlackListDictConverter;
import com.paic.ncbs.claim.model.vo.blacklist.ClmsBlackListVO;
import com.paic.ncbs.claim.service.blacklist.ClmsBlackListService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.utils.PageResult;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 黑名单信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Service
public class ClmsBlackListServiceImpl implements ClmsBlackListService {

    @Autowired
    private ClmsBlackListMapper clmsBlackListMapper;

    @Autowired
    private ClmsBlackListAuditMapper clmsBlackListAuditMapper;

    @Autowired
    private ClmsBlackListRecordMapper clmsBlackListRecordMapper;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private BlackListDictConverter dictConverter;


    @Override
    public PageResult<ClmsBlackListVO> getBlackListByCondition(ClmsBlackListVO clmsBlackListVO) {
        List<ClmsBlackList> blackListByCondition = clmsBlackListMapper.getBlackListByCondition(clmsBlackListVO);
        // 获取总条数
        int total = blackListByCondition.size();

        // 实现分页
        List<ClmsBlackList> pagedResult = blackListByCondition;
        int currentPage = 1;
        int pageSize = total;

        // 只有当前端传入了分页参数时才使用分页
        if (clmsBlackListVO.getCurrentPage() != null && clmsBlackListVO.getPageSize() != null
                && clmsBlackListVO.getCurrentPage() > 0 && clmsBlackListVO.getPageSize() > 0) {
            currentPage = clmsBlackListVO.getCurrentPage();
            pageSize = clmsBlackListVO.getPageSize();
            int startIndex = (currentPage - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, blackListByCondition.size());

            // 处理边界情况
            if (startIndex >= blackListByCondition.size() || startIndex < 0) {
                pagedResult = Collections.emptyList();
            } else {
                pagedResult = blackListByCondition.subList(startIndex, endIndex);
            }
        }

        List<ClmsBlackListVO> result = pagedResult.stream().map(entity -> {
            ClmsBlackListVO vo = new ClmsBlackListVO();
            BeanUtils.copyProperties(entity, vo);

            // 添加黑名单类型中文描述
            if (StringUtils.isNotBlank(vo.getPartyType())) {
                vo.setPartyTypeName(dictConverter.getPartyTypeNames(vo.getPartyType()));
            }
            // 添加证件类型中文描述
            if (StringUtils.isNotBlank(vo.getIdType())) {
                vo.setIdTypeName(dictConverter.getIdTypeName(vo.getIdType()));
            }
            // 添加风险类型中文描述
            if (StringUtils.isNotBlank(vo.getRiskType())) {
                vo.setRiskTypeName(dictConverter.getRiskTypeNames(vo.getRiskType()));
            }
            return vo;
        }).collect(Collectors.toList());
        return new PageResult<>(result, total, currentPage, pageSize);
    }

    /**
     * 新增黑名单信息
     * 该方法用于新增黑名单数据，根据是否需要审批执行不同流程：
     * 1. 若为菜单进入操作（无需审批），直接新增黑名单信息
     * 2. 若为按钮进入操作（需审批），则提交到审批表、操作记录表并启动工作流
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBlackList(ClmsBlackListVO clmsBlackListVO) throws Exception {
        ClmsBlackList clmsBlackList = new ClmsBlackList();
        String userId = WebServletContext.getUserId();
        String uuid = UuidUtil.getUUID();

        // 参数非空校验
        if (clmsBlackListVO == null){
            throw new GlobalBusinessException("黑名单提交数据为空，请检查！");
        }

        String partyName = clmsBlackListVO.getPartyName();
        String idNum = clmsBlackListVO.getIdNum();

        //根据姓名和证件号码校验
        if (StringUtils.isNotBlank(partyName)) {
            int count;
            count = clmsBlackListMapper.countByNameAndCertNo(partyName, idNum);
            if (count > 0) {
                throw new GlobalBusinessException("数据重复，请修改后再提交！");
            }
            //根据姓名和证件号码校验人员是否在审批中
            count = clmsBlackListRecordMapper.countByNameAndCertNo(partyName, idNum);
            if (count > 0) {
                throw new GlobalBusinessException("人员信息重复，该人员已在待审批黑名单中！");
            }
        }

        // 页面直接操作（无需审批）
        if ("Y".equals(clmsBlackListVO.getIsSettle())){
            //设置无需审批状态
            BeanUtils.copyProperties(clmsBlackListVO, clmsBlackList);
            clmsBlackList.setAuditStatus("4");
            clmsBlackList.setId(uuid);
            clmsBlackList.setBlackSource("理赔");
            clmsBlackList.setCreatedBy(userId);
            clmsBlackList.setUpdatedBy(userId);
            clmsBlackList.setValidFlag("Y");
            clmsBlackListMapper.saveBlackList(clmsBlackList);
        }else if ("N".equals(clmsBlackListVO.getIsSettle())){
            // 按钮操作（需要审批）
            ClmsBlackListAudit clmsBlackListAudit = new ClmsBlackListAudit();
            ClmsBlackListRecord clmsBlackListRecord = new ClmsBlackListRecord();

            // 审批表插入一条待审批的数据
            BeanUtils.copyProperties(clmsBlackListVO, clmsBlackListAudit);
            clmsBlackListAudit.setBlackListId(uuid);
            clmsBlackListAudit.setAuditStatus("1");
            clmsBlackListAudit.setSubmitBy(userId);
            clmsBlackListAudit.setCreatedBy(userId);
            clmsBlackListAudit.setUpdatedBy(userId);
            clmsBlackListAuditMapper.saveClmsBlackListAudit(clmsBlackListAudit);

            // 记录表插入一条待新增数据
            BeanUtils.copyProperties(clmsBlackListVO, clmsBlackListRecord);
            clmsBlackListRecord.setBlackListId(uuid);
            clmsBlackListRecord.setAuditStatus("1");
            clmsBlackListRecord.setBlackSource("理赔");
            clmsBlackListRecord.setCreatedBy(userId);
            clmsBlackListRecord.setUpdatedBy(userId);
            clmsBlackListRecord.setValidFlag("Y");
            clmsBlackListRecord.setRelatedReportNo(clmsBlackListVO.getReportNo());
            clmsBlackListRecordMapper.saveClmsBlackListRecord(clmsBlackListRecord);

            // 数据提交，开始创建任务
            bpmService.startProcessOc(clmsBlackListVO.getReportNo(),clmsBlackListVO.getCaseTimes(),BpmConstants.OC_BLACK_LIST,uuid,null, Constants.DEPARTMENT_CODE);
            // 记录操作记录
            operationRecordService.insertOperationRecordByLabour(clmsBlackListVO.getReportNo(), BpmConstants.OC_BLACK_LIST, "提交审批", clmsBlackListVO.getRemark());
            LogUtil.audit("#开始提交至工作流#:reportNo=%s,caseTimes=%s", clmsBlackListVO.getReportNo(), clmsBlackListVO.getCaseTimes());
        }

    }

    /**
     * 更新黑名单信息
     * 该方法用于更新黑名单数据，根据是否需要审批执行不同流程：
     * 1. 若为菜单进入操作（无需审批），直接更新黑名单状态
     * 2. 若为按钮进入操作（需审批），则创建审批记录、操作记录并启动工作流
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBlackList(ClmsBlackListVO clmsBlackListVO) throws Exception {
        //使用id设置锁
        String lockKey = "BLACKLIST_UPDATE_LOCK:" + clmsBlackListVO.getId();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁（等待3秒，锁有效期30秒）
            if (lock.tryLock(3, 30, TimeUnit.SECONDS)) {
                String userId = WebServletContext.getUserId();

                // 校验黑名单数据是否存在
                ClmsBlackList clmsBlackList = clmsBlackListMapper.getBlackListById(clmsBlackListVO.getId());
                if (clmsBlackList == null){
                    throw new GlobalBusinessException("数据不存在,请检查！");
                }

                // 关联报案号集合（包含历史报案号和当前报案号）
                String relatedReportNo = clmsBlackList.getRelatedReportNo();
                Set<String> reportNoSet = new LinkedHashSet<>();
                if (StringUtils.isNotBlank(relatedReportNo)) {
                    Collections.addAll(reportNoSet, relatedReportNo.split(","));
                }
                reportNoSet.add(clmsBlackListVO.getReportNo());

                if ("Y".equals(clmsBlackListVO.getIsSettle())) {
                    // 无需审批
                    BeanUtils.copyProperties(clmsBlackListVO, clmsBlackList);
                    clmsBlackList.setUpdatedBy(userId);
                    clmsBlackList.setAuditStatus("4");
                    clmsBlackListMapper.updateBlackList(clmsBlackList);
                } else if ("N".equals(clmsBlackListVO.getIsSettle())) {
                    // 需要审批
                    ClmsBlackListRecord record = new ClmsBlackListRecord();
                    ClmsBlackListAudit audit = new ClmsBlackListAudit();
                    ClmsBlackList blackList = new ClmsBlackList();

                    // 保存审批表
                    BeanUtils.copyProperties(clmsBlackListVO, audit);
                    audit.setBlackListId(clmsBlackListVO.getId());
                    audit.setAuditStatus("1");
                    audit.setSubmitBy(userId);
                    audit.setCreatedBy(userId);
                    audit.setUpdatedBy(userId);
                    clmsBlackListAuditMapper.saveClmsBlackListAudit(audit);

                    // 保存记录表
                    BeanUtils.copyProperties(clmsBlackList, record);
                    record.setBlackListId(clmsBlackListVO.getId());
                    record.setReportNo(clmsBlackListVO.getReportNo());
                    record.setCaseTimes(clmsBlackListVO.getCaseTimes());
                    record.setOperateType(clmsBlackListVO.getOperateType());
                    record.setAuditStatus(clmsBlackList.getAuditStatus());
                    record.setCreatedBy(userId);
                    record.setUpdatedBy(userId);
                    record.setRelatedReportNo(String.join(",", reportNoSet));
                    //区分修改和启用/禁用
                    if ("2".equals(clmsBlackListVO.getOperateType())) {
                        record.setPartyType(clmsBlackListVO.getPartyType());
                        record.setRiskType(clmsBlackListVO.getRiskType());
                        record.setPhoneNum(clmsBlackListVO.getPhoneNum());
                        record.setRemark(clmsBlackListVO.getRemark());
                    } else {
                        record.setValidFlag(clmsBlackListVO.getValidFlag());
                    }
                    clmsBlackListRecordMapper.saveClmsBlackListRecord(record);

                    // 更新源数据为审批中状态
                    blackList.setId(clmsBlackListVO.getId());
                    blackList.setAuditStatus("1");
                    blackList.setUpdatedBy(userId);
                    clmsBlackListMapper.updateBlackList(blackList);

                    // 数据提交，开始创建任务
                    bpmService.startProcessOc(clmsBlackListVO.getReportNo(),clmsBlackListVO.getCaseTimes(),BpmConstants.OC_BLACK_LIST,clmsBlackListVO.getId(),null,Constants.DEPARTMENT_CODE);
                    // 记录操作
                    operationRecordService.insertOperationRecordByLabour(clmsBlackListVO.getReportNo(), BpmConstants.OC_BLACK_LIST, "提交审批", clmsBlackListVO.getRemark());
                    LogUtil.audit("#开始提交至工作流#:reportNo=%s,caseTimes=%s", clmsBlackListVO.getReportNo(), clmsBlackListVO.getCaseTimes());
                }
            } else {
                throw new GlobalBusinessException("系统繁忙，请稍后重试");
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    @Override
    public ClmsBlackListVO getBlackListById(String id) throws Exception {
        ClmsBlackList blackListById = clmsBlackListMapper.getBlackListById(id);
        if (blackListById != null){
            ClmsBlackListVO vo = new ClmsBlackListVO();
            BeanUtils.copyProperties(blackListById, vo);

            // 添加黑名单类型中文描述
            if (StringUtils.isNotBlank(blackListById.getPartyType())) {
                vo.setPartyTypeName(dictConverter.getPartyTypeNames(blackListById.getPartyType()));
            }
            // 添加证件类型中文描述
            if (StringUtils.isNotBlank(blackListById.getIdType())) {
                vo.setIdTypeName(dictConverter.getIdTypeName(blackListById.getIdType()));
            }
            // 添加风险类型中文描述
            if (StringUtils.isNotBlank(blackListById.getRiskType())) {
                vo.setRiskTypeName(dictConverter.getRiskTypeNames(blackListById.getRiskType()));
            }
            return vo;
        }
        return null;
    }
}
