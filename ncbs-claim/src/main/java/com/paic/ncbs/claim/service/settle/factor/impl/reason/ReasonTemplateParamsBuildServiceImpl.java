package com.paic.ncbs.claim.service.settle.factor.impl.reason;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.*;
import com.paic.ncbs.claim.service.settle.factor.interfaces.reason.ReasonTemplateParamsBuildService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 理算依据模板参数构建
 */
@Service
public class ReasonTemplateParamsBuildServiceImpl implements ReasonTemplateParamsBuildService {
    @Override
    public DetailSettleReasonTemplateDTO buildParams(List<EverySettleReasonParamsDTO> setttleList, DutyDetailPayDTO dutyDetailPayDTO) {
        DetailSettleReasonTemplateDTO detailSettleReasonTemplateDTO =new DetailSettleReasonTemplateDTO();
        detailSettleReasonTemplateDTO.setEverySetttleList(setReasonTemplateDTO(setttleList));
        detailSettleReasonTemplateDTO.setDutyDetailName(dutyDetailPayDTO.getDutyDetailName());
        detailSettleReasonTemplateDTO.setAutoSettleAmount(BigDecimalUtils.toString(dutyDetailPayDTO.getAutoSettleAmount()));
        detailSettleReasonTemplateDTO.setBeInHospitalFlag(dutyDetailPayDTO.getBeInHospitalFlag());
        detailSettleReasonTemplateDTO.setNotice(dutyDetailPayDTO.getNotice());
        return detailSettleReasonTemplateDTO;
    }
    /**
     * 理算依据参数平铺
     * @param setttleList
     */
    private   List<EverySettleTemplateDTO> setReasonTemplateDTO(List<EverySettleReasonParamsDTO> setttleList) {
        if(CollectionUtil.isEmpty(setttleList)){
            throw new GlobalBusinessException("医疗类型理算依据参数不能为空");
        }
        List<EverySettleTemplateDTO> returList=new ArrayList<>();

        for (EverySettleReasonParamsDTO dto :setttleList) {
            EverySettleTemplateDTO esrpTemplate=new EverySettleTemplateDTO();
            esrpTemplate.setStrBillDate(dto.getStrBillDate());

            esrpTemplate.setEffectiveFlag(dto.getEffectiveFlag());
            esrpTemplate.setExceedMothPayDays(dto.getExceedMothPayDays());
            esrpTemplate.setExceedYearlyPayDays(dto.getExceedYearlyPayDays());
            esrpTemplate.setWaitFlag(dto.getWaitFlag());

            esrpTemplate.setAutoSettleAmount(BigDecimalUtils.toString(dto.getAutoSettleAmount()));

            esrpTemplate.setFormulaSettleAmount(BigDecimalUtils.toString(dto.getFormulaSettleAmount()));
            esrpTemplate.setNoLimtsettleFlag(dto.getNoLimtsettleFlag());
            esrpTemplate.setSettleZeroFlag(dto.getSettleZeroFlag());

            List<SettleReasonParamsDTO>  list =  dto.getSettleReasonParamsDTOList();
            if(CollectionUtil.isEmpty(list)){
                returList.add(esrpTemplate);
                continue;
            }
            SettleReasonTemplateDTO templateDTO =new SettleReasonTemplateDTO();
            for (SettleReasonParamsDTO settleDto :list) {
                if(Objects.equals("Y",settleDto.getMedicalSettleFlag())) {
                    templateDTO.setMedicalSettleFlag(settleDto.getMedicalSettleFlag());
                    templateDTO.setMedicalAutoSettleAmount(BigDecimalUtils.toString(settleDto.getAutoSettleAmount()));
                    templateDTO.setMedicalReasonableAmount(BigDecimalUtils.toString(settleDto.getAutoSettleAmount()));
                    templateDTO.setMedicalRemitAmount(BigDecimalUtils.toString(settleDto.getAutoSettleAmount()));
                    templateDTO.setMedicalPayProportion(settleDto.getPayProportion());
                    templateDTO.setMedicalLessThanRemitAmountFlag(settleDto.getLessThanRemitAmountFlag());
                    templateDTO.setMedicalFormulaData(settleDto.getFormulaData());
                }else{
                    templateDTO.setNoMedicalSettleFlag("Y");
                    templateDTO.setAutoSettleAmount(BigDecimalUtils.toString(settleDto.getAutoSettleAmount()));
                    templateDTO.setReasonableAmount(BigDecimalUtils.toString(settleDto.getReasonableAmount()));
                    templateDTO.setRemitAmount(BigDecimalUtils.toString(settleDto.getRemitAmount()));
                    templateDTO.setPayProportion(BigDecimalUtils.toString(settleDto.getPayProportion()));
                    templateDTO.setLessThanRemitAmountFlag(settleDto.getLessThanRemitAmountFlag());
                    templateDTO.setFormulaData(settleDto.getFormulaData());
                }

            }

            if(list.size()==2){
                templateDTO.setFormulaSettleAmount(BigDecimalUtils.toString(dto.getFormulaSettleAmount()));
                templateDTO.setTwoFlag("2");
            }else{
                templateDTO.setTwoFlag("1");
            }
            templateDTO.setSum(BigDecimalUtils.toString(dto.getAutoSettleAmount()));
            templateDTO.setSettleZeroFlag(dto.getSettleZeroFlag());
            templateDTO.setNoLimtsettleFlag(dto.getNoLimtsettleFlag());
            esrpTemplate.setTemplateDto(templateDTO);
            returList.add(esrpTemplate);
        }
        return  returList;
    }
}
