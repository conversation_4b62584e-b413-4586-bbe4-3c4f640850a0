package com.paic.ncbs.claim.service.dashboard;

import com.paic.ncbs.claim.dao.entity.dashboard.JobDashBoard;
import com.paic.ncbs.claim.dao.mapper.dashboard.JobDashboardMapper;
import com.paic.ncbs.claim.model.vo.dashboard.JobDashBoardVo;
import com.paic.ncbs.claim.model.vo.doc.RequestDataVo;
import com.paic.ncbs.claim.model.vo.openapi.GlobalWorkCountVo;
import com.paic.ncbs.claim.model.vo.openapi.GlobalWorkReqVo;
import com.paic.ncbs.claim.model.vo.openapi.GlobalWorkResVo;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Objects;

/**
 * 作业指标服务
 */
@Service
public class JobDashBoardService {

    @Resource
    private JobDashboardMapper mapper;
    /** 本日 */
    private static final String current_day = "current-day";
    /** 本周 */
    private static final String current_week = "current-week";
    /** 本月 */
    private static final String current_month = "current-month";
    /** 上个月 */
    private static final String last_month = "last-month";
    /** 本年 */
    private static final String current_year = "current-year";

    @Autowired
    private TaskListService taskListService;

    /**
     * 查询指定人指定口径的指标结果
     *
     * @param userCode
     * @param userName
     * @param dashBoardPeriod
     * @return
     */
    public JobDashBoardVo query(String userCode, String userName, String dashBoardPeriod) {
        // 计算起止时间口径
        LocalDateTime[] timeArray = getStartAndEndDate(dashBoardPeriod);
        // 计算oc系统值
        JobDashBoardVo vo = dataByOC(userCode, userName, timeArray);
        // 计算global系统的值
        addGlobalData(userCode, timeArray, vo);
        //返回前端时金额单位处理
        units(vo);

        return vo;
    }

    private void units(JobDashBoardVo vo) {
        vo.setClaimAmount(vo.getClaimAmount().setScale(2, RoundingMode.HALF_UP));
        vo.setEstimateAmount(vo.getEstimateAmount().setScale(2, RoundingMode.HALF_UP));
        vo.setVerifyAmount(vo.getVerifyAmount().setScale(2, RoundingMode.HALF_UP));
        vo.setEndCaseAmount(vo.getEndCaseAmount().setScale(2, RoundingMode.HALF_UP));
        vo.setRestartCaseEstimateAmount(vo.getRestartCaseEstimateAmount().setScale(2, RoundingMode.HALF_UP));
    }

    private LocalDateTime[] getStartAndEndDate(String dashBoardPeriod) {
        LocalDateTime[] timeArray = new LocalDateTime[2];
        LocalDate today = LocalDate.now();
        if (current_day.equals(dashBoardPeriod)) {
            timeArray[0] = today.atStartOfDay();
            timeArray[1] = today.atTime(LocalTime.MAX);
        } else if (current_week.equals(dashBoardPeriod)) {
            timeArray[0] = today.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY)).atStartOfDay();
            timeArray[1]  = today.with(TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY)).atTime(LocalTime.MAX);
        } else if (current_month.equals(dashBoardPeriod)) {
            timeArray[0] = today.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
            timeArray[1] = today.with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX);
        } else if (last_month.equals(dashBoardPeriod)){
            LocalDate subMonth = today.minusMonths(1);
            timeArray[0] = subMonth.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
            timeArray[1] = subMonth.with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX);
        } else if (current_year.equals(dashBoardPeriod)) {
            timeArray[0] = today.with(TemporalAdjusters.firstDayOfYear()).atStartOfDay();
            timeArray[1] = today.with(TemporalAdjusters.lastDayOfYear()).atTime(LocalTime.MAX);
        }
        return timeArray;
    }

        /**
         * 调用数据库查询
         *
         * @param userCode
         * @param userName
         * @param timeArray
         * @return
         */
        private JobDashBoardVo dataByOC(String userCode,String userName, LocalDateTime[] timeArray ){
            JobDashBoardVo vo = new JobDashBoardVo();
            // 报案
            vo.setReportNum(mapper.reportInfo(userCode, timeArray[0], timeArray[1]));
            // 立案
            JobDashBoard claim = mapper.claimInfo(userCode, timeArray[0], timeArray[1]);
            vo.setClaimNum(claim.getNum());
            vo.setClaimAmount(claim.getAmount());
            // 结案
            vo.setEndCaseNum(mapper.endCaseNum(userCode, timeArray[0], timeArray[1]));
            vo.setEndCaseAmount(mapper.endCaseAmount(userCode, timeArray[0], timeArray[1]));
            // 估损
            JobDashBoard estimate = mapper.estimateInfo(userCode, timeArray[0], timeArray[1]);
            vo.setEstimateNum(estimate.getNum());
            vo.setEstimateAmount(estimate.getAmount());
            //  零注拒
            vo.setNopaidNum(mapper.zeroCancelRefuseNum(userCode, timeArray[0], timeArray[1]));
            // 差错
            vo.setMistakeNum(mapper.mistakeNum(userCode, timeArray[0], timeArray[1]));
            // 重开
            JobDashBoard reopenCase = mapper.reopenCaseInfo(userName, timeArray[0], timeArray[1]);
            vo.setRestartCaseNum(reopenCase.getNum());
            vo.setRestartCaseEstimateAmount(reopenCase.getAmount());
            // 核赔
            JobDashBoard verify = mapper.verifyInfo(userCode, timeArray[0], timeArray[1]);
            vo.setVerifyNum(verify.getNum());
            vo.setVerifyAmount(verify.getAmount());

            return vo;
        }

        /**
         * 添加global数据并做数据项加和
         *
         * @param userCode
         * @param timeArray
         * @param vo
         */
        private void addGlobalData(String userCode, LocalDateTime[] timeArray, JobDashBoardVo vo) {
            //入参组装
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            GlobalWorkReqVo globalWorkReqVo = new GlobalWorkReqVo();
            RequestDataVo requestDataVo = new RequestDataVo();
            requestDataVo.setDealCode(userCode);
//            requestDataVo.setStartDate(simpleDateFormat.format(timeArray[0]));
            requestDataVo.setStartDate(String.valueOf(timeArray[0].getYear())+
                    String.valueOf(timeArray[0].getMonthValue())+
                    String.valueOf(timeArray[0].getDayOfMonth()));
//            requestDataVo.setEndDate(simpleDateFormat.format(timeArray[1]));
            requestDataVo.setEndDate(String.valueOf(timeArray[1].getYear())+
                    String.valueOf(timeArray[1].getMonthValue())+
                    String.valueOf(timeArray[1].getDayOfMonth()));
            globalWorkReqVo.setRequestData(requestDataVo);
            // 调用global查询接口
            GlobalWorkResVo globalWorkResVo = taskListService.workbenchGlobalCount(globalWorkReqVo);
            GlobalWorkCountVo globalWorkCountVo = globalWorkResVo.getResponseData().getClaimCountInfo();
            if(!Objects.isNull(globalWorkCountVo)){
                //计算global值 与oc计算结果进行累加
                // 报案
                vo.setReportNum(vo.getReportNum()+Integer.valueOf(globalWorkCountVo.getReopenCount()));
                // 立案估损
                vo.setClaimNum(vo.getClaimNum()+Integer.valueOf(globalWorkCountVo.getReserveCount()));
                vo.setClaimAmount(vo.getClaimAmount().add(new BigDecimal(globalWorkCountVo.getReserveAmount())));
                // 结案
                vo.setEndCaseNum(vo.getEndCaseNum()+Integer.valueOf(globalWorkCountVo.getCloseCount()));
                vo.setEndCaseAmount(vo.getEndCaseAmount().add(new BigDecimal(globalWorkCountVo.getCloseAmount())));
                // 估损 估损调整
                vo.setEstimateNum(vo.getEstimateNum()+Integer.valueOf(globalWorkCountVo.getReserveAdjustCount()));
                vo.setEstimateAmount(vo.getEstimateAmount().add(new BigDecimal(globalWorkCountVo.getReserveAdjustAmount())));
                //  零注拒
                vo.setNopaidNum(vo.getNopaidNum()+Integer.valueOf(globalWorkCountVo.getZeroCancelCount()));
                // 差错
                vo.setMistakeNum(vo.getMistakeNum()+Integer.valueOf(globalWorkCountVo.getMistakeCount()));
                // 重开
                vo.setRestartCaseNum(vo.getRestartCaseNum()+Integer.valueOf(globalWorkCountVo.getReopenCount()));
                vo.setRestartCaseEstimateAmount(vo.getRestartCaseEstimateAmount().add(new BigDecimal(globalWorkCountVo.getReopenAmount())));
                // 核赔
                vo.setVerifyNum(vo.getVerifyNum()+Integer.valueOf(globalWorkCountVo.getPaymentCount()));
                vo.setVerifyAmount(vo.getVerifyAmount().add(new BigDecimal(globalWorkCountVo.getPaymentAmount())));
            }
        }
}
