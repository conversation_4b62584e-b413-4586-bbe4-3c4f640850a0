package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyBatchPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayHisInfo;
import com.paic.ncbs.claim.model.vo.settle.EpcisRequestVO;
import com.paic.ncbs.claim.model.vo.settle.PolicyPayInfoVO;
import com.paic.ncbs.claim.model.vo.settle.SettlesFormVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface PolicyPayService {

    List<PolicyPayDTO>  initPolicyPayInfo(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    /**
     * 查询案件理算信息
     * @param reportNo
     * @param caseTimes
     * @param scene 查询场景（暂时只用于区分查询历史案件，查询历史案件时 计算剩余理赔金额的已赔付金额要包含当前报案号）
     * @return
     */
    PolicyPayInfoVO getPolicyPayInfo(String reportNo, Integer caseTimes, String scene);

    void addSettles(SettlesFormVO settlesFormVO);

    void sendSettles(SettlesFormVO settlesFormVO) throws GlobalBusinessException;

    void initPolicyForRefuse(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    void reCalculateSettleAmount(String reportNo, Integer caseTime);

    void handlePolicyPaysByConclusion(String reportNo, Integer caseTimes, String conclusion, Boolean clearPolicyFee) throws GlobalBusinessException;

    BigDecimal getPrePayAmount(String policyNo, Integer caseTimes) throws GlobalBusinessException;

    BigDecimal getFeePayAmount(PolicyPayDTO policyPay, String claimType);

    boolean checkExists(String reportNo, Integer caseTime);
     //单独抽出来放在ClmBatchService接口里了
    //String insertBatch(String reportNo, Integer caseTimes, String settleStatus);

    List<PolicyPayDTO> selectFromPolicyCopy(String reportNo, Integer caseTimes);

    List<PolicyPayDTO> getByReportNo(String reportNo, Integer caseTimes);

    List<PolicyPayDTO> getByReportNo(String reportNo, Integer caseTime, String claimType);

    void insertPolicyBatch(List<PolicyPayDTO> copyPolicyPays, String idAhcsBatch);

    void insertPayInfo(List<PolicyPayDTO> policyPays);

    void setPolicyFeePay(List<PolicyPayDTO> policyPays);

    EpcisRequestVO getEpicsRequest(@Param("clientNo")String clientNo, @Param("policyNo")String policyNo, @Param("reportNo")String reportNo);

    BigDecimal getSumPayFee(String reportNo, Integer caseTimes);


    List<PolicyPayDTO> getPolicyPayListByReportNoAndCaseTimes(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    void bathAddOrUpdatePayInfo(List<PolicyBatchPayDTO> policyBathPayList, List<PolicyPayDTO> updatePolicyPays, List<PolicyPayDTO> addPolicyPays,
                                List<PlanPayDTO> planPays, List<DutyPayDTO> dutyPays) throws GlobalBusinessException;

    List<PolicyPayDTO> getSimplePolicyDutyList(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    void updatePolicyFee(String reportNo,Integer caseTimes);

    List<PolicyPayHisInfo>queryCaseInfo(String reportNo, Integer caseTime);

    void deletePolicyPays(String reportNo,Integer caseTimes);

    Boolean isPolicyHasPrePay(String reportNo, Integer caseTimes,String policyNo);

    /**
     * 获取前次已支付赔款金额：当前重开报案号下前次支付的理赔款理算金额合计
     * @param reportNo
     * @param caseTimes
     * @return
     */
    BigDecimal getLastPolicyPayAmount(String reportNo, Integer caseTimes);

    /**
     * 获取前次已支付赔款金额：当前重开报案号下前次支付的理赔款理算金额合计（保单层级）
     * @param reportNo
     * @param caseTimes
     * @return
     */
    BigDecimal getLastPolicyPayAmount(String reportNo, Integer caseTimes, String policyNo);

    /**
     * 根据报案号+赔付次数 更新案件状态
     * @param reportNo  报案号
     * @param caseTimes 赔付次数
     * @param caseStatus    案件状态
     */
    public void updateCaseStatus(String reportNo,Integer caseTimes,String caseStatus);
}