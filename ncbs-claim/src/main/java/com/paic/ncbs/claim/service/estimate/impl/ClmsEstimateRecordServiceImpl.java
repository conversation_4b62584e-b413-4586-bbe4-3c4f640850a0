package com.paic.ncbs.claim.service.estimate.impl;

import com.paic.ncbs.claim.common.enums.EstimateTypeEnum;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.estimate.ClmsEstimateRecordMapper;
import com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord;
import com.paic.ncbs.claim.service.estimate.ClmsEstimateRecordService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service("clmsEstimateRecordService")
public class ClmsEstimateRecordServiceImpl implements ClmsEstimateRecordService {

    @Autowired
    private ClmsEstimateRecordMapper clmsEstimateRecordMapper;
    @Autowired
    private UserInfoService userInfoService;


    @Override
    public void addEstimateRecord(ClmsEstimateRecord record) {
        clmsEstimateRecordMapper.insert(record);
    }

    @Override
    public List<ClmsEstimateRecord> getRecordByReportNoAndType(String reportNo, String caseTimes, String estimateType) {
        List<ClmsEstimateRecord> estimateRecordList = Optional.ofNullable(
                clmsEstimateRecordMapper.selectByReportNoAndType(reportNo,caseTimes,estimateType)).orElse(new ArrayList<>());
        for (ClmsEstimateRecord record : estimateRecordList) {
            record.setEstimateTypeName(EstimateTypeEnum.getName(record.getEstimateType()));
            try {
                record.setRecordUserName(Optional.ofNullable(userInfoService.getUserNameById(record.getRecordUserId())).orElse(""));
            }catch (Exception e){
                LogUtil.error("获取用户姓名失败",e);
            }
        }
        return estimateRecordList;
    }

    @Override
    public BigDecimal getEstimateRecordAmount(String reportNo, Integer caseTimes) {
        return clmsEstimateRecordMapper.getEstimateRecordAmount(reportNo,caseTimes);
    }

    @Override
    public BigDecimal getEstimateLossAmount(String reportNo, Integer caseTimes) {
        try {
            List<ClmsEstimateRecord> list = getRecordByReportNoAndType(reportNo,String.valueOf(caseTimes),EstimateTypeEnum.REPORT_PENDING.getType());
            if(ListUtils.isEmptyList(list)){
                return new BigDecimal("0");
            }
            return Optional.ofNullable(list.get(0).getEstimateAmount()).orElse(new BigDecimal("0"));
        }catch (Exception e){
            LogUtil.error("查报案预估金额失败",e);
        }
        return new BigDecimal("0");
    }
}
