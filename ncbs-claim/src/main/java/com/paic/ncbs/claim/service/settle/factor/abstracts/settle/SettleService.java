package com.paic.ncbs.claim.service.settle.factor.abstracts.settle;


import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.CalculateFormulaDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.model.vo.settle.SettleSelectVO;
import com.paic.ncbs.claim.model.vo.settle.SettleSelectVO;
import com.paic.ncbs.claim.service.endcase.DutyBillLimitInfoService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.base.BaseSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.bill.ExecuteBillFilterService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.modelinit.ExecuteDataInitializeService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.modelsettle.ExecuteModelSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle.ExecuteSaveSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle.SaveSettleService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.rulefilter.ExecuteDutyRuleFilterService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 理算抽象类
 */
@Slf4j
public abstract class SettleService {
    @Autowired
    private DutyBillLimitInfoService dutyBillLimitInfoService;
    /**
     * 模型初始化
     */
    private ExecuteDataInitializeService executeDataInitializeService;

    private ExecuteBillFilterService executeBillFilterService;
    /**
     * 核责接口
     */
    private ExecuteDutyRuleFilterService executeDutyRuleFilterService;
    /**
     * 模型理算接口
     */
    private ExecuteModelSettleService executeModelSettleService;
    /**
     * 保存数据接口
     */
    private ExecuteSaveSettleService executeSaveSettleService;

    /**
     * 设置模型初始化对象
     * @param executeDataInitializeService
     */
    public void setDataInitializeService(ExecuteDataInitializeService executeDataInitializeService) {
        this.executeDataInitializeService = executeDataInitializeService;
    }

    public ExecuteBillFilterService getExecuteBillFilterService(ExecuteBillFilterService executeBillFilterService) {
        return this.executeBillFilterService=executeBillFilterService;
    }

    public void setExecuteBillFilterService(ExecuteBillFilterService executeBillFilterService) {
        this.executeBillFilterService = executeBillFilterService;
    }

    /**
     * 设置核责对象
     * @param executeDutyRuleFilterService
     */
    public void setUwRuleServiceValue(ExecuteDutyRuleFilterService executeDutyRuleFilterService){
        this.executeDutyRuleFilterService = executeDutyRuleFilterService;
    }

    public void setModelSettleServiceValue(ExecuteModelSettleService executeModelSettleService){
        this.executeModelSettleService = executeModelSettleService;
    }

    public void setExecuteSaveSettleServiceValue(ExecuteSaveSettleService executeSaveSettleService){
        this.executeSaveSettleService=executeSaveSettleService;
    }
    /**
     * 理算
     * @param reportNo
     * @param caseTimes
     * flag :0 - 正常理算 1-重新理算
     */
    public final ClaimCaseDTO settle(String reportNo, Integer caseTimes,String flag){
        ClaimCaseDTO claimCaseDTO =new ClaimCaseDTO();
        claimCaseDTO.setReportNo(reportNo);
        claimCaseDTO.setCaseTimes(caseTimes);
        claimCaseDTO.setFlag(flag);
        //模型数据初始化
        executeDataInitializeService.initialize(claimCaseDTO);
        //通用过滤发票规则：不需要在理算依据中体现的在此过滤处理，
        executeBillFilterService.billFilter(claimCaseDTO);
        //核责：处理赔付险种规则等逻辑
        executeDutyRuleFilterService.ruleFilter(claimCaseDTO);
        //模型数据理算
        executeModelSettleService.modelSettle(claimCaseDTO);
        //理算接口保存入库
        executeSaveSettleService.saveResult(claimCaseDTO);
        return claimCaseDTO;
    }

    /**
     * 理算数据初始化
     * @param reportNo
     * @param caseTimes
     * flag :0 - 正常理算 1-重新理算
     */
    public final ClaimCaseDTO settleInit(String reportNo, Integer caseTimes,String flag){
        ClaimCaseDTO claimCaseDTO =new ClaimCaseDTO();
        claimCaseDTO.setReportNo(reportNo);
        claimCaseDTO.setCaseTimes(caseTimes);
        claimCaseDTO.setFlag(flag);
        //模型数据初始化
        executeDataInitializeService.initialize(claimCaseDTO);
        //通用过滤发票规则：不需要在理算依据中体现的在此过滤处理，
//        executeBillFilterService.billFilter(claimCaseDTO);
        //核责：处理赔付险种规则等逻辑
        executeDutyRuleFilterService.ruleFilter(claimCaseDTO);

        if(CollectionUtil.isEmpty(claimCaseDTO.getPolicyPayDTOList())){
            return claimCaseDTO;
        }
        for (PolicyPayDTO policy : claimCaseDTO.getPolicyPayDTOList()) {
            List<PlanPayDTO> planList = policy.getPlanPayArr();
            for (PlanPayDTO plan : planList) {
                List<DutyPayDTO> dutyList =  plan.getDutyPayArr();
                for (DutyPayDTO duty :dutyList) {
                    List<DutyDetailPayDTO>  detailPayDTOS = duty.getDutyDetailPayArr();
                    duty.setReportNo(claimCaseDTO.getReportNo());
                    duty.setRemitAmount(BigDecimal.ZERO);
                    duty.setSettleAmount(BigDecimal.ZERO);
                    duty.setSettleReason("");
                    for (DutyDetailPayDTO detail : detailPayDTOS) {
                        detail.setAutoSettleAmount(BigDecimal.ZERO);
                        detail.setRemitAmount(BigDecimal.ZERO);
                        detail.setReasonableAmount(BigDecimal.ZERO);
                        List<BIllSettleResultDTO> billSettleResultDTOList = detail.getBillSettleResultDTOList();
                        if(CollectionUtil.isNotEmpty(billSettleResultDTOList)){
                            for (BIllSettleResultDTO bIllSettleResultDTO : billSettleResultDTOList) {
                                bIllSettleResultDTO.setAutoSettleAmount(BigDecimal.ZERO);
                                bIllSettleResultDTO.setRemitAmount(BigDecimal.ZERO);
                            }
                        }
                    }
                }
            }

        }

        //理算接口保存入库
        executeSaveSettleService.saveResult(claimCaseDTO);
        return claimCaseDTO;
    }

    /**
     *
     * 单责任明细理算
     */
    public ClaimCaseDTO settleSelect(SettleSelectVO settleSelectVO){
        ClaimCaseDTO claimCaseDTO =new ClaimCaseDTO();
        claimCaseDTO.setReportNo(settleSelectVO.getReportNo());
        claimCaseDTO.setCaseTimes(settleSelectVO.getCaseTimes());
        claimCaseDTO.setFlag("0");
        //模型数据初始化
        executeDataInitializeService.initialize(claimCaseDTO);
        //通用过滤发票规则：不需要在理算依据中体现的在此过滤处理，
        executeBillFilterService.billFilter(claimCaseDTO);
        //核责：处理赔付险种规则等逻辑
//        executeDutyRuleFilterService.ruleFilter(claimCaseDTO);
        PolicyPayDTO policyPayDTO = claimCaseDTO.getPolicyPayDTOList().get(0);
        List<PlanPayDTO> planPayDTOList = policyPayDTO.getPlanPayArr().stream().filter(i -> settleSelectVO.getPlanCode().equals(i.getPlanCode())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(planPayDTOList)){
            return claimCaseDTO;
        }
        PlanPayDTO planPayDTO = planPayDTOList.get(0);
        List<DutyPayDTO> dutyPayDTOList = planPayDTO.getDutyPayArr().stream().filter(i -> settleSelectVO.getDutyCode().equals(i.getDutyCode())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(dutyPayDTOList)){
            return claimCaseDTO;
        }
        DutyPayDTO dutyPayDTO = dutyPayDTOList.get(0);
        List<DutyDetailPayDTO> dutyDetailPayDTOList = dutyPayDTO.getDutyDetailPayArr().stream().filter(i -> settleSelectVO.getDutyDetailCode().equals(i.getDutyDetailCode())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(dutyDetailPayDTOList)){
            return claimCaseDTO;
        }
        dutyDetailPayDTOList.get(0).setIsSettleFlag("Y");
        dutyPayDTO.setDutyDetailPayArr(dutyDetailPayDTOList);
        planPayDTO.setDutyPayArr(dutyPayDTOList);
        policyPayDTO.setPlanPayArr(planPayDTOList);
        log.info("单责任明细理算责任过滤后结果：{}", JsonUtils.toJsonString(claimCaseDTO));

        //模型数据理算
        executeModelSettleService.modelSettle(claimCaseDTO);

        //处理日限额
        List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOList = claimCaseDTO.getPolicyPayDTOList().get(0).getPlanPayArr().get(0).getDutyPayArr().get(0).getDutyDetailPayArr().get(0).getDutyBillLimitInfoDTOList();
        if(CollectionUtils.isNotEmpty(dutyBillLimitInfoDTOList)){
            dutyBillLimitInfoService.saveListBydutyDetailCode(dutyBillLimitInfoDTOList);
        }
        //理算接口保存入库
//        executeSaveSettleService.saveResult(claimCaseDTO);
        return claimCaseDTO;
    }


}
