package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.dao.mapper.checkloss.PropertyLossMapper;
import com.paic.ncbs.claim.model.dto.checkloss.PropertyLossDTO;
import com.paic.ncbs.claim.service.checkloss.PropertyLossService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class PropertyLossServiceImpl implements PropertyLossService {

	@Autowired
    PropertyLossMapper propertyLossDao;

	@Override
	@Transactional
	public void savePropertyLoss(List<PropertyLossDTO> propertyLossList, String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
		List<PropertyLossDTO> existPropertyLossList = propertyLossDao.getPropertyLoss(reportNo, caseTimes,null,taskCode, channelProcessId);
		if(ListUtils.isNotEmpty(existPropertyLossList)){

			PropertyLossDTO propertyLossDTO = new PropertyLossDTO();
			propertyLossDTO.setReportNo(reportNo);
			propertyLossDTO.setCaseTimes(caseTimes);
			propertyLossDTO.setTaskCode(taskCode);
			propertyLossDTO.setIdAhcsChannelProcess(channelProcessId);
			propertyLossDTO.setUpdatedBy(propertyLossList.get(0).getUpdatedBy());
			propertyLossDao.updateEffective(propertyLossDTO);

		}
		propertyLossDao.addPropertyLoss(propertyLossList);
	}

	@Override
	public void removePropertyLoss(String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
		propertyLossDao.removePropertyLoss(reportNo, caseTimes, taskCode, channelProcessId);
	}

	@Override
	public void updateEffective(PropertyLossDTO propertyLossDTO) {
		propertyLossDao.updateEffective(propertyLossDTO);
	}

	public List<PropertyLossDTO> getPropertyLoss(String reportNo, Integer caseTimes, String status, String taskCode, String channelProcessId) {
		return propertyLossDao.getPropertyLoss(reportNo, caseTimes, status,taskCode,channelProcessId);
	}

	@Override
	public List<PropertyLossDTO> getPropertyLossAll(String reportNo, Integer caseTimes, String status, String taskCode) {
		List<PropertyLossDTO> propertyList = propertyLossDao.getPropertyLoss(reportNo, caseTimes, status,taskCode,null);
		if(ListUtils.isNotEmpty(propertyList)){
			PropertyLossDTO propertyLossFri = propertyList.get(0);
			if(propertyLossFri.getThirdPropertyLossAmount()!=null){
				PropertyLossDTO propertyLossDTO = new PropertyLossDTO();
				propertyLossDTO.setAccidentType(SettleConst.PR_ACC_TYPE_13);
				propertyLossDTO.setLossAmount(propertyLossFri.getThirdPropertyLossAmount());
				propertyList.add(propertyLossDTO);
			}
		}
		return propertyList;
	}

	@Override
	public List<PropertyLossDTO> getPropertyLossByReportNo(String reportNo, Integer caseTimes, String status, String taskCode) {
		return propertyLossDao.getPropertyLossByReportNo(reportNo, caseTimes, status,taskCode);
	}
}
