package com.paic.ncbs.claim.service.settle.factor.impl.strategy.attributes;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.DutyAttributeConst;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeDetailEntity;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonDisabilityMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.attributes.DutyAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service
public class DisabilityDutyAttributeServiceImpl implements DutyAttributeService {
    @Autowired
    private PersonDisabilityMapper personDisabilityMapper;
    @Override
    public void setDutyDetailAttribute(DutyDetailPayDTO detail, Map<String, Optional<String>> attributes, Map<String, Map<String, String>> attributesdetailMap) {

        LogUtil.audit("---获取残疾类型责任属性---");
        //伤残信息
        PersonDisabilityDTO personDisability = new PersonDisabilityDTO();
        personDisability.setReportNo(detail.getReportNo());
        personDisability.setCaseTimes(detail.getCaseTimes());
        personDisability.setTaskId(BpmConstants.CHECK_DUTY);
        List<PersonDisabilityDTO> disableList = personDisabilityMapper.getList(personDisability);
        if (attributes.containsKey(DutyAttributeConst.DISABILITY_RATE)) {
            String disabilityGrade = null;
            if (!disableList.isEmpty()) {
                disabilityGrade = disableList
                        .stream().filter(Objects::nonNull)
                        .min(Comparator.comparingInt(d -> Integer.parseInt(d.getDisabilityGrade()))).get().getDisabilityGrade();
            }
            //根据收单录入的【伤残等级】取产品工厂中对应的【残疾对应赔付比例】

            String grade = DutyAttributeConst.DISABILITY_GRADE_RATE_MAP.get(disabilityGrade);
            LogUtil.audit("---伤残等级：{}，属性code：{}", disabilityGrade, grade);
            if (attributesdetailMap.containsKey(grade)) {
                detail.setDisabilityRate(nvl(BigDecimalUtils.toPercent(new BigDecimal(attributesdetailMap.get(grade).get(DutyAttributeConst.ATTRIBUTE_DETAIL_VALUE))), 0));
            } else {
                detail.setDisabilityRate(BigDecimal.ONE);
            }

        } else {
            LogUtil.audit("伤残类型责任属性为空,案件reportNo={},caseTimes={},保单号={}", detail.getReportNo(), detail.getCaseTimes(), detail.getPolicyNo());
        }
    }
}
