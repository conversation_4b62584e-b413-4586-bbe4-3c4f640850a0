package com.paic.ncbs.claim.service.endcase.impl;

import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.response.MockJsonUtil;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyPlanMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.ChannelProcessMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassDefineMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.trace.ClmsTraceRecordMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDefineDTO;
import com.paic.ncbs.claim.model.dto.endcase.SubCaseClassDefineDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPlanDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsTraceRecordDTO;
import com.paic.ncbs.claim.model.vo.duty.DutySurveyVO;
import com.paic.ncbs.claim.model.vo.endcase.CaseClassDefineVO;
import com.paic.ncbs.claim.service.base.impl.SwitchServiceUtil;
import com.paic.ncbs.claim.service.endcase.CaseClassService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.CaseClassConstant.PRODUCT_CLASS_CASE_CLASS_MAP;

@Service("caseClassService")
public class CaseClassServiceImpl implements CaseClassService {

    @Autowired
    private CaseClassDefineMapper caseClassDefineDao;

    @Autowired
    private CaseClassMapper caseClassDao;

    @Autowired
    private ChannelProcessMapper channelProcessDao;

    @Autowired
    private OcasMapper ocasMapper;

    @Autowired
    private AhcsPolicyPlanMapper ahcsPolicyPlanMapper;
    @Autowired
    private ClmsTraceRecordMapper clmsTraceRecordMapper;

    @Override
    public List<CaseClassDefineVO> getCaseClassDefines(String reportNo, int caseTimes) {
        if(SwitchServiceUtil.getStandardVersionSwitch()){
            return MockJsonUtil.getJavaListFromFile("case_class_define.json",CaseClassDefineVO.class);
        }
        List<CaseClassDefineDTO> caseClassDefines = caseClassDefineDao.getCaseClassDefines();
        List<ChannelProcessDTO> channelProcessDTOs = channelProcessDao.getChannelProcessIds(reportNo, caseTimes);

        //查询保单险种
        List<PolicyPlanDTO> policyPlans =  ahcsPolicyPlanMapper.getPolicyPlanInfo(reportNo);
        List<String> allCaseClassList = new ArrayList<>();
        for(PolicyPlanDTO policyPayDTO : policyPlans) {
            //查询险种大类
            String kindCode = ocasMapper.getPlyPlanInfo(policyPayDTO.getPolicyNo(), policyPayDTO.getPlanCode());
            List<String> caseClassList = PRODUCT_CLASS_CASE_CLASS_MAP.get(kindCode);
            if(CollectionUtils.isNotEmpty(caseClassList)){
                allCaseClassList.addAll(caseClassList);
            }
        }
        if(CollectionUtils.isNotEmpty(allCaseClassList)) {
            //根据险种大类来过滤
            caseClassDefines = caseClassDefines.stream().filter(item -> allCaseClassList.contains(item.getClassCode())).collect(Collectors.toList());
        }
        List<CaseClassDefineVO> retCaseClassDefines = new ArrayList<>();
        for (CaseClassDefineDTO caseClassDefine : caseClassDefines) {
            CaseClassDefineVO caseClassDefineVO = new CaseClassDefineVO();
            BeanUtils.copyProperties(caseClassDefine, caseClassDefineVO, CaseClassDefineVO.class);

            for (ChannelProcessDTO channelProcess : channelProcessDTOs) {
                if (caseClassDefineVO.getClassCode().equals(channelProcess.getChannelType())) {
                    caseClassDefineVO.setIdAhcsChannelProcess(channelProcess.getIdAhcsChannelProcess());
                }
            }

//            for (SubCaseClassDefineDTO subCaseClassDefineDTO : caseClassDefineVO.getSubCaseClassDefines()) {
//                subCaseClassDefineDTO.setModules(
//                        ChecklossConst.CASE_CALSS_AND_MODULE_RELATIONS.get(subCaseClassDefineDTO.getClassCode()));
//            }
            retCaseClassDefines.add(caseClassDefineVO);
        }
        ClmsTraceRecordDTO traceRecordDTO = new ClmsTraceRecordDTO();
        traceRecordDTO.setReportNo(reportNo);
        traceRecordDTO.setCaseTimes(caseTimes);
        return retCaseClassDefines;
    }

    @Override
    public List<CaseClassDefineDTO> getCaseClassDefineList() throws GlobalBusinessException {
        List<CaseClassDefineDTO> caseClassDefineList = caseClassDefineDao.getCaseClassDefines();

        for (CaseClassDefineDTO caseClassDefineDTO : caseClassDefineList) {
            List<SubCaseClassDefineDTO> subCaseClassDefines = caseClassDefineDao.getSubCaseClass(caseClassDefineDTO.getClassCode());
            if (ListUtils.isNotEmpty(subCaseClassDefines)) {
                caseClassDefineDTO.setSubCaseClassDefines(subCaseClassDefines);
            }
        }

        return caseClassDefineList;
    }

    @Override
    public List<String> getCaseClassCodeList(String reportNo, Integer caseTimes, String taskId) {
        return caseClassDao.getCaseClassCodeList(reportNo, caseTimes, taskId);
    }

    @Override
    public boolean importantCase(String reportNo, int caseTimes) {

        List<String> caseSubClassList = caseClassDao.getCaseClassList(reportNo, caseTimes, null);

        if (ListUtils.isEmptyList(caseSubClassList)) {
            return true;
        }
        for (String caseClass : caseSubClassList) {
            if (ChecklossConst.CASE_CLASS_IMPORTANT_ACCIDENT.containsKey(caseClass)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public List<String> getCaseClassList(String reportNo, int caseTimes,String taskId){
		return caseClassDao.getCaseClassList(reportNo, caseTimes,null);
	}

    /**
     * 案件类别信息处理
     * @param dutySurveyVO
     * @return
     */
    @Override
    public  List<String> saveClassData(DutySurveyVO dutySurveyVO) {
        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();
        String status = dutySurveyVO.getStatus();
        String taskId = dutySurveyVO.getTaskId();
        //重新组装、保存本次的案件类别
        List<String> caseSubClassList = dutySurveyVO.getCaseSubClass();
        if(caseSubClassList == null || caseSubClassList.size() == 0){
            caseSubClassList = caseClassDao.getCaseClassCodeList(reportNo, caseTimes, null);
        }
        //将之前保存的案件类别先设置为“失效”状态
        CaseClassDTO param = new CaseClassDTO();
        param.setReportNo(reportNo);
        param.setCaseTimes(caseTimes);
//        param.setTaskId(taskId);
        param.setUpdatedBy(dutySurveyVO.getUserId());
        caseClassDao.updateEffective(param);

        if (CollectionUtils.isNotEmpty(caseSubClassList)) {
            List<CaseClassDTO> caseClassList = caseSubClassList.stream().map(e-> new CaseClassDTO()
                    .setReportNo(reportNo)
                    .setCaseTimes(caseTimes)
                    .setTaskId(taskId)
                    .setStatus(status)
                    .setCaseSubClass(e)).collect(Collectors.toList());
            caseClassDao.addCaseClassList(caseClassList, caseTimes, dutySurveyVO.getUserId());
        }
        return caseSubClassList;
    }
    @Override
    public  List<String> saveTPAClassData(DutySurveyVO dutySurveyVO) {
        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();
        String status = dutySurveyVO.getStatus();
        String taskId = dutySurveyVO.getTaskId();

        //将之前保存的案件类别先设置为“失效”状态
        CaseClassDTO param = new CaseClassDTO();
        param.setReportNo(reportNo);
        param.setCaseTimes(caseTimes);
        param.setTaskId(taskId);
        param.setUpdatedBy(dutySurveyVO.getUserId());
        caseClassDao.updateEffective(param);
        //重新组装、保存本次的案件类别
        List<String> caseSubClassList = dutySurveyVO.getCaseSubClass();
        if (CollectionUtils.isNotEmpty(caseSubClassList)) {
            List<CaseClassDTO> caseClassList=new ArrayList<>();
            Date date =new Date();
            for (String casecls : caseSubClassList) {
                CaseClassDTO dto =  new CaseClassDTO();
                dto.setReportNo(reportNo);
                dto.setCaseTimes(caseTimes);
                dto.setTaskId(taskId);
                dto.setStatus(status);
                dto.setCaseSubClass(casecls);
                dto.setCreatedDate(date);
                dto.setUpdatedDate(date);
                caseClassList.add(dto);

            }
            if(CollectionUtils.isNotEmpty(caseClassList)){
                LogUtil.info("saveTPAClassData报案号={}案件类别信息保存入参={}",reportNo, JsonUtils.toJsonString(caseClassList));
                caseClassDao.saveCaseClassList(caseClassList, caseTimes, dutySurveyVO.getUserId(),null);
            }

        }
        return caseSubClassList;
    }
}
