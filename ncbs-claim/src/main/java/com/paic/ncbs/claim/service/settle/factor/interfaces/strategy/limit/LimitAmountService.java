package com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EverySettleReasonParamsDTO;

/**
 * 限额接口
 */
public interface LimitAmountService {
    /**
     * 限额处理
     * @param detail
     */
    public void settleLimt(DutyDetailPayDTO detail, EverySettleReasonParamsDTO dto);
}
