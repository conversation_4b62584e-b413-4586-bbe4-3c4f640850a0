package com.paic.ncbs.claim.controller.duty;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.enums.AccidentCauseFlightEnum;
import com.paic.ncbs.claim.common.enums.AccidentCauseLossEnum;
import com.paic.ncbs.claim.common.enums.LossTypeEnum;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity;
import com.paic.ncbs.claim.dao.mapper.duty.LossItemDefineMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.ClaimRejectionApprovalRecordEntityMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.SurveyDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.vo.duty.*;
import com.paic.ncbs.claim.service.autoclaimsettle.AutoClaimSettleService;
import com.paic.ncbs.claim.service.checkloss.OtherLossPptService;
import com.paic.ncbs.claim.service.duty.DutySurveyService;
import com.paic.ncbs.claim.service.duty.DutyVerifyService;
import com.paic.ncbs.claim.service.duty.impl.DutySurveyServiceImpl;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Api(tags = "收单（核责）")
@RestController
@RequestMapping("/duty/app/dutySurveyAction")
public class DutySurveyController extends BaseController {

    @Autowired
    private DutySurveyService dutySurveyService;

    @Autowired
    private DutyVerifyService dutyVerifyService;

    @Autowired
    private ReportInfoService reportInfoService;

    @Autowired
    private OtherLossPptService otherLossPptService;

    @Autowired
    private ClaimRejectionApprovalRecordEntityMapper claimRejectionApprovalRecordEntityMapper;
    @Autowired
    private LossItemDefineMapper lossItemDefineMapper;
    @Autowired
    private AutoClaimSettleService autoClaimSettleService;
    @Autowired
    private DutySurveyServiceImpl dutySurveyServiceImpl;


    @InitBinder
    protected void initBinder(WebDataBinder binder) {
        binder.setAutoGrowCollectionLimit(2048);
    }


    @ApiOperation("保存收单信息")
    @PostMapping(value = "/saveDutySurvey")
    public ResponseResult<Object> saveDutyOrSurvey(@RequestBody DutySurveyVO dutyVO) throws Exception {
        LogUtil.info("收单提交saveDutySurvey报案号{}，请求入参{}", dutyVO.getReportNo(), JSON.toJSONString(dutyVO));
        dutySurveyService.handleDutySurvey(dutyVO);
        try {
            if ("N".equals(dutyVO.getIsSettle()) && ChecklossConst.STATUS_TMP_SUBMIT.equals(dutyVO.getStatus())
                    || "Y".equals(dutyVO.getIsSettle())) {
                // 调用自动理算提交
                LogUtil.info("收单提交自动理算报案号={}", dutyVO.getReportNo());
                autoClaimSettleService.autoSettleSubmit(dutyVO);
            }
        } catch (GlobalBusinessException ge) {
            // 手动异常不要告警了
            LogUtil.info("收单autoSettleSubmit:案件{} ,自动理赔校验异常：{}", dutyVO.getReportNo(), ge.getMessage());
        } catch (Exception e) {
            LogUtil.info("收单autoSettleSubmit:案件{} ,自动理赔异常：{}", dutyVO.getReportNo(), e.getMessage());
            LogUtil.error("收单autoSettleSubmit异常：", e);
        }
        return ResponseResult.success();
    }

    @ApiOperation("获取核责信息")
    @GetMapping(value = "/getDutySurvey/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<DutySurveyVO> getDutySurvey(@PathVariable("reportNo") String reportNo,
                                                        @PathVariable("caseTimes") int caseTimes) throws Exception {
        DutySurveyVO vo = getDutySurveyInfo(reportNo, caseTimes);
        return ResponseResult.success(vo);
    }



    @GetMapping(value = "/getPeopleHurt/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<PeopleHurtVO> getPeopleHurt(@PathVariable("reportNo") String reportNo,
                                                      @PathVariable("caseTimes") int caseTimes){
        PeopleHurtVO peopleHurtVO = dutySurveyService.getPeopleHurtVO(reportNo, caseTimes);
        return ResponseResult.success(peopleHurtVO);
    }

    private DutySurveyVO getDutySurveyInfo(String reportNo, int caseTimes) throws Exception {
        DutySurveyVO dutySurveyVO = dutySurveyService.getDutySurvey(reportNo, caseTimes,null);
        try {
            ReportBaseInfoResData reportBaseInfoResData = reportInfoService.requestReportBaseInfo(reportNo);
            if (Objects.nonNull(reportBaseInfoResData)) {
                dutySurveyVO.setName(reportBaseInfoResData.getName());
                dutySurveyVO.setBirthDay(reportBaseInfoResData.getBirthday());
                dutySurveyVO.setCertificateType(reportBaseInfoResData.getCertificateType());
                dutySurveyVO.setCertificateNo(reportBaseInfoResData.getCertificateNo());
            }
        } catch (Exception e) {
            LogUtil.audit("#获取收单核责信息异常#。reportNo={},caseTimes={}", reportNo, caseTimes);
        }
        return dutySurveyVO;
    }

    @ApiOperation("获取核责信息2")
    @PostMapping(value = "/getDutySurvey")
    public ResponseResult<DutySurveyVO> getDutySurvey(@RequestBody DutySurveyVO dutySurveyVO) throws Exception {
        LogUtil.audit("#获取核责信息2#入参#reprotNo =" + dutySurveyVO.getReportNo());
        DutySurveyVO vo = dutySurveyService.getDutySurvey(dutySurveyVO.getReportNo(), dutySurveyVO.getCaseTimes(), dutySurveyVO.getTaskId());
        return ResponseResult.success(vo);
    }

    @ApiOperation("拒赔审批记录")
    @PostMapping(value = "/rejectionRecords/{reportNo}/{caseTimes}")
    public ResponseResult<List<ClaimRejectionApprovalRecordEntity>> getRejectionRecords(@PathVariable("reportNo") String reportNo, @PathVariable("caseTimes") int caseTimes)   {
        LogUtil.audit("#拒赔审批记录#入参#reprotNo =" + reportNo);
        List<ClaimRejectionApprovalRecordEntity> claimRejectionApprovalRecordEntities = claimRejectionApprovalRecordEntityMapper.selectByReportNo(reportNo,caseTimes);
        if(!CollectionUtils.isEmpty(claimRejectionApprovalRecordEntities)){
            claimRejectionApprovalRecordEntities=claimRejectionApprovalRecordEntities.stream().
                    filter(e-> !StringUtils.isEmptyStr(e.getAuditOpinion())).collect(Collectors.toList());
        }
        return ResponseResult.success(claimRejectionApprovalRecordEntities);
    }


    @ApiOperation("获取核责结论、收单信息")
    @GetMapping(value = "/getDutyVerify/{reportNo}/{caseTimes}")
    @ApiImplicitParams({@ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
                        @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)})
    public ResponseResult<DutyVerifyVO> getDutyVerify(@PathVariable("reportNo") String reportNo,
                                                      @PathVariable("caseTimes") int caseTimes) throws Exception {
        DutyVerifyVO vo = dutyVerifyService.getDutyVerify(reportNo, caseTimes);
        return ResponseResult.success(vo);
    }


    @ApiOperation("获取查勘数据")
    @PostMapping(value = "/getSurveyList")
    public ResponseResult<List<SurveyCaseClassVO>> getSurveyList(@RequestBody SurveyDTO surveyDTO) throws GlobalBusinessException {
        LogUtil.audit("通过报案号获取查勘数据。reportNo=" + surveyDTO.getReportNo());
        List<SurveyCaseClassVO> surveyCaseClassList = dutyVerifyService.getSurveyList(surveyDTO);
        return ResponseResult.success(surveyCaseClassList);
    }


    @ApiOperation("获取意外信息(是否重灾)")
    @GetMapping(value = "/getHugeAccident/{reportNo}/{caseTimes}")
    @ApiImplicitParams({@ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
                        @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)})
    public ResponseResult<Object> getHugeAccident(@PathVariable("reportNo") String reportNo,
                                          @PathVariable("caseTimes") Integer caseTimes) throws GlobalBusinessException {
        LogUtil.audit("#根据报案号获取意外信息(是否重灾)#:传入参数reportNo=%s,caseTimes=%s", reportNo, caseTimes);
        WholeCaseBaseDTO wholeCaseBaseDTO = dutyVerifyService.getHugeAccident(reportNo, caseTimes);

        return ResponseResult.success(wholeCaseBaseDTO);
    }

    @ApiOperation("获取事故类型参数")
    @GetMapping(value = "/getAccidentTypeParameter/{reportNo}/{collectionCode}")
    @ApiImplicitParams({@ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String" ,dataTypeClass=String.class),
                        @ApiImplicitParam(name = "collectionCode", value = "参数类型码",dataType = "String",dataTypeClass=Integer.class)})
    public ResponseResult<Object> getAccidentTypeParameter(@PathVariable("reportNo") String reportNo,
                                                   @PathVariable("collectionCode") String collectionCode) throws GlobalBusinessException {
        List<Map<String, String>> list = dutySurveyService.getAccidentTypeParameter(reportNo, collectionCode);
        return ResponseResult.success(list);
    }


    @ApiOperation("获取事故原因列表")
    @GetMapping(value = "/getAccidentReasonList/{reportNo}")
    public ResponseResult<Object> getAccidentReasonList(@ApiParam("报案号") @PathVariable("reportNo") String reportNo) throws GlobalBusinessException {
        List<String> list = otherLossPptService.getAccidentReasonList(reportNo);
        return ResponseResult.success(list);
    }

    @GetMapping(value = "/getLossTypeEnum")
    public ResponseResult<List<LossTypeEnum>> getLossTypeEnum(){
        return ResponseResult.success(Arrays.asList(LossTypeEnum.values()));
    }

    @GetMapping(value = "/getAccidentCauseLossEnum")
    public ResponseResult<List<AccidentCauseLossEnum>> getAccidentCauseLossEnum(){
        return ResponseResult.success(Arrays.asList(AccidentCauseLossEnum.values()));
    }

    @GetMapping(value = "/getLossItems")
    public ResponseResult<List<LossItemDefineVO>> getLossItems(){
        return ResponseResult.success(lossItemDefineMapper.getLossItems());
    }

    @GetMapping(value = "/getAccidentCauseFlightEnum")
    public ResponseResult<List<AccidentCauseFlightEnum>> getAccidentCauseFlightEnum(){
        return ResponseResult.success(Arrays.asList(AccidentCauseFlightEnum.values()));
    }

    @ApiOperation("保存诊断信息")
    @PostMapping(value = "/saveDiagnosticInformation")
    public ResponseResult<Object> saveDiagnosticInformation(@RequestBody DutySurveyVO dutyVO){
        dutySurveyServiceImpl.saveDiagnosticInformation(dutyVO);
        return ResponseResult.success();
    }

}
