package com.paic.ncbs.claim.service.investigate;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateAuditVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskAuditVO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditDTO;

public interface InvestigateTaskAuditService {

	void finishTaskAudit(InvestigateTaskAuditDTO investigateTaskAuditDTO, String userId) throws GlobalBusinessException;

	InvestigateAuditVO getApproveInfoByIdAhcsInvestigateTask(String idAhcsInvestigateTask);

	InvestigateTaskAuditVO getInvestigateTaskAuditForReportByInvestigateId(String idAhcsInvestigate) throws GlobalBusinessException;

	void transferApproval(InvestigateTaskAuditDTO investigateTaskAuditDTO, String userId) throws GlobalBusinessException;


}