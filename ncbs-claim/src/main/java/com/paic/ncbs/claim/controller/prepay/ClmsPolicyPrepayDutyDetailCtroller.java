package com.paic.ncbs.claim.controller.prepay;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.dao.mapper.prepay.PrePayMapper;
import com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.PrepayDutyDetailRequestDTO;
import com.paic.ncbs.claim.service.prepay.ClmsPolicyPrepayDutyDetailService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "责任明细信息")
@RestController
@RequestMapping("/prepay")
public class ClmsPolicyPrepayDutyDetailCtroller {
    @Autowired
    private ClmsPolicyPrepayDutyDetailService clmsPolicyPrepayDutyDetailService;

    @Autowired
    private PrePayMapper prePayMapper;

    /**
     * 预配责任明细保存
     * @param requestDTO
     */
    @RequestMapping("/saveDutyDetail")
    public ResponseResult saveDutyDetail(@RequestBody PrepayDutyDetailRequestDTO requestDTO){
        clmsPolicyPrepayDutyDetailService.saveDutyDetail(requestDTO.getDtoList());
        return  ResponseResult.success();
    }

    /**
     * 预配申请任务添加测试
     * @param payInfoDTO
     */
    @RequestMapping("/savePrepayData")
    public ResponseResult savePrepayData(@RequestBody PrePayInfoDTO payInfoDTO){
        prePayMapper.savePrePayInfo(payInfoDTO);
        return  ResponseResult.success();
    }

}
