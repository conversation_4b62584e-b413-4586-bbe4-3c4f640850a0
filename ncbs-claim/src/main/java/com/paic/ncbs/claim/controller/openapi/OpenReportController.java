package com.paic.ncbs.claim.controller.openapi;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.dto.api.StandardRequestDTO;
import com.paic.ncbs.claim.model.dto.openapi.*;
import com.paic.ncbs.claim.model.vo.openapi.DutyPayByCustomResVO;
import com.paic.ncbs.claim.model.vo.openapi.OpenReportInfoVO;
import com.paic.ncbs.claim.model.vo.openapi.ReportQueryResVO;
import com.paic.ncbs.claim.service.duty.DutyPayService;
import com.paic.ncbs.claim.service.openapi.OpenReportService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.claim.validator.OpenReportValidator;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/public/report")
public class OpenReportController extends BaseController {

    @Autowired
    private OpenReportService openReportService;
    @Autowired
    private DutyPayService dutyPayService;

    @ApiOperation("根据报案号查询案件信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", required = true, dataType = "Int", dataTypeClass = Integer.class)
    })
    @GetMapping(value = "/getReportInfo")
    public ResponseResult<OpenReportInfoVO> getReportInfo(@RequestParam("reportNo") String reportNo,
                                                          @RequestParam("caseTimes") Integer caseTimes) {
        OpenReportInfoVO vo = openReportService.getReportInfo(reportNo, caseTimes);
        return ResponseResult.success(vo);
    }

    /**
     * 保单历史案件信息列表，客服系统用
     * @param requestDTO
     * @return
     */
    @PostMapping(value = "/getPolicyHistoryClaimInfo")
    public ResponseResult<List<PolicyClaimHistoryReportInfoDTO>> getPolicyHistoryClaimInfo(@RequestBody QueryClaimReportRequestDTO requestDTO){
        return ResponseResult.success(openReportService.getPolicyHistoryClaimInfo(requestDTO));
    }

    /**
     * 案件详情信息 客服系统用
     * @return
     */
    @GetMapping(value = "/getReportDetailInfo/{reportNo}")
    public ResponseResult<ClaimDetailedInfoDTO> getReportDetailInfo(@PathVariable("reportNo") String reportNo){
        return ResponseResult.success(openReportService.getReportDetailInfo(reportNo));
    }

    @ApiOperation("获取客户历史赔付责任列表接口")
    @PostMapping(value = "/listDutyPayByCustom")
    public List<DutyPayByCustomResVO> listDutyPayByCustom(@RequestBody DutyPayByCustomReqDTO req) {
        OpenReportValidator.checkListDutyPayByCustom(req);
        return dutyPayService.listDutyPayByCustom(req);
    }

    @ApiOperation("获取客户理赔案件列表")
    @PostMapping(value = "/queryCaseInfoList")
    public ReportQueryResVO queryCaseInfoList(@RequestBody ReportQueryReqDTO req) {
        OpenReportValidator.checkQueryCase(req);
        return openReportService.queryCaseInfoList(req);
    }

    @ApiOperation("获取客户理赔案件列表")
    @PostMapping(value = "/queryReport")
    public ReportQueryResVO queryReport(@RequestBody ReportQueryReqDTO req) {
        OpenReportValidator.checkQueryReport(req);
        return openReportService.queryReport(req);
    }

    @ApiOperation("获取客户理赔案件列表(重疾)")
    @PostMapping(value = "/queryReport4Severe")
    public ReportQueryResVO queryReport4Severe(@RequestBody ReportQueryReqDTO req) {
        OpenReportValidator.checkQueryReport(req);
        return openReportService.queryReport4Severe(req);
    }

    @ApiOperation("获取客户理赔案件信息")
    @PostMapping(value = "/queryCusReportInfo")
    public ReportQueryResVO queryCusReportInfo(@RequestBody ReportQueryReqDTO req) {
        LogUtil.info("出单查询理赔历史记录入参={}", JsonUtils.toJsonString(req));
        ReportQueryResVO resp = openReportService.getQueryCusReportInfo(req);
        return resp;
    }

    @ApiOperation("查询客户历史案件理赔信息")
    @PostMapping(value = "/getHistoryCaseList")
    public ResponseResult<Object> getHistoryCaseList(@RequestBody StandardRequestDTO standardRequestDTO){
        LogUtil.info("查询客户历史案件理赔信息入参={}", JsonUtils.toJsonString(standardRequestDTO));
        ReportQueryReqDTO queryVO = JsonUtils.toObject(JsonUtils.toJsonString(standardRequestDTO.getRequestData()), ReportQueryReqDTO.class);//        try{
            return ResponseResult.success(openReportService.getHistoryCaseList(queryVO));
    }
}
