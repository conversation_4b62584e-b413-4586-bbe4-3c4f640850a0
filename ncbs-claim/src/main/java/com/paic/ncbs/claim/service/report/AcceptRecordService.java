package com.paic.ncbs.claim.service.report;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.report.AcceptRecordDTO;

import java.util.List;

public interface AcceptRecordService {

	AcceptRecordDTO addAcceptRecord(AcceptRecordDTO acceptRecordDTO) throws GlobalBusinessException;

	AcceptRecordDTO getAcceptRecordByAsc(String reportNo, Integer caseTimes);

	AcceptRecordDTO getAcceptRecord(String reportNo, Integer caseTimes);

	List<AcceptRecordDTO> getAcceptRecordByReportNo(String reportNo, Integer caseTimes);
}
