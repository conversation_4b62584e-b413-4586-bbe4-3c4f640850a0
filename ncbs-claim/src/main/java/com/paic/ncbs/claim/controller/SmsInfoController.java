package com.paic.ncbs.claim.controller;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.vo.record.OperationRecordVO;
import com.paic.ncbs.claim.model.vo.record.SmsRecordVO;
import com.paic.ncbs.claim.service.other.MailSendService;
import com.paic.ncbs.claim.service.other.SmsInfoService;
import com.paic.ncbs.claim.service.taskdeal.impl.TaskPoolServiceImpl;
import com.paic.ncbs.message.model.dto.SmsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/app/smsAction")
public class SmsInfoController {

    @Autowired
    private SmsInfoService smsInfoService;
    @Autowired
    private MailSendService mailSendService;
    @Autowired
    private TaskPoolServiceImpl taskPoolServiceImpl;

    @GetMapping("/querySmsResult")
    public ResponseResult<SmsResult> querySmsResult(@RequestParam("id") String id){
        return ResponseResult.success(smsInfoService.querySms(id));
    }

    @RequestMapping(value = "/querySmsRecordByReportNo",produces = {"application/json"},method = RequestMethod.GET)
    public ResponseResult<List<SmsRecordVO>> querySmsRecordByReportNo(@RequestParam("reportNo") String reportNo){
        List<SmsRecordVO> smsRecordVOS = smsInfoService.querySmsRecordByReportNo(reportNo);
        return ResponseResult.success(smsRecordVOS);
    }
    
}
