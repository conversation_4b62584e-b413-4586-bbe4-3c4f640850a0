package com.paic.ncbs.claim.service.blacklist.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackList;
import com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListAudit;
import com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListRecord;
import com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListAuditMapper;
import com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListMapper;
import com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListRecordMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.blacklist.*;
import com.paic.ncbs.claim.service.blacklist.ClmsBlackListAuditService;
import com.paic.ncbs.claim.service.blacklist.ClmsBlackListRecordService;
import com.paic.ncbs.claim.service.blacklist.ClmsBlackListService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.utils.PageResult;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 黑名单审批记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Service
public class ClmsBlackListAuditServiceImpl implements ClmsBlackListAuditService {

    @Autowired
    private ClmsBlackListMapper blackListMapper;

    @Autowired
    private ClmsBlackListAuditMapper auditMapper;

    @Autowired
    private ClmsBlackListRecordMapper blackListRecordMapper;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private BlackListDictConverter dictConverter;

    @Autowired
    private ClmsBlackListService clmsBlackListService;

    @Autowired
    private ClmsBlackListRecordService clmsBlackListRecordService;

    @Override
    public PageResult<ClmsBlackListAuditVO> getPendingAudits(ClmsBlackListAuditVO queryVO) {
        List<ClmsBlackListAuditVO> pendingAudits = auditMapper.getPendingAudits();
        // 为每个待审批项添加中文描述
        pendingAudits.forEach(vo -> {
            // 添加黑名单类型中文描述
            if (StringUtils.isNotBlank(vo.getPartyType())) {
                vo.setPartyTypeName(dictConverter.getPartyTypeNames(vo.getPartyType()));
            }
            // 添加证件类型中文描述
            if (StringUtils.isNotBlank(vo.getIdType())) {
                vo.setIdTypeName(dictConverter.getIdTypeName(vo.getIdType()));
            }
            // 添加风险类型中文描述
            if (StringUtils.isNotBlank(vo.getRiskType())) {
                vo.setRiskTypeName(dictConverter.getRiskTypeNames(vo.getRiskType()));
            }
        });
        // 获取总记录数
        int total = pendingAudits.size();
        // 实现分页逻辑
        List<ClmsBlackListAuditVO> pagedResult = pendingAudits;
        int currentPage = 1;
        int pageSize = total;

        // 只有当前端传入了分页参数时才使用分页
        if (queryVO != null && queryVO.getCurrentPage() != null && queryVO.getPageSize() != null
                && queryVO.getCurrentPage() > 0 && queryVO.getPageSize() > 0) {
            currentPage = queryVO.getCurrentPage();
            pageSize = queryVO.getPageSize();
            int startIndex = (currentPage - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, pendingAudits.size());

            // 处理边界情况
            if (startIndex >= pendingAudits.size() || startIndex < 0) {
                pagedResult = Collections.emptyList();
            } else {
                pagedResult = pendingAudits.subList(startIndex, endIndex);
            }
        }
        return new PageResult<>(pagedResult, total, currentPage, pageSize);
    }

    /**
     * 根据黑名单ID获取黑名单审批详情信息
     * @param id 黑名单ID
     * @return BlackListAuditDetailVO 包含审批详情、当前黑名单状态和历史记录
     */
    @Override
    public BlackListAuditDetailVO getBlackListAuditById(String id) throws Exception {
        // 查询审批信息
        ClmsBlackListAudit auditInfo = auditMapper.getBlackListAuditById(id);
        // 查询当前黑名单信息
        ClmsBlackListVO blackListById = clmsBlackListService.getBlackListById(id);
        // 查询记录信息
        ClmsBlackListRecordVO blackListRecordById = clmsBlackListRecordService.getBlackListRecordById(id);
        // 组装并返回组合对象
        return new BlackListAuditDetailVO(blackListById, auditInfo, blackListRecordById);
    }

    /**
     * 黑名单审批流程处理
     * 该方法用于处理黑名单审批操作，包括同意和拒绝两种情况，涉及状态更新、数据同步及后续流程处理
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void blacklistApprovalProcess(ClmsBlackListAuditVO clmsBlackListAuditVO) throws Exception {
        //使用id设置锁
        String lockKey = "BLACKLIST_APPROVAL_LOCK:" + clmsBlackListAuditVO.getBlackListId();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁（等待3秒，锁有效期30秒）
            if (lock.tryLock(3, 30, TimeUnit.SECONDS)) {

                ClmsBlackListAudit audit = new ClmsBlackListAudit();
                String userId = WebServletContext.getUserId();
                ClmsBlackList blackList = new ClmsBlackList();
                String blackListId = clmsBlackListAuditVO.getBlackListId();

                // 查询待审批数据
                ClmsBlackListRecord record = blackListRecordMapper.getByBlackListId(blackListId);
                if (record == null){
                    throw new GlobalBusinessException("数据不存在,请检查！");
                }

                // 更新审批记录
                BeanUtils.copyProperties(clmsBlackListAuditVO, audit);
                audit.setUpdatedBy(userId);
                audit.setAuditBy(userId);

                // 审批同意
                if ("1".equals(clmsBlackListAuditVO.getAuditResult())) {
                    audit.setAuditStatus("3");

                    BeanUtils.copyProperties(record, blackList);

                    blackList.setId(blackListId);
                    blackList.setAuditStatus("2");
                    blackList.setUpdatedBy(userId);

                    // 根据操作类型更新黑名单信息
                    switch (record.getOperateType()) {
                        //新增
                        case "1":
                            blackListMapper.saveBlackList(blackList);
                            break;
                        //修改
                        case "2":
                            blackListMapper.updateBlackList(blackList);
                            break;
                        //启用
                        case "3":
                            blackList.setValidFlag("Y");
                            blackListMapper.updateBlackList(blackList);
                            break;
                        //禁用
                        case "4":
                            blackList.setValidFlag("N");
                            blackListMapper.updateBlackList(blackList);
                            break;

                        default:
                            LogUtil.error("未知操作类型: {}", record.getOperateType());
                            throw new GlobalBusinessException("未知操作类型: {}", record.getOperateType());
                    }
                    // 记录操作日志
                    operationRecordService.insertOperationRecordByLabour(record.getReportNo(),BpmConstants.OC_BLACK_LIST,"审批通过",clmsBlackListAuditVO.getAuditDesc());
                    LogUtil.audit("#提交至工作流完成任务-结束#:reportNo=%s,caseTimes=%s", record.getReportNo(), record.getCaseTimes());
                } else {
                    // 审批拒绝
                    audit.setAuditStatus("4");

                    // 非新增操作时，则退回提交审批前状态
                    if (!"1".equals(record.getOperateType())){
                        blackList.setId(record.getBlackListId());
                        blackList.setAuditStatus(record.getAuditStatus());
                        blackList.setUpdatedBy(userId);
                        blackListMapper.updateBlackList(blackList);
                    }
                    // 记录操作日志
                    operationRecordService.insertOperationRecordByLabour(record.getReportNo(),BpmConstants.OC_BLACK_LIST,"审批不通过",clmsBlackListAuditVO.getAuditDesc());
                    LogUtil.audit("#提交至工作流完成任务-结束#:reportNo=%s,caseTimes=%s", record.getReportNo(), record.getCaseTimes());
                }
                // 更新审批记录状态
                auditMapper.updateAuditStatus(audit);
                // 审批完成后清理操作记录
                blackListRecordMapper.deleteByBlackListId(blackListId);
                // 审批完成，结束任务
                bpmService.completeTask_oc(record.getReportNo(), record.getCaseTimes(), BpmConstants.OC_BLACK_LIST, blackListId);
            } else {
                throw new GlobalBusinessException("系统繁忙，请稍后重试");
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    @Override
    public ClmsBlackListAuditVO getPendingAuditByBlackListId(String blackListId) {
        ClmsBlackListAuditVO auditVO = auditMapper.getPendingAuditByBlackListId(blackListId);
        if (auditVO != null) {
            // 添加黑名单类型中文描述
            if (StringUtils.isNotBlank(auditVO.getPartyType())) {
                auditVO.setPartyTypeName(dictConverter.getPartyTypeNames(auditVO.getPartyType()));
            }
            // 添加证件类型中文描述
            if (StringUtils.isNotBlank(auditVO.getIdType())) {
                auditVO.setIdTypeName(dictConverter.getIdTypeName(auditVO.getIdType()));
            }
            // 添加风险类型中文描述
            if (StringUtils.isNotBlank(auditVO.getRiskType())) {
                auditVO.setRiskTypeName(dictConverter.getRiskTypeNames(auditVO.getRiskType()));
            }
        }
        return auditVO;
    }

    @Override
    public PageResult<ClmsBlackListAuditVO> getBlackListByCase(ClmsBlackListAuditVO clmsBlackListAuditVO) {
        String reportNo = clmsBlackListAuditVO.getReportNo();
        String caseTimes = String.valueOf(clmsBlackListAuditVO.getCaseTimes());
        List<ClmsBlackListAuditVO> list = new ArrayList<>();

        List<ClmsBlackListAuditVO> blackList = auditMapper.getBlackList(reportNo, caseTimes);
        if (blackList != null && !blackList.isEmpty()){
            blackList.forEach( auditVO -> {
                // 添加黑名单类型中文描述
                if (StringUtils.isNotBlank(auditVO.getPartyType())) {
                    auditVO.setPartyTypeName(dictConverter.getPartyTypeNames(auditVO.getPartyType()));
                }
                // 添加证件类型中文描述
                if (StringUtils.isNotBlank(auditVO.getIdType())) {
                    auditVO.setIdTypeName(dictConverter.getIdTypeName(auditVO.getIdType()));
                }
                // 添加风险类型中文描述
                if (StringUtils.isNotBlank(auditVO.getRiskType())) {
                    auditVO.setRiskTypeName(dictConverter.getRiskTypeNames(auditVO.getRiskType()));
                }
                auditVO.setAuditStatus("2");
                list.add(auditVO);
            });
        }
        List<ClmsBlackListAuditVO> blackListRecord = auditMapper.getBlackListRecord(reportNo, caseTimes);
        if (blackListRecord != null && !blackListRecord.isEmpty()){
            blackListRecord.forEach(auditVO -> {
                // 添加黑名单类型中文描述
                if (StringUtils.isNotBlank(auditVO.getPartyType())) {
                    auditVO.setPartyTypeName(dictConverter.getPartyTypeNames(auditVO.getPartyType()));
                }
                // 添加证件类型中文描述
                if (StringUtils.isNotBlank(auditVO.getIdType())) {
                    auditVO.setIdTypeName(dictConverter.getIdTypeName(auditVO.getIdType()));
                }
                // 添加风险类型中文描述
                if (StringUtils.isNotBlank(auditVO.getRiskType())) {
                    auditVO.setRiskTypeName(dictConverter.getRiskTypeNames(auditVO.getRiskType()));
                }
                auditVO.setAuditStatus("1");
                list.add(auditVO);
            });
        }
        // 获取总记录数
        int total = list.size();
        // 实现分页逻辑
        List<ClmsBlackListAuditVO> pagedResult = list;
        int currentPage = 1;
        int pageSize = total;
        // 只有当前端传入了分页参数时才使用分页
        if (clmsBlackListAuditVO.getCurrentPage() != null && clmsBlackListAuditVO.getPageSize() != null
                && clmsBlackListAuditVO.getCurrentPage() > 0 && clmsBlackListAuditVO.getPageSize() > 0) {
            currentPage = clmsBlackListAuditVO.getCurrentPage();
            pageSize = clmsBlackListAuditVO.getPageSize();
            int startIndex = (currentPage - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, list.size());
            // 处理边界情况
            if (startIndex >= list.size() || startIndex < 0) {
                pagedResult = Collections.emptyList();
            } else {
                pagedResult = list.subList(startIndex, endIndex);
            }
        }
        return new PageResult<>(pagedResult, total, currentPage, pageSize);
    }
}
