package com.paic.ncbs.claim.controller.duty;


import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.verify.VerifyConclusionCauseDTO;
import com.paic.ncbs.claim.service.verify.VerifyConclusionCauseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Api(tags = "核赔结论原因")
@Controller
@RequestMapping("/duty/app/verifyConclusionCauseAction")
public class VerifyConclusionCauseController extends BaseController {

	@Autowired
	private VerifyConclusionCauseService verifyConclusionCauseService;

	@ApiOperation("获取核责结论原因信息列表")
	@ResponseBody
	@RequestMapping(value="/getVerifyConclusionCauseList", method=RequestMethod.GET)
	public ResponseResult<List<VerifyConclusionCauseDTO>> getVerifyConclusionCauseList()throws GlobalBusinessException {
		LogUtil.audit("获取核责结论原因信息。");

		List<VerifyConclusionCauseDTO> list =  verifyConclusionCauseService.getVerifyConclusionCauseList();
		return ResponseResult.success(list);
	}
	
}
