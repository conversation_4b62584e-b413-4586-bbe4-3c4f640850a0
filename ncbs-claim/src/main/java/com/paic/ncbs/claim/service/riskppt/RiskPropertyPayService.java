package com.paic.ncbs.claim.service.riskppt;

import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.*;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;

import java.util.List;

public interface RiskPropertyPayService {

    /**
     * 查询保单标的赔付列表
     * @param riskPropertyPayDTO
     * @return
     */
    List<RiskPropertyPayDTO> getRiskPropertyPayList(RiskPropertyPayDTO riskPropertyPayDTO);

    /**
     * 保存案件标的赔付列表
     * @param riskPropertyPayList
     * @return
     */
    void saveRiskPropertyPayList(List<RiskPropertyPayDTO> riskPropertyPayList);

    /**
     * 结案保存案件标的赔付明细
     * @param wholeCaseBaseDTO
     * @return
     */
    void saveRiskPropertyPay(WholeCaseBaseDTO wholeCaseBaseDTO);

    /**
     * 初始化标的责任最大给付额
     * @param policyPays
     */
    void initRiskPropertyMaxPay(List<PolicyPayDTO> policyPays);

    /**
     * 初始化标的责任最大给付额
     * @param estimatePolicyList
     */
    void initEstRiskPropertyMaxPay(List<EstimatePolicyDTO> estimatePolicyList);

    /**
     * 校验赔付信息
     * @param policyPays
     */
    void checkRiskPropertyPay(List<PolicyPayDTO> policyPays);

    /**
     * 校验责任险限额
     * @param reportNo
     * @param policyPays
     */
    void checkRiskPay(String reportNo, List<PolicyPayDTO> policyPays);
}
