package com.paic.ncbs.claim.service.settle.impl;

import com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyClaimCaseMapper;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.settle.PolicyClaimCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("policyClaimCaseService")
public class PolicyClaimCaseServiceImpl implements PolicyClaimCaseService {

    @Autowired
    private AhcsCommonService ahcsCommonService;
    @Autowired
    private PolicyClaimCaseMapper policyClaimCaseDao;

    @Override
    public void addBatchPolicyClaimCase(List<PolicyClaimCaseDTO> policyClaimCaseDTOList) {
        ahcsCommonService.batchHandlerTransactionalWithArgs(PolicyClaimCaseMapper.class, policyClaimCaseDTOList,
                ListUtils.GROUP_NUM, "addBatchPolicyClaimCase");
    }

    @Override
    public List<PolicyClaimCaseDTO> getPolicyClaimCaseListByReportNo(String reportNo) {
        return policyClaimCaseDao.getPolicyClaimCaseListByReportNo(reportNo);
    }
}
