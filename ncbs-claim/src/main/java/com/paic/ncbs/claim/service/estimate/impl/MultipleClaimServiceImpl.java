package com.paic.ncbs.claim.service.estimate.impl;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.other.MultiClaimApplyMapper;
import com.paic.ncbs.claim.service.estimate.MultipleClaimService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("multipleClaimService")
public class MultipleClaimServiceImpl implements MultipleClaimService {
    @Autowired
    private MultiClaimApplyMapper multiClaimApplyDao;

    @Override
    public String getMultiClaimPriorityReason(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        LogUtil.audit("#根据报案号、赔付次数获取重开优先原因#入参# reportNo=" + reportNo + ", caseTimes=" + caseTimes);

        return multiClaimApplyDao.getMultiClaimPriorityReason(reportNo, caseTimes);
    }

    @Override
    public int getMultiClaimIngByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        return multiClaimApplyDao.getMultiClaimIngByReportNo(reportNo, caseTimes);
    }

    @Override
    public String getCurrentMultiClaimApplyUm(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        LogUtil.audit("#获取当前赔付次数最新的重开的申请人#入参# reportNo=" + reportNo + ", caseTimes=" + caseTimes);
        String multiClaimApplyUm = null;
        int applyTimes = multiClaimApplyDao.getApplyTimesByRnCt(reportNo, caseTimes);
        if (applyTimes >= 1) {
            multiClaimApplyUm = multiClaimApplyDao.getCurrentMultiClaimApplyUm(reportNo, caseTimes, applyTimes);
        }
        return multiClaimApplyUm;
    }
}