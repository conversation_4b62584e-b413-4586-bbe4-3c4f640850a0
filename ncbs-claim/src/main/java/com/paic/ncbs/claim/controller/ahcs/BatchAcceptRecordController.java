package com.paic.ncbs.claim.controller.ahcs;

import cn.hutool.crypto.digest.DigestUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.FileUploadConstants;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CertificateTypeEnum;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.poi.BaseExcelUtil;
import com.paic.ncbs.claim.common.poi.ExcelHelper;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.FileUtil;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.RapeCheckUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsBatchEntity;
import com.paic.ncbs.claim.dao.entity.report.BatchReportTempEntity;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDomainDTO;
import com.paic.ncbs.claim.model.vo.batch.BatchAcceptTemplateExcel;
import com.paic.ncbs.claim.service.ahcs.AhcsBatchReportService;
import com.paic.ncbs.claim.service.ahcs.BatchReportTempEntityService;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/report")
@Api(tags = "批量上传")
@RefreshScope
public class BatchAcceptRecordController extends BaseController {

    @Autowired
    private BatchReportTempEntityService batchReportTempEntityService;

    @Autowired
    @Lazy
    private AhcsBatchReportService ahcsBatchReportService;

    @Autowired
    private IOBSFileUploadService iobsFileUploadService;

    @Autowired
    private CommonService commonService ;

    @Value("${iobs.enable:true}")
    private boolean iobs;

    @Value("${file.batchUploadReport.maxLoadSize}")
    private Integer  maxLoadSize;


    @RequestMapping(value = "/getInfoByBatchNoAndType", method = RequestMethod.GET)
    @ApiOperation(value = "查询批次信息")
    public ResponseResult getInfoByBatchNoAndType(String batchNo, String batchType, Pager pager) {
        LogUtil.info("批次号：{}批次类型:{},分页信息", batchNo, batchType, pager);
        PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
        List<BatchReportTempEntity> list = batchReportTempEntityService.getInfoByBatchNoAndType(batchNo, batchType);
        PageInfo<BatchReportTempEntity> pageInfo = new PageInfo<BatchReportTempEntity>(list);
        pager.setTotalRows((int) pageInfo.getTotal());
        PageMethod.clearPage();
        if (RapeCheckUtil.isNotEmpty(list)) {
            for (BatchReportTempEntity batchReportTempEntity : list) {
                batchReportTempEntity.setCertificateType(CertificateTypeEnum.getName(batchReportTempEntity.getCertificateType()));
            }
        }
        return ResponseResult.success(list, pager);
    }

    @ApiOperation(value = "下载模板")
    @RequestMapping(value = "/downloadTemplate", method = RequestMethod.GET)
    public void downloadBatchReportTemplate(HttpServletResponse response, @RequestParam("templateName") String templateName) {
        BufferedOutputStream out = null;
        try {
            //获取输入流，原始模板位置
            InputStream bis = this.getClass().getResourceAsStream("/templates/意健险批量报案模板.xlsx");
            //转码，免得文件名中文乱码
            String fileName = "意健险批量报案模板.xlsx";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            //设置文件下载头
            response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
            //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");
            out = new BufferedOutputStream(response.getOutputStream());
            int len = 0;
            while ((len = bis.read()) != -1) {
                out.write(len);
                out.flush();
            }
        } catch (IOException e) {
            LogUtil.error("下载文件异常", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                LogUtil.error("流关闭异常", e);
            }
        }

    }


    @ApiOperation(value = "上传文件")
    @RequestMapping(value = "/uploadFile", method = RequestMethod.POST)
    public ResponseResult saveBatchReportNew(@RequestParam("batchReportFile") MultipartFile file, String batchType,HttpServletResponse response) throws Exception {
        LogUtil.info("batchType{}", batchType);
        String reportBatchNo =commonService.generateNo( NoConstants.BEATCH_REPORT_NO, VoucherTypeEnum.BARCH_REPORT_CASE_NO,WebServletContext.getDepartmentCode()) ;
        LogUtil.info("batchAcceptRecord-start:{},{}", reportBatchNo,Runtime.getRuntime().availableProcessors());
        String userId = WebServletContext.getUserId();
        List<BatchAcceptTemplateExcel> entityExcelList = BaseExcelUtil.readFileToWorkbook(file.getInputStream(), BatchAcceptTemplateExcel.class, 1);
        String md5 = DigestUtil.sha256Hex(file.getInputStream());
        int count = ahcsBatchReportService.queryMd5Count(md5);
        LogUtil.info("批量导入入参文件流md5:{}",md5);

        AhcsBatchEntity ahcsBatchEntity =new AhcsBatchEntity();
        ahcsBatchEntity.setBatchUploadNums(entityExcelList.size());
        ahcsBatchEntity.setFileName(file.getOriginalFilename());

        if (count > 0) {
            ahcsBatchEntity.setOperateShow("请不要重复导入文件");
            ahcsBatchEntity.setUpFileCode(ErrorCode.FILE_HAVE_PROBLEM) ;
            ahcsBatchEntity.setUpFileCodeDescribe("请不要重复导入文件") ;
            return ResponseResult.success(ahcsBatchEntity.setUpFileStatus(ConstValues.FAIL));
        }
        if (entityExcelList.size() > maxLoadSize || entityExcelList.size() == 0  ) {
            ahcsBatchEntity.setOperateShow("模板数据量超过"+maxLoadSize+"或文件为空,请删减后重试");
            ahcsBatchEntity.setUpFileCode(ErrorCode.FILE_HAVE_PROBLEM) ;
            ahcsBatchEntity.setUpFileCodeDescribe("模板数据量超过"+maxLoadSize+"+或文件为空,请删减后重试") ;
            return ResponseResult.success(ahcsBatchEntity.setUpFileStatus(ConstValues.FAIL));
        }
        long countNumberCer =  entityExcelList.stream().map(BatchAcceptTemplateExcel :: getCertificateNo).distinct().count() ;
        long countNumberNum =  entityExcelList.stream().map(BatchAcceptTemplateExcel :: getCertificateNo).distinct().count() ;

        if (countNumberCer != entityExcelList.size() || countNumberNum!=entityExcelList.size()) {
            ahcsBatchEntity.setOperateShow("证件号或序号存在重复");
            ahcsBatchEntity.setUpFileCode(ErrorCode.FILE_HAVE_PROBLEM) ;
            ahcsBatchEntity.setUpFileCodeDescribe("证件号或序号存在重复") ;
            return ResponseResult.success(ahcsBatchEntity.setUpFileStatus(ConstValues.FAIL));
        }

        Map<String,List<AhcsPolicyDomainDTO>> listMap = new HashMap<>();
        LogUtil.info("批量导入入参实体:{}",entityExcelList);

        Map<String, Object> combineMap = null;
        try {
            combineMap = ahcsBatchReportService.fetchResult(batchType, response, reportBatchNo, userId, entityExcelList, md5, ahcsBatchEntity,listMap);
        } catch (Exception e) {
            try {
                md5 = new BaseExcelUtil().buildModelSuitIobs(entityExcelList, ExcelHelper.batchReportTitle, ExcelHelper.batchReportField, new HashMap<>(), md5, FileUploadConstants.LOCAL_PATH, iobs);
                ahcsBatchEntity.setUpFileStatus(ConstValues.FAIL) ;
                ahcsBatchEntity.setFileId(md5);
                return  ResponseResult.success(ahcsBatchEntity);
            } catch (FileNotFoundException e1) {
                LogUtil.info("combineMap-error:{}",e.getMessage());
                e1.printStackTrace();
            }
            LogUtil.info("combineMap-error:{}",e.getMessage());
            e.printStackTrace();
        }

        // 模板校验不通过  上面部分内容异常不能捕捉
        for(BatchAcceptTemplateExcel entity :entityExcelList){
            if(!StringUtils.isEmpty(entity.getReturnMsg()) && !entity.getReturnMsg().contains(FileUploadConstants.REMOVE_POLICYS)){
                md5 = new BaseExcelUtil().buildModelSuitIobs(entityExcelList, ExcelHelper.batchReportTitle, ExcelHelper.batchReportField, new HashMap<>(), md5, FileUploadConstants.LOCAL_PATH, iobs);
                ahcsBatchEntity.setUpFileStatus(ConstValues.FAIL) ;
                ahcsBatchEntity.setFileId(md5);
                throw new GlobalBusinessException(GlobalResultStatus.RESULT_ERROR.format("模板校验不通过"));
            }
        }

        if(MapUtils.isEmpty(combineMap)){
            ahcsBatchEntity.setOperateShow("取号器或保单信息有误");
            ahcsBatchEntity.setUpFileStatus(ConstValues.FAIL) ;
            ahcsBatchEntity.setUpFileCodeDescribe("取号器或保单信息有误") ;
            uploadAndShowBack(combineMap,entityExcelList,md5,ahcsBatchEntity);
            return  ResponseResult.success(ahcsBatchEntity);
        }
        // 回显剔除保单并上传服务器
        ahcsBatchEntity.setUpFileStatus(ConstValues.SUCCESS) ;
        uploadAndShowBack(combineMap,entityExcelList,md5,ahcsBatchEntity);
        ahcsBatchEntity.setReportBatchNo(reportBatchNo).setUpFileStatus(ConstValues.SUCCESS) ;
        //入库
        ahcsBatchReportService.saveResult(batchType,  response,  reportBatchNo,  userId,
                entityExcelList,  md5,  ahcsBatchEntity,combineMap, (Map<String, String>) combineMap.get("reportNos"));
        return  ResponseResult.success(ahcsBatchEntity);

    }


    private void uploadAndShowBack(Map<String, Object> combineMap, List<BatchAcceptTemplateExcel> entityExcelList, String md5, AhcsBatchEntity ahcsBatchEntity) {
        //没有异常操作返回报案号列表回显并上传至服务器
        Map<String, String> reportNos = (Map<String, String>) combineMap.get("reportNos");
        for (String  key :reportNos.keySet()){
            for (BatchAcceptTemplateExcel e:entityExcelList ){
                if (key.equals(e.getNum()) && ( (e.getReturnMsg().contains("被剔除的保单")) || StringUtils.isEmpty(e.getReturnMsg()))){
                    e.setReturnMsg(e.getReturnMsg()+"报案号:"+reportNos.get(key));
                }
            }
        }
        try {
            md5 = new BaseExcelUtil().buildModelSuitIobs(entityExcelList, ExcelHelper.batchReportTitle, ExcelHelper.batchReportField, new HashMap<>(), md5, FileUploadConstants.LOCAL_PATH, iobs);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        ahcsBatchEntity.setFileId(md5);
    }


    @ApiOperation(value = "下载文件")
    @RequestMapping(value = "/downFile", method = RequestMethod.GET)
    public ResponseResult downFileReport(@RequestParam("fileId")  String batchReportFile, HttpServletResponse response) {
        String iobsFileDownloadUrl ="" ;
        String fileName = batchReportFile + ".xlsx";
        if (iobs){
              iobsFileDownloadUrl = iobsFileUploadService.getPerpetualDownloadUrlForBatchUpload(batchReportFile, fileName);
              LogUtil.info("获取下载链接地址:"+iobsFileDownloadUrl);
            if (iobsFileDownloadUrl.startsWith("http://")){
                iobsFileDownloadUrl=iobsFileDownloadUrl.replaceAll("http://","https://");
            }
            return  ResponseResult.success(iobsFileDownloadUrl) ;
        }else {
             // 本地服务下载
             FileUtil.downLoadFile(response,FileUploadConstants.LOCAL_PATH, fileName);
             return  ResponseResult.success(iobsFileDownloadUrl) ;

        }

    }


    @ApiOperation(value = "删除文件")
    @RequestMapping(value = "/deleteFile", method = RequestMethod.POST)
    public ResponseResult deleteFileReport(@RequestParam("fileId") String fileId) throws IOException {
        if (iobs){
            iobsFileUploadService.deleteByFileId(fileId) ;
        }else {
           FileUtil.deleteFile(FileUploadConstants.LOCAL_PATH,fileId+".xlsx");
        }
        return ResponseResult.success( ) ;
    }





}
