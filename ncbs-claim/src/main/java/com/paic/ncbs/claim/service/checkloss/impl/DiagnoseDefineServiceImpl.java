package com.paic.ncbs.claim.service.checkloss.impl;

import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.dao.mapper.checkloss.DiagnoseDefineMapper;
import com.paic.ncbs.claim.model.dto.checkloss.DiagnoseDefineDTO;
import com.paic.ncbs.claim.model.vo.checkloss.DiagnoseDefineVO;
import com.paic.ncbs.claim.service.checkloss.DiagnoseDefineService;
import com.paic.ncbs.claim.service.checkloss.HospitalInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


@Service("diagnoseDefineService")
public class DiagnoseDefineServiceImpl implements DiagnoseDefineService {
	
	@Autowired
	private DiagnoseDefineMapper diagnoseDefineDao;

	@Autowired
	private HospitalInfoService hospitalInfoService;

	@Override
	public List<DiagnoseDefineVO> getDiagnoseDefines(String searchStr,String reportNo) {
		List<DiagnoseDefineVO> diagnoseDefineVOs = new ArrayList<DiagnoseDefineVO>();
		//根据报案号查询所属机构，判断医院编码类型 1-全国，2-北京，3-上海，监管上报需要
		String orgType = hospitalInfoService.getOrgTypeByReportNo(reportNo, NcbsConstant.DIAGNOSE);

		List<DiagnoseDefineDTO> diagnoseDefineDTOs = diagnoseDefineDao.getDiagnoseDefines(searchStr,orgType);
		
		for(DiagnoseDefineDTO diagnoseDefineDTO:diagnoseDefineDTOs){
			DiagnoseDefineVO diagnoseDefineVO = new DiagnoseDefineVO();
			BeanUtils.copyProperties(diagnoseDefineDTO, diagnoseDefineVO);
			diagnoseDefineVOs.add(diagnoseDefineVO);
		}
		
		return diagnoseDefineVOs;
	}

	@Override
	public List<DiagnoseDefineVO> getDiagnoseDefineList(DiagnoseDefineDTO diagnoseDefineDTO) {
		List<DiagnoseDefineVO> diagnoseDefineVOs = new ArrayList<DiagnoseDefineVO>();
		List<DiagnoseDefineDTO> diagnoseDefineDTOs = diagnoseDefineDao.getDiagnoseDefineList(diagnoseDefineDTO);
		for(DiagnoseDefineDTO diagnoseDefineDTO_tmp:diagnoseDefineDTOs){
			DiagnoseDefineVO diagnoseDefineVO = new DiagnoseDefineVO();
			BeanUtils.copyProperties(diagnoseDefineDTO_tmp, diagnoseDefineVO);
			diagnoseDefineVOs.add(diagnoseDefineVO);
		}
		return diagnoseDefineVOs;
	}

	/**
	 * 查询国际ICD编码以S开头的数据
	 * @param searchStr
	 * @param reportNo
	 * @return
	 */
	@Override
	public List<DiagnoseDefineVO> getDiagnoseDefineVos(String searchStr, String reportNo) {
		List<DiagnoseDefineVO> diagnoseDefineVOs = new ArrayList<DiagnoseDefineVO>();
		//根据报案号查询所属机构，判断医院编码类型 1-全国，2-北京，3-上海，监管上报需要
		String orgType = hospitalInfoService.getOrgTypeByReportNo(reportNo, NcbsConstant.DIAGNOSE);
		List<DiagnoseDefineDTO> diagnoseDefineDTOs = diagnoseDefineDao.getDiagnoseDefineDTOs(searchStr,orgType,"S");
		for(DiagnoseDefineDTO diagnoseDefineDTO:diagnoseDefineDTOs){
			DiagnoseDefineVO diagnoseDefineVO = new DiagnoseDefineVO();
			BeanUtils.copyProperties(diagnoseDefineDTO, diagnoseDefineVO);
			diagnoseDefineVOs.add(diagnoseDefineVO);
		}
		return diagnoseDefineVOs;
	}
}
