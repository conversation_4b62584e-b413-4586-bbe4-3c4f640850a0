package com.paic.ncbs.claim.service.schedule;

import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

public interface OpsJobService {

    /**
     * 处理退货运费险结案之后的操作（送收付，送再保）。
     * @param reportNos 补传报案号集合
     * @param isManual 是否人工补传
     * @return
     */
    Map<String,List<String>> saveCloseCaseAfter(List<String> reportNos,String isManual);


    /**
     * 批量修改支付退回任务
     * @param reportNos 报案号集合
     * @return
     */
    List<String> batchModifyPayBack(List<String> reportNos);


    /**
     * 批量补推自动核赔后继逻辑
     * @param reportNos
     * @return
     */
    List<String> batchAutoVerifyAfter(List<String> reportNos);


    /**
     * 批量补推理赔费用信息到收付Q13
     * @param reportNos
     * @return
     */
    void batchSendClaimVatInfo(List<String> reportNos);

    /**
     * 退运险数据修改
     * @param reportNos
     * @param isManual
     * @return
     */
    void batchUpdatePolicyPay(@RequestParam(value = "reportNos",required = false) List<String> reportNos,
                         @RequestParam(value = "isManual") String isManual);

    /**
     * 批量补推理赔费用信息到收付Q13(包含预赔费用)
     * @param paySerialNoS
     * @return
     */
    void batchSendClaimVatInfoByPaySerialNo(List<String> paySerialNoS);

}
