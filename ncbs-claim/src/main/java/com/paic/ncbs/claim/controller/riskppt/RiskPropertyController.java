package com.paic.ncbs.claim.controller.riskppt;

import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.PlyRiskGroupQueryDTO;
import com.paic.ncbs.claim.model.dto.riskppt.PolicyGroupDTO;
import com.paic.ncbs.claim.service.common.RedisService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyPayService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "案件标的明细API")
@RestController
@RequestMapping("/app/riskProperty")
public class RiskPropertyController {

	@Autowired
	private RiskPropertyService riskPropertyService;
	@Autowired
	private RiskPropertyPayService riskPropertyPayService;
	@Autowired
	private RedisService redisService;

	/**
	 * 查询保单标的列表
	 * @return
	 */
	@PostMapping("/getPlyRiskProperty")
	public ResponseResult<List<PolicyGroupDTO>> getPlyRiskProperty(@RequestBody PlyRiskGroupQueryDTO queryDTO){
		checkPlyRiskPropertyParam(queryDTO);
		return ResponseResult.success(riskPropertyService.getPlyRiskProperty(queryDTO));
	}

	private void checkPlyRiskPropertyParam(PlyRiskGroupQueryDTO queryDTO){
		if(ListUtils.isEmptyList(queryDTO.getPolicyNoList())
				&& StringUtils.isEmptyStr(queryDTO.getCertificateNo())
				&& StringUtils.isEmptyStr(queryDTO.getInsuredName())
				&& StringUtils.isEmptyStr(queryDTO.getSubCertificateNo())
				&& StringUtils.isEmptyStr(queryDTO.getSubName())){
			throw new GlobalBusinessException("保单号、被保人证件号、标的证件号至少输入一项");
		}
	}

	/**
	 * 查询案件标的列表
	 * @return
	 */
	@PostMapping("/getCaseRiskProperty")
	public ResponseResult<List<PolicyGroupDTO>> getCaseRiskProperty(@RequestBody CaseRiskPropertyDTO caseDTO){
		checkCaseRiskPropertyParam(caseDTO);
		return ResponseResult.success(riskPropertyService.getCaseRiskProperty(caseDTO));
	}

	private void checkCaseRiskPropertyParam(CaseRiskPropertyDTO caseDTO){
		if(caseDTO== null || StringUtils.isEmptyStr(caseDTO.getReportNo())){
			throw new GlobalBusinessException("报案号不能为空");
		}

		if(caseDTO.getCaseTimes() == null){
			throw new GlobalBusinessException("赔付次数不能为空");
		}

		if(StringUtils.isEmptyStr(caseDTO.getTaskId())){
			throw new GlobalBusinessException("环节号不能为空");
		}
	}

	/**
	 * 根据案件号、标的id查询立案险种
	 * @param riskPropertyDTO
	 * @return
	 */
	@PostMapping("/getEstPolicyByRiskPropertyId")
	public ResponseResult<List<EstimatePolicyDTO>> getEstPolicyByRiskPropertyId(@RequestBody CaseRiskPropertyDTO riskPropertyDTO){
		if(riskPropertyDTO == null || StringUtils.isEmptyStr(riskPropertyDTO.getReportNo())){
			throw new GlobalBusinessException("报案号不能为空");
		}
		if(riskPropertyDTO.getCaseTimes() == null){
			throw new GlobalBusinessException("赔付次数不能为空");
		}
		if(StringUtils.isEmptyStr(riskPropertyDTO.getIdPlyRiskProperty())){
			throw new GlobalBusinessException("标的id不能为空");
		}

		return ResponseResult.success(riskPropertyService.getEstPolicyByRiskPropertyId(riskPropertyDTO));
	}

	@GetMapping("/testEndCase")
	public ResponseResult testEndCase(WholeCaseBaseDTO wholeCaseBaseDTO){
//		riskPropertyPayService.saveRiskPropertyPay(wholeCaseBaseDTO);
		System.out.println(Thread.currentThread().getName());
		String lockValue = UuidUtil.getUUID();
		String lockName = String.format(BaseConstant.POLICY_PAY_INIT_LOCK,"906453");
		boolean lock = redisService.tryLock(lockName,lockValue);
		System.out.println("lock_value="+lockValue);
		System.out.println("lock="+lock);
		if(!lock){
			throw new GlobalBusinessException("锁定中。。。");
		}
		try {
			Thread.sleep(10000);
		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		}
//		redisService.removeLock(lockName,lockValue);
		return ResponseResult.success();
	}

}

