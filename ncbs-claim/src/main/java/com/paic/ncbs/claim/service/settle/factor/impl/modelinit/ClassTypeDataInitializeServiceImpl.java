package com.paic.ncbs.claim.service.settle.factor.impl.modelinit;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.model.dto.settle.factor.ClaimCaseDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.modelinit.ModelDataInitializeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 案件类别
 */
@Slf4j
@Order(4)
@Service
public class ClassTypeDataInitializeServiceImpl implements ModelDataInitializeService {
    @Autowired
    private CaseClassMapper caseClassDao;
    @Override
    public ClaimCaseDTO initialize(ClaimCaseDTO claimCaseDTO) {
        List<String> caseClassList = caseClassDao.getCaseClassList(claimCaseDTO.getReportNo(), claimCaseDTO.getCaseTimes(), BpmConstants.CHECK_DUTY);
        claimCaseDTO.setCaseClassList(caseClassList);
        return claimCaseDTO;
    }
}
