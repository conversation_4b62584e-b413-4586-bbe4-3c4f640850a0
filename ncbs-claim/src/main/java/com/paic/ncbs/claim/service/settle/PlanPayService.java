package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;

import java.util.List;

public interface PlanPayService {

    List<PlanPayDTO> getByPolicy(PolicyPayDTO policy);

    void reQueryDutyDetail(PolicyPayDTO policy);

    void insertPlanPayList(List<PlanPayDTO> planPayArr);

    void deleteByBatchId(String idAhcsBatch, String claimType);

    void batchModify(List<PlanPayDTO> planPayArr);

}
