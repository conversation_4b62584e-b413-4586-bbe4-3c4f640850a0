package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentExEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentExMapper;
import com.paic.ncbs.claim.service.report.ReportAccidentExService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportAccientExServiceImpl extends BaseServiceImpl<ReportAccidentExEntity> implements ReportAccidentExService {

    @Autowired
    private ReportAccidentExMapper reportAccidentExMapper;

    @Override
    public BaseDao<ReportAccidentExEntity> getDao() {
        return reportAccidentExMapper;
    }

    @Override
    public ReportAccidentExEntity getReportAccidentEx(String reportNo) {
        return reportAccidentExMapper.getReportAccidentEx(reportNo);
    }

}
