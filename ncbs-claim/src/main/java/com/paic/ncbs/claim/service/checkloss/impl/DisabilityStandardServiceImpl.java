package com.paic.ncbs.claim.service.checkloss.impl;


import com.paic.ncbs.claim.dao.mapper.checkloss.DisabilityStandardMapper;
import com.paic.ncbs.claim.model.vo.checkloss.DisabilityStandardVO;
import com.paic.ncbs.claim.service.checkloss.DisabilityStandardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("disabilityStandardService")
public class DisabilityStandardServiceImpl implements DisabilityStandardService {
	
	@Autowired
	private DisabilityStandardMapper disabilityStandardMapper;

	 
	@Override
	public List<DisabilityStandardVO> getDisabilityStandardList() {
		return disabilityStandardMapper.selectDisabilityStandards();
	}
	


}
