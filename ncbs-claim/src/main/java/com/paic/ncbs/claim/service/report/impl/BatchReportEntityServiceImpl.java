package com.paic.ncbs.claim.service.report.impl;


import com.paic.ncbs.claim.model.dto.report.BatchReportEntity;
import com.paic.ncbs.claim.dao.mapper.report.BatchReportEntityMapper;
import com.paic.ncbs.claim.service.report.BatchReportEntityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BatchReportEntityServiceImpl implements BatchReportEntityService {

	@Autowired
	private BatchReportEntityMapper batchReportEntityMapper;

	@Override
	public void insertSelective(BatchReportEntity record) {
		batchReportEntityMapper.insertSelective(record);
	}


}