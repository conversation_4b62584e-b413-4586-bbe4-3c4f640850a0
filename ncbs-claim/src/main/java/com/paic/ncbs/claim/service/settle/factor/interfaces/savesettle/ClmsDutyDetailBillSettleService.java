package com.paic.ncbs.claim.service.settle.factor.interfaces.savesettle;


import com.paic.ncbs.claim.model.dto.settle.DutyDetailBillSettleRequest;
import com.paic.ncbs.claim.model.dto.settle.DutyDetailSettleRequest;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;

import java.util.List;

public interface ClmsDutyDetailBillSettleService {
    void saveBatch(List<BIllSettleResultDTO> list);

    void updateClmsDutyDetailBillSettle(ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO);

    List<ClmsDutyDetailBillSettleDTO> getAllInfoByReportNo(PolicyPayDTO policy);

    void updateListById(List<ClmsDutyDetailBillSettleDTO> detailBillSettleList);

    /**
     * 手动更新责任明细支付信息
     * @param request
     */
    List<ClmsDutyDetailBillSettleDTO> autoAllocation(DutyDetailSettleRequest request);

    /**
     * 核赔提交校验年免赔额
     * @param reportNo
     * @param caseTimes
     */
    void checkRemimount(String reportNo,Integer caseTimes);

}
