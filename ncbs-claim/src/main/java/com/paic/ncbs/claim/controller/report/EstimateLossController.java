package com.paic.ncbs.claim.controller.report;

import com.paic.ncbs.claim.model.dto.report.EstimateLossDTO;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.service.report.EstimateLossService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "报案估损")
@RestController
@RequestMapping("/report/estimateLossAction")
public class EstimateLossController {

    @Autowired
    private EstimateLossService estimateLossService;

    @ApiOperation("获取报案估损配置列表")
    @GetMapping("/getEstimatLossConfig")
    public ResponseResult<List<EstimateLossDTO>> getEstimatLossConfig(){

        return ResponseResult.success(estimateLossService.getAllEstimatLossConfig());
    }

    @ApiOperation("提交报案估损配置列表")
    @PostMapping("/setEstimatLossConfig")
    public ResponseResult setEstimatLossConfig(@RequestBody List<EstimateLossDTO> estimateLossList){
        estimateLossService.setAllEstimatLossConfig(estimateLossList);
        return ResponseResult.success();
    }
}
