package com.paic.ncbs.claim.replevy.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，表clms_replevy_detail的VO对象<br/>
 * 对应表名：clms_replevy_detail,备注：追偿明细表
 *
 */
@Data
public class ClmsReplevyDetailVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	private String id;
	/** 对应字段：replevy_id,备注：追偿主表id */
	private String replevyId;
	/** 对应字段：report_no,备注：报案号 */
	private String reportNo;
	/** 对应字段：replevy_no,备注：追偿案件号 */
	private String replevyNo;
	/** 对应字段：replevy_times,备注：追偿次数 */
	private Integer replevyTimes;
	/** 对应字段：case_times,备注：赔付次数 */
	private Integer caseTimes;
	/** 对应字段：clause_code,备注：条款 */
	private String clauseCode;
	/** 对应字段：kind_code,备注：险别 */
	private String kindCode;
	/** 对应字段：replevied_name,备注：被追偿方名称 */
	private String repleviedName;
	/** 对应字段：replevied_type,备注：被追偿方类型 */
	private String repleviedType;
	/** 对应字段：replevied_certi_type,备注：被追偿方证件类型 */
	private String repleviedCertiType;
	/** 对应字段：replevied_certi_code,备注：被追偿方证件号码 */
	private String repleviedCertiCode;
	/**
	 * 被追偿方客户号
	 */
	private String customerNo;
	/** 对应字段：replevied_country_code,备注：被追偿方国别代码 */
	private String repleviedCountryCode;
	/** 对应字段：replevied_location,备注：追偿地点 */
	private String repleviedLocation;
	/** 对应字段：replevy_sum,备注：应追金额 */
	private BigDecimal replevySum;
	/** 对应字段：replevied_mobile,备注：被追偿人手机 */
	private String repleviedMobile;
	/** 对应字段：replevied_phone,备注：被追偿人电话 */
	private String repleviedPhone;
	/** 对应字段：replevied_address,备注：被追偿人地址 */
	private String repleviedAddress;
	/** 对应字段：replevied_post_code,备注：被追偿人邮编 */
	private String repleviedPostCode;
	/** 对应字段：replevied_tax,备注：被追偿人传真 */
	private String repleviedTax;
	/** 对应字段：replevied_email,备注：被追偿人邮箱 */
	private String repleviedEmail;
	/** 对应字段：replevy_type,备注：追偿类型 */
	private String replevyType;
	/** 对应字段：replevy_agency,备注：追偿机构 */
	private String replevyAgency;
	/** 对应字段：replevy_person,备注：追偿人员 */
	private String replevyPerson;
	/** 对应字段：replevy_person_tel,备注：追偿人员电话 */
	private String replevyPersonTel;
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	/** 对应字段：replevy_date,备注：追偿日期 */
	private Date replevyDate;
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	/** 对应字段：replevy_handle_date,备注：追回日期 */
	private Date replevyHandleDate;
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	/** 对应字段：transfer_date,备注：权益转让日期 */
	private Date transferDate;
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	/** 对应字段：replevye_ffect_date,备注：诉讼时效日期 */
	private Date replevyeFfectDate;
	/** 对应字段：replevy_way,备注：追偿途径 */
	private String replevyWay;
	/** 对应字段：replevy_reason,备注：追偿原因 */
	private String replevyReason;
	/** 对应字段：real_replevy,备注：追偿收入 */
	private BigDecimal realReplevy;
	/** 对应字段：replevy_fee,备注：追偿费用 */
	private BigDecimal replevyFee;
	/** 对应字段：status,备注：注销标志 */
	private String status;
	/** 对应字段：approve_flag,备注：高级审核状态 */
	private String approveFlag;
	/** 对应字段：valid_flag,备注：有效标志 */
	private String validFlag;
	/** 对应字段：flag,备注：标志字段 */
	private String flag;
	/** 对应字段：currency,备注：币别 */
	private String currency;
	/** 对应字段：replevy_progress,备注：追偿进度 */
	private String replevyProgress;
	/** 对应字段：replevied_area,备注：追偿地区 */
	private String repleviedArea;
	/** 对应字段：replevied_area_code,备注：追偿地区代码 */
	private String repleviedAreaCode;
	/** 对应字段：replevy_approve_flag,备注：追偿高级审核状态 */
	private String replevyApproveFlag;
	/** 对应字段：approve_person,备注：高级审核人员 */
	private String approvePerson;
	/** 对应字段：serial_no,备注：序号 */
	private Integer serialNo;
	/** 对应字段：approve_date,备注：高级审核日期 */
	private Date approveDate;
	/** 对应字段：created_by,备注：创建人 */
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	private Date sysCtime;
	/** 对应字段：updated_by,备注：修改人员 */
	private String updatedBy;
	/** 对应字段：sys_utime,备注：修改时间 */
	private Date sysUtime;

}
