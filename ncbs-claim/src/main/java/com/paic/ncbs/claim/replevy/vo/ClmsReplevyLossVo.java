package com.paic.ncbs.claim.replevy.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，表clms_replevy_loss的VO对象<br/>
 * 对应表名：clms_replevy_loss,备注：追偿损失表
 *
 */
@Data
public class ClmsReplevyLossVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	private String id;
	/** 对应字段：replevy_detail_id,备注：追偿明细表id */
	private String replevyDetailId;
	/** 对应字段：report_no,备注：报案号 */
	private String reportNo;
	/** 对应字段：replevy_no,备注：追偿案件号 */
	private String replevyNo;
	/** 对应字段：replevy_times,备注：追偿次数 */
	private Integer replevyTimes;
	/** 对应字段：case_times,备注：赔付次数 */
	private Integer caseTimes;
	/** 对应字段：clause_code,备注：条款 */
	private String clauseCode;
	/** 对应字段：plan_code,备注：险种 */
	private String planCode;
	/** 对应字段：plan_name,备注：险种名称 */
	private String planName;
	/** 对应字段：duty_code,备注：责任代码 */
	private String dutyCode;
	/** 对应字段：duty_name,备注：责任名称 */
	private String dutyName;
	/** 对应字段：duty_detail_code,备注：责任明细代码 */
	private String dutyDetailCode;
	/** 对应字段：replevied_type,备注：追回类型 */
	private String repleviedType;
	/** 对应字段：replevied_loss,备注：追回物品 */
	private String repleviedLoss;
	/** 对应字段：price,备注：物品的单价 */
	private BigDecimal price;
	/** 对应字段：unit,备注：物品的单位 */
	private String unit;
	/** 对应字段：currency,备注：币别 */
	private String currency;
	/** 对应字段：replevied_money,备注：实际追回金额/变现金额 */
	private BigDecimal repleviedMoney;
	/** 对应字段：approve_flag,备注：高级审核状态 */
	private String approveFlag;
	/** 对应字段：valid_flag,备注：有效标志 */
	private String validFlag;
	/** 对应字段：flag,备注：标志字段 */
	private String flag;
	/** 对应字段：serial_no,备注：序号 */
	private Integer serialNo;
	/** 对应字段：created_by,备注：创建人 */
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	private Date sysCtime;
	/** 对应字段：updated_by,备注：修改人员 */
	private String updatedBy;
	/** 对应字段：sys_utime,备注：修改时间 */
	private Date sysUtime;

}
