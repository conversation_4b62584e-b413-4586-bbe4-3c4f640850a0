package com.paic.ncbs.claim.controller.schedule;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.service.schedule.JobService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.paic.ncbs.claim.common.util.LogUtil;

@RestController
@RequestMapping("/public/schedule")
@Api(tags = {"任务控制器"})
public class JobController {

    @Autowired
    private JobService jobService;

    /**
     * 补录材料任务通知
     * @return
     */
    @GetMapping("/supplementsMaterial/smsNotification")
    public ResponseResult saveCloseCaseAfter() {
        jobService.supplementsMaterialTaskNotification();
        return ResponseResult.success();
    }

    /**
     * 合并支付
     * @return
     */
    @GetMapping("/pay/mergePayment")
    public ResponseResult mergePayment() {
        LogUtil.info("定时任务合并支付开始执行。。。");
        jobService.mergePayment(null);
        return ResponseResult.success();
    }

}
