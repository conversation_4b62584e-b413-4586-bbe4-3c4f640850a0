package com.paic.ncbs.claim.service.settle.factor.interfaces.bill;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EveryDayBillInfoDTO;

/**
 * 责任发票理算要素接口
 */
public interface BIllSettleBuildService {

    public BIllSettleResultDTO dealBIllSettleResultDTO(EveryDayBillInfoDTO edto, DutyDetailPayDTO detailPayDTO, BIllSettleResultDTO bIllSettleResultDTO);
}
