package com.paic.ncbs.claim.model.dto.print;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 打印模板定义表
 */
@Data
public class PrintTemplateDTO {
    /**
     * 打印模板定义表主键
     */
    private Long id;

    /**
     * 调查信息表主键
     */
    private String idAhcsInvestigate;

    /**
     * 委托信息表主键
     */
    private String idEntrust;
    /**
     * printFlag 1-提调 2-委托
     */
    private String printFlag;

    /**
     * 模板编码
     */
    private String printTemplateCode;

    /**
     * 模板名称
     */
    private String printTemplateName;

    /**
     * 打印单证类型，参考acss_parameter的collection_code为PRINT_TEMPLATE_TYPE
     */
    private String printTemplateType;

    /**
     * 打印模式（0-ireport画模版；1-FreeMarker+html；）
     */
    private Integer printMode;

    /**
     * 显示序号
     */
    private Integer displayNo;

    /**
     * 是否有效（0-无效；1-有效）
     */
    private Integer printStatus;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 修改时间
     */
    private Date sysUtime;

    /**
     * 签约关键字参数（打印模式为1-FreeMarker+html，此字段必须有值）
     */
    private String keyword;

    /**
     * 模板数据
     */
    private String printTemplateValue;

    /**
     * 模板预览入参
     */
    private String printParameterJson;


}
