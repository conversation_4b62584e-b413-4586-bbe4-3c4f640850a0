package com.paic.ncbs.claim.service.settle.factor.impl.formula;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.formula.FormulaService;
import org.springframework.stereotype.Service;

/**
 * 责任
 */
@Service
public class DutyFormulaServiceImpl implements FormulaService {
    @Override
    public String getFormula(DutyDetailPayDTO detail) {
        return null;
    }
}
