package com.paic.ncbs.claim.service.settle.factor.impl.strategy.template;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.service.settle.factor.impl.common.ClaimApplicationAwareUtil;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.template.AbstractTemplateService;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.template.ClmsFreemarkTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 医疗类型理算依据模板生成
 */
@Slf4j
@Service
public class ClmsTemplateServiceImpl implements ClmsFreemarkTemplateService {
    @Autowired
    private ClaimApplicationAwareUtil claimApplicationAwareUtil;
    @Override
    public String getSettleReason(DutyDetailPayDTO detailPayDTO) {
        String serviceName= Constants.TEMPLATE_IMPL_MAP.get(detailPayDTO.getDutyDetailType());
        ApplicationContext context = claimApplicationAwareUtil.getApplicationContext();
        AbstractTemplateService service = (AbstractTemplateService) context.getBean(serviceName);

        String reason =service.replaceSettleReason(detailPayDTO);
        return reason;
    }
}
