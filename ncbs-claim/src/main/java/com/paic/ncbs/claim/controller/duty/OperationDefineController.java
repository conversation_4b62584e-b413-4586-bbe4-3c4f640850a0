package com.paic.ncbs.claim.controller.duty;


import com.paic.ncbs.claim.model.dto.duty.OperationDefineFirstDTO;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.duty.TherapyOperationVO;
import com.paic.ncbs.claim.service.duty.OperationDefineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Api(tags = "津贴信息-手术等级")
@Controller
@RequestMapping("/duty/app/operationDefineAction")
public class OperationDefineController extends BaseController {
	
	@Autowired
	OperationDefineService operationDefineService;

	@ApiOperation("查询所有科室信息")
	@RequestMapping(value = "/getOperationDefine", method = RequestMethod.GET)
	@ResponseBody
	public ResponseResult<List<OperationDefineFirstDTO>> getOperationDefine() {
		LogUtil.audit("#津贴信息-手术等级-查询所有科室信息#");
		return ResponseResult.success(operationDefineService.getOperationDefine());
	}

	@ApiOperation("模糊查询治疗方式中的手术")
	@RequestMapping(value = "/getTherapyOperation", method = RequestMethod.POST)
	@ResponseBody
	public ResponseResult<List<TherapyOperationVO>> getTherapyOperation(@RequestBody TherapyOperationVO therapyOperation)throws GlobalBusinessException {
		LogUtil.audit("#模糊查询治疗方式中的手术#,operationName=%s",therapyOperation.getOperationName());
		LogUtil.audit("#报案号#,reportNo=%s",therapyOperation.getReportNo());
		List<TherapyOperationVO> therapyOperationVOList=operationDefineService.getTherapyOperation(therapyOperation);
		LogUtil.audit("#模糊查询治疗方式中的手术#,operationName=%s,size=%s",therapyOperation.getOperationName(),therapyOperationVOList.size());
		return ResponseResult.success(therapyOperationVOList);
	}

}
