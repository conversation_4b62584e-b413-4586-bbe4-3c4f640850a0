package com.paic.ncbs.claim.controller.ahcs;

import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.enums.EstimateTypeEnum;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateChangeMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord;
import com.paic.ncbs.claim.service.estimate.ClmsEstimateRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "未决信息")
@RestController
@RequestMapping("/ahcs/do/app/clmsEstimateRecordAction")
public class ClmsEstimateRecordController extends BaseController {

	@Autowired
	private ClmsEstimateRecordService clmsEstimateRecordService;
	@Autowired
	private EstimateChangeMapper estimateChangeMapper;


	@ApiOperation("历史案件页面查询已立案信息")
	@GetMapping(value = "/getRecordByReportNoAndType")
	public ResponseResult<List<ClmsEstimateRecord>> getRecordByReportNoAndType(@RequestParam("reportNo") String reportNo,
																			   @RequestParam("caseTimes") String caseTimes,
																			   @RequestParam("estimateType") String estimateType) throws GlobalBusinessException {
		return ResponseResult.success(clmsEstimateRecordService.getRecordByReportNoAndType(reportNo,caseTimes,estimateType));
	}

	@ApiOperation("查询案件所有未决记录")
	@GetMapping(value = "/getAllEstimateRecord")
	public ResponseResult<List<ClmsEstimateRecord>> getAllEstimateRecord(@RequestParam("reportNo") String reportNo,
																		 @RequestParam("caseTimes") String caseTimes){
		List<ClmsEstimateRecord> recordList = clmsEstimateRecordService.getRecordByReportNoAndType(reportNo,caseTimes,null);
//		BigDecimal changeAmt = estimateChangeMapper.getEstimateChangeAmount(reportNo,Integer.valueOf(caseTimes));
		if(ListUtils.isNotEmpty(recordList)){
			for (ClmsEstimateRecord record : recordList) {
				if(StringUtils.isNotEmpty(record.getRecordUserName())){
					record.setRecordUserId(record.getRecordUserId()+"-"+record.getRecordUserName());
				}
				if(EstimateTypeEnum.REPORT_PENDING.getType().equals(record.getEstimateType())){
					record.setRecordUserId(ConstValues.SYSTEM);
				}
			}
		}
		return ResponseResult.success(recordList);
	}


}
