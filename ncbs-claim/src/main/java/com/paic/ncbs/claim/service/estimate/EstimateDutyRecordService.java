package com.paic.ncbs.claim.service.estimate;

import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;

import java.math.BigDecimal;
import java.util.List;

public interface EstimateDutyRecordService {

    void addEstimateDutyRecordList(List<EstimateDutyRecordDTO> paramList);

    public void addRecordList(List<EstimateDutyRecordDTO> paramList);

    public List<EstimateDutyRecordDTO> getRecordsOfRegistCase(String caseNo, Integer caseTimes, String estimateType);

    void updateEffectiveByCaseNos(List<String> caseNoList, Integer caseTimes, String updatedBy);

    BigDecimal getRegisterAmountByApplyId(String applyId);

}