package com.paic.ncbs.claim.service.checkloss.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ChecklossConst;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.mapper.settle.FlightDelayMapper;
import com.paic.ncbs.claim.model.dto.checkloss.FlightAllInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.FlightDelayDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyFilghtInfoVO;
import com.paic.ncbs.claim.model.vo.duty.FlightDelayVO;
import com.paic.ncbs.claim.service.checkloss.FlightDelayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("flightDelayService")
public class FlightDelayServiceImpl implements FlightDelayService {

    @Autowired
    FlightDelayMapper flightDelayMapper;

    @Override
    @Transactional
    public void saveFlighterDelay(FlightDelayDTO flightDelayDTO) {
        String reportNo = flightDelayDTO.getReportNo();
        Integer caseTimes = flightDelayDTO.getCaseTimes();
        String channelProcessId = flightDelayDTO.getIdAhcsChannelProcess();
        // 先查询是否存在航班延误记录
        FlightDelayDTO existFlightDelayDTO = flightDelayMapper.getFlightDelay(reportNo, caseTimes, null, flightDelayDTO.getTaskCode(), channelProcessId);
        // 若存在则先删除
        if (existFlightDelayDTO != null) {
            flightDelayMapper.removeFlightDelay(reportNo, caseTimes, flightDelayDTO.getTaskCode(), channelProcessId);
        }
        if(StringUtils.isEmptyStr(flightDelayDTO.getIdAhcsFlightDelay())){
            flightDelayDTO.setIdAhcsFlightDelay(UuidUtil.getUUID());
        }
        // 添加航班延误记录
        flightDelayMapper.addFlightDelay(flightDelayDTO);
    }

    @Override
    public void removeFlightDelay(String reportNo, Integer caseTimes, String taskCode, String channelProcessId) {
        flightDelayMapper.removeFlightDelay(reportNo, caseTimes, taskCode, channelProcessId);
    }

    @Override
    public FlightDelayDTO getFlighterDelay(String reportNo, Integer caseTimes, String status, String taskCode, String channelProcessId) {
        return flightDelayMapper.getFlightDelay(reportNo, caseTimes, status, taskCode, channelProcessId);
    }

    @Override
    public FlightDelayVO flightDelayQueryInfo(FlightDelayVO flightDelayParam){
        return new FlightDelayVO();

//        // 被保险人信息
//        InsuredPersonDTO insuredPersonDTO = insuredPersonService.getInsuredPersonDTO(flightDelayParam.getReportNo());
//        String insuredName = insuredPersonDTO.getName();
//        String certificateNo = insuredPersonDTO.getCertificateNo();
//        String flightNo = flightDelayParam.getOriginalFlightNo().toUpperCase();
//        String reportNo = flightDelayParam.getReportNo();
//        Integer caseTimes = null == flightDelayParam.getCaseTimes() ? 1 : flightDelayParam.getCaseTimes();
//        // 获取报案信息
//        JSONObject reportInfoData = rapeApiService.requestReportDomainInfo(reportNo);
//        // 事故信息
//        ReportAccidentEx reportAccidentEx = reportInfoData.getObject("reportAccidentEx", ReportAccidentEx.class);
//        // 出发地
//        String departureAirport = reportAccidentEx.getDepaturePalce();
//        // 目的地
//        String destinationAirport = reportAccidentEx.getDestination();
//        // 航班日期
//        String flightDate = parseToFormatString(reportAccidentEx.getOriginalDepartureTime());
//
//        String companyCodeByReportNo = caseProcessService.getCompanyCodeByReportNo(reportNo);
//        String companyId = autoClaimCommService.getCompanyId(companyCodeByReportNo);
//
//        // 查询系统是否存在航延数据,注意: 2020.1.15按王磊要求不需要查询本地直接查询航联接口
//        FlightAllInfoDTO flightDelayQueryDTO = new FlightAllInfoDTO();
//        try {
//            // 查询航联接口,此方法会更新本地数据
//            List<FlightAllInfoDTO> flightDelayQueryDTOs = ptsForwardService.getFlightDelayQueryDTOList(flightNo, flightDate, companyId);
//            if (ListUtils.isNotEmpty(flightDelayQueryDTOs)) {
//                if (flightDelayQueryDTOs.size() == 1) {
//                    BeanUtils.copyProperties(flightDelayQueryDTOs.get(0), flightDelayQueryDTO);
//                } else {
//                    // 从航班数据中获取报案时客户填的航段数据
//                    for (FlightAllInfoDTO delayQueryDTO : flightDelayQueryDTOs) {
//                        if (delayQueryDTO.getOriginAirPortCode().equals(departureAirport) && delayQueryDTO.getDestAirPortCode().equals(destinationAirport)) {
//                            BeanUtils.copyProperties(delayQueryDTO, flightDelayQueryDTO);
//                            break;
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            LogUtil.error("报案号={},查询航联接口flightDelayQueryInfo报错", e, reportNo);
//        }
//
//        // 系统内查询客票数据
//        CustomerTicketDTO customerTicketDTO = new CustomerTicketDTO();
//        String policyNo = "";
//        List<PolicyPayDTO> policyPayList = policyPayService.selectFromPolicyCopy(reportNo, caseTimes);
//        if (ListUtils.isNotEmpty(policyPayList)) {
//            policyNo = policyPayList.get(0).getPolicyNo();
//        }
//        FlightInfoQueryParam flightInfoQueryParamVO = new FlightInfoQueryParam();
//        flightInfoQueryParamVO.setFlightDate(flightDate);
//        flightInfoQueryParamVO.setFlightNo(flightNo);
//        flightInfoQueryParamVO.setInsureNo(policyNo);
//        flightInfoQueryParamVO.setOriginAirPortCode(departureAirport);
//        flightInfoQueryParamVO.setDestAirPortCode(destinationAirport);
//        flightInfoQueryParamVO.setPassengerId(certificateNo);
//        flightInfoQueryParamVO.setPassengerName(insuredName);
//        flightInfoQueryParamVO.setCompanyId(companyId);
//        try {
//            //
//            CustomerTicketResult customerTicketVO = ptsForwardService.getCustomerTicketStatusByFlightInfo(flightInfoQueryParamVO);
//            if (null != customerTicketVO) {
//                BeanUtils.copyProperties(customerTicketVO, customerTicketDTO);
//                // saveCustomerTicketDTO(customerTicketVO, "", insuredPersonDTO);
//            }
//        } catch (Exception e) {
//            LogUtil.error("报案号={},查询客票接口flightDelayQueryInfo报错", e, reportNo);
//        }
//
//        // 返回到页面的数据
//        FlightDelayVO flightDelayResult = new FlightDelayVO();
//        if (null != flightDelayQueryDTO) {
//            flightDelayResult.setOriginalFlightNo(flightDelayQueryDTO.getFlightNo());
//            flightDelayResult.setOriginalDepartTime(flightDelayQueryDTO.getPlanDepTime());
//            flightDelayResult.setOriginalArrivalTime(flightDelayQueryDTO.getPlanArrTime());
//            flightDelayResult.setDepartPlace(flightDelayQueryDTO.getOriginAirPortCode());
//            flightDelayResult.setArrivalPlace(flightDelayQueryDTO.getDestAirPortCode());
//            flightDelayResult.setFlightStatus(flightDelayQueryDTO.getFlightStatus());
//        }
//
//        if (null != customerTicketDTO) {
//            flightDelayResult.setIsBuyTicket(customerTicketDTO.getIsBuyTicket());
//            flightDelayResult.setIsTicketUsed(customerTicketDTO.getIsTicketUsed());
//        }
//        return flightDelayResult;
    }

    /**
     * @Description: Date类型的日期转成String类型
     * @param date
     * @return
     */
    private String parseToFormatString(Date date) {
        String dateStr = null;
        try {
            dateStr = DateUtils.parseToFormatString(date, DateUtils.SIMPLE_DATE_STR);
        } catch (ParseException e) {
            LogUtil.error("日期转回异常={}", e);
        }
        return dateStr;
    }


    @Override
    public void updateAutoFlightDelayStatus(String reportNo, Integer caseTimes, String status, String taskCode) {
        flightDelayMapper.updateAutoFlightDelayStatus(reportNo, caseTimes, status, taskCode);
    }

    @Override
    public void updateFlightDelayTicketInfo(FlightDelayDTO flightDelayDTO) {
        flightDelayMapper.updateFlightDelayTicketInfo(flightDelayDTO);
    }

    @Override
    public DutyFilghtInfoVO getFlightInfoByDuty(FlightDelayVO flightDelayVO) {
        DutyFilghtInfoVO dutyFilghtInfoVO = new DutyFilghtInfoVO();
        String reportNo = flightDelayVO.getReportNo();
        Integer caseTimes = flightDelayVO.getCaseTimes();
        String originalFlightNo = flightDelayVO.getOriginalFlightNo();
        String realFlightNo = flightDelayVO.getRealFlightNo();
        String certificateNo = flightDelayVO.getCertificateNo();
        String clientName = flightDelayVO.getClientName();
        String certificateType = flightDelayVO.getCertificateType();
        // isOriginal = true 查询实际航班
        boolean isOriginal = StringUtils.isEmptyStr(originalFlightNo) ? false : true;
        Date flightDate = isOriginal ? flightDelayVO.getOriginalDepartTime() : flightDelayVO.getRealDepartTime();
        String departPlace = flightDelayVO.getDepartPlace();
        String arrivalPlace = flightDelayVO.getArrivalPlace();
        String flightNo = StringUtils.isNotEmpty(originalFlightNo) ? originalFlightNo : realFlightNo;
        
        LogUtil.audit("#重新获取航班信息#入参#reportNo="+ reportNo +",isOriginal="+ isOriginal +",flightDate="+ flightDate +",flightNo="+ flightNo);
        
        try {
            // 航延表数据,取出发地,目的地数据
            FlightDelayDTO selectFlightDelayDTO = this.getFlighterDelay(reportNo, caseTimes, null, null, null);
            if (selectFlightDelayDTO != null) {
                if (StringUtils.isEmptyStr(departPlace)) {
                    departPlace = StringUtils.isEmptyStr(selectFlightDelayDTO.getDepartPlace()) ? "" : selectFlightDelayDTO.getDepartPlace();
                }
                if (StringUtils.isEmptyStr(arrivalPlace)) {
                    arrivalPlace = StringUtils.isEmptyStr(selectFlightDelayDTO.getArrivalPlace()) ? "" : selectFlightDelayDTO.getArrivalPlace();
                }
            }
            // 出发地目的地为空则取报案数据
//            JSONObject reportInfoData = rapeApiService.requestReportDomainInfo(reportNo);
//            if (StringUtils.isEmptyStr(departPlace) && StringUtils.isEmptyStr(arrivalPlace)) {
//                ReportAccidentFlightVO reportAccidentFlightVO = reportInfoData.getObject("reportAccidentFlight", ReportAccidentFlightVO.class);
//                if (null != reportAccidentFlightVO) {
//                    departPlace = reportAccidentFlightVO.getDeparturePlace();
//                    arrivalPlace = reportAccidentFlightVO.getDestination();
//                }
//            }

//            if (null == flightDate) {
//                flightDate = getAccidentDateToFlightDate(reportNo, reportInfoData);
//            }

            // 设置默认计算延误方式=皆可
            dutyFilghtInfoVO.setCalculateMode("DELAY_CALC_03");

            // 日期转成String类型
            String tempFlightDate = DateUtils.parseToFormatString(flightDate, DateUtils.SIMPLE_DATE_STR);
            List<FlightAllInfoDTO> flightAllInfoDTOs = null;

//            flightAllInfoDTOs = flightAllInfoService.getFlightAllInfoByFlightNo(flightNo, tempFlightDate);
//
//
//            if (ListUtils.isNotEmpty(flightAllInfoDTOs)) {
//                if (flightAllInfoDTOs.size() == 1) {
//                    FlightAllInfoDTO flightAllInfoDTO = flightAllInfoDTOs.get(0);
//                    transToDtyFilghtInfoVO(dutyFilghtInfoVO, isOriginal, flightAllInfoDTO);
//                } else {
//                    if (StringUtils.isNotEmpty(departPlace) && StringUtils.isNotEmpty(arrivalPlace)) {
//                        for (FlightAllInfoDTO flightAllInfoDTO : flightAllInfoDTOs) {
//                            // 匹配航班信息数据跟报案时填写的出发地目的地相同的数据.
//                            if (flightAllInfoDTO.getOriginAirPortCode().equals(departPlace) && flightAllInfoDTO.getDestAirPortCode().equals(arrivalPlace)) {
//                                transToDtyFilghtInfoVO(dutyFilghtInfoVO, isOriginal, flightAllInfoDTO);
//                                break;
//                            }
//                        }
//                    }
//                }
//                // 赋值机场名称
//                for (FlightAllInfoDTO flightAllInfoDTO : flightAllInfoDTOs) {
//                    AirportInfoDTO departAirportInfo = airportInfoService.getAirportInfoByAirportCode(flightAllInfoDTO.getOriginAirPortCode(), null);
//                    flightAllInfoDTO.setOriginAirPortName(departAirportInfo.getAirportName());
//                    AirportInfoDTO arrivalAirportInfo = airportInfoService.getAirportInfoByAirportCode(flightAllInfoDTO.getDestAirPortCode(), null);
//                    flightAllInfoDTO.setDestAirPortName(arrivalAirportInfo.getAirportName());
//                }
//                dutyFilghtInfoVO.setFlightAllInfoList(flightAllInfoDTOs);
//            }

//            if (isOriginal) {
//                // 查询客票信息数据
//                List<PolicyPayDTO> policyPayList = policyPayService.selectFromPolicyCopy(reportNo, caseTimes);
//                if (ListUtils.isNotEmpty(policyPayList)) {
//                    FlightCustomerVO flightCustomerVO = new FlightCustomerVO();
//                    flightCustomerVO.setFlightNo(flightNo);
//                    flightCustomerVO.setFilghtDate(tempFlightDate);
//                    flightCustomerVO.setDepartureAirport(dutyFilghtInfoVO.getDepartPlace());
//                    flightCustomerVO.setDestinationAirport(dutyFilghtInfoVO.getArrivalPlace());
//                    flightCustomerVO.setReportNo(reportNo);
//                    flightCustomerVO.setPolicyNo(policyPayList.get(0).getPolicyNo());
//                    flightCustomerVO.setEleTicketNo(null);
//                    flightCustomerVO.setCompanyId(companyId);
//                    if (StringUtils.isNotEmpty(clientName) && StringUtils.isNotEmpty(certificateNo)) {
//                        flightCustomerVO.setCertificateNo(certificateNo);
//                        flightCustomerVO.setClientName(clientName);
//                        flightCustomerVO.setCertificateType(certificateType);
//                    } else {
//                        InsuredPersonDTO insuredPersonDTO = insuredPersonService.getInsuredPersonDTO(reportNo);
//                        if (null != insuredPersonDTO) {
//                            flightCustomerVO.setCertificateNo(insuredPersonDTO.getCertificateNo());
//                            flightCustomerVO.setClientName(insuredPersonDTO.getName());
//                            flightCustomerVO.setCertificateType(insuredPersonDTO.getCertificateType());
//                        }
//                    }
//                    if (StringUtils.isNotEmpty(flightCustomerVO.getClientName()) && StringUtils.isNotEmpty(flightCustomerVO.getCertificateNo())) {
////                        LogUtil.audit("报案号={},查询客票信息开始,入参={}.", reportNo, JSONObject.toJSONString(flightCustomerVO));
//                        CustomerTicketDTO customerTicketDTO = null;
//                        if (ConstValues.NO.equals(value)) {
//                            customerTicketDTO = customerTicketService.getCustomerTicketDTO(flightCustomerVO);
//                        } else {
//                            customerTicketDTO = customerTicketService.getCustomerTicketDTOByDuty(flightCustomerVO);
//                        }
////                        LogUtil.audit("报案号={},查询客票信息结束,出参={}.", reportNo, JSONObject.toJSONString(customerTicketDTO));
//                        if (customerTicketDTO != null) {
//                            // 设置客票信息
//                            dutyFilghtInfoVO.setTicketNo(customerTicketDTO.getEleTicketNo());
//                            dutyFilghtInfoVO.setIsBuyTicket(customerTicketDTO.getIsBuyTicket());
//                            dutyFilghtInfoVO.setIsTicketUsed(customerTicketDTO.getIsTicketUsed());
//                        }
//                    }
//                }
//            }
        } catch (Exception e) {
            LogUtil.error("报案号={},查询航班信息出现异常", e, reportNo);
        }
        
        LogUtil.audit("#重新获取航班信息#结束#reportNo="+ reportNo);
        
        return dutyFilghtInfoVO;
    }

//    protected void transToDtyFilghtInfoVO(DutyFilghtInfoVO dutyFilghtInfoVO, boolean isOriginal, FlightAllInfoDTO flightAllInfoDTO) {
//        if (isOriginal) {
//            List<String> flightStatusPageList = new ArrayList<String>();
//            BigDecimal delayDuration = flightAllInfoService.calculateDelayDuration(flightAllInfoDTO);
//            dutyFilghtInfoVO.setDelayDuration(delayDuration);
//            if (FlightDelayConstValues.LANDED.equals(flightAllInfoDTO.getFlightStatus())) {
//                flightStatusPageList.add(FlightDelayConstValues.AHCS_FLIGHT_STATUS_01);
//            }
//            dutyFilghtInfoVO.setFlightStatusArr(flightStatusPageList.toArray(new String[] {}));
//            dutyFilghtInfoVO.setOriginalFlightNo(flightAllInfoDTO.getFlightNo());
//            if (flightAllInfoDTO.getPlanDepTime() != null) {
//                dutyFilghtInfoVO.setOriginalDepartTime(DateUtils.parseToFormatString(flightAllInfoDTO.getPlanDepTime()));
//            }
//            if (flightAllInfoDTO.getPlanArrTime() != null) {
//                dutyFilghtInfoVO.setOriginalArrivalTime(DateUtils.parseToFormatString(flightAllInfoDTO.getPlanArrTime()));
//            }
//            dutyFilghtInfoVO.setQueryFlightStatus(flightAllInfoDTO.getFlightStatus());
//
//            if (flightAllInfoDTO.getArrTime() != null && flightAllInfoDTO.getDepTime() != null) {
//                dutyFilghtInfoVO.setRealFlightNo(flightAllInfoDTO.getFlightNo());
//                dutyFilghtInfoVO.setRealDepartTime(DateUtils.parseToFormatString(flightAllInfoDTO.getDepTime()));
//                dutyFilghtInfoVO.setRealArrivalTime(DateUtils.parseToFormatString(flightAllInfoDTO.getArrTime()));
//            }
//            dutyFilghtInfoVO.setDepartPlace(flightAllInfoDTO.getOriginAirPortCode());
//            dutyFilghtInfoVO.setArrivalPlace(flightAllInfoDTO.getDestAirPortCode());
//            // 赋值机场名称
//            if (StringUtils.isNotEmpty(dutyFilghtInfoVO.getDepartPlace())) {
//                AirportInfoDTO departAirportInfo = airportInfoService.getAirportInfoByAirportCode(dutyFilghtInfoVO.getDepartPlace(), null);
//                dutyFilghtInfoVO.setDepartPlaceName(departAirportInfo.getAirportName());
//            }
//            if (StringUtils.isNotEmpty(dutyFilghtInfoVO.getArrivalPlace())) {
//                AirportInfoDTO arrivalAirportInfo = airportInfoService.getAirportInfoByAirportCode(dutyFilghtInfoVO.getArrivalPlace(), null);
//                dutyFilghtInfoVO.setArrivalPlaceName(arrivalAirportInfo.getAirportName());
//            }
//
//        } else {
//            dutyFilghtInfoVO.setRealFlightNo(flightAllInfoDTO.getFlightNo());
//            if (flightAllInfoDTO.getArrTime() != null && flightAllInfoDTO.getDepTime() != null) {
//                dutyFilghtInfoVO.setRealDepartTime(DateUtils.parseToFormatString(flightAllInfoDTO.getDepTime()));
//                dutyFilghtInfoVO.setRealArrivalTime(DateUtils.parseToFormatString(flightAllInfoDTO.getArrTime()));
//                BigDecimal delayDuration = flightAllInfoService.calculateDelayDuration(flightAllInfoDTO);
//                dutyFilghtInfoVO.setDelayDuration(delayDuration);
//                dutyFilghtInfoVO.setDepartPlace(flightAllInfoDTO.getOriginAirPortCode());
//                dutyFilghtInfoVO.setArrivalPlace(flightAllInfoDTO.getDestAirPortCode());
//                // 赋值机场名称
//                if (StringUtils.isNotEmpty(dutyFilghtInfoVO.getDepartPlace())) {
//                    AirportInfoDTO departAirportInfo = airportInfoService.getAirportInfoByAirportCode(dutyFilghtInfoVO.getDepartPlace(), null);
//                    dutyFilghtInfoVO.setDepartPlaceName(departAirportInfo.getAirportName());
//                }
//                if (StringUtils.isNotEmpty(dutyFilghtInfoVO.getArrivalPlace())) {
//                    AirportInfoDTO arrivalAirportInfo = airportInfoService.getAirportInfoByAirportCode(dutyFilghtInfoVO.getArrivalPlace(), null);
//                    dutyFilghtInfoVO.setArrivalPlaceName(arrivalAirportInfo.getAirportName());
//                }
//            }
//        }
//    }

    /**
    * @Description: 获取事故日期作为航班日期
    */
//    private Date getAccidentDateToFlightDate(String reportNo, JSONObject reportInfoData) {
//        Date flightDate = null;
//        // 获取报案信息
//        try {
//            if (null == reportInfoData) {
//                reportInfoData = rapeApiService.requestReportDomainInfo(reportNo);
//            }
//            if (reportInfoData != null) {
//                JSONObject accidentObj = reportInfoData.getJSONObject("reportAccident");
//                if (accidentObj != null) {
//                    flightDate = DateUtils.formatStringToDate(accidentObj.getString("accidentDate"), DateUtils.FULL_DATE_STR);
//                }
//            }
//        } catch (ClaimBusinessException e) {
//            LogUtil.audit("#调用结报系统获取报案信息出错，e={}#", e);
//        }
//        return flightDate;
//    }
    
    @Override
    public void modifyFlighterTravelVerify(FlightDelayDTO flightDelay) {
        String reportNo = flightDelay.getReportNo();
        Integer caseTimes = flightDelay.getCaseTimes();

        FlightDelayDTO flightDelayDTO = flightDelayMapper.getFlightDelay(reportNo, caseTimes, null, BpmConstants.CHECK_DUTY, null);
        if (flightDelayDTO != null) {
            flightDelayDTO.setStatus(ChecklossConst.STATUS_TMP_SAVE);
            flightDelayDTO.setTaskCode(BpmConstants.CHECK_DUTY);
            flightDelayDTO.setCreatedBy(ConstValues.SYSTEM);
            flightDelayDTO.setUpdatedBy(ConstValues.SYSTEM);
            if(StringUtils.isNotEmpty(flightDelayDTO.getOriginalFlightNo())){
                flightDelayDTO.setOriginalTravelVerify("YES");
            }
            if(StringUtils.isNotEmpty(flightDelayDTO.getRealFlightNo())){
                flightDelayDTO.setRealTravelVerify("YES");
            }
            flightDelayDTO.setFlightTravelStatus(flightDelay.getFlightTravelStatus());
            this.saveFlighterDelay(flightDelayDTO);
        }
    }
}
