{"saleDepartmentName": "江苏", "notShowFileForPolicy": false, "hideChannelInfoPas": false, "contractDTO": {"baseInfo": {"departmentCode": "**********", "discount": 1.0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "acceptInsuranceDate": "2022-09-03 15:18:59", "productName": "ZJ社保团体医疗保险（2021版）WH", "quotationNo": "Q000000005098", "rateTotal": 30.0, "insuranceDate": "2022-09-03 15:02:50", "contractPactCode": "Pd0010515", "applyPolicyNo": "5**********00005515", "totalInsuredAmount": 321000.0, "productVersion": "1.04", "exchangeRate": 1.0, "businessTypeName": "团体", "underwriteName": "TESTUWSZB1", "inputByName": "TESTER", "id": "1565962362303614976", "coinsuranceMark": "2", "shareholdersFlag": "0", "saleAmount": 240.0, "saleRate": 10.0, "updatedBy": "TESTUWSZB1", "productClass": "19", "payTermNo": 1, "endorseNo": "1**********00000439", "performanceAmount": 240.0, "inputDate": "2022-09-03 15:18:13", "quotationDate": "2022-09-03 15:14:02", "totalVATExcludedPremium": 2400.0, "productKind": "0", "isAccommodationName": "", "totalAgreePremium": 2400.0, "performanceRate": 10.0, "insuredTotalNum": 3, "insuranceLimit": "27天", "dataSource": "NCBS_NBS", "commissionAmount": 240.0, "status": "B5", "commissionRate": 10.0, "isRoundName": "", "inputBy": "TEST", "insuranceEndDate": "2022-09-30 23:59:59", "quotationName": "社保团体医疗保险2021版WH", "totalValueAddedTax": 0.0, "archiveDate": "2022-09-30 00:00:00", "totalStandardPremium": 2400.0, "renewalTypeName": "", "premiumCurrencyCode": "01", "carBusinessType": "0", "systemId": "06", "totalActualPremium": 2400.0, "renewalType": "0", "operationTypeName": "", "amountCurrencyCode": "01", "createdDate": "2022-09-03 15:18:59", "productCode": "MP19000054", "createdBy": "TEST", "underwriteDate": "2022-09-03 15:18:57", "dayNum": "-1天", "amountTotal": 720.0, "discountPermium": 2400.0, "endorsementTimes": 0, "formatTypeName": "", "businessType": "2", "insuranceBeginDate": "2022-09-04 00:00:00"}, "specialPromiseList": [], "endorseNo": "1**********00000439", "policyNo": "1**********00000439", "attachmentGroupList": [{"documentInfoList": [{"documentType": "OT99", "uploadPath": "http://stg.iobs.paic.com.cn/download/life-image-sf-stg/e895c375617849f2bd86307499c7a066?attname=e895c375617849f2bd86307499c7a066.xls", "documentName": "0000105201662189149648.xls", "documentSize": "541.00", "documentOrder": "1", "documentClass": "T99", "url": "http://stg.iobs.paic.com.cn/download/life-image-sf-stg/e895c375617849f2bd86307499c7a066?attname=e895c375617849f2bd86307499c7a066.xls", "uploadDate": "2022-09-03 15:12:29", "uploadIsDate": "2022-09-03 15:12:29", "documentId": "20010522", "documentStatus": "Y", "documentFormat": "xls", "uploadPersonnel": "TEST", "originName": "0000105201662189149648.xls"}], "createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "documentGroupId": "00010521", "documentGroupType": "01", "createdBy": "TEST", "archiveDate": "2022-09-30 00:00:00", "endorseNo": "", "policyNo": "1**********00000439", "id": "20f2be491b824c1aa08c924992f0a5ed", "updatedDate": "2022-09-03 15:18:59", "baseInfoId": "1565962362303614976"}], "extendInfo": {"disputedSettleModeName": "", "updatedBy": "TEST", "isFacultativeBusiness": "0", "isStarHighInsurance": "0", "endorseNo": "", "isGiftInsurance": "0", "validateCode": "6PSP36VXJ", "applyApproach": "02", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "isTransProtectBusiness": "0", "isPolicyHealthInsurance": "0", "baseInfoId": "1565962362303614976", "isPolicyBeforePayfee": "1", "disputedSettleMode": "1", "createdDate": "2022-09-03 15:18:59", "isPastsign": "0", "createdBy": "TEST", "id": "1565962363293470720", "isVipCustomer": "1", "applyApproachName": "FQS批量出单", "isPrintAgencyInfo": "1"}, "noclaimInfoList": [], "compensationLimitInfoList": [], "insurantInfoList": [], "quotationNo": "Q000000005098", "applyPolicyNo": "5**********00005515", "saleInfo": {"departmentName": "第三方系统财产保险股份有限公司南京市浦口支公司", "updatedBy": "TEST", "endorseNo": "", "brokerInfoList": [], "departmentCode": "**********", "channelSourceCode": "0", "employeeInfoList": [{"saleInfoId": "775c1b33e70648f7b425f5d3f7d38e0e", "documentCost": 0.0, "employeeName": "张锋", "updatedBy": "TEST", "performanceValue1Modify": 0.0, "endorseNo": "", "offlineServiceFee": 0.0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "onlineServiceFee": 0.0, "employeeCode": "232123400", "mainEmployeeFlag": "1", "createdDate": "2022-09-03 15:18:59", "createdBy": "TEST", "marginCharge1": 0.0, "performanceValue2Modify": 0.0, "id": "a09a46ad314c4cb49dab08db522110f5"}], "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "signDate": "2022-09-03 15:19:00", "agentInfoList": [{"saleInfoId": "775c1b33e70648f7b425f5d3f7d38e0e", "createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "createdBy": "TEST", "agentCode": "U200002007115", "endorseNo": "", "agentName": "江苏新润保险代理有限公司", "policyNo": "1**********00000439", "id": "068d14918cbd42a982497a4cd1d6d90a", "updatedDate": "2022-09-03 15:18:59"}], "baseInfoId": "1565962362303614976", "createdDate": "2022-09-03 15:18:59", "createdBy": "TEST", "channelSourceName": "直接业务", "id": "775c1b33e70648f7b425f5d3f7d38e0e", "applyDate": "2022-09-03 15:19:00", "partnerInfoList": []}, "rescueServiceList": [], "applicantInfoList": [{"linkModeType": "01", "city": "110100", "county": "110101", "clientNo": "123456", "organizationTypeName": "", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "baseInfoId": "1565962362303614976", "province": "110000", "applicantType": "2", "sexName": "", "isConfirmName": "0", "id": "e123f8e747004e03b06563acd741cac6", "personnelTypeName": "个人", "updatedBy": "TESTUWSZB1", "address": "北京市东城区", "endorseNo": "", "certificateIssueDate": "2022-09-03 00:00:00", "certificateNo": "223456789Q11112", "certificateTypeName": "护照", "sexCode": "X", "linkManName": "荣芳百世科技公司", "createdDate": "2022-09-03 15:18:59", "mobileTelephone": "0513-84566588", "createdBy": "TEST", "personnelType": "1", "isConfirmNotification": "0", "name": "荣芳百世科技公司", "certificateValidDate": "9999-12-31 00:00:00", "certificateType": "02"}], "riskGroupInfoList": [{"updatedBy": "TEST", "productClass": "19", "endorseNo": "", "totalActualPremium": 800.0, "virtualRiskNum": 0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "applyNum": 1.0, "combinedProductVersion": "1.04", "baseInfoId": "1565962362303614976", "totalInsuredAmount": 321000.0, "createdDate": "2022-09-03 15:18:59", "planInfoList": [{"updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "isMain": "1", "endorseNo": "", "totalActualPremium": 800.0, "planName": "社保团体医疗保险2021版WH", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "planCode": "PL1900015", "totalVATExcludedPremium": 2400.0, "totalInsuredAmount": 321000.0, "createdDate": "2022-09-03 15:18:59", "totalAgreePremium": 800.0, "totalValueAddedTax": 0.0, "createdBy": "TEST", "archiveDate": "2022-09-30 00:00:00", "dutyInfoList": [{"commissionRate": 10.0, "premiumRate": 0.013333, "discount": 100.0, "dutyCode": "CVBT002", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "dutyDetailInfoList": [{"updatedBy": "TESTUWSZB1", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "04", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-03 15:18:59", "dutyDetailName": "门诊医疗保险金费用WH", "createdBy": "TESTUWSZB1", "dutyDetailCode": "DD00238", "secondTypeName": "", "id": "ae1c4b582b5811eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "3790662d301041ad80ed8fc1024c613b", "effectiveDate": "2022-07-14 10:58:07"}], "isImportName": "", "totalInsuredAmount": 15000.0, "totalValueAddedTax": 0.0, "id": "3790662d301041ad80ed8fc1024c613b", "totalStandardPremium": 200.0, "insuredAmount": 5000.0, "saleRate": 10.0, "updatedBy": "TEST", "riskDutyRelationInfoList": [{"updatedBy": "TESTUWSZB1", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "idRiskClass": "edd0b1a6bcdb48fe9981dde8cd031bc6", "classRiskNum": 3, "endorseNo": "", "totalActualPremium": 200.0, "dutyCode": "CVBT002", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "insuranceEndDate": "2022-09-30 23:59:59", "riskInfoId": "2fb24d00b1fd4b98ba24cabcfa34bde8", "agreePremium": 0.0, "totalInsuredAmount": 5000.0, "createdDate": "2022-09-03 15:18:59", "createdBy": "TESTUWSZB1", "archiveDate": "2022-09-30 00:00:00", "standardPremium": 0.0, "id": "1565962539928195072", "insuredAmount": 5000.0, "actualPremium": 0.0, "dutyInfoId": "3790662d301041ad80ed8fc1024c613b", "insuranceBeginDate": "2022-09-04 00:00:00"}], "endorseNo": "", "totalActualPremium": 200.0, "dutyDesc": "", "dutyName": "门诊医疗保险金WH", "totalVATExcludedPremium": 600.0, "createdDate": "2022-09-03 15:18:59", "totalAgreePremium": 200.0, "dutyAttributeInfoList": [{"createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "dutyAttributeName": "赔付比例", "createdBy": "TEST", "dutyAttrCode": "365", "dutyAttrAmountValue": "0.5", "policyNo": "1**********00000439", "id": "ea184409c98d431cb30288e76a03fbba", "updatedDate": "2022-09-03 15:18:59", "dutyInfoId": "3790662d301041ad80ed8fc1024c613b"}, {"createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "dutyAttributeName": "每次事故免赔额", "createdBy": "TEST", "dutyAttrCode": "53", "dutyAttrAmountValue": "500", "policyNo": "1**********00000439", "id": "fb3916268b584ec987152fda45446872", "updatedDate": "2022-09-03 15:18:59", "dutyInfoId": "3790662d301041ad80ed8fc1024c613b"}, {"createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "dutyAttributeName": "每人每次医疗费用限额", "createdBy": "TEST", "dutyAttrCode": "616", "dutyAttrAmountValue": "1000", "policyNo": "1**********00000439", "id": "67e71b2e8cad478aaa9e682b81434cbb", "updatedDate": "2022-09-03 15:18:59", "dutyInfoId": "3790662d301041ad80ed8fc1024c613b"}], "createdBy": "TEST", "performanceRate": 10.0, "discountPermium": 200.0, "planInfoId": "1584230e2d484b0e9ca15b91eecc80a1"}, {"commissionRate": 10.0, "premiumRate": 0.001333, "discount": 100.0, "dutyCode": "CVBS007", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "dutyDetailInfoList": [{"updatedBy": "TESTUWSZB1", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "04", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-03 15:18:59", "dutyDetailName": "住院医疗保险金费用WH", "createdBy": "TESTUWSZB1", "dutyDetailCode": "DD00237", "secondTypeName": "", "id": "ae2292222b5811eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "390ae0b9675348d3933558812038b5ab", "effectiveDate": "2022-07-14 10:57:22"}], "isImportName": "", "totalInsuredAmount": 300000.0, "totalValueAddedTax": 0.0, "id": "390ae0b9675348d3933558812038b5ab", "totalStandardPremium": 400.0, "insuredAmount": 100000.0, "saleRate": 10.0, "updatedBy": "TEST", "riskDutyRelationInfoList": [{"updatedBy": "TESTUWSZB1", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "idRiskClass": "edd0b1a6bcdb48fe9981dde8cd031bc6", "classRiskNum": 3, "endorseNo": "", "totalActualPremium": 400.0, "dutyCode": "CVBS007", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "insuranceEndDate": "2022-09-30 23:59:59", "riskInfoId": "2fb24d00b1fd4b98ba24cabcfa34bde8", "agreePremium": 0.0, "totalInsuredAmount": 100000.0, "createdDate": "2022-09-03 15:18:59", "createdBy": "TESTUWSZB1", "archiveDate": "2022-09-30 00:00:00", "standardPremium": 0.0, "id": "1565962539957555200", "insuredAmount": 100000.0, "actualPremium": 800.0, "dutyInfoId": "390ae0b9675348d3933558812038b5ab", "insuranceBeginDate": "2022-09-04 00:00:00"}], "endorseNo": "", "totalActualPremium": 400.0, "dutyDesc": "", "dutyName": "住院医疗保险金WH", "totalVATExcludedPremium": 1200.0, "createdDate": "2022-09-03 15:18:59", "totalAgreePremium": 400.0, "dutyAttributeInfoList": [{"createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "dutyAttributeName": "免赔额", "createdBy": "TEST", "dutyAttrCode": "271", "dutyAttrAmountValue": "500", "policyNo": "1**********00000439", "id": "0ae334291ff94fe38597a91c75f19470", "updatedDate": "2022-09-03 15:18:59", "dutyInfoId": "390ae0b9675348d3933558812038b5ab"}, {"createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "dutyAttributeName": "赔付比例", "createdBy": "TEST", "dutyAttrCode": "365", "dutyAttrAmountValue": "0.7", "policyNo": "1**********00000439", "id": "4cb11cc2fd5948e183aa648a521ad40d", "updatedDate": "2022-09-03 15:18:59", "dutyInfoId": "390ae0b9675348d3933558812038b5ab"}, {"createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "dutyAttributeName": "每人每次医疗费用限额", "createdBy": "TEST", "dutyAttrCode": "616", "dutyAttrAmountValue": "1000", "policyNo": "1**********00000439", "id": "88b18a41c60e47e3b1f4fa404e0fb15b", "updatedDate": "2022-09-03 15:18:59", "dutyInfoId": "390ae0b9675348d3933558812038b5ab"}], "createdBy": "TEST", "performanceRate": 10.0, "discountPermium": 400.0, "planInfoId": "1584230e2d484b0e9ca15b91eecc80a1"}, {"commissionRate": 10.0, "premiumRate": 0.033333, "discount": 100.0, "dutyCode": "CVBU002", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "dutyDetailInfoList": [{"updatedBy": "TESTUWSZB1", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "04", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-03 15:18:59", "dutyDetailName": "特定医疗费用保险金费用WH", "createdBy": "TESTUWSZB1", "dutyDetailCode": "DD00239", "secondTypeName": "", "id": "ae168a7d2b5811eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "83942992d4b44e94986cc09f5dbc3f5c", "effectiveDate": "2022-07-14 10:58:25"}], "isImportName": "", "totalInsuredAmount": 3000.0, "totalValueAddedTax": 0.0, "id": "83942992d4b44e94986cc09f5dbc3f5c", "totalStandardPremium": 100.0, "insuredAmount": 1000.0, "saleRate": 10.0, "updatedBy": "TEST", "riskDutyRelationInfoList": [{"updatedBy": "TESTUWSZB1", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "idRiskClass": "edd0b1a6bcdb48fe9981dde8cd031bc6", "classRiskNum": 3, "endorseNo": "", "totalActualPremium": 100.0, "dutyCode": "CVBU002", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "insuranceEndDate": "2022-09-30 23:59:59", "riskInfoId": "2fb24d00b1fd4b98ba24cabcfa34bde8", "agreePremium": 0.0, "totalInsuredAmount": 1000.0, "createdDate": "2022-09-03 15:18:59", "createdBy": "TESTUWSZB1", "archiveDate": "2022-09-30 00:00:00", "standardPremium": 0.0, "id": "1565962539886252032", "insuredAmount": 1000.0, "actualPremium": 0.0, "dutyInfoId": "83942992d4b44e94986cc09f5dbc3f5c", "insuranceBeginDate": "2022-09-04 00:00:00"}], "endorseNo": "", "totalActualPremium": 100.0, "dutyDesc": "", "dutyName": "特定医疗费用保险金WH", "totalVATExcludedPremium": 300.0, "createdDate": "2022-09-03 15:18:59", "totalAgreePremium": 100.0, "dutyAttributeInfoList": [{"createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "dutyAttributeName": "免赔额", "createdBy": "TEST", "dutyAttrCode": "271", "dutyAttrAmountValue": "500", "policyNo": "1**********00000439", "id": "3124af306cbe4cd69fa0f374adf671c2", "updatedDate": "2022-09-03 15:18:59", "dutyInfoId": "83942992d4b44e94986cc09f5dbc3f5c"}, {"createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "dutyAttributeName": "是否区分社保", "createdBy": "TEST", "dutyAttrCode": "360", "dutyAttrAmountValue": "1", "policyNo": "1**********00000439", "id": "7406c16ebdde491a8e07dab858ccff4a", "updatedDate": "2022-09-03 15:18:59", "dutyInfoId": "83942992d4b44e94986cc09f5dbc3f5c"}, {"createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "dutyAttributeName": "赔付比例", "createdBy": "TEST", "dutyAttrCode": "365", "dutyAttrAmountValue": "0.8", "policyNo": "1**********00000439", "id": "9708d71340dd4686b5b6f96bc04470b5", "updatedDate": "2022-09-03 15:18:59", "dutyInfoId": "83942992d4b44e94986cc09f5dbc3f5c"}], "createdBy": "TEST", "performanceRate": 10.0, "discountPermium": 100.0, "planInfoId": "1584230e2d484b0e9ca15b91eecc80a1"}, {"commissionRate": 10.0, "premiumRate": 0.033333, "discount": 100.0, "dutyCode": "CVBV002", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "dutyDetailInfoList": [{"updatedBy": "TESTUWSZB1", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "05", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-03 15:18:59", "dutyDetailName": "住院日额津贴保险金费用WH", "createdBy": "TESTUWSZB1", "dutyDetailCode": "DD00240", "secondTypeName": "", "id": "ae1141342b5811eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "f2c5db6b14e74b5aabe7d34941cc68c8", "effectiveDate": "2022-07-14 10:59:14"}], "isImportName": "", "totalInsuredAmount": 3000.0, "totalValueAddedTax": 0.0, "id": "f2c5db6b14e74b5aabe7d34941cc68c8", "totalStandardPremium": 100.0, "insuredAmount": 1000.0, "saleRate": 10.0, "updatedBy": "TEST", "riskDutyRelationInfoList": [{"updatedBy": "TESTUWSZB1", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "idRiskClass": "edd0b1a6bcdb48fe9981dde8cd031bc6", "classRiskNum": 3, "endorseNo": "", "totalActualPremium": 100.0, "dutyCode": "CVBV002", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "insuranceEndDate": "2022-09-30 23:59:59", "riskInfoId": "2fb24d00b1fd4b98ba24cabcfa34bde8", "agreePremium": 0.0, "totalInsuredAmount": 1000.0, "createdDate": "2022-09-03 15:18:59", "createdBy": "TESTUWSZB1", "archiveDate": "2022-09-30 00:00:00", "standardPremium": 0.0, "id": "1565962539848503296", "insuredAmount": 1000.0, "actualPremium": 0.0, "dutyInfoId": "f2c5db6b14e74b5aabe7d34941cc68c8", "insuranceBeginDate": "2022-09-04 00:00:00"}], "endorseNo": "", "totalActualPremium": 100.0, "dutyDesc": "", "dutyName": "住院日额津贴保险金WH", "totalVATExcludedPremium": 300.0, "createdDate": "2022-09-03 15:18:59", "totalAgreePremium": 100.0, "dutyAttributeInfoList": [{"createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "dutyAttributeName": "每人每日住院津贴额", "createdBy": "TEST", "dutyAttrCode": "117", "dutyAttrAmountValue": "100", "policyNo": "1**********00000439", "id": "506e81295b1c4403866ded07f65a96ea", "updatedDate": "2022-09-03 15:18:59", "dutyInfoId": "f2c5db6b14e74b5aabe7d34941cc68c8"}, {"createdDate": "2022-09-03 15:18:59", "updatedBy": "TEST", "dutyAttributeName": "免赔天数", "createdBy": "TEST", "dutyAttrCode": "14", "dutyAttrAmountValue": "3", "policyNo": "1**********00000439", "id": "81e3303f57c24561bc9a1ddd5d1e1c75", "updatedDate": "2022-09-03 15:18:59", "dutyInfoId": "f2c5db6b14e74b5aabe7d34941cc68c8"}], "createdBy": "TEST", "performanceRate": 10.0, "discountPermium": 100.0, "planInfoId": "1584230e2d484b0e9ca15b91eecc80a1"}], "id": "1584230e2d484b0e9ca15b91eecc80a1", "totalStandardPremium": 800.0}], "totalAgreePremium": 800.0, "createdBy": "TEST", "riskGroupName": "方案一", "archiveDate": "2022-09-30 00:00:00", "applyRiskNum": 3, "riskPersonInfoList": [{"birthday": "1983-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "relationshipWithApplicant": "9", "id": "27e9e22297da4980b4e864a51743d1ab", "totalStandardPremium": 800.0, "pregnancyWeek": 0, "riskPersonNo": "2", "updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "address": "李师师", "idRiskClass": "edd0b1a6bcdb48fe9981dde8cd031bc6", "virtualInsuredNum": 0, "totalActualPremium": 800.0, "postcode": "否", "certificateIssueDate": "2022-09-03 00:00:00", "personnelCode": "CL0000000000978", "vehicleNum": 0, "certificateNo": "110101198301019914", "createdDate": "2022-09-03 15:18:59", "mobileTelephone": "15876587699", "totalAgreePremium": 800.0, "createdBy": "TEST", "name": "李师师", "isSociaSecurity": "2", "certificateValidDate": "2023-09-03 00:00:00", "beneficaryInfoList": [{"updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "baseInfoId": "1565962362303614976", "createdDate": "2022-09-03 15:18:59", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "27e9e22297da4980b4e864a51743d1ab", "id": "d61e5756addb48648947b7d938b5cfe7"}], "masterMark": "1", "age": 39, "certificateType": "01"}, {"birthday": "1983-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "relationshipWithApplicant": "9", "id": "2fb24d00b1fd4b98ba24cabcfa34bde8", "totalStandardPremium": 800.0, "pregnancyWeek": 0, "riskPersonNo": "1", "updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "address": "李大胆", "idRiskClass": "edd0b1a6bcdb48fe9981dde8cd031bc6", "virtualInsuredNum": 0, "totalActualPremium": 800.0, "postcode": "否", "certificateIssueDate": "2022-09-03 00:00:00", "personnelCode": "CL0000000000977", "vehicleNum": 0, "certificateNo": "110101198301019930", "createdDate": "2022-09-03 15:18:59", "mobileTelephone": "15876587698", "totalAgreePremium": 800.0, "createdBy": "TEST", "name": "李大胆", "isSociaSecurity": "2", "certificateValidDate": "2023-09-03 00:00:00", "beneficaryInfoList": [{"updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "baseInfoId": "1565962362303614976", "createdDate": "2022-09-03 15:18:59", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "2fb24d00b1fd4b98ba24cabcfa34bde8", "id": "7925c558b6aa4f1991f5792da2ac5969"}], "masterMark": "1", "age": 39, "certificateType": "01"}, {"birthday": "1983-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "relationshipWithApplicant": "9", "id": "f9ac458288164e1e8fd32dfbd912bba9", "totalStandardPremium": 800.0, "pregnancyWeek": 0, "riskPersonNo": "3", "updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "address": "李大头", "idRiskClass": "edd0b1a6bcdb48fe9981dde8cd031bc6", "virtualInsuredNum": 0, "totalActualPremium": -800.0, "postcode": "否", "certificateIssueDate": "2022-09-03 00:00:00", "personnelCode": "CL0000000000979", "vehicleNum": 0, "certificateNo": "110101198301019893", "createdDate": "2022-09-03 15:18:59", "mobileTelephone": "15876587697", "totalAgreePremium": -800.0, "createdBy": "TEST", "name": "李大头", "isSociaSecurity": "2", "certificateValidDate": "2023-09-03 00:00:00", "beneficaryInfoList": [{"updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "baseInfoId": "1565962362303614976", "createdDate": "2022-09-03 15:18:59", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "f9ac458288164e1e8fd32dfbd912bba9", "id": "b499b4aa60dd42c6a62f82d1036e2477"}], "masterMark": "1", "age": 39, "certificateType": "01"}], "riskGroupType": "18", "id": "9648504ca58f4cf9b70cddb897dfad53", "totalStandardPremium": 800.0, "combinedProductCode": "MP19000054", "beneficaryInfoList": [{"updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "baseInfoId": "1565962362303614976", "createdDate": "2022-09-03 15:18:59", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "2fb24d00b1fd4b98ba24cabcfa34bde8", "id": "7925c558b6aa4f1991f5792da2ac5969"}, {"updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "baseInfoId": "1565962362303614976", "createdDate": "2022-09-03 15:18:59", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "f9ac458288164e1e8fd32dfbd912bba9", "id": "b499b4aa60dd42c6a62f82d1036e2477"}, {"updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "baseInfoId": "1565962362303614976", "createdDate": "2022-09-03 15:18:59", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "27e9e22297da4980b4e864a51743d1ab", "id": "d61e5756addb48648947b7d938b5cfe7"}]}], "payInfoList": [{"paymentPersonName": "荣芳百世科技公司", "noticeNo": "", "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:15", "baseInfoId": "1565962362303614976", "installmentType": "0", "paymentItemName": "0", "agreePremium": 2400.0, "paymentEndDate": "2022-09-03 23:59:59", "paymentItem": "0", "comExchangeRate": 1.0, "currencyName": "", "archiveDate": "2022-09-30 00:00:00", "statusName": "收款", "noticeStatus": "", "id": "e1dc33ec88c64ca8a969844a0a17c783", "updatedBy": "TEST", "isInstallment": "0", "payerTypeName": "", "createdDate": "2022-09-03 15:18:15", "createdBy": "TEST", "termNo": 1, "paymentBeginDate": "2022-09-03 00:00:00", "billTypeName": "", "currencyCode": "CNY", "defComExchangeRate": 1.0, "status": "01"}], "beneficaryInfoList": [{"updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "baseInfoId": "1565962362303614976", "createdDate": "2022-09-03 15:18:59", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "2fb24d00b1fd4b98ba24cabcfa34bde8", "id": "7925c558b6aa4f1991f5792da2ac5969"}, {"updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "baseInfoId": "1565962362303614976", "createdDate": "2022-09-03 15:18:59", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "f9ac458288164e1e8fd32dfbd912bba9", "id": "b499b4aa60dd42c6a62f82d1036e2477"}, {"updatedBy": "TEST", "riskGroupId": "9648504ca58f4cf9b70cddb897dfad53", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000439", "updatedDate": "2022-09-03 15:18:59", "baseInfoId": "1565962362303614976", "createdDate": "2022-09-03 15:18:59", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "27e9e22297da4980b4e864a51743d1ab", "id": "d61e5756addb48648947b7d938b5cfe7"}]}, "hideBusinessDetail": false, "commissionCharge": "0.00", "commissionChargeProportion": "0.00%", "hideEmployeeInfo": false, "technicProductCode": "TP1900019", "isElecSubPolicyNo": "0", "endorseSceneInfoDTO": {"plyPrintTimes": 0, "edrPrintTimes": 0}, "saleChannelSourceName": "", "isShowCommission": true, "hideChannelInfo": false, "delLicenceWhippletre": false, "hideSaleGroupName": false, "saleDepartmentCode": "2320000000", "isShowCommissionInter": true, "showPremium": false}