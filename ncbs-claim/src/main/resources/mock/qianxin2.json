{"saleDepartmentName": "江苏", "notShowFileForPolicy": false, "hideChannelInfoPas": false, "contractDTO": {"baseInfo": {"departmentCode": "2320100000", "discount": 1.0, "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "acceptInsuranceDate": "2022-09-15 10:40:24", "productName": "理赔测试专用CLA002", "quotationNo": "5232010000000006370", "rateTotal": 3.0, "insuranceDate": "2022-09-15 10:37:53", "contractPactCode": "U200002007115-01", "applyPolicyNo": "5232010000000006371", "totalInsuredAmount": 100000.0, "productVersion": "1.01", "exchangeRate": 1.0, "businessTypeName": "个人", "underwriteName": "核保测试总部管理员", "inputByName": "TESTER", "id": "1570241001627127808", "coinsuranceMark": "2", "shareholdersFlag": "1", "saleAmount": 10.0, "saleRate": 1.0, "updatedBy": "TESTUWSZB", "productClass": "03", "payTermNo": 1, "endorseNo": "1232010000000000708", "performanceAmount": 10.0, "inputDate": "2022-09-15 10:39:59", "totalVATExcludedPremium": 1000.0, "productKind": "0", "isAccommodationName": "", "totalAgreePremium": 1000.0, "performanceRate": 1.0, "insuranceLimit": "365天", "dataSource": "ICORE_PNBS", "commissionAmount": 10.0, "status": "B5", "commissionRate": 1.0, "isRoundName": "否", "inputBy": "TEST", "insuranceEndDate": "2023-09-15 23:59:59", "archiveDate": "2023-09-15 00:00:00", "totalStandardPremium": 1000.0, "renewalTypeName": "", "premiumCurrencyCode": "01", "carBusinessType": "0", "systemId": "06", "totalActualPremium": 1000.0, "renewalType": "0", "isRound": "0", "operationTypeName": "", "amountCurrencyCode": "01", "createdDate": "2022-09-15 10:40:24", "productCode": "MP03100034", "createdBy": "TEST", "underwriteDate": "2022-09-15 10:40:23", "dayNum": "-1天", "amountTotal": 30.0, "endorsementTimes": 0, "formatTypeName": "", "businessType": "1", "insuranceBeginDate": "2022-08-01 00:00:00"}, "specialPromiseList": [{"promiseCode": "TY000000000000022", "createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "promiseType": "N", "createdBy": "TEST", "sortIndex": 1, "promiseDesc": "理赔专用特约。", "endorseNo": "", "policyNo": "1232010000000000708", "id": "1564148179309805568", "updatedDate": "2022-08-29 15:09:22", "baseInfoId": "1570241001627127808"}], "endorseNo": "1232010000000000708", "policyNo": "1232010000000000708", "extendInfo": {"disputedSettleModeName": "", "updatedBy": "TEST", "isFacultativeBusiness": "0", "isStarHighInsurance": "0", "endorseNo": "", "isGiftInsurance": "0", "validateCode": "VWTRZ/895D/2", "applyApproach": "01", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "baseInfoId": "1570241001627127808", "isPolicyBeforePayfee": "1", "disputedSettleMode": "1", "createdDate": "2022-09-15 10:40:24", "payType": "", "isPastsign": "0", "createdBy": "TEST", "id": "1570241002306605056", "isVipCustomer": "0", "applyApproachName": "FQS单笔出单", "isPrintAgencyInfo": "1"}, "insurantInfoList": [], "applyPolicyNo": "5232010000000006371", "saleInfo": {"departmentName": "第三方系统财产保险股份有限公司南京分公司", "updatedBy": "TEST", "endorseNo": "", "brokerInfoList": [], "departmentCode": "2320100000", "channelSourceCode": "0", "employeeInfoList": [{"saleInfoId": "46c71418897847e4bf983f72b5abe2a1", "documentCost": 0.0, "employeeName": "张锋", "updatedBy": "TEST", "performanceValue1Modify": 0.0, "endorseNo": "", "offlineServiceFee": 0.0, "employeeProfCertifNo": "", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "onlineServiceFee": 0.0, "employeeCode": "232123400", "mainEmployeeFlag": "1", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "marginCharge1": 0.0, "performanceValue2Modify": 0.0, "id": "e1df91cf24c5478ba7e0e82fa9d5771e"}], "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "signDate": "2022-09-15 10:40:25", "agentInfoList": [{"saleInfoId": "46c71418897847e4bf983f72b5abe2a1", "createdDate": "2022-09-15 10:40:24", "updatedBy": "TEST", "createdBy": "TEST", "agentCode": "U200002007115", "endorseNo": "", "agentName": "江苏新润保险代理有限公司", "policyNo": "1232010000000000708", "id": "2d33c6f2c74143eeaf384897cfdefaf5", "updatedDate": "2022-09-15 10:40:24"}], "baseInfoId": "1570241001627127808", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "channelSourceName": "直接业务", "id": "46c71418897847e4bf983f72b5abe2a1", "applyDate": "2022-09-15 10:40:25", "partnerInfoList": []}, "rescueServiceList": [], "applicantInfoList": [{"birthday": "2000-02-11 08:00:00", "linkModeType": "03", "clientNo": "213244", "organizationTypeName": "", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "baseInfoId": "1570241001627127808", "applicantType": "1", "sexName": "", "isConfirmName": "0", "id": "9313d9f469e147d98a8920be1278b6e2", "personnelTypeName": "个人", "updatedBy": "TESTUWSZB", "address": "上海浦东唐镇", "endorseNo": "", "certificateIssueDate": "2022-09-01 00:00:00", "certificateNo": "23213333332332", "certificateTypeName": "护照", "sexCode": "F", "createdDate": "2022-09-15 10:40:24", "mobileTelephone": "13112341234", "createdBy": "TEST", "personnelType": "1", "isConfirmNotification": "0", "name": "未嗯", "certificateValidDate": "2022-09-30 00:00:00", "age": 22, "certificateType": "02"}], "riskGroupInfoList": [{"isSpecifyVehicle": "1", "virtualRiskNum": 0, "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "combinedProductVersion": "1.01", "baseInfoId": "1570241001627127808", "totalInsuredAmount": 100000.0, "planInfoList": [{"updatedBy": "TEST", "riskGroupId": "b8387485817449a0a2b25d11b7323624", "regulatorType": "01", "isMain": "1", "endorseNo": "", "totalActualPremium": 1000.0, "planName": "理赔测试专用CL1", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "planCode": "PL0300213", "totalVATExcludedPremium": 1000.0, "totalInsuredAmount": 100000.0, "createdDate": "2022-09-15 10:40:24", "totalAgreePremium": 1000.0, "totalValueAddedTax": 0.0, "createdBy": "TEST", "archiveDate": "2023-09-15 00:00:00", "dutyInfoList": [{"discountProportion": 1.0, "commissionRate": 1.0, "premiumRate": 0.01, "discount": 100.0, "dutyCode": "CVAC009", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "dutyDetailInfoList": [{"updatedBy": "TESTUWSZB", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "01", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-15 10:40:24", "dutyDetailName": "身故CL", "createdBy": "TESTUWSZB", "dutyDetailCode": "DD00359", "secondTypeName": "", "id": "c017ca2e349f11eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "effectiveDate": "2022-08-06 20:14:47"}, {"updatedBy": "TESTUWSZB", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "02", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-15 10:40:24", "dutyDetailName": "残疾CL", "createdBy": "TESTUWSZB", "dutyDetailCode": "DD00362", "secondTypeName": "", "id": "c017faf6349f11eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "effectiveDate": "2022-08-06 20:16:22"}], "isImportName": "", "isDutySharedAmount": "0", "totalInsuredAmount": 100000.0, "totalValueAddedTax": 0.0, "id": "558755e769594a6bb5af626bcf0c6006", "totalStandardPremium": 1000.0, "insuredAmount": 100000.0, "saleRate": 1.0, "updatedBy": "TEST", "riskDutyRelationInfoList": [{"updatedBy": "TEST", "riskGroupId": "b8387485817449a0a2b25d11b7323624", "dutyCode": "CVAC009", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "insuranceEndDate": "2023-09-15 23:59:59", "riskInfoId": "5182bd84627331e6ab4f1fa3b815dc16", "agreePremium": 1000.0, "totalInsuredAmount": 100000.0, "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "archiveDate": "2023-09-15 00:00:00", "standardPremium": 1000.0, "id": "1570241002159804416", "insuredAmount": 100000.0, "actualPremium": 1000.0, "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "insuranceBeginDate": "2022-09-16 00:00:00"}], "endorseNo": "", "totalActualPremium": 1000.0, "dutyDesc": "", "planCode": "PL0300213", "dutyName": "残疾+身故CL", "totalVATExcludedPremium": 1000.0, "createdDate": "2022-09-15 10:40:24", "totalAgreePremium": 1000.0, "dutyAttributeInfoList": [{"createdDate": "2022-09-15 10:40:24", "updatedBy": "TEST", "dutyAttributeName": "残疾对应赔付比例", "createdBy": "TEST", "dutyAttrCode": "369", "policyNo": "1232010000000000708", "id": "e05ebcf8c273431d9be46332ceabbb3a", "updatedDate": "2022-09-15 10:40:24", "dutyAttrDetailList": [{"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "一级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "attrColumnNo": "0", "dutyAttrInfoId": "e05ebcf8c273431d9be46332ceabbb3a", "dutyAttrDetailCode": "369_0", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "id": "b3634e12-349f-11ed-a979-fe9982256455", "dutyAttrDetailValue": "100", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "二级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "attrColumnNo": "0", "dutyAttrInfoId": "e05ebcf8c273431d9be46332ceabbb3a", "dutyAttrDetailCode": "369_1", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "id": "b36380b2-349f-11ed-a979-fe9982256455", "dutyAttrDetailValue": "90", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "三级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "attrColumnNo": "0", "dutyAttrInfoId": "e05ebcf8c273431d9be46332ceabbb3a", "dutyAttrDetailCode": "369_2", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "id": "b363b5a0-349f-11ed-a979-fe9982256455", "dutyAttrDetailValue": "80", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "四级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "attrColumnNo": "0", "dutyAttrInfoId": "e05ebcf8c273431d9be46332ceabbb3a", "dutyAttrDetailCode": "369_3", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "id": "b363f454-349f-11ed-a979-fe9982256455", "dutyAttrDetailValue": "70", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "五级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "attrColumnNo": "0", "dutyAttrInfoId": "e05ebcf8c273431d9be46332ceabbb3a", "dutyAttrDetailCode": "369_4", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "id": "b364263e-349f-11ed-a979-fe9982256455", "dutyAttrDetailValue": "60", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "六级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "attrColumnNo": "0", "dutyAttrInfoId": "e05ebcf8c273431d9be46332ceabbb3a", "dutyAttrDetailCode": "369_5", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "id": "b3645e91-349f-11ed-a979-fe9982256455", "dutyAttrDetailValue": "50", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "七级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "attrColumnNo": "0", "dutyAttrInfoId": "e05ebcf8c273431d9be46332ceabbb3a", "dutyAttrDetailCode": "369_6", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "id": "b3649550-349f-11ed-a979-fe9982256455", "dutyAttrDetailValue": "40", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "八级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "attrColumnNo": "0", "dutyAttrInfoId": "e05ebcf8c273431d9be46332ceabbb3a", "dutyAttrDetailCode": "369_7", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "id": "b364c7ae-349f-11ed-a979-fe9982256455", "dutyAttrDetailValue": "30", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "九级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "attrColumnNo": "0", "dutyAttrInfoId": "e05ebcf8c273431d9be46332ceabbb3a", "dutyAttrDetailCode": "369_8", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "id": "b365370c-349f-11ed-a979-fe9982256455", "dutyAttrDetailValue": "20", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "十级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "attrColumnNo": "0", "dutyAttrInfoId": "e05ebcf8c273431d9be46332ceabbb3a", "dutyAttrDetailCode": "369_9", "createdDate": "2022-09-15 10:40:24", "createdBy": "TEST", "id": "b3657212-349f-11ed-a979-fe9982256455", "dutyAttrDetailValue": "10", "dutyInfoId": "558755e769594a6bb5af626bcf0c6006", "dutyAttributeDetailUnit": "%"}], "dutyInfoId": "558755e769594a6bb5af626bcf0c6006"}], "createdBy": "TEST", "performanceRate": 1.0, "planInfoId": "01b4b1ca04a34a6c974ea9967426d33d"}, {"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.000333, "discount": 100, "dutyCode": "CVAA011", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "dutySharedAmountMerge": "CVAA011,CVAA012", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "04", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "住院医疗费用QUQ", "createdBy": "TEST", "dutyDetailCode": "DDQUQ01", "limitAmount": 0.6, "secondTypeName": "", "id": "8241445c276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "34b4ecc18e20472bb9aba55e30b9be34", "effectiveDate": "2022-03-17 14:51:25"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "04", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "住院医疗费用ZYQ", "createdBy": "TEST", "dutyDetailCode": "DDZYQ01", "limitAmount": 0.4, "secondTypeName": "", "id": "82416f79276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "34b4ecc18e20472bb9aba55e30b9be34", "effectiveDate": "2022-03-11 14:25:01"}], "isImportName": "", "isDutySharedAmount": "0", "totalInsuredAmount": 30000, "totalValueAddedTax": 0, "id": "34b4ecc18e20472bb9aba55e30b9be34", "totalStandardPremium": 10, "insuredAmount": 30000, "saleRate": 1, "updatedBy": "c", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX住院医疗费用", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "免赔额", "createdBy": "TEST", "dutyAttrCode": "271", "dutyAttrAmountValue": "200", "policyNo": "1232010000000000708", "id": "918c821ab8734afabe959dccc808e807", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "34b4ecc18e20472bb9aba55e30b9be34"}, {"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "赔付比例", "createdBy": "TEST", "dutyAttrCode": "365", "dutyAttrAmountValue": "78", "policyNo": "1232010000000000708", "id": "1ea5b351ccb544cb82890a597368c5bd", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "34b4ecc18e20472bb9aba55e30b9be34"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}, {"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.001, "discount": 100, "dutyCode": "CVAA007", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "07", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "一般定额明细", "createdBy": "TEST", "dutyDetailCode": "DD00036", "limitAmount": 1, "secondTypeName": "", "id": "8239c3ad276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "4d6d7f99fd104e668148594da477e66e", "effectiveDate": "2022-06-14 15:16:27"}], "isImportName": "", "isDutySharedAmount": "0", "totalInsuredAmount": 10000, "totalValueAddedTax": 0, "id": "4d6d7f99fd104e668148594da477e66e", "totalStandardPremium": 10, "insuredAmount": 10000, "saleRate": 1, "updatedBy": "TEST", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX一般定额", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "等待期", "createdBy": "TEST", "dutyAttrCode": "174", "dutyAttrAmountValue": "2", "policyNo": "1232010000000000708", "id": "dc2670a7db34466bb7158f96f2694d89", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "4d6d7f99fd104e668148594da477e66e"}, {"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "是否区分社保", "createdBy": "TEST", "dutyAttrCode": "360", "dutyAttrAmountValue": "1", "policyNo": "1232010000000000708", "id": "5acdd0be34624a6995c0f5b57a83f9ed", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "4d6d7f99fd104e668148594da477e66e"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}, {"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.001, "discount": 100, "dutyCode": "CVAA006", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "05", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "意外伤害津贴", "createdBy": "TEST", "dutyDetailCode": "DDF0001", "limitAmount": 0.5, "secondTypeName": "", "id": "8237c7e0276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "6f589e8045a54296956f77ad3f029d84", "effectiveDate": "2015-05-29 10:55:08"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "05", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "意外住院津贴", "createdBy": "TEST", "dutyDetailCode": "DDF0003", "limitAmount": 0.2, "secondTypeName": "", "id": "8237efe8276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "6f589e8045a54296956f77ad3f029d84", "effectiveDate": "2015-05-29 10:55:08"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "05", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "医疗误工津贴", "createdBy": "TEST", "dutyDetailCode": "DDF0004", "limitAmount": 0.1, "secondTypeName": "", "id": "82381661276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "6f589e8045a54296956f77ad3f029d84", "effectiveDate": "2015-05-29 10:55:08"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "05", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "住院护理津贴", "createdBy": "TEST", "dutyDetailCode": "DDF0005", "limitAmount": 0.1, "secondTypeName": "", "id": "82383c87276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "6f589e8045a54296956f77ad3f029d84", "effectiveDate": "2015-05-29 10:55:08"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "05", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "疾病住院津贴", "createdBy": "TEST", "dutyDetailCode": "DDF0006", "limitAmount": 0.1, "secondTypeName": "", "id": "82386235276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "6f589e8045a54296956f77ad3f029d84", "effectiveDate": "2015-05-29 10:55:08"}], "isImportName": "", "isDutySharedAmount": "0", "totalInsuredAmount": 10000, "totalValueAddedTax": 0, "id": "6f589e8045a54296956f77ad3f029d84", "totalStandardPremium": 10, "insuredAmount": 10000, "saleRate": 1, "updatedBy": "TEST", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX津贴", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "免赔天数", "createdBy": "TEST", "dutyAttrCode": "14", "dutyAttrAmountValue": "2", "policyNo": "1232010000000000708", "id": "7b1fa5224e794c1c832fd1007726b278", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "6f589e8045a54296956f77ad3f029d84"}, {"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "每人每日住院津贴额", "createdBy": "TEST", "dutyAttrCode": "117", "dutyAttrAmountValue": "100", "policyNo": "1232010000000000708", "id": "b1cb40405d1947aa95d31843000efc1d", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "6f589e8045a54296956f77ad3f029d84"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}, {"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.0002, "discount": 100, "dutyCode": "CVAA009", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "03", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "重大疾病", "createdBy": "TEST", "dutyDetailCode": "DD0110A", "limitAmount": 1, "secondTypeName": "", "id": "823e7093276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "80a8d694a99d4c3f9c6f5c94c487580e", "effectiveDate": "2018-04-27 23:04:03"}], "isImportName": "", "isDutySharedAmount": "1", "totalInsuredAmount": 50000, "totalValueAddedTax": 0, "id": "80a8d694a99d4c3f9c6f5c94c487580e", "totalStandardPremium": 10, "insuredAmount": 50000, "saleRate": 1, "updatedBy": "TEST", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX重大疾病", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "等待期", "createdBy": "TEST", "dutyAttrCode": "174", "dutyAttrAmountValue": "3", "policyNo": "1232010000000000708", "id": "1eddea8915f044d199315ac2d3e1be80", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "80a8d694a99d4c3f9c6f5c94c487580e"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}, {"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.000333, "discount": 100, "dutyCode": "CVAA012", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "dutySharedAmountMerge": "CVAA011,CVAA012", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "04", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "门诊手术医疗费用QUQ", "createdBy": "TEST", "dutyDetailCode": "DDQUQ04", "limitAmount": 0.8, "secondTypeName": "", "id": "8242c80e276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "effectiveDate": "2022-03-17 15:08:19"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "04", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "OC特殊门诊医疗费用", "createdBy": "TEST", "dutyDetailCode": "DD00158", "limitAmount": 0.2, "secondTypeName": "", "id": "8242ee3c276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "effectiveDate": "2022-08-08 15:59:56"}], "isImportName": "", "isDutySharedAmount": "1", "totalInsuredAmount": 30000, "totalValueAddedTax": 0, "id": "9b2823546cba42f791758c08d1f1dc25", "totalStandardPremium": 10, "insuredAmount": 30000, "saleRate": 1, "updatedBy": "TEST", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX门诊医疗费用", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "免赔额", "createdBy": "TEST", "dutyAttrCode": "271", "dutyAttrAmountValue": "250", "policyNo": "1232010000000000708", "id": "0cc03c9090ef43e69594252eb4610837", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25"}, {"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "赔付比例类型", "createdBy": "TEST", "dutyAttrCode": "364", "dutyAttrAmountValue": "2", "policyNo": "1232010000000000708", "id": "bbcf057b524a4337853791b0650736f8", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25"}, {"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "非标准级距赔付比例", "createdBy": "TEST", "dutyAttrCode": "366", "dutyAttrAmountValue": "", "policyNo": "1232010000000000708", "id": "c04ecf39a8b8411d8fbeff28f89fca2a", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [{"updatedBy": "TEST", "attrRowNo": "1", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "1", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815634ff-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "0", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "1", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "2", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8156b65b-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "1001", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "1", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "3", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8157c0bc-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "5001", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "1", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "4", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815869a3-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "8001", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "1", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "5", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815ef9bc-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "10000", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "2", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "1", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8156604b-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "1000", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "2", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "2", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8156e0f8-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "5000", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "2", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "3", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8157eebe-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "8000", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "2", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "4", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815a2679-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "9999", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "2", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "5", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "81626664-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "50000", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "3", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "1", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "81568cb7-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "25", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "3", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "2", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815777dc-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "45", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "3", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "3", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "81581988-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "65", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "3", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "4", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815d2fbd-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "85", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "3", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "5", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8165e7f0-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "99", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}], "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}, {"discountProportion": 1.0, "commissionRate": 1.0, "premiumRate": 0.01, "discount": 100.0, "dutyCode": "CVAC016", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "dutyDetailInfoList": [{"updatedBy": "TESTUWSZB", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "06", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-13 20:00:49", "dutyDetailName": "服务CL", "createdBy": "TESTUWSZB", "dutyDetailCode": "DD00366", "secondTypeName": "", "id": "b55c4d9d335b11eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "5d5ddaa0b50c43b1a222f3397288d0f4", "effectiveDate": "2022-08-06 20:18:22"}], "isImportName": "", "isDutySharedAmount": "0", "totalInsuredAmount": 30000.0, "totalValueAddedTax": 0.0, "id": "5d5ddaa0b50c43b1a222f3397288d0f4", "totalStandardPremium": 300.0, "insuredAmount": 10000.0, "saleRate": 1.0, "updatedBy": "TEST", "riskDutyRelationInfoList": [{"updatedBy": "TEST", "riskGroupId": "452cbe59385848869b55f354d3a90d89", "dutyCode": "CVAC016", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "insuranceEndDate": "2023-09-13 23:59:59", "riskInfoId": "35841848fe88ae7252f332b635ba302d", "agreePremium": 100.0, "totalInsuredAmount": 10000.0, "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "archiveDate": "2023-09-13 00:00:00", "standardPremium": 100.0, "id": "1569656676221263872", "insuredAmount": 10000.0, "actualPremium": 100.0, "dutyInfoId": "5d5ddaa0b50c43b1a222f3397288d0f4", "insuranceBeginDate": "2022-09-14 00:00:00"}, {"updatedBy": "TEST", "riskGroupId": "452cbe59385848869b55f354d3a90d89", "dutyCode": "CVAC016", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "insuranceEndDate": "2023-09-13 23:59:59", "riskInfoId": "454e17fe3923bb2b0571d0671921e74e", "agreePremium": 100.0, "totalInsuredAmount": 10000.0, "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "archiveDate": "2023-09-13 00:00:00", "standardPremium": 100.0, "id": "1569656676229652480", "insuredAmount": 10000.0, "actualPremium": 100.0, "dutyInfoId": "5d5ddaa0b50c43b1a222f3397288d0f4", "insuranceBeginDate": "2022-09-14 00:00:00"}, {"updatedBy": "TEST", "riskGroupId": "452cbe59385848869b55f354d3a90d89", "dutyCode": "CVAC016", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "insuranceEndDate": "2023-09-13 23:59:59", "riskInfoId": "d9e77c09f714e78fce89a6caa1b073a9", "agreePremium": 100.0, "totalInsuredAmount": 10000.0, "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "archiveDate": "2023-09-13 00:00:00", "standardPremium": 100.0, "id": "1569656676233846784", "insuredAmount": 10000.0, "actualPremium": 100.0, "dutyInfoId": "5d5ddaa0b50c43b1a222f3397288d0f4", "insuranceBeginDate": "2022-09-14 00:00:00"}], "endorseNo": "", "totalActualPremium": 300.0, "dutyDesc": "", "planCode": "PL0300207", "dutyName": "服务CL", "totalVATExcludedPremium": 300.0, "createdDate": "2022-09-13 20:00:49", "totalAgreePremium": 300.0, "dutyAttributeInfoList": [{"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "每次事故赔偿天数", "createdBy": "TEST", "dutyAttrCode": "25", "dutyAttrAmountValue": "90", "policyNo": "1232010000000000708", "id": "f7ed8f1918bd439d9795ae66cf23cbc3", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "5d5ddaa0b50c43b1a222f3397288d0f4"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "等待期", "createdBy": "TEST", "dutyAttrCode": "174", "dutyAttrAmountValue": "30", "policyNo": "1232010000000000708", "id": "723afedd616d46748c2d9e2e8329a866", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "5d5ddaa0b50c43b1a222f3397288d0f4"}], "createdBy": "TEST", "performanceRate": 1.0, "planInfoId": "6e643954be464d7cad84955316e109fc"}, {"discountProportion": 1.0, "compensationMaxAmount": 200.0, "commissionRate": 1.0, "premiumRate": 0.01, "discount": 100.0, "dutyCode": "CVAC015", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "dutyDetailInfoList": [{"updatedBy": "TESTUWSZB", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "99", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-13 20:00:49", "dutyDetailName": "其他CL", "createdBy": "TESTUWSZB", "dutyDetailCode": "DD00365", "secondTypeName": "", "id": "b522c138335b11eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "effectiveDate": "2022-08-06 20:18:02"}], "isImportName": "", "isDutySharedAmount": "0", "totalInsuredAmount": 30000.0, "totalValueAddedTax": 0.0, "id": "f2b314aef2664dae80fe40b8704100e7", "totalStandardPremium": 300.0, "insuredAmount": 10000.0, "saleRate": 1.0, "updatedBy": "TEST", "riskDutyRelationInfoList": [{"updatedBy": "TEST", "riskGroupId": "452cbe59385848869b55f354d3a90d89", "dutyCode": "CVAC015", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "insuranceEndDate": "2023-09-13 23:59:59", "riskInfoId": "35841848fe88ae7252f332b635ba302d", "agreePremium": 100.0, "totalInsuredAmount": 10000.0, "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "archiveDate": "2023-09-13 00:00:00", "standardPremium": 100.0, "id": "1569656675587923968", "insuredAmount": 10000.0, "actualPremium": 100.0, "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "insuranceBeginDate": "2022-09-14 00:00:00"}, {"updatedBy": "TEST", "riskGroupId": "452cbe59385848869b55f354d3a90d89", "dutyCode": "CVAC015", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "insuranceEndDate": "2023-09-13 23:59:59", "riskInfoId": "454e17fe3923bb2b0571d0671921e74e", "agreePremium": 100.0, "totalInsuredAmount": 10000.0, "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "archiveDate": "2023-09-13 00:00:00", "standardPremium": 100.0, "id": "1569656675596312576", "insuredAmount": 10000.0, "actualPremium": 100.0, "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "insuranceBeginDate": "2022-09-14 00:00:00"}, {"updatedBy": "TEST", "riskGroupId": "452cbe59385848869b55f354d3a90d89", "dutyCode": "CVAC015", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "insuranceEndDate": "2023-09-13 23:59:59", "riskInfoId": "d9e77c09f714e78fce89a6caa1b073a9", "agreePremium": 100.0, "totalInsuredAmount": 10000.0, "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "archiveDate": "2023-09-13 00:00:00", "standardPremium": 100.0, "id": "1569656675600506880", "insuredAmount": 10000.0, "actualPremium": 100.0, "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "insuranceBeginDate": "2022-09-14 00:00:00"}], "endorseNo": "", "totalActualPremium": 300.0, "dutyDesc": "", "planCode": "PL0300207", "dutyName": "其他CL", "totalVATExcludedPremium": 300.0, "createdDate": "2022-09-13 20:00:49", "totalAgreePremium": 300.0, "dutyAttributeInfoList": [{"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "赔偿限额", "createdBy": "TEST", "dutyAttrCode": "6", "dutyAttrAmountValue": "200", "policyNo": "1232010000000000708", "id": "f6a15ff0bd2b4444af93f8ae41dfe406", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "赔偿限额类型", "createdBy": "TEST", "dutyAttrCode": "12", "dutyAttrAmountValue": "0", "policyNo": "1232010000000000708", "id": "c055cc93bbff423796af345803129e6d", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "免赔天数", "createdBy": "TEST", "dutyAttrCode": "14", "dutyAttrAmountValue": "5", "policyNo": "1232010000000000708", "id": "fd257098c9724b9bb6ac6627ace08378", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "累计赔偿限额", "createdBy": "TEST", "dutyAttrCode": "16", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "d4c9d414617043f1b3c6eff7c2f40cc6", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "每次事故赔偿限额", "createdBy": "TEST", "dutyAttrCode": "17", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "bdb191f46faf4ef88f745032bdcb860c", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "每次事故财产损失赔偿限额", "createdBy": "TEST", "dutyAttrCode": "18", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "9dd9f4fff17840a88747aafd277ee02f", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "每次事故赔偿天数", "createdBy": "TEST", "dutyAttrCode": "25", "dutyAttrAmountValue": "10", "policyNo": "1232010000000000708", "id": "edf9508877d445f5b7bf98e84e0a387e", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "每人赔偿限额", "createdBy": "TEST", "dutyAttrCode": "42", "dutyAttrAmountValue": "200", "policyNo": "1232010000000000708", "id": "12eb443b7dff47a7ae0c349451cff3d6", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "每人人身伤亡赔偿限额", "createdBy": "TEST", "dutyAttrCode": "50", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "2d37420cbc3a496f90f75be2b5c960aa", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "每次事故免赔额", "createdBy": "TEST", "dutyAttrCode": "53", "dutyAttrAmountValue": "200", "policyNo": "1232010000000000708", "id": "d3372b59ddfd486c99d2db5230b1390a", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "每人每日住院津贴额", "createdBy": "TEST", "dutyAttrCode": "117", "dutyAttrAmountValue": "200", "policyNo": "1232010000000000708", "id": "ccf8c564ea4743bc83fda56bbcdd234e", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "每次赔偿限额", "createdBy": "TEST", "dutyAttrCode": "151", "dutyAttrAmountValue": "1000", "policyNo": "1232010000000000708", "id": "18f26e9e0fe04e1b9293558c72036b53", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "等待期", "createdBy": "TEST", "dutyAttrCode": "174", "dutyAttrAmountValue": "60", "policyNo": "1232010000000000708", "id": "2caecd0540924adba3afd7187e90cf5c", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "免赔额", "createdBy": "TEST", "dutyAttrCode": "271", "dutyAttrAmountValue": "400", "policyNo": "1232010000000000708", "id": "0cdbed51ae424a9dbb4069ca3c9d7e4d", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "免赔率", "createdBy": "TEST", "dutyAttrCode": "272", "dutyAttrAmountValue": "1", "policyNo": "1232010000000000708", "id": "9dfbea1995944d3094a91664bd81d34b", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "是否区分社保", "createdBy": "TEST", "dutyAttrCode": "360", "dutyAttrAmountValue": "0", "policyNo": "1232010000000000708", "id": "25ef45759c13475dbb9bddf4870a8c85", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "有社保", "createdBy": "TEST", "dutyAttrCode": "361", "dutyAttrAmountValue": "", "policyNo": "1232010000000000708", "id": "e99cdc3d74574b2aab5cd5a7f6758d89", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [{"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "免赔额", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "1", "dutyAttrInfoId": "e99cdc3d74574b2aab5cd5a7f6758d89", "dutyAttrDetailCode": "361_1000", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "559fad9f-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "200", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "免赔类型", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "1", "dutyAttrInfoId": "e99cdc3d74574b2aab5cd5a7f6758d89", "dutyAttrDetailCode": "361_1001", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "559fef3b-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "0", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "每次免赔限额", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "1", "dutyAttrInfoId": "e99cdc3d74574b2aab5cd5a7f6758d89", "dutyAttrDetailCode": "361_1002", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a01ab6-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "200", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "赔付比例类型", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "1", "dutyAttrInfoId": "e99cdc3d74574b2aab5cd5a7f6758d89", "dutyAttrDetailCode": "361_1003", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a04aa6-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "3", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "经医保结算赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "e99cdc3d74574b2aab5cd5a7f6758d89", "dutyAttrDetailCode": "361_4000", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a074d8-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "30", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "未经医保结算赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "e99cdc3d74574b2aab5cd5a7f6758d89", "dutyAttrDetailCode": "361_4001", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a09fe8-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "40", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "无社保", "createdBy": "TEST", "dutyAttrCode": "362", "dutyAttrAmountValue": "", "policyNo": "1232010000000000708", "id": "87076918c4af4f8ab94e9fc316629b7b", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [{"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "免赔额", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "1", "dutyAttrInfoId": "87076918c4af4f8ab94e9fc316629b7b", "dutyAttrDetailCode": "362_1000", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a0f400-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "200", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "免赔类型", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "1", "dutyAttrInfoId": "87076918c4af4f8ab94e9fc316629b7b", "dutyAttrDetailCode": "362_1001", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a11d88-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "1", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "每次免赔限额", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "1", "dutyAttrInfoId": "87076918c4af4f8ab94e9fc316629b7b", "dutyAttrDetailCode": "362_1002", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a145a4-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "200", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "赔付比例类型", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "1", "dutyAttrInfoId": "87076918c4af4f8ab94e9fc316629b7b", "dutyAttrDetailCode": "362_1003", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a16dab-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "4", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "医保目录内费用经医保结算赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "87076918c4af4f8ab94e9fc316629b7b", "dutyAttrDetailCode": "362_5000", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a19883-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "1", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "医保目录内费用未经医保结算赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "87076918c4af4f8ab94e9fc316629b7b", "dutyAttrDetailCode": "362_5001", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a2fee7-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "2", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "医保目录外费用经医保结算赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "87076918c4af4f8ab94e9fc316629b7b", "dutyAttrDetailCode": "362_5002", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a33841-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "3", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "医保目录外费用未经医保结算赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "87076918c4af4f8ab94e9fc316629b7b", "dutyAttrDetailCode": "362_5003", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a36545-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "4", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "免赔类型", "createdBy": "TEST", "dutyAttrCode": "363", "dutyAttrAmountValue": "1", "policyNo": "1232010000000000708", "id": "0f30cf03dafb449eac371fe1f3a3f08e", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "赔付比例类型", "createdBy": "TEST", "dutyAttrCode": "364", "dutyAttrAmountValue": "1", "policyNo": "1232010000000000708", "id": "e4dd5d00d80c4dc2a758bd8639c54f0d", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "赔付比例", "createdBy": "TEST", "dutyAttrCode": "365", "dutyAttrAmountValue": "20", "policyNo": "1232010000000000708", "id": "573a025ba43c462eb891846f52275e7f", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "就诊医院赔付比例", "createdBy": "TEST", "dutyAttrCode": "367", "dutyAttrAmountValue": "", "policyNo": "1232010000000000708", "id": "cfbb46daea55474bb622891a285503e0", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [{"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "一级医院赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "cfbb46daea55474bb622891a285503e0", "dutyAttrDetailCode": "367_0", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a43503-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "1", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "二级医院赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "cfbb46daea55474bb622891a285503e0", "dutyAttrDetailCode": "367_1", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a461fe-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "2", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "三级医院赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "cfbb46daea55474bb622891a285503e0", "dutyAttrDetailCode": "367_2", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a48f7f-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "3", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "四级医院赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "cfbb46daea55474bb622891a285503e0", "dutyAttrDetailCode": "367_3", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a4ba13-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "4", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "伤残标准类型", "createdBy": "TEST", "dutyAttrCode": "368", "dutyAttrAmountValue": "0", "policyNo": "1232010000000000708", "id": "4aa824a94de94d1c9098b09c0a75abe8", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "残疾对应赔付比例", "createdBy": "TEST", "dutyAttrCode": "369", "policyNo": "1232010000000000708", "id": "6944d9b50d404f1b8f2d103d6ea84116", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [{"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "一级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "6944d9b50d404f1b8f2d103d6ea84116", "dutyAttrDetailCode": "369_0", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a542f9-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "100", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "二级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "6944d9b50d404f1b8f2d103d6ea84116", "dutyAttrDetailCode": "369_1", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a56f09-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "90", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "三级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "6944d9b50d404f1b8f2d103d6ea84116", "dutyAttrDetailCode": "369_2", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a598d0-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "80", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "四级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "6944d9b50d404f1b8f2d103d6ea84116", "dutyAttrDetailCode": "369_3", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a5c491-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "70", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "五级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "6944d9b50d404f1b8f2d103d6ea84116", "dutyAttrDetailCode": "369_4", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a5ed9c-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "60", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "六级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "6944d9b50d404f1b8f2d103d6ea84116", "dutyAttrDetailCode": "369_5", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a61aa8-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "50", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "七级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "6944d9b50d404f1b8f2d103d6ea84116", "dutyAttrDetailCode": "369_6", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a64d40-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "40", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "八级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "6944d9b50d404f1b8f2d103d6ea84116", "dutyAttrDetailCode": "369_7", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a68ffd-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "30", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "九级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "6944d9b50d404f1b8f2d103d6ea84116", "dutyAttrDetailCode": "369_8", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a6bced-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "20", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "十级伤残赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "6944d9b50d404f1b8f2d103d6ea84116", "dutyAttrDetailCode": "369_9", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55a6e788-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "10", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "一类职业每人死亡伤残赔偿限额", "createdBy": "TEST", "dutyAttrCode": "412", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "b61ff867999f422391ff8d45e1f8bcac", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "二类职业每人死亡伤残赔偿限额", "createdBy": "TEST", "dutyAttrCode": "413", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "3c26a5af94cc4292ae64f40341951c87", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "三类职业每人死亡伤残赔偿限额", "createdBy": "TEST", "dutyAttrCode": "414", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "4765f5a2a7994eebbe9a2ec312ee7a50", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "四类职业每人死亡伤残赔偿限额", "createdBy": "TEST", "dutyAttrCode": "415", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "977cf3a6007f4c5982dd81fe88a11a22", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "五类职业每人死亡伤残赔偿限额", "createdBy": "TEST", "dutyAttrCode": "416", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "ec895fe9a91246288078074d36da9777", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "六类职业每人死亡伤残赔偿限额", "createdBy": "TEST", "dutyAttrCode": "417", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "8f0ea50f4f924d0fab72b9b6f7f30190", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "一类职业每人医疗费用赔偿限额", "createdBy": "TEST", "dutyAttrCode": "418", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "9b9bfaede7a94c7aa42568ca63fe8b14", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "二类职业每人医疗费用赔偿限额", "createdBy": "TEST", "dutyAttrCode": "419", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "cefece6060bc40539a3e5b276d0b27ab", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "三类职业每人医疗费用赔偿限额", "createdBy": "TEST", "dutyAttrCode": "420", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "92e9a835ff904322bb588ea80d5a5f11", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "四类职业每人医疗费用赔偿限额", "createdBy": "TEST", "dutyAttrCode": "421", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "4caaf610cf484f64965212bd08af9428", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "五类职业每人医疗费用赔偿限额", "createdBy": "TEST", "dutyAttrCode": "422", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "f75adc3df06244c0bcf84d0c7bd3d2fc", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "六类职业每人医疗费用赔偿限额", "createdBy": "TEST", "dutyAttrCode": "423", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "23a40ba1edb04a919c3ee9b68ec36700", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "赔付次数", "createdBy": "TEST", "dutyAttrCode": "541", "dutyAttrAmountValue": "3", "policyNo": "1232010000000000708", "id": "a3386d1b5c0c4e05b35f6d587926c054", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "重大疾病种类数量", "createdBy": "TEST", "dutyAttrCode": "542", "dutyAttrAmountValue": "0", "policyNo": "1232010000000000708", "id": "357fb9f8966143188db3bcf93de9b443", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "最高给付天数", "createdBy": "TEST", "dutyAttrCode": "543", "dutyAttrAmountValue": "30", "policyNo": "1232010000000000708", "id": "935461b6f45d4c2980c53463439f1306", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "是否共享免赔", "createdBy": "TEST", "dutyAttrCode": "549", "dutyAttrAmountValue": "0", "policyNo": "1232010000000000708", "id": "62c5581a78c44fe0808a84e86e159f4e", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "每人每次医疗费用限额", "createdBy": "TEST", "dutyAttrCode": "616", "dutyAttrAmountValue": "2000", "policyNo": "1232010000000000708", "id": "87f16bda405c452d8756b1ccd440b59f", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "每月赔付次数", "createdBy": "TEST", "dutyAttrCode": "617", "dutyAttrAmountValue": "5", "policyNo": "1232010000000000708", "id": "066c5a0d2ecb474b9df2daf7d4f9823c", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "处方药赔偿范围", "createdBy": "TEST", "dutyAttrCode": "618", "dutyAttrAmountValue": "1", "policyNo": "1232010000000000708", "id": "863300fe01844c8e9cbcdaf6ffa833b8", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "慢性病赔偿范围", "createdBy": "TEST", "dutyAttrCode": "619", "dutyAttrAmountValue": "1", "policyNo": "1232010000000000708", "id": "4bb4efd450cf4f7ab82b1b4485db5bdb", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "购药渠道", "createdBy": "TEST", "dutyAttrCode": "620", "dutyAttrAmountValue": "1", "policyNo": "1232010000000000708", "id": "7a1122a3ee5f4c57820231a3693f1164", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "医疗机构范围对应给付比例", "createdBy": "TEST", "dutyAttrCode": "621", "policyNo": "1232010000000000708", "id": "c591ebdb4c2e4b9b9dc611635b1f7f3e", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [{"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "仅限二级及以上公立医院赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "c591ebdb4c2e4b9b9dc611635b1f7f3e", "dutyAttrDetailCode": "621_0", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55abe7e7-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "1", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "包括一级医院赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "c591ebdb4c2e4b9b9dc611635b1f7f3e", "dutyAttrDetailCode": "621_1", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55ac1230-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "2", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "包括公立医院特需部赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "c591ebdb4c2e4b9b9dc611635b1f7f3e", "dutyAttrDetailCode": "621_2", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55ac3bcd-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "3", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "包括境外医疗机构赔付比例", "policyNo": "1232010000000000708", "updatedDate": "2022-09-13 20:00:49", "attrColumnNo": "0", "dutyAttrInfoId": "c591ebdb4c2e4b9b9dc611635b1f7f3e", "dutyAttrDetailCode": "621_3", "createdDate": "2022-09-13 20:00:49", "createdBy": "TEST", "id": "55ac64a6-335b-11ed-a979-fe9982256455", "dutyAttrDetailValue": "4", "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7", "dutyAttributeDetailUnit": "%"}], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}, {"createdDate": "2022-09-13 20:00:49", "updatedBy": "TEST", "dutyAttributeName": "疾病种类数量", "createdBy": "TEST", "dutyAttrCode": "622", "dutyAttrAmountValue": "20", "policyNo": "1232010000000000708", "id": "3bba28a7abba432f9c94eeead2c4683b", "updatedDate": "2022-09-13 20:00:49", "dutyAttrDetailList": [], "dutyInfoId": "f2b314aef2664dae80fe40b8704100e7"}], "createdBy": "TEST", "performanceRate": 1.0, "planInfoId": "6e643954be464d7cad84955316e109fc"}], "id": "01b4b1ca04a34a6c974ea9967426d33d", "totalStandardPremium": 1000.0}], "archiveDate": "2023-09-15 00:00:00", "applyRiskNum": 1, "id": "b8387485817449a0a2b25d11b7323624", "totalStandardPremium": 1000.0, "combinedProductCode": "MP03100034", "updatedBy": "TEST", "productClass": "03", "endorseNo": "", "totalActualPremium": 1000.0, "applyNum": 1.0, "createdDate": "2022-09-15 10:40:24", "totalAgreePremium": 1000.0, "createdBy": "TEST", "riskGroupName": "方案一", "riskPersonInfoList": [{"birthday": "2000-02-11 00:00:00", "personnelAttribute": "100", "linkModeType": "03", "city": "", "county": "", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:24", "relationshipWithApplicant": "1", "province": "", "id": "5182bd84627331e6ab4f1fa3b815dc16", "totalStandardPremium": 1000.0, "pregnancyWeek": 0, "riskPersonNo": "1", "updatedBy": "TEST", "riskGroupId": "b8387485817449a0a2b25d11b7323624", "address": "上海浦东唐镇", "virtualInsuredNum": 0, "endorseNo": "", "totalActualPremium": 1000.0, "certificateIssueDate": "2022-09-01 00:00:00", "personnelCode": "CL0000000001550", "vehicleNum": 0, "certificateNo": "23213333332332", "sexCode": "F", "createdDate": "2022-09-15 10:40:24", "mobileTelephone": "13112341234", "totalAgreePremium": 1000.0, "createdBy": "TEST", "riskDetail": "{\"isRenewal\":\"0\"}", "name": "未来", "certificateValidDate": "2022-09-30 00:00:00", "beneficaryInfoList": [], "masterMark": "1", "age": 22, "certificateType": "02"}], "riskGroupType": "01", "productPackageType": "PK00001151", "shareInfoList": [{"createdDate": "2022-09-15 10:40:24", "updatedBy": "TEST", "riskGroupId": "b8387485817449a0a2b25d11b7323624", "createdBy": "TEST", "dutyAttrCode": "riskShareAmount", "endorseNo": "", "dutyAttrAmountValue": 100000.0, "dutyAttrRateValue": 1.0, "policyNo": "1232010000000000708", "id": "602ffb596e1e44ec9c5600ad95046a46", "updatedDate": "2022-09-15 10:40:24", "groupCode": "rsaCode"}]}], "payInfoList": [{"paymentPersonName": "未嗯", "noticeNo": "", "policyNo": "1232010000000000708", "updatedDate": "2022-09-15 10:40:02", "baseInfoId": "1570241001627127808", "installmentType": "0", "paymentItemName": "0", "agreePremium": 1000.0, "paymentEndDate": "2022-09-15 23:59:59", "paymentItem": "0", "comExchangeRate": 1.0, "currencyName": "", "archiveDate": "2023-09-15 00:00:00", "statusName": "收款", "noticeStatus": "", "id": "035c329730d14c6fa0d10377278627f4", "updatedBy": "TEST", "isInstallment": "0", "payerTypeName": "", "createdDate": "2022-09-15 10:40:02", "createdBy": "TEST", "termNo": 1, "paymentBeginDate": "2022-09-15 00:00:00", "billTypeName": "", "currencyCode": "CNY", "defComExchangeRate": 1.0, "status": "01"}], "accountInfoList": []}, "hideBusinessDetail": false, "commissionCharge": "0.00", "commissionChargeProportion": "0.00%", "hideEmployeeInfo": false, "technicProductCode": "TP0300116", "isElecSubPolicyNo": "0", "endorseSceneInfoDTO": {"plyPrintTimes": 0, "edrPrintTimes": 0}, "saleChannelSourceName": "", "isShowCommission": true, "hideChannelInfo": false, "delLicenceWhippletre": false, "hideSaleGroupName": false, "saleDepartmentCode": "**********", "isShowCommissionInter": true, "showPremium": false}