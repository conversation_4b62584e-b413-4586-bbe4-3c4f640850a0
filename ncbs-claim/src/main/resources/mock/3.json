{"saleDepartmentName": "江苏", "notShowFileForPolicy": false, "hideChannelInfoPas": false, "contractDTO": {"baseInfo": {"departmentCode": "**********", "discount": 1.0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "acceptInsuranceDate": "2022-09-03 16:04:19", "productName": "ZJ团体重大疾病保险2021版WH", "quotationNo": "Q000000005104", "rateTotal": 30.0, "insuranceDate": "2022-09-03 15:51:44", "contractPactCode": "Pd0010538", "applyPolicyNo": "5**********00005521", "totalInsuredAmount": 330000.0, "productVersion": "1.01", "exchangeRate": 1.0, "businessTypeName": "团体", "underwriteName": "TESTUWSZB1", "inputByName": "测试admin2", "id": "1565973782751678464", "coinsuranceMark": "2", "shareholdersFlag": "0", "saleAmount": 330.0, "saleRate": 10.0, "updatedBy": "TESTUWSZB1", "productClass": "19", "payTermNo": 1, "endorseNo": "1**********00000445", "performanceAmount": 330.0, "inputDate": "2022-09-03 16:03:37", "quotationDate": "2022-09-03 16:01:07", "totalVATExcludedPremium": 3300.0, "productKind": "0", "isAccommodationName": "", "totalAgreePremium": 3300.0, "performanceRate": 10.0, "insuredTotalNum": 3, "insuranceLimit": "58天", "dataSource": "NCBS_NBS", "commissionAmount": 330.0, "status": "B5", "commissionRate": 10.0, "isRoundName": "", "inputBy": "admin2", "insuranceEndDate": "2022-10-31 23:59:59", "quotationName": "团体重大疾病保险2021版WH", "totalValueAddedTax": 0.0, "archiveDate": "2022-10-31 00:00:00", "totalStandardPremium": 3300.0, "renewalTypeName": "", "premiumCurrencyCode": "01", "carBusinessType": "0", "systemId": "06", "totalActualPremium": 3300.0, "renewalType": "0", "operationTypeName": "", "amountCurrencyCode": "01", "createdDate": "2022-09-03 16:04:20", "productCode": "MP19000061", "createdBy": "admin2", "underwriteDate": "2022-09-03 16:04:16", "dayNum": "-1天", "amountTotal": 990.0, "discountPermium": 3300.0, "endorsementTimes": 0, "formatTypeName": "", "businessType": "2", "insuranceBeginDate": "2022-09-04 00:00:00"}, "specialPromiseList": [], "endorseNo": "1**********00000445", "policyNo": "1**********00000445", "attachmentGroupList": [{"documentInfoList": [{"documentType": "OT99", "uploadPath": "http://stg.iobs.paic.com.cn/download/life-image-sf-stg/b74f3af4155b43fab00578ff8189b4bd?attname=b74f3af4155b43fab00578ff8189b4bd.xls", "documentName": "0000105411662192024148.xls", "documentSize": "559.00", "documentOrder": "1", "documentClass": "T99", "url": "http://stg.iobs.paic.com.cn/download/life-image-sf-stg/b74f3af4155b43fab00578ff8189b4bd?attname=b74f3af4155b43fab00578ff8189b4bd.xls", "uploadDate": "2022-09-03 16:00:24", "uploadIsDate": "2022-09-03 16:00:24", "documentId": "20010543", "documentStatus": "Y", "documentFormat": "xls", "uploadPersonnel": "admin2", "originName": "0000105411662192024148.xls"}], "createdDate": "2022-09-03 16:04:20", "updatedBy": "admin2", "documentGroupId": "00010542", "documentGroupType": "01", "createdBy": "admin2", "archiveDate": "2022-10-31 00:00:00", "endorseNo": "", "policyNo": "1**********00000445", "id": "4e59644b8cc94024b3d62b61dcdbd1c8", "updatedDate": "2022-09-03 16:04:20", "baseInfoId": "1565973782751678464"}], "extendInfo": {"disputedSettleModeName": "", "updatedBy": "admin2", "isFacultativeBusiness": "0", "isStarHighInsurance": "0", "endorseNo": "", "isGiftInsurance": "0", "validateCode": "56FY4L65UNX/", "applyApproach": "02", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "isTransProtectBusiness": "0", "isPolicyHealthInsurance": "0", "baseInfoId": "1565973782751678464", "isPolicyBeforePayfee": "1", "disputedSettleMode": "1", "createdDate": "2022-09-03 16:04:20", "isPastsign": "0", "createdBy": "admin2", "id": "1565973783754117120", "isVipCustomer": "1", "applyApproachName": "FQS批量出单", "isPrintAgencyInfo": "1"}, "noclaimInfoList": [], "compensationLimitInfoList": [], "insurantInfoList": [], "quotationNo": "Q000000005104", "applyPolicyNo": "5**********00005521", "saleInfo": {"departmentName": "第三方系统财产保险股份有限公司南京市浦口支公司", "updatedBy": "admin2", "endorseNo": "", "brokerInfoList": [], "departmentCode": "**********", "channelSourceCode": "1", "employeeInfoList": [{"saleInfoId": "78c2d0fb40b74d6bbbf6b056b1edac92", "documentCost": 0.0, "employeeName": "黄伟良", "updatedBy": "admin2", "performanceValue1Modify": 0.0, "endorseNo": "", "offlineServiceFee": 0.0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:21", "onlineServiceFee": 0.0, "employeeCode": "244523002", "mainEmployeeFlag": "1", "createdDate": "2022-09-03 16:04:21", "createdBy": "admin2", "marginCharge1": 0.0, "performanceValue2Modify": 0.0, "id": "378cdb31300442f89a7386735e69ab49"}], "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:21", "signDate": "2022-09-03 16:04:21", "agentInfoList": [{"saleInfoId": "78c2d0fb40b74d6bbbf6b056b1edac92", "createdDate": "2022-09-03 16:04:21", "updatedBy": "admin2", "createdBy": "admin2", "agentCode": "U200002006881", "endorseNo": "", "agentName": "微民保险代理有限公司", "policyNo": "1**********00000445", "id": "ccf68984a4be4b96b8d02fe7a1b82b40", "updatedDate": "2022-09-03 16:04:21"}], "baseInfoId": "1565973782751678464", "createdDate": "2022-09-03 16:04:21", "createdBy": "admin2", "channelSourceName": "个人代理", "id": "78c2d0fb40b74d6bbbf6b056b1edac92", "applyDate": "2022-09-03 16:04:21", "partnerInfoList": []}, "rescueServiceList": [], "applicantInfoList": [{"birthday": "1992-01-30 08:00:00", "linkModeType": "03", "city": "150400", "yearlySalaries": 250.0, "county": "150421", "clientNo": "123456", "organizationTypeName": "", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "baseInfoId": "1565973782751678464", "billingDepositBankAccount": "***********", "province": "150000", "applicantType": "1", "sexName": "", "isConfirmName": "0", "id": "e870d20991d84fefbab6b51297d423fb", "personnelTypeName": "个人", "email": "<EMAIL>", "workUnit": "***********", "updatedBy": "TESTUWSZB1", "address": "内蒙古自治区赤峰市阿鲁科尔沁旗", "professionCode": "A0403003", "endorseNo": "", "certificateIssueDate": "2022-09-03 00:00:00", "certificateNo": "830000199201300022", "certificateTypeName": "港澳台居民居住证", "sexCode": "F", "createdDate": "2022-09-03 16:04:20", "mobileTelephone": "***********", "createdBy": "admin2", "personnelType": "1", "isConfirmNotification": "0", "name": "路鸣泽", "certificateValidDate": "9999-12-31 00:00:00", "billingDepositBank": "000003", "age": 30, "certificateType": "09"}], "riskGroupInfoList": [{"updatedBy": "admin2", "productClass": "19", "endorseNo": "", "totalActualPremium": 1100.0, "virtualRiskNum": 0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "applyNum": 1.0, "combinedProductVersion": "1.01", "baseInfoId": "1565973782751678464", "totalInsuredAmount": 330000.0, "createdDate": "2022-09-03 16:04:20", "planInfoList": [{"updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "isMain": "1", "endorseNo": "", "totalActualPremium": 1100.0, "planName": "团体重大疾病保险2021版WH", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "planCode": "PL1900011", "totalVATExcludedPremium": 3300.0, "totalInsuredAmount": 330000.0, "createdDate": "2022-09-03 16:04:20", "totalAgreePremium": 1100.0, "totalValueAddedTax": 0.0, "createdBy": "admin2", "archiveDate": "2022-10-31 00:00:00", "dutyInfoList": [{"commissionRate": 10.0, "premiumRate": 0.003333, "discount": 100.0, "dutyCode": "CVAP003", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "dutyDetailInfoList": [{"updatedBy": "TESTUWSZB1", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "03", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-03 16:04:20", "dutyDetailName": "重度疾病保险金费用WH", "createdBy": "TESTUWSZB1", "dutyDetailCode": "DD00233", "secondTypeName": "", "id": "0427d3012b5f11eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "7240465eee5543c6a1f3dce213a2bc4f", "effectiveDate": "2022-07-14 10:26:25"}], "isImportName": "", "totalInsuredAmount": 300000.0, "totalValueAddedTax": 0.0, "id": "7240465eee5543c6a1f3dce213a2bc4f", "totalStandardPremium": 1000.0, "insuredAmount": 100000.0, "saleRate": 10.0, "updatedBy": "admin2", "riskDutyRelationInfoList": [{"updatedBy": "TESTUWSZB1", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "idRiskClass": "93be0d120ffa4f628b233abd67d16dd6", "classRiskNum": 3, "endorseNo": "", "totalActualPremium": 1000.0, "dutyCode": "CVAP003", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:21", "insuranceEndDate": "2022-10-31 23:59:59", "riskInfoId": "9bc7b8ca93b1485c826e0da6baf5422b", "agreePremium": 0.0, "totalInsuredAmount": 100000.0, "createdDate": "2022-09-03 16:04:21", "createdBy": "TESTUWSZB1", "archiveDate": "2022-10-31 00:00:00", "standardPremium": 0.0, "id": "1565973947290030080", "insuredAmount": 100000.0, "actualPremium": 1100.0, "dutyInfoId": "7240465eee5543c6a1f3dce213a2bc4f", "insuranceBeginDate": "2022-09-04 00:00:00"}], "endorseNo": "", "totalActualPremium": 1000.0, "dutyDesc": "", "dutyName": "重度疾病保险金WH", "totalVATExcludedPremium": 3000.0, "createdDate": "2022-09-03 16:04:20", "totalAgreePremium": 1000.0, "dutyAttributeInfoList": [{"createdDate": "2022-09-03 16:04:21", "updatedBy": "admin2", "dutyAttributeName": "等待期", "createdBy": "admin2", "dutyAttrCode": "174", "dutyAttrAmountValue": "30", "policyNo": "1**********00000445", "id": "93f0859d49f244eba7428ed26be47f51", "updatedDate": "2022-09-03 16:04:21", "dutyInfoId": "7240465eee5543c6a1f3dce213a2bc4f"}], "createdBy": "admin2", "performanceRate": 10.0, "discountPermium": 1000.0, "planInfoId": "fc3ee197f2c54b0ab407f3ef768ff0dc"}, {"commissionRate": 10.0, "premiumRate": 0.003333, "discount": 100.0, "dutyCode": "CVAQ002", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "dutyDetailInfoList": [{"updatedBy": "TESTUWSZB1", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "03", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-03 16:04:20", "dutyDetailName": "轻度疾病保险金费用WH", "createdBy": "TESTUWSZB1", "dutyDetailCode": "DD00227", "secondTypeName": "", "id": "0417b53a2b5f11eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "28ca49bc65e94f2c934dc9216bf5f356", "effectiveDate": "2022-07-14 10:04:50"}], "isImportName": "", "totalInsuredAmount": 30000.0, "totalValueAddedTax": 0.0, "id": "28ca49bc65e94f2c934dc9216bf5f356", "totalStandardPremium": 100.0, "insuredAmount": 10000.0, "saleRate": 10.0, "updatedBy": "admin2", "riskDutyRelationInfoList": [{"updatedBy": "TESTUWSZB1", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "idRiskClass": "93be0d120ffa4f628b233abd67d16dd6", "classRiskNum": 3, "endorseNo": "", "totalActualPremium": 100.0, "dutyCode": "CVAQ002", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "insuranceEndDate": "2022-10-31 23:59:59", "riskInfoId": "9bc7b8ca93b1485c826e0da6baf5422b", "agreePremium": 0.0, "totalInsuredAmount": 10000.0, "createdDate": "2022-09-03 16:04:20", "createdBy": "TESTUWSZB1", "archiveDate": "2022-10-31 00:00:00", "standardPremium": 0.0, "id": "1565973947000623104", "insuredAmount": 10000.0, "actualPremium": 0.0, "dutyInfoId": "28ca49bc65e94f2c934dc9216bf5f356", "insuranceBeginDate": "2022-09-04 00:00:00"}], "endorseNo": "", "totalActualPremium": 100.0, "dutyDesc": "", "dutyName": "轻度疾病保险金WH", "totalVATExcludedPremium": 300.0, "createdDate": "2022-09-03 16:04:20", "totalAgreePremium": 100.0, "dutyAttributeInfoList": [{"createdDate": "2022-09-03 16:04:20", "updatedBy": "admin2", "dutyAttributeName": "等待期", "createdBy": "admin2", "dutyAttrCode": "174", "dutyAttrAmountValue": "30", "policyNo": "1**********00000445", "id": "5ec5ab76e8a240c983779d8af95fb84d", "updatedDate": "2022-09-03 16:04:20", "dutyInfoId": "28ca49bc65e94f2c934dc9216bf5f356"}], "createdBy": "admin2", "performanceRate": 10.0, "discountPermium": 100.0, "planInfoId": "fc3ee197f2c54b0ab407f3ef768ff0dc"}], "id": "fc3ee197f2c54b0ab407f3ef768ff0dc", "totalStandardPremium": 1100.0}], "totalAgreePremium": 1100.0, "createdBy": "admin2", "riskGroupName": "方案一", "archiveDate": "2022-10-31 00:00:00", "applyRiskNum": 3, "riskPersonInfoList": [{"birthday": "1983-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "relationshipWithApplicant": "9", "id": "9bc7b8ca93b1485c826e0da6baf5422b", "totalStandardPremium": 1100.0, "pregnancyWeek": 0, "riskPersonNo": "1", "updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "address": "李大胆", "idRiskClass": "93be0d120ffa4f628b233abd67d16dd6", "virtualInsuredNum": 0, "totalActualPremium": 1100.0, "postcode": "否", "certificateIssueDate": "2022-09-03 00:00:00", "personnelCode": "CL0000000000985", "vehicleNum": 0, "certificateNo": "110101198301019930", "createdDate": "2022-09-03 16:04:20", "mobileTelephone": "15876587698", "totalAgreePremium": 1100.0, "createdBy": "admin2", "name": "李大胆", "isSociaSecurity": "3", "certificateValidDate": "2023-09-03 00:00:00", "beneficaryInfoList": [{"updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "baseInfoId": "1565973782751678464", "createdDate": "2022-09-03 16:04:20", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "9bc7b8ca93b1485c826e0da6baf5422b", "id": "f438f9ec716c4366ab2b3ef351473051"}], "masterMark": "1", "age": 39, "certificateType": "01"}, {"birthday": "1983-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "relationshipWithApplicant": "9", "id": "f631e768657147d0b10e7d1a7cff752a", "totalStandardPremium": 1100.0, "pregnancyWeek": 0, "riskPersonNo": "2", "updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "address": "李虱虱", "idRiskClass": "93be0d120ffa4f628b233abd67d16dd6", "virtualInsuredNum": 0, "totalActualPremium": 1100.0, "postcode": "否", "certificateIssueDate": "2022-09-03 00:00:00", "personnelCode": "CL0000000000986", "vehicleNum": 0, "certificateNo": "110101198301019981", "createdDate": "2022-09-03 16:04:20", "mobileTelephone": "***********", "totalAgreePremium": 1100.0, "createdBy": "admin2", "name": "李虱虱", "isSociaSecurity": "3", "certificateValidDate": "2023-09-03 00:00:00", "beneficaryInfoList": [{"updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "baseInfoId": "1565973782751678464", "createdDate": "2022-09-03 16:04:20", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "f631e768657147d0b10e7d1a7cff752a", "id": "d4e5e1f6221b4e679df71b241d62c7d8"}], "masterMark": "1", "age": 39, "certificateType": "01"}, {"birthday": "1983-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "relationshipWithApplicant": "9", "id": "fed7908852d947e3aeca463216a9642b", "totalStandardPremium": 1100.0, "pregnancyWeek": 0, "riskPersonNo": "3", "updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "address": "李大头", "idRiskClass": "93be0d120ffa4f628b233abd67d16dd6", "virtualInsuredNum": 0, "totalActualPremium": -1100.0, "postcode": "否", "certificateIssueDate": "2022-09-03 00:00:00", "personnelCode": "CL0000000000987", "vehicleNum": 0, "certificateNo": "110101198301019893", "createdDate": "2022-09-03 16:04:20", "mobileTelephone": "15876587697", "totalAgreePremium": -1100.0, "createdBy": "admin2", "name": "李大头", "isSociaSecurity": "3", "certificateValidDate": "2023-09-03 00:00:00", "beneficaryInfoList": [{"updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "baseInfoId": "1565973782751678464", "createdDate": "2022-09-03 16:04:20", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "fed7908852d947e3aeca463216a9642b", "id": "4408d90bc3e24516bba78ea37f6469f1"}], "masterMark": "1", "age": 39, "certificateType": "01"}], "riskGroupType": "18", "id": "43a1d87ed06f4c4293615b3057b3d8fb", "totalStandardPremium": 1100.0, "combinedProductCode": "MP19000061", "beneficaryInfoList": [{"updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "baseInfoId": "1565973782751678464", "createdDate": "2022-09-03 16:04:20", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "fed7908852d947e3aeca463216a9642b", "id": "4408d90bc3e24516bba78ea37f6469f1"}, {"updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "baseInfoId": "1565973782751678464", "createdDate": "2022-09-03 16:04:20", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "f631e768657147d0b10e7d1a7cff752a", "id": "d4e5e1f6221b4e679df71b241d62c7d8"}, {"updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "baseInfoId": "1565973782751678464", "createdDate": "2022-09-03 16:04:20", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "9bc7b8ca93b1485c826e0da6baf5422b", "id": "f438f9ec716c4366ab2b3ef351473051"}]}], "payInfoList": [{"paymentPersonName": "路鸣泽", "noticeNo": "", "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:03:38", "baseInfoId": "1565973782751678464", "installmentType": "0", "paymentItemName": "0", "agreePremium": 3300.0, "paymentEndDate": "2022-09-03 23:59:59", "paymentItem": "0", "comExchangeRate": 1.0, "currencyName": "", "archiveDate": "2022-10-31 00:00:00", "statusName": "收款", "noticeStatus": "", "id": "bcc792027c4a4dbe8dbb7c76bc2ec633", "updatedBy": "admin2", "isInstallment": "0", "payerTypeName": "", "createdDate": "2022-09-03 16:03:38", "createdBy": "admin2", "termNo": 1, "paymentBeginDate": "2022-09-03 00:00:00", "billTypeName": "", "currencyCode": "CNY", "defComExchangeRate": 1.0, "status": "01"}], "beneficaryInfoList": [{"updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "baseInfoId": "1565973782751678464", "createdDate": "2022-09-03 16:04:20", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "fed7908852d947e3aeca463216a9642b", "id": "4408d90bc3e24516bba78ea37f6469f1"}, {"updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "baseInfoId": "1565973782751678464", "createdDate": "2022-09-03 16:04:20", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "f631e768657147d0b10e7d1a7cff752a", "id": "d4e5e1f6221b4e679df71b241d62c7d8"}, {"updatedBy": "admin2", "riskGroupId": "43a1d87ed06f4c4293615b3057b3d8fb", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000445", "updatedDate": "2022-09-03 16:04:20", "baseInfoId": "1565973782751678464", "createdDate": "2022-09-03 16:04:20", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "9bc7b8ca93b1485c826e0da6baf5422b", "id": "f438f9ec716c4366ab2b3ef351473051"}]}, "hideBusinessDetail": false, "commissionCharge": "0.00", "commissionChargeProportion": "0.00%", "hideEmployeeInfo": false, "technicProductCode": "TP1900029", "isElecSubPolicyNo": "0", "endorseSceneInfoDTO": {"plyPrintTimes": 0, "edrPrintTimes": 0}, "saleChannelSourceName": "", "isShowCommission": true, "hideChannelInfo": false, "delLicenceWhippletre": false, "hideSaleGroupName": false, "saleDepartmentCode": "2320000000", "isShowCommissionInter": true, "showPremium": false}