{"saleDepartmentName": "第三方系统财产保险股份有限公司", "notShowFileForPolicy": false, "hideChannelInfoPas": false, "contractDTO": {"baseInfo": {"departmentCode": "**********", "discount": 1, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "acceptInsuranceDate": "2022-09-08 16:36:44", "productName": "理赔测试专用QX", "quotationNo": "Q000000005510", "rateTotal": 4.02, "insuranceDate": "2022-09-08 16:25:16", "contractPactCode": "Pd0011941", "applyPolicyNo": "5**********00006054", "totalInsuredAmount": 120000, "productVersion": "1.03", "exchangeRate": 1, "businessTypeName": "团体", "underwriteName": "SYSTEM", "inputByName": "TESTER", "id": "1567794035370500096", "coinsuranceMark": "2", "shareholdersFlag": "0", "saleAmount": 0.57, "saleRate": 1.01, "updatedBy": "TEST", "productClass": "19", "payTermNo": 1, "endorseNo": "1**********00000595", "performanceAmount": 1.13, "inputDate": "2022-09-08 16:36:38", "quotationDate": "2022-09-08 16:32:08", "totalVATExcludedPremium": 56.6, "productKind": "0", "isAccommodationName": "", "totalAgreePremium": 60, "performanceRate": 2, "insuredTotalNum": 3, "insuranceLimit": "365天", "dataSource": "NCBS_NBS", "commissionAmount": 0.57, "status": "B5", "commissionRate": 1.01, "isRoundName": "", "inputBy": "TEST", "insuranceEndDate": "2023-09-18 23:59:59", "quotationName": "测试", "totalValueAddedTax": 3.4, "archiveDate": "2023-09-18 00:00:00", "totalStandardPremium": 60, "renewalTypeName": "", "premiumCurrencyCode": "01", "carBusinessType": "0", "systemId": "06", "totalActualPremium": 60, "renewalType": "0", "operationTypeName": "", "amountCurrencyCode": "01", "createdDate": "2022-09-08 16:36:45", "productCode": "MP19000055", "createdBy": "TEST", "underwriteDate": "2022-09-08 16:36:43", "dayNum": "-11天", "amountTotal": 2.27, "endorsementTimes": 0, "formatTypeName": "", "businessType": "2", "insuranceBeginDate": "2022-09-01 00:00:00"}, "specialPromiseList": [{"promiseCode": "TY000000000000022", "createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "promiseType": "N", "createdBy": "TEST", "sortIndex": 1, "promiseDesc": "理赔专用特约。", "endorseNo": "", "policyNo": "1**********00000595", "id": "1564148179309805568", "updatedDate": "2022-08-29 15:09:22", "baseInfoId": "1567794035370500096"}], "endorseNo": "1**********00000595", "policyNo": "1**********00000595", "attachmentGroupList": [{"createdDate": "2022-09-08 16:36:45", "updatedBy": "TEST", "documentGroupId": "00011944", "documentGroupType": "01", "createdBy": "TEST", "archiveDate": "2023-09-18 00:00:00", "endorseNo": "", "policyNo": "1**********00000595", "id": "b7f8ac9d89c949ad9b8eaf9d51b57afa", "updatedDate": "2022-09-08 16:36:45", "baseInfoId": "1567794035370500096"}], "extendInfo": {"disputedSettleModeName": "", "updatedBy": "TEST", "isFacultativeBusiness": "0", "isStarHighInsurance": "0", "endorseNo": "", "isGiftInsurance": "0", "validateCode": "E2S57/55XBB", "applyApproach": "02", "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "isTransProtectBusiness": "0", "isPolicyHealthInsurance": "0", "baseInfoId": "1567794035370500096", "isPolicyBeforePayfee": "1", "disputedSettleMode": "1", "createdDate": "2022-09-08 16:36:45", "isPastsign": "0", "createdBy": "TEST", "id": "1567794036175806464", "isVipCustomer": "1", "applyApproachName": "FQS批量出单", "isPrintAgencyInfo": "1"}, "noclaimInfoList": [], "compensationLimitInfoList": [], "insurantInfoList": [], "quotationNo": "Q000000005510", "applyPolicyNo": "5**********00006054", "saleInfo": {"departmentName": "第三方系统财产保险股份有限公司", "updatedBy": "TEST", "endorseNo": "", "brokerInfoList": [], "departmentCode": "**********", "channelSourceCode": "0", "employeeInfoList": [{"saleInfoId": "5ac6a9b1aa4547c194d8988c2096a15f", "documentCost": 0, "employeeName": "张锋", "updatedBy": "TEST", "performanceValue1Modify": 0, "endorseNo": "", "offlineServiceFee": 0, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "onlineServiceFee": 0, "employeeCode": "232123400", "mainEmployeeFlag": "1", "createdDate": "2022-09-08 16:36:45", "createdBy": "TEST", "marginCharge1": 0, "performanceValue2Modify": 0, "id": "3e7993a316514d5db2f7d67f9ea537ed"}], "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "signDate": "2022-09-08 16:36:45", "agentInfoList": [{"saleInfoId": "5ac6a9b1aa4547c194d8988c2096a15f", "createdDate": "2022-09-08 16:36:45", "updatedBy": "TEST", "createdBy": "TEST", "agentCode": "U200002007115", "endorseNo": "", "agentName": "江苏新润保险代理有限公司", "policyNo": "1**********00000595", "id": "b4cd82b030ad46498786588b35a52fda", "updatedDate": "2022-09-08 16:36:45"}], "baseInfoId": "1567794035370500096", "createdDate": "2022-09-08 16:36:45", "createdBy": "TEST", "channelSourceName": "直接业务", "id": "5ac6a9b1aa4547c194d8988c2096a15f", "applyDate": "2022-09-08 16:36:45", "partnerInfoList": []}, "rescueServiceList": [], "applicantInfoList": [{"linkModeType": "03", "clientNo": "213244", "organizationTypeName": "", "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "baseInfoId": "1567794035370500096", "applicantType": "2", "sexName": "", "isConfirmName": "0", "id": "64c91786707f43e2bf437cef6fa63ed9", "personnelTypeName": "个人", "updatedBy": "TEST", "address": "上海浦东唐镇", "endorseNo": "", "certificateIssueDate": "2022-09-01 00:00:00", "certificateNo": "23243545", "certificateTypeName": "", "sexCode": "X", "linkManName": "陈一", "createdDate": "2022-09-08 16:36:45", "mobileTelephone": "13112341234", "createdBy": "TEST", "personnelType": "1", "isConfirmNotification": "0", "name": "标的责任共享", "certificateValidDate": "2022-10-31 00:00:00", "certificateType": "03"}], "riskGroupInfoList": [{"virtualRiskNum": 0, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "combinedProductVersion": "1.03", "baseInfoId": "1567794035370500096", "totalInsuredAmount": 120000, "planInfoList": [{"updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "isMain": "1", "endorseNo": "", "totalActualPremium": 20, "planName": "理赔专用意外险QX", "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "planCode": "PL1900029", "totalVATExcludedPremium": 56.6, "totalInsuredAmount": 120000, "createdDate": "2022-09-08 16:36:45", "totalAgreePremium": 20, "totalValueAddedTax": 3.4, "createdBy": "TEST", "archiveDate": "2023-09-18 00:00:00", "dutyInfoList": [{"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.000333, "discount": 100, "dutyCode": "CVAA011", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "dutySharedAmountMerge": "CVAA011,CVAA012", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "04", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "住院医疗费用QUQ", "createdBy": "TEST", "dutyDetailCode": "DDQUQ01", "limitAmount": 0.6, "secondTypeName": "", "id": "8241445c276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "34b4ecc18e20472bb9aba55e30b9be34", "effectiveDate": "2022-03-17 14:51:25"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "04", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "住院医疗费用ZYQ", "createdBy": "TEST", "dutyDetailCode": "DDZYQ01", "limitAmount": 0.4, "secondTypeName": "", "id": "82416f79276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "34b4ecc18e20472bb9aba55e30b9be34", "effectiveDate": "2022-03-11 14:25:01"}], "isImportName": "", "isDutySharedAmount": "0", "totalInsuredAmount": 30000, "totalValueAddedTax": 0, "id": "34b4ecc18e20472bb9aba55e30b9be34", "totalStandardPremium": 10, "insuredAmount": 30000, "saleRate": 1, "updatedBy": "c", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX住院医疗费用", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "免赔额", "createdBy": "TEST", "dutyAttrCode": "271", "dutyAttrAmountValue": "200", "policyNo": "1**********00000595", "id": "918c821ab8734afabe959dccc808e807", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "34b4ecc18e20472bb9aba55e30b9be34"}, {"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "赔付比例", "createdBy": "TEST", "dutyAttrCode": "365", "dutyAttrAmountValue": "78", "policyNo": "1**********00000595", "id": "1ea5b351ccb544cb82890a597368c5bd", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "34b4ecc18e20472bb9aba55e30b9be34"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}, {"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.0002, "discount": 100, "dutyCode": "CVAA008", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "dutyDetailType": "02", "endorseNo": "", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "疾病残疾", "createdBy": "TEST", "dutyDetailCode": "DDB0014", "limitAmount": 0.55, "secondTypeName": "", "id": "823b1e4a276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "effectiveDate": "2015-05-29 10:55:08"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "02", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "意外残疾", "createdBy": "TEST", "dutyDetailCode": "DDB0040", "limitAmount": 0.45, "secondTypeName": "", "id": "823b4596276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "effectiveDate": "2018-09-13 15:33:56"}], "isImportName": "", "isDutySharedAmount": "0", "totalInsuredAmount": 50000, "totalValueAddedTax": 0, "id": "3dfbae855cca409abc66ca090ba30fb2", "totalStandardPremium": 10, "insuredAmount": 50000, "saleRate": 1, "updatedBy": "TEST", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX残疾", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "伤残标准类型", "createdBy": "TEST", "dutyAttrCode": "368", "dutyAttrAmountValue": "0", "policyNo": "1**********00000595", "id": "53486a9ac1174249a1876a285557eda6", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2"}, {"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "残疾对应赔付比例", "createdBy": "TEST", "dutyAttrCode": "369", "policyNo": "1**********00000595", "id": "68c5c2e38b794c8f871c245f8bb57fac", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [{"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "一级伤残赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "0", "dutyAttrInfoId": "68c5c2e38b794c8f871c245f8bb57fac", "dutyAttrDetailCode": "369_0", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8142813f-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "10", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "二级伤残赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "0", "dutyAttrInfoId": "68c5c2e38b794c8f871c245f8bb57fac", "dutyAttrDetailCode": "369_1", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "814603df-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "20", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "三级伤残赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "0", "dutyAttrInfoId": "68c5c2e38b794c8f871c245f8bb57fac", "dutyAttrDetailCode": "369_2", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8147dd6b-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "30", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "四级伤残赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "0", "dutyAttrInfoId": "68c5c2e38b794c8f871c245f8bb57fac", "dutyAttrDetailCode": "369_3", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "814b32dd-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "40", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "五级伤残赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "0", "dutyAttrInfoId": "68c5c2e38b794c8f871c245f8bb57fac", "dutyAttrDetailCode": "369_4", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "814d6b7c-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "50", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "六级伤残赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "0", "dutyAttrInfoId": "68c5c2e38b794c8f871c245f8bb57fac", "dutyAttrDetailCode": "369_5", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8150b1c0-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "60", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "七级伤残赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "0", "dutyAttrInfoId": "68c5c2e38b794c8f871c245f8bb57fac", "dutyAttrDetailCode": "369_6", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8151bc4b-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "70", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "八级伤残赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "0", "dutyAttrInfoId": "68c5c2e38b794c8f871c245f8bb57fac", "dutyAttrDetailCode": "369_7", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8151ec5c-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "80", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "九级伤残赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "0", "dutyAttrInfoId": "68c5c2e38b794c8f871c245f8bb57fac", "dutyAttrDetailCode": "369_8", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815218ff-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "90", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "0", "dutyAttributeDetailName": "十级伤残赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "0", "dutyAttrInfoId": "68c5c2e38b794c8f871c245f8bb57fac", "dutyAttrDetailCode": "369_9", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815246b0-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "98", "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2", "dutyAttributeDetailUnit": "%"}], "dutyInfoId": "3dfbae855cca409abc66ca090ba30fb2"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}, {"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.001, "discount": 100, "dutyCode": "CVAA007", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "07", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "一般定额明细", "createdBy": "TEST", "dutyDetailCode": "DD00036", "limitAmount": 1, "secondTypeName": "", "id": "8239c3ad276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "4d6d7f99fd104e668148594da477e66e", "effectiveDate": "2022-06-14 15:16:27"}], "isImportName": "", "isDutySharedAmount": "0", "totalInsuredAmount": 10000, "totalValueAddedTax": 0, "id": "4d6d7f99fd104e668148594da477e66e", "totalStandardPremium": 10, "insuredAmount": 10000, "saleRate": 1, "updatedBy": "TEST", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX一般定额", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "等待期", "createdBy": "TEST", "dutyAttrCode": "174", "dutyAttrAmountValue": "2", "policyNo": "1**********00000595", "id": "dc2670a7db34466bb7158f96f2694d89", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "4d6d7f99fd104e668148594da477e66e"}, {"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "是否区分社保", "createdBy": "TEST", "dutyAttrCode": "360", "dutyAttrAmountValue": "1", "policyNo": "1**********00000595", "id": "5acdd0be34624a6995c0f5b57a83f9ed", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "4d6d7f99fd104e668148594da477e66e"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}, {"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.0002, "discount": 100, "dutyCode": "CVAA010", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "01", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "意外身故", "createdBy": "TEST", "dutyDetailCode": "DD0111A", "limitAmount": 0.45, "secondTypeName": "", "id": "823fca21276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "64c8b995cae942f58a44f511ee8e8805", "effectiveDate": "2018-04-27 23:04:03"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "01", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "疾病身故", "createdBy": "TEST", "dutyDetailCode": "DDA0012", "limitAmount": 0.55, "secondTypeName": "", "id": "82401d11276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "64c8b995cae942f58a44f511ee8e8805", "effectiveDate": "2015-05-29 10:55:08"}], "isImportName": "", "isDutySharedAmount": "0", "totalInsuredAmount": 50000, "totalValueAddedTax": 0, "id": "64c8b995cae942f58a44f511ee8e8805", "totalStandardPremium": 10, "insuredAmount": 50000, "saleRate": 1, "updatedBy": "TEST", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX身故", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "等待期", "createdBy": "TEST", "dutyAttrCode": "174", "dutyAttrAmountValue": "4", "policyNo": "1**********00000595", "id": "70fea899a8304f7bbde29cbf5b8479ef", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "64c8b995cae942f58a44f511ee8e8805"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}, {"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.001, "discount": 100, "dutyCode": "CVAA006", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "05", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "意外伤害津贴", "createdBy": "TEST", "dutyDetailCode": "DDF0001", "limitAmount": 0.5, "secondTypeName": "", "id": "8237c7e0276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "6f589e8045a54296956f77ad3f029d84", "effectiveDate": "2015-05-29 10:55:08"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "05", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "意外住院津贴", "createdBy": "TEST", "dutyDetailCode": "DDF0003", "limitAmount": 0.2, "secondTypeName": "", "id": "8237efe8276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "6f589e8045a54296956f77ad3f029d84", "effectiveDate": "2015-05-29 10:55:08"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "05", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "医疗误工津贴", "createdBy": "TEST", "dutyDetailCode": "DDF0004", "limitAmount": 0.1, "secondTypeName": "", "id": "82381661276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "6f589e8045a54296956f77ad3f029d84", "effectiveDate": "2015-05-29 10:55:08"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "05", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "住院护理津贴", "createdBy": "TEST", "dutyDetailCode": "DDF0005", "limitAmount": 0.1, "secondTypeName": "", "id": "82383c87276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "6f589e8045a54296956f77ad3f029d84", "effectiveDate": "2015-05-29 10:55:08"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "05", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "疾病住院津贴", "createdBy": "TEST", "dutyDetailCode": "DDF0006", "limitAmount": 0.1, "secondTypeName": "", "id": "82386235276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "6f589e8045a54296956f77ad3f029d84", "effectiveDate": "2015-05-29 10:55:08"}], "isImportName": "", "isDutySharedAmount": "0", "totalInsuredAmount": 10000, "totalValueAddedTax": 0, "id": "6f589e8045a54296956f77ad3f029d84", "totalStandardPremium": 10, "insuredAmount": 10000, "saleRate": 1, "updatedBy": "TEST", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX津贴", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "免赔天数", "createdBy": "TEST", "dutyAttrCode": "14", "dutyAttrAmountValue": "2", "policyNo": "1**********00000595", "id": "7b1fa5224e794c1c832fd1007726b278", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "6f589e8045a54296956f77ad3f029d84"}, {"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "每人每日住院津贴额", "createdBy": "TEST", "dutyAttrCode": "117", "dutyAttrAmountValue": "100", "policyNo": "1**********00000595", "id": "b1cb40405d1947aa95d31843000efc1d", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "6f589e8045a54296956f77ad3f029d84"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}, {"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.0002, "discount": 100, "dutyCode": "CVAA009", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "03", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "重大疾病", "createdBy": "TEST", "dutyDetailCode": "DD0110A", "limitAmount": 1, "secondTypeName": "", "id": "823e7093276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "80a8d694a99d4c3f9c6f5c94c487580e", "effectiveDate": "2018-04-27 23:04:03"}], "isImportName": "", "isDutySharedAmount": "1", "totalInsuredAmount": 50000, "totalValueAddedTax": 0, "id": "80a8d694a99d4c3f9c6f5c94c487580e", "totalStandardPremium": 10, "insuredAmount": 50000, "saleRate": 1, "updatedBy": "TEST", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX重大疾病", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "等待期", "createdBy": "TEST", "dutyAttrCode": "174", "dutyAttrAmountValue": "3", "policyNo": "1**********00000595", "id": "1eddea8915f044d199315ac2d3e1be80", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "80a8d694a99d4c3f9c6f5c94c487580e"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}, {"discountProportion": 1, "commissionRate": 1, "premiumRate": 0.000333, "discount": 100, "dutyCode": "CVAA012", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "dutySharedAmountMerge": "CVAA011,CVAA012", "dutyDetailInfoList": [{"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "04", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "门诊手术医疗费用QUQ", "createdBy": "TEST", "dutyDetailCode": "DDQUQ04", "limitAmount": 0.8, "secondTypeName": "", "id": "8242c80e276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "effectiveDate": "2022-03-17 15:08:19"}, {"updatedBy": "TEST", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "04", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "claimProportion": 1, "secondLimitPropertyName": "", "createdDate": "2022-08-29 15:09:22", "dutyDetailName": "OC特殊门诊医疗费用", "createdBy": "TEST", "dutyDetailCode": "DD00158", "limitAmount": 0.2, "secondTypeName": "", "id": "8242ee3c276911ed980ec20004284251", "claimAmountAccTypeName": "", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "effectiveDate": "2022-08-08 15:59:56"}], "isImportName": "", "isDutySharedAmount": "1", "totalInsuredAmount": 30000, "totalValueAddedTax": 0, "id": "9b2823546cba42f791758c08d1f1dc25", "totalStandardPremium": 10, "insuredAmount": 30000, "saleRate": 1, "updatedBy": "TEST", "endorseNo": "", "totalActualPremium": 10, "dutyDesc": "", "planCode": "*********", "dutyName": "QX门诊医疗费用", "totalVATExcludedPremium": 10, "createdDate": "2022-08-29 15:09:22", "totalAgreePremium": 10, "dutyAttributeInfoList": [{"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "免赔额", "createdBy": "TEST", "dutyAttrCode": "271", "dutyAttrAmountValue": "250", "policyNo": "1**********00000595", "id": "0cc03c9090ef43e69594252eb4610837", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25"}, {"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "赔付比例类型", "createdBy": "TEST", "dutyAttrCode": "364", "dutyAttrAmountValue": "2", "policyNo": "1**********00000595", "id": "bbcf057b524a4337853791b0650736f8", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [], "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25"}, {"createdDate": "2022-08-29 15:09:22", "updatedBy": "TEST", "dutyAttributeName": "非标准级距赔付比例", "createdBy": "TEST", "dutyAttrCode": "366", "dutyAttrAmountValue": "", "policyNo": "1**********00000595", "id": "c04ecf39a8b8411d8fbeff28f89fca2a", "updatedDate": "2022-08-29 15:09:22", "dutyAttrDetailList": [{"updatedBy": "TEST", "attrRowNo": "1", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "1", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815634ff-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "0", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "1", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "2", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8156b65b-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "1001", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "1", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "3", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8157c0bc-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "5001", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "1", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "4", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815869a3-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "8001", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "1", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "5", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815ef9bc-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "10000", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "2", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "1", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8156604b-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "1000", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "2", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "2", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8156e0f8-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "5000", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "2", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "3", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8157eebe-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "8000", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "2", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "4", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815a2679-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "9999", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "2", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "5", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "81626664-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "50000", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "3", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "1", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "81568cb7-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "25", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "3", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "2", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815777dc-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "45", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "3", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "3", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "81581988-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "65", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "3", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "4", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "815d2fbd-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "85", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}, {"updatedBy": "TEST", "attrRowNo": "3", "dutyAttributeDetailName": "非标准级距赔付比例", "policyNo": "1**********00000595", "updatedDate": "2022-08-29 15:09:22", "attrColumnNo": "5", "dutyAttrInfoId": "c04ecf39a8b8411d8fbeff28f89fca2a", "dutyAttrDetailCode": "37", "createdDate": "2022-08-29 15:09:22", "createdBy": "TEST", "id": "8165e7f0-2769-11ed-980e-c20004284251", "dutyAttrDetailValue": "99", "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25", "dutyAttributeDetailUnit": "%"}], "dutyInfoId": "9b2823546cba42f791758c08d1f1dc25"}], "createdBy": "TEST", "performanceRate": 1, "planInfoId": "9ce4059e901e4431bb2bd341406b84c3"}], "id": "299c53b6f3524016b17116f9761fe563", "totalStandardPremium": 20}], "archiveDate": "2023-09-18 00:00:00", "applyRiskNum": 3, "id": "080e1300c63b4dd8abe3651d2af93ea0", "totalStandardPremium": 20, "combinedProductCode": "MP19000055", "updatedBy": "TEST", "productClass": "19", "endorseNo": "", "totalActualPremium": 20, "applyNum": 1, "createdDate": "2022-09-08 16:36:45", "totalAgreePremium": 20, "createdBy": "TEST", "riskGroupName": "方案一", "riskPersonInfoList": [{"birthday": "1990-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "relationshipWithApplicant": "9", "id": "0b1239c0ec504fafbf2e069a328a0428", "totalStandardPremium": 20, "pregnancyWeek": 0, "riskPersonNo": "2", "updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "address": "龙华中路", "idRiskClass": "bbb57581bfec4ae0b3c86b10fd2d8761", "virtualInsuredNum": 0, "totalActualPremium": 20, "certificateIssueDate": "2012-01-01 00:00:00", "personnelCode": "CL0000000001309", "vehicleNum": 0, "certificateNo": "210101199001010035", "sexCode": "M", "createdDate": "2022-09-08 16:36:45", "mobileTelephone": "13312341224", "totalAgreePremium": 20, "createdBy": "TEST", "name": "承太郎", "isSociaSecurity": "2", "certificateValidDate": "2032-01-01 00:00:00", "beneficaryInfoList": [{"updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "isLegal": "1", "endorseNo": "", "benefitRatio": 1, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "baseInfoId": "1567794035370500096", "createdDate": "2022-09-08 16:36:45", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "0b1239c0ec504fafbf2e069a328a0428", "id": "4fa6b576a3cc423c9f2b0e271a163cc8"}], "masterMark": "1", "age": 32, "certificateType": "01"}, {"birthday": "1990-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "relationshipWithApplicant": "9", "id": "381a024a673d49b49bee2a1d6c2b1439", "totalStandardPremium": 20, "pregnancyWeek": 0, "riskPersonNo": "3", "updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "address": "龙华中路", "idRiskClass": "bbb57581bfec4ae0b3c86b10fd2d8761", "virtualInsuredNum": 0, "totalActualPremium": -20, "certificateIssueDate": "2012-01-01 00:00:00", "personnelCode": "CL0000000001310", "vehicleNum": 0, "certificateNo": "210101199001010051", "sexCode": "M", "createdDate": "2022-09-08 16:36:45", "mobileTelephone": "13312341224", "totalAgreePremium": -20, "createdBy": "TEST", "name": "花京院", "isSociaSecurity": "2", "certificateValidDate": "2032-01-01 00:00:00", "beneficaryInfoList": [{"updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "isLegal": "1", "endorseNo": "", "benefitRatio": 1, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "baseInfoId": "1567794035370500096", "createdDate": "2022-09-08 16:36:45", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "381a024a673d49b49bee2a1d6c2b1439", "id": "bd82df8f7b464f499c8000f9a8485c91"}], "masterMark": "1", "age": 32, "certificateType": "01"}, {"birthday": "1990-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "relationshipWithApplicant": "9", "id": "b22db3e8810940b195408aae6ae4e782", "totalStandardPremium": 20, "pregnancyWeek": 0, "riskPersonNo": "1", "updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "address": "龙华中路", "idRiskClass": "bbb57581bfec4ae0b3c86b10fd2d8761", "virtualInsuredNum": 0, "totalActualPremium": 20, "certificateIssueDate": "2012-01-01 00:00:00", "personnelCode": "CL0000000001308", "vehicleNum": 0, "certificateNo": "210101199001010019", "sexCode": "M", "createdDate": "2022-09-08 16:36:45", "mobileTelephone": "13312341224", "totalAgreePremium": 20, "createdBy": "TEST", "name": "徐伦", "isSociaSecurity": "2", "certificateValidDate": "2032-01-01 00:00:00", "beneficaryInfoList": [{"updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "isLegal": "1", "endorseNo": "", "benefitRatio": 1, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "baseInfoId": "1567794035370500096", "createdDate": "2022-09-08 16:36:45", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "b22db3e8810940b195408aae6ae4e782", "id": "48fd5d4b102849b5ada26006b1aa6bd3"}], "masterMark": "1", "age": 32, "certificateType": "01"}], "riskGroupType": "18", "productPackageType": "PK00000870", "beneficaryInfoList": [{"updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "isLegal": "1", "endorseNo": "", "benefitRatio": 1, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "baseInfoId": "1567794035370500096", "createdDate": "2022-09-08 16:36:45", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "b22db3e8810940b195408aae6ae4e782", "id": "48fd5d4b102849b5ada26006b1aa6bd3"}, {"updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "isLegal": "1", "endorseNo": "", "benefitRatio": 1, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "baseInfoId": "1567794035370500096", "createdDate": "2022-09-08 16:36:45", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "0b1239c0ec504fafbf2e069a328a0428", "id": "4fa6b576a3cc423c9f2b0e271a163cc8"}, {"updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "isLegal": "1", "endorseNo": "", "benefitRatio": 1, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "baseInfoId": "1567794035370500096", "createdDate": "2022-09-08 16:36:45", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "381a024a673d49b49bee2a1d6c2b1439", "id": "bd82df8f7b464f499c8000f9a8485c91"}]}], "payInfoList": [{"paymentPersonName": "标的责任共享", "noticeNo": "", "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:40", "baseInfoId": "1567794035370500096", "installmentType": "0", "paymentItemName": "0", "agreePremium": 60, "paymentEndDate": "2022-09-08 23:59:59", "paymentItem": "0", "comExchangeRate": 1, "currencyName": "", "archiveDate": "2023-09-18 00:00:00", "statusName": "收款", "noticeStatus": "", "id": "303ec59899f94c5e8e3db7b890367ca6", "updatedBy": "TEST", "isInstallment": "0", "payerTypeName": "", "createdDate": "2022-09-08 16:36:40", "createdBy": "TEST", "termNo": 1, "paymentBeginDate": "2022-09-08 00:00:00", "billTypeName": "", "currencyCode": "CNY", "defComExchangeRate": 1, "status": "01"}], "beneficaryInfoList": [{"updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "isLegal": "1", "endorseNo": "", "benefitRatio": 1, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "baseInfoId": "1567794035370500096", "createdDate": "2022-09-08 16:36:45", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "b22db3e8810940b195408aae6ae4e782", "id": "48fd5d4b102849b5ada26006b1aa6bd3"}, {"updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "isLegal": "1", "endorseNo": "", "benefitRatio": 1, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "baseInfoId": "1567794035370500096", "createdDate": "2022-09-08 16:36:45", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "0b1239c0ec504fafbf2e069a328a0428", "id": "4fa6b576a3cc423c9f2b0e271a163cc8"}, {"updatedBy": "TEST", "riskGroupId": "080e1300c63b4dd8abe3651d2af93ea0", "isLegal": "1", "endorseNo": "", "benefitRatio": 1, "policyNo": "1**********00000595", "updatedDate": "2022-09-08 16:36:45", "baseInfoId": "1567794035370500096", "createdDate": "2022-09-08 16:36:45", "createdBy": "TEST", "personnelType": "1", "name": "法定", "riskPersonId": "381a024a673d49b49bee2a1d6c2b1439", "id": "bd82df8f7b464f499c8000f9a8485c91"}]}, "hideBusinessDetail": false, "commissionCharge": "0.00", "commissionChargeProportion": "0.00%", "hideEmployeeInfo": false, "technicProductCode": "TP1900025", "isElecSubPolicyNo": "0", "endorseSceneInfoDTO": {"plyPrintTimes": 0, "edrPrintTimes": 0}, "saleChannelSourceName": "", "isShowCommission": true, "hideChannelInfo": false, "delLicenceWhippletre": false, "hideSaleGroupName": false, "saleDepartmentCode": "**********", "isShowCommissionInter": true, "showPremium": false}