{"saleDepartmentName": "江苏", "notShowFileForPolicy": false, "hideChannelInfoPas": false, "contractDTO": {"baseInfo": {"departmentCode": "**********", "discount": 1.0, "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:22", "acceptInsuranceDate": "2022-09-03 15:39:31", "productName": "ZJ团体特定疾病保险2020版A款WH", "quotationNo": "Q000000005101", "rateTotal": 30.0, "insuranceDate": "2022-09-03 15:29:43", "endorseSystemId": "ICORE-PAS", "contractPactCode": "Pd0010529", "applyPolicyNo": "5**********00005518", "totalInsuredAmount": 1800000.0, "productVersion": "1.01", "exchangeRate": 1.0, "businessTypeName": "团体", "underwriteName": "TESTUWSZB1", "inputByName": "测试admin2", "id": "1565967578621087744", "coinsuranceMark": "2", "shareholdersFlag": "0", "saleAmount": 600.0, "saleRate": 10.0, "updatedBy": "TESTUWSZB1", "productClass": "19", "payTermNo": 1, "endorseNo": "33201021900000010113", "performanceAmount": 600.0, "inputDate": "2022-09-03 15:38:58", "quotationDate": "2022-09-03 15:37:04", "totalVATExcludedPremium": 6000.0, "productKind": "0", "isAccommodationName": "", "totalAgreePremium": 6000.0, "performanceRate": 10.0, "insuredTotalNum": 3, "insuranceLimit": "365天", "dataSource": "NCBS_NBS", "commissionAmount": 600.0, "status": "B5", "commissionRate": 10.0, "isRoundName": "", "inputBy": "admin2", "insuranceEndDate": "2023-09-03 23:59:59", "quotationName": "团体特定疾病保险2020版A款WH", "totalValueAddedTax": 0.0, "archiveDate": "2023-09-03 00:00:00", "totalStandardPremium": 6000.0, "renewalTypeName": "", "premiumCurrencyCode": "01", "carBusinessType": "0", "systemId": "06", "totalActualPremium": 6000.0, "renewalType": "0", "operationTypeName": "", "amountCurrencyCode": "01", "createdDate": "2022-09-03 15:39:31", "productCode": "MP19000056", "createdBy": "admin2", "underwriteDate": "2022-09-03 15:39:30", "dayNum": "-1天", "amountTotal": 1800.0, "discountPermium": 6000.0, "endorsementTimes": 1, "formatTypeName": "", "businessType": "2", "insuranceBeginDate": "2022-09-04 00:00:00"}, "specialPromiseList": [], "endorseNo": "33201021900000010113", "policyNo": "1**********00000444", "attachmentGroupList": [{"documentInfoList": [{"documentType": "OT99", "uploadPath": "http://stg.iobs.paic.com.cn/download/life-image-sf-stg/b22431705a134d16a5cbac93a782ca04?attname=b22431705a134d16a5cbac93a782ca04.xls", "documentName": "0000105321662190553069.xls", "documentSize": "541.00", "documentOrder": "1", "documentClass": "T99", "url": "http://stg.iobs.paic.com.cn/download/life-image-sf-stg/b22431705a134d16a5cbac93a782ca04?attname=b22431705a134d16a5cbac93a782ca04.xls", "uploadDate": "2022-09-03 15:35:53", "uploadIsDate": "2022-09-03 15:35:53", "documentId": "20010534", "documentStatus": "Y", "documentFormat": "xls", "uploadPersonnel": "admin2", "originName": "0000105321662190553069.xls"}], "createdDate": "2022-09-03 15:39:31", "updatedBy": "admin2", "documentGroupId": "00010533", "documentGroupType": "01", "createdBy": "admin2", "archiveDate": "2023-09-03 00:00:00", "endorseNo": "", "policyNo": "1**********00000444", "id": "e775484eb02345bf86cf4e9e0c6ca2f6", "updatedDate": "2022-09-08 15:32:23", "baseInfoId": "1565967578621087744"}], "extendInfo": {"disputedSettleModeName": "", "updatedBy": "admin2", "isFacultativeBusiness": "0", "isStarHighInsurance": "0", "endorseNo": "", "isGiftInsurance": "0", "validateCode": "V7NE/2T2YR98", "applyApproach": "02", "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:22", "isTransProtectBusiness": "0", "isPolicyHealthInsurance": "0", "baseInfoId": "1565967578621087744", "isPolicyBeforePayfee": "1", "disputedSettleMode": "1", "createdDate": "2022-09-03 15:39:31", "isPastsign": "0", "createdBy": "admin2", "id": "1565967579220873216", "isVipCustomer": "1", "applyApproachName": "FQS批量出单", "isPrintAgencyInfo": "1"}, "noclaimInfoList": [], "compensationLimitInfoList": [], "insurantInfoList": [], "quotationNo": "Q000000005101", "applyPolicyNo": "5**********00005518", "saleInfo": {"departmentName": "第三方系统财产保险股份有限公司南京市浦口支公司", "updatedBy": "admin2", "endorseNo": "", "brokerInfoList": [], "departmentCode": "**********", "channelSourceCode": "2", "employeeInfoList": [{"saleInfoId": "2da7ef2bdc964bc9b43216544531060f", "documentCost": 0.0, "employeeName": "黄晓雁", "updatedBy": "admin2", "performanceValue1Modify": 0.0, "endorseNo": "", "offlineServiceFee": 0.0, "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:22", "onlineServiceFee": 0.0, "employeeCode": "244523027", "mainEmployeeFlag": "1", "createdDate": "2022-09-03 15:39:32", "createdBy": "admin2", "marginCharge1": 0.0, "performanceValue2Modify": 0.0, "id": "8d620a862ff2422385e302de1df0dc82"}], "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:22", "signDate": "2022-09-03 15:39:32", "agentInfoList": [{"saleInfoId": "2da7ef2bdc964bc9b43216544531060f", "createdDate": "2022-09-03 15:39:32", "updatedBy": "admin2", "createdBy": "admin2", "agentCode": "U200002007115", "endorseNo": "", "agentName": "江苏新润保险代理有限公司", "policyNo": "1**********00000444", "id": "db53efd88ad24507862dc8a5203cc13f", "updatedDate": "2022-09-08 15:32:22"}], "baseInfoId": "1565967578621087744", "createdDate": "2022-09-03 15:39:32", "createdBy": "admin2", "channelSourceName": "专业代理", "id": "2da7ef2bdc964bc9b43216544531060f", "applyDate": "2022-09-03 15:39:32", "partnerInfoList": []}, "rescueServiceList": [], "applicantInfoList": [{"birthday": "1994-12-01 00:00:00", "linkModeType": "03", "city": "130200", "yearlySalaries": 100.0, "county": "130203", "clientNo": "123456", "organizationTypeName": "", "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:23", "baseInfoId": "1565967578621087744", "billingDepositBankAccount": "*********", "province": "130000", "applicantType": "1", "sexName": "", "isConfirmName": "0", "id": "ac11e79e8d57456684c07c326e69b91a", "personnelTypeName": "个人", "email": "<EMAIL>", "workUnit": "女子监狱", "updatedBy": "TESTUWSZB1", "address": "河北省唐山市路北区", "professionCode": "A0102002", "endorseNo": "", "certificateIssueDate": "2022-09-03 00:00:00", "certificateNo": "***********", "certificateTypeName": "港澳台通行证", "sexCode": "M", "createdDate": "2022-09-03 15:39:31", "mobileTelephone": "***********", "createdBy": "admin2", "personnelType": "1", "isConfirmNotification": "0", "name": "路明非", "certificateValidDate": "9999-12-31 00:00:00", "billingDepositBank": "000001", "age": 27, "certificateType": "04"}], "riskGroupInfoList": [{"updatedBy": "TESTUWSZB1", "productClass": "19", "endorseNo": "", "totalActualPremium": 2813.0, "virtualRiskNum": 0, "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:22", "applyNum": 1.0, "combinedProductVersion": "1.01", "baseInfoId": "1565967578621087744", "totalInsuredAmount": 1800000.0, "createdDate": "2022-09-03 15:39:31", "planInfoList": [{"updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "isMain": "1", "endorseNo": "", "totalActualPremium": 2813.0, "planName": "团体特定疾病保险2020版A款WH", "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:22", "planCode": "PL1900014", "totalVATExcludedPremium": 6000.0, "totalInsuredAmount": 1800000.0, "createdDate": "2022-09-03 15:39:31", "totalAgreePremium": 2813.0, "totalValueAddedTax": 0.0, "createdBy": "admin2", "archiveDate": "2023-09-03 00:00:00", "dutyInfoList": [{"commissionRate": 10.0, "premiumRate": 0.001111, "discount": 100.0, "dutyCode": "CVBA003", "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:22", "dutyDetailInfoList": [{"updatedBy": "TESTUWSZB1", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "03", "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:23", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-03 15:39:32", "dutyDetailName": "恶性肿瘤保险金费用WH", "createdBy": "TESTUWSZB1", "dutyDetailCode": "DD00236", "secondTypeName": "", "id": "8cb9ce5b2b5b11eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "c9673b4b687d48409df60273be0b3a9a", "effectiveDate": "2022-07-14 10:35:14"}], "isImportName": "", "totalInsuredAmount": 900000.0, "totalValueAddedTax": 0.0, "id": "c9673b4b687d48409df60273be0b3a9a", "totalStandardPremium": 1651.0, "insuredAmount": 300000.0, "saleRate": 10.0, "updatedBy": "admin2", "riskDutyRelationInfoList": [{"updatedBy": "TESTUWSZB1", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "idRiskClass": "e6c02562c4bf4dfc94fe9deb994e73e2", "classRiskNum": 3, "endorseNo": "", "totalActualPremium": 1000.0, "dutyCode": "CVBA003", "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:32", "insuranceEndDate": "2023-09-03 23:59:59", "riskInfoId": "c792988982854b87aa32eeaaa320d75c", "agreePremium": 0.0, "totalInsuredAmount": 300000.0, "createdDate": "2022-09-03 15:39:32", "createdBy": "TESTUWSZB1", "archiveDate": "2023-09-03 00:00:00", "standardPremium": 0.0, "id": "1565967710267707392", "insuredAmount": 300000.0, "actualPremium": 2000.0, "dutyInfoId": "c9673b4b687d48409df60273be0b3a9a", "insuranceBeginDate": "2022-09-04 00:00:00"}], "endorseNo": "", "totalActualPremium": 1651.0, "dutyDesc": "", "dutyName": "恶性肿瘤保险金WH", "totalVATExcludedPremium": 3000.0, "createdDate": "2022-09-03 15:39:32", "totalAgreePremium": 1651.0, "dutyAttributeInfoList": [{"createdDate": "2022-09-03 15:39:32", "updatedBy": "admin2", "dutyAttributeName": "等待期", "createdBy": "admin2", "dutyAttrCode": "174", "dutyAttrAmountValue": "30", "policyNo": "1**********00000444", "id": "855c78e49fb14a4c826e7b2a864e7c2a", "updatedDate": "2022-09-08 15:32:23", "dutyInfoId": "c9673b4b687d48409df60273be0b3a9a"}], "createdBy": "admin2", "performanceRate": 10.0, "discountPermium": 1000.0, "planInfoId": "02ad719fcabe45e88437a85e44eed327"}, {"commissionRate": 10.0, "premiumRate": 0.001111, "discount": 100.0, "dutyCode": "CVBA004", "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:23", "dutyDetailInfoList": [{"updatedBy": "TESTUWSZB1", "amountTypeName": "", "endorseNo": "", "dutyDetailType": "03", "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:23", "claimProportion": 1.0, "secondLimitPropertyName": "", "createdDate": "2022-09-03 15:39:32", "dutyDetailName": "原位癌保险金费用WH", "createdBy": "TESTUWSZB1", "dutyDetailCode": "DD00235", "secondTypeName": "", "id": "8ca67eb22b5b11eda979fe9982256455", "claimAmountAccTypeName": "", "dutyInfoId": "e91b4fc4d3804d8fa8a2917264af8257", "effectiveDate": "2022-07-14 10:34:57"}], "isImportName": "", "totalInsuredAmount": 900000.0, "totalValueAddedTax": 0.0, "id": "e91b4fc4d3804d8fa8a2917264af8257", "totalStandardPremium": 1162.0, "insuredAmount": 300000.0, "saleRate": 10.0, "updatedBy": "admin2", "riskDutyRelationInfoList": [{"updatedBy": "TESTUWSZB1", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "idRiskClass": "e6c02562c4bf4dfc94fe9deb994e73e2", "classRiskNum": 3, "endorseNo": "", "totalActualPremium": 1000.0, "dutyCode": "CVBA004", "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:32", "insuranceEndDate": "2023-09-03 23:59:59", "riskInfoId": "c792988982854b87aa32eeaaa320d75c", "agreePremium": 0.0, "totalInsuredAmount": 300000.0, "createdDate": "2022-09-03 15:39:32", "createdBy": "TESTUWSZB1", "archiveDate": "2023-09-03 00:00:00", "standardPremium": 0.0, "id": "1565967710221570048", "insuredAmount": 300000.0, "actualPremium": 0.0, "dutyInfoId": "e91b4fc4d3804d8fa8a2917264af8257", "insuranceBeginDate": "2022-09-04 00:00:00"}], "endorseNo": "", "totalActualPremium": 1162.0, "dutyDesc": "", "dutyName": "原位癌保险金WH", "totalVATExcludedPremium": 3000.0, "createdDate": "2022-09-03 15:39:31", "totalAgreePremium": 1162.0, "dutyAttributeInfoList": [{"createdDate": "2022-09-03 15:39:32", "updatedBy": "admin2", "dutyAttributeName": "等待期", "createdBy": "admin2", "dutyAttrCode": "174", "dutyAttrAmountValue": "30", "policyNo": "1**********00000444", "id": "d2744e8b253647fa8be605e84d815ba1", "updatedDate": "2022-09-08 15:32:23", "dutyInfoId": "e91b4fc4d3804d8fa8a2917264af8257"}], "createdBy": "admin2", "performanceRate": 10.0, "discountPermium": 1000.0, "planInfoId": "02ad719fcabe45e88437a85e44eed327"}], "id": "02ad719fcabe45e88437a85e44eed327", "totalStandardPremium": 2813.0}], "totalAgreePremium": 2813.0, "createdBy": "admin2", "riskGroupName": "方案一", "archiveDate": "2023-09-03 00:00:00", "applyRiskNum": 4, "riskPersonInfoList": [{"birthday": "1983-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "relationshipWithApplicant": "9", "id": "1a4a8990d90a43d08963b2ee7868567e", "totalStandardPremium": 2000.0, "pregnancyWeek": 0, "riskPersonNo": "2", "updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "address": "李虱虱", "idRiskClass": "e6c02562c4bf4dfc94fe9deb994e73e2", "virtualInsuredNum": 0, "totalActualPremium": 2000.0, "postcode": "否", "certificateIssueDate": "2022-09-03 00:00:00", "personnelCode": "CL0000000000982", "vehicleNum": 0, "certificateNo": "110101198301019981", "createdDate": "2022-09-03 15:39:31", "mobileTelephone": "15876587695", "totalAgreePremium": 2000.0, "createdBy": "admin2", "name": "李虱虱", "isSociaSecurity": "2", "certificateValidDate": "2023-09-03 00:00:00", "beneficaryInfoList": [{"updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "baseInfoId": "1565967578621087744", "createdDate": "2022-09-03 15:39:31", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "1a4a8990d90a43d08963b2ee7868567e", "id": "563b65f156764f978a4699971c02904e"}], "masterMark": "1", "age": 39, "certificateType": "01"}, {"birthday": "1983-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "relationshipWithApplicant": "9", "id": "726a842c2397474d8fb8ba20b247adb3", "totalStandardPremium": 2000.0, "pregnancyWeek": 0, "riskPersonNo": "3", "updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "address": "李大头", "idRiskClass": "e6c02562c4bf4dfc94fe9deb994e73e2", "virtualInsuredNum": 0, "totalActualPremium": -2000.0, "postcode": "否", "certificateIssueDate": "2022-09-03 00:00:00", "personnelCode": "CL0000000000983", "vehicleNum": 0, "certificateNo": "110101198301019893", "createdDate": "2022-09-03 15:39:31", "mobileTelephone": "15876587697", "totalAgreePremium": -2000.0, "createdBy": "admin2", "name": "李大头", "isSociaSecurity": "2", "certificateValidDate": "2023-09-03 00:00:00", "beneficaryInfoList": [{"updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "baseInfoId": "1565967578621087744", "createdDate": "2022-09-03 15:39:31", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "726a842c2397474d8fb8ba20b247adb3", "id": "c058b7749ceb46b2be9498b5ac150a55"}], "masterMark": "1", "age": 39, "certificateType": "01"}, {"birthday": "1983-01-01 00:00:00", "personnelAttribute": "100", "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "relationshipWithApplicant": "9", "id": "c792988982854b87aa32eeaaa320d75c", "totalStandardPremium": 2000.0, "pregnancyWeek": 0, "riskPersonNo": "1", "updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "address": "李大胆", "idRiskClass": "e6c02562c4bf4dfc94fe9deb994e73e2", "virtualInsuredNum": 0, "totalActualPremium": 2000.0, "postcode": "否", "certificateIssueDate": "2022-09-03 00:00:00", "personnelCode": "CL0000000000981", "vehicleNum": 0, "certificateNo": "110101198301019930", "createdDate": "2022-09-03 15:39:31", "mobileTelephone": "15876587698", "totalAgreePremium": 2000.0, "createdBy": "admin2", "name": "李大胆", "isSociaSecurity": "2", "certificateValidDate": "2023-09-03 00:00:00", "beneficaryInfoList": [{"updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "baseInfoId": "1565967578621087744", "createdDate": "2022-09-03 15:39:31", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "c792988982854b87aa32eeaaa320d75c", "id": "8bf41991a2b347bd99bf8177043d183f"}], "masterMark": "1", "age": 39, "certificateType": "01"}, {"birthday": "1988-04-20 23:00:00", "personnelAttribute": "100", "clientNo": "6072945D130A4A969AC4", "policyNo": "1**********00000444", "updatedDate": "2022-09-08 15:32:22", "relationshipWithApplicant": "9", "id": "f980664e4bca41b2857a16f1b64ffa41", "totalStandardPremium": 0.0, "email": "", "pregnancyWeek": 0, "riskPersonNo": "4", "updatedBy": "TEST", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "address": "", "idRiskClass": "94531DEF169543E5A0B0D5FFC27F33DD", "professionCode": "", "totalActualPremium": 0.0, "postcode": "", "personnelCode": "19000000000000010022", "vehicleNum": 0, "certificateNo": "320324198804210547", "sexCode": "F", "createdDate": "2022-09-08 15:32:22", "nationality": "", "totalAgreePremium": 0.0, "createdBy": "TEST", "riskDetail": "{}", "name": "哈哈", "beneficaryInfoList": [{"createdDate": "2022-09-08 15:32:23", "updatedBy": "TEST", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "createdBy": "TEST", "isLegal": "1", "personnelType": "1", "benefitType": "001", "endorseNo": "", "policyNo": "1**********00000444", "riskPersonId": "f980664e4bca41b2857a16f1b64ffa41", "id": "B5005FAFC82E441C9F99FB0677D074FA", "updatedDate": "2022-09-08 15:32:23"}], "age": 34, "certificateType": "01"}], "riskGroupType": "18", "id": "b1f791df894a43b18463c1a859d86385", "totalStandardPremium": 2813.0, "combinedProductCode": "MP19000056", "beneficaryInfoList": [{"updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "baseInfoId": "1565967578621087744", "createdDate": "2022-09-03 15:39:31", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "1a4a8990d90a43d08963b2ee7868567e", "id": "563b65f156764f978a4699971c02904e"}, {"updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "baseInfoId": "1565967578621087744", "createdDate": "2022-09-03 15:39:31", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "c792988982854b87aa32eeaaa320d75c", "id": "8bf41991a2b347bd99bf8177043d183f"}, {"createdDate": "2022-09-08 15:32:23", "updatedBy": "TEST", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "createdBy": "TEST", "isLegal": "1", "personnelType": "1", "benefitType": "001", "endorseNo": "", "policyNo": "1**********00000444", "riskPersonId": "f980664e4bca41b2857a16f1b64ffa41", "id": "B5005FAFC82E441C9F99FB0677D074FA", "updatedDate": "2022-09-08 15:32:23"}, {"updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "baseInfoId": "1565967578621087744", "createdDate": "2022-09-03 15:39:31", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "726a842c2397474d8fb8ba20b247adb3", "id": "c058b7749ceb46b2be9498b5ac150a55"}]}], "payInfoList": [{"paymentPersonName": "路明非", "noticeNo": "", "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:38:59", "baseInfoId": "1565967578621087744", "installmentType": "0", "paymentItemName": "0", "agreePremium": 6000.0, "paymentEndDate": "2022-09-03 23:59:59", "paymentItem": "0", "comExchangeRate": 1.0, "currencyName": "", "archiveDate": "2023-09-03 00:00:00", "statusName": "收款", "noticeStatus": "", "id": "7d384064488c48349f3f42afcc3b1c6b", "updatedBy": "admin2", "isInstallment": "0", "payerTypeName": "", "createdDate": "2022-09-03 15:38:59", "createdBy": "admin2", "termNo": 1, "paymentBeginDate": "2022-09-03 00:00:00", "billTypeName": "", "currencyCode": "CNY", "defComExchangeRate": 1.0, "status": "01"}], "beneficaryInfoList": [{"updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "baseInfoId": "1565967578621087744", "createdDate": "2022-09-03 15:39:31", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "1a4a8990d90a43d08963b2ee7868567e", "id": "563b65f156764f978a4699971c02904e"}, {"updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "baseInfoId": "1565967578621087744", "createdDate": "2022-09-03 15:39:31", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "c792988982854b87aa32eeaaa320d75c", "id": "8bf41991a2b347bd99bf8177043d183f"}, {"createdDate": "2022-09-08 15:32:23", "updatedBy": "TEST", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "createdBy": "TEST", "isLegal": "1", "personnelType": "1", "benefitType": "001", "endorseNo": "", "policyNo": "1**********00000444", "riskPersonId": "f980664e4bca41b2857a16f1b64ffa41", "id": "B5005FAFC82E441C9F99FB0677D074FA", "updatedDate": "2022-09-08 15:32:23"}, {"updatedBy": "admin2", "riskGroupId": "b1f791df894a43b18463c1a859d86385", "isLegal": "1", "endorseNo": "", "benefitRatio": 1.0, "policyNo": "1**********00000444", "updatedDate": "2022-09-03 15:39:31", "baseInfoId": "1565967578621087744", "createdDate": "2022-09-03 15:39:31", "createdBy": "admin2", "personnelType": "1", "name": "法定", "riskPersonId": "726a842c2397474d8fb8ba20b247adb3", "id": "c058b7749ceb46b2be9498b5ac150a55"}]}, "hideBusinessDetail": false, "commissionCharge": "0.00", "commissionChargeProportion": "0.00%", "hideEmployeeInfo": false, "technicProductCode": "TP1900018", "isElecSubPolicyNo": "0", "endorseSceneInfoDTO": {"plyPrintTimes": 0, "edrPrintTimes": 0}, "saleChannelSourceName": "", "isShowCommission": true, "hideChannelInfo": false, "delLicenceWhippletre": false, "hideSaleGroupName": false, "saleDepartmentCode": "2320000000", "isShowCommissionInter": true, "showPremium": false}