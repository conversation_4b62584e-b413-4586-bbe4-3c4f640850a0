{"returnCode": "200", "returnMsg": "success", "data": {"contractDTO": {"index": 0, "baseInfo": {"id": "1666351272397889536", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "applyPolicyNo": "Z7083335922005640839", "endorseNo": "", "quotationNo": "10664A3201002200006G", "index": 0, "totalStandardPremium": 72, "totalAgreePremium": 72, "totalActualPremium": 6, "totalInsuredAmount": 6000000, "discount": 1, "totalVATExcludedPremium": 71.16, "totalValueAddedTax": 0.84, "discountPermium": 72, "commissionRate": 0.13, "commissionAmount": 112.3, "saleRate": 1.02, "saleAmount": 0.23, "performanceRate": 1.02, "performanceAmount": 0.23, "atTotalPremium": 71.16, "rateTotal": 3.06, "amountTotal": 0.69, "amountCurrencyCode": "CNY", "premiumCurrencyCode": "CNY", "dataSource": "SYSTEM", "inputBy": "TEST", "inputDate": "2022-09-19 17:17:01", "productCode": "MP02P00002", "productName": "少儿医疗（渠道联调）", "coinsuranceMark": "1", "renewalType": "0", "underwriteDate": "2022-09-19 00:00:00", "productVersion": "1.13", "inputByName": "", "status": "B5", "insuranceBeginDate": "2023-06-08 00:00:00", "insuranceEndDate": "2024-06-07 23:59:59", "businessType": "P", "acceptInsuranceDate": "2023-04-11 17:08:07", "payTermNo": 12, "departmentCode": "651", "underwriteName": "", "productKind": "0", "carBusinessType": "0", "insuranceLimit": "365天", "dayNum": "-2天", "quotationName": "", "contractPactCode": "", "productClass": "02", "shareholdersFlag": "1", "insuranceDate": "2022-09-19 00:00:00", "onlineOrderNo": "35353301"}, "extendInfo": {"id": "1666351272666324992", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "baseInfoId": "1666351272397889536", "isPolicyBeforePayfee": "0", "isPastsign": "", "disputedSettleMode": "01", "validateCode": "", "applyApproach": "01", "underwriteRuleTips": "[{\"declaration\":1,\"declarationKey\":\"\",\"declarationType\":1,\"isUnderWriting\":2,\"underWritingInfo\":{\"uwConclusion\":\"核保结论\"},\"underWritingVersion\":\"\"}]", "isGiftInsurance": "0", "isStarHighInsurance": "0", "isFacultativeBusiness": "0", "isVipCustomer": "0", "isContainVirtualInsurance": "", "isTransProtectBusiness": "0", "transProtectReason": "", "lastYearUnderwriteCompany": "0", "dutyDifferent": "", "historyUnderwriteOrClaimDetail": "", "relatedPersonNum": 0, "isGroupHead": 0, "isGroupPayment": 0}, "saleInfo": {"id": "8181868931e64cc0aa406b093c4e4249", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "agentInfoList": [{"id": "cd5e4670aca545a184e36ec74465a49f", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "saleInfoId": "8181868931e64cc0aa406b093c4e4249", "agencyCode": "3543535354", "agencyName": "", "agentCode": "1001", "agentName": "代理人名称", "agentAgreementNo": "10", "supplementAgreementNo": "1001", "masterMark": "1", "agencySaleName": "", "agencySaleProfCertifNo": ""}], "brokerInfoList": [{"id": "26db7e3a46b64e26b805c3efff827aef", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "saleInfoId": "8181868931e64cc0aa406b093c4e4249", "brokerCode": "6575711", "brokerName": "经纪人名称", "brokerAddress": "fdfdgdfg", "masterMark": "1", "agencySaleName": "", "agencySaleProfCertifNo": "1", "agentCharacter": "", "agentChineseName": "", "registerPlace": "1", "telephone": "", "mobile": ""}], "employeeInfoList": [{"index": 0, "employeeCode": "200000163", "employeeName": "邵丽萍"}], "partnerInfoList": [{"id": "565da440106244f9ab5a0b23843d6d1a", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "saleInfoId": "8181868931e64cc0aa406b093c4e4249", "partnerType": "01", "partnerName": "", "partnerCode": "34"}], "baseInfoId": "1666351272397889536", "departmentCode": "651", "departmentName": "三星财险-内部账户组", "businessSourceCode": "WB00000001", "businessSourceName": "WB00000001-微保", "businessSourceDetailCode": "", "channelSourceCode": "200", "channelSourceName": "代理业务", "channelSourceDetailCode": "210", "channelSourceDetailName": "个人代理人业务", "signDate": "2023-06-07 15:47:57", "applyDate": "2023-06-07 15:47:57", "teamType": "10", "contractPactCode": "10", "insuredType": "新保", "shareholdersFlag": "1", "applyApproach": "01", "productClass": "健康险"}, "payInfoList": [{"id": "14976577754b40f383b666668ec52b98", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 1, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2023-06-08 00:00:00", "paymentEndDate": "2023-06-12 23:59:59", "actualPremium": 6, "noticeNo": "", "noticeStatus": "2", "status": "01", "actualDate": "2023-06-07 15:48:08", "paymentBeginCircDate": "2023-06-08 00:00:00", "paymentEndCircDate": "2023-07-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}, {"id": "dcee0275fb7246a5a48dff76d7dffdf9", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 2, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2023-07-08 00:00:00", "paymentEndDate": "2023-07-12 23:59:59", "noticeNo": "", "noticeStatus": "", "status": "01", "paymentBeginCircDate": "2023-07-08 00:00:00", "paymentEndCircDate": "2023-08-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}, {"id": "3ee4f89ca3f64aafb8ed810aeccb20f0", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 3, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2023-08-08 00:00:00", "paymentEndDate": "2023-08-12 23:59:59", "noticeNo": "", "noticeStatus": "", "status": "01", "paymentBeginCircDate": "2023-08-08 00:00:00", "paymentEndCircDate": "2023-09-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}, {"id": "6da7096f02864304bfbb1766502221eb", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 4, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2023-09-08 00:00:00", "paymentEndDate": "2023-09-12 23:59:59", "noticeNo": "", "noticeStatus": "", "status": "01", "paymentBeginCircDate": "2023-09-08 00:00:00", "paymentEndCircDate": "2023-10-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}, {"id": "b60e9d5b7372489d8133f8bc07f57e99", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 5, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2023-10-08 00:00:00", "paymentEndDate": "2023-10-12 23:59:59", "noticeNo": "", "noticeStatus": "", "status": "01", "paymentBeginCircDate": "2023-10-08 00:00:00", "paymentEndCircDate": "2023-11-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}, {"id": "b86b73ef81c14a369986096de9a05dfc", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 6, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2023-11-08 00:00:00", "paymentEndDate": "2023-11-12 23:59:59", "noticeNo": "", "noticeStatus": "", "status": "01", "paymentBeginCircDate": "2023-11-08 00:00:00", "paymentEndCircDate": "2023-12-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}, {"id": "c61dba1f376d4e7193c07af275cf12c1", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 7, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2023-12-08 00:00:00", "paymentEndDate": "2023-12-12 23:59:59", "noticeNo": "", "noticeStatus": "", "status": "01", "paymentBeginCircDate": "2023-12-08 00:00:00", "paymentEndCircDate": "2024-01-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}, {"id": "9462d7f09bd64ddc809f84c6a8f0a587", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 8, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2024-01-08 00:00:00", "paymentEndDate": "2024-01-12 23:59:59", "noticeNo": "", "noticeStatus": "", "status": "01", "paymentBeginCircDate": "2024-01-08 00:00:00", "paymentEndCircDate": "2024-02-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}, {"id": "b3daec3420a64a148aa58a63097a4e15", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 9, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2024-02-08 00:00:00", "paymentEndDate": "2024-02-12 23:59:59", "noticeNo": "", "noticeStatus": "", "status": "01", "paymentBeginCircDate": "2024-02-08 00:00:00", "paymentEndCircDate": "2024-03-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}, {"id": "b9a69ba60cdd420794962c94c05d120d", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 10, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2024-03-08 00:00:00", "paymentEndDate": "2024-03-12 23:59:59", "noticeNo": "", "noticeStatus": "", "status": "01", "paymentBeginCircDate": "2024-03-08 00:00:00", "paymentEndCircDate": "2024-04-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}, {"id": "da58e702fa8e4d1482321e64887da144", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 11, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2024-04-08 00:00:00", "paymentEndDate": "2024-04-12 23:59:59", "noticeNo": "", "noticeStatus": "", "status": "01", "paymentBeginCircDate": "2024-04-08 00:00:00", "paymentEndCircDate": "2024-05-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}, {"id": "9481a02d4d044d87bf8b6663ea058fc4", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "installmentType": "1", "paymentPath": "10", "currencyCode": "CNY", "curTermAgreePremium": 6, "termNo": 12, "paymentPersonName": "付款人姓名", "agreePremium": 6, "paymentBeginDate": "2024-05-08 00:00:00", "paymentEndDate": "2024-05-12 23:59:59", "noticeNo": "", "noticeStatus": "", "status": "01", "paymentBeginCircDate": "2024-05-08 00:00:00", "paymentEndCircDate": "2024-06-07 23:59:59", "paymentItem": "0", "vatexcludedPremium": 5.93, "valueAddedTax": 0.07, "commissionAmount": 9.36}], "riskGroupInfoList": [{"id": "d4681bd716f043ee9e1c2b0baa5a2705", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "riskPersonInfoList": [{"id": "023f444e11434ea1a7d70ee0bd119da9", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "personnelAttribute": "100", "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "familyNameSpell": "", "firstNameSpell": "", "name": "被保人RR", "relationshipWithApplicant": "05", "beneficaryInfoList": [{"id": "492325946de04cb89f4330fe5935bbb4", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "name": "法定", "clientNo": "07E2AAD188A94036B403", "riskPersonId": "023f444e11434ea1a7d70ee0bd119da9", "isLegal": "1", "baseInfoId": "1666351272397889536", "linkModeType": ""}], "masterMark": "1", "birthday": "2006-02-11 00:00:00", "birthdayStr": "2006-02-11", "age": 17, "sexCode": "M", "nationality": "156", "certificateNo": "456457474", "certificateType": "02", "certificateIssueDate": "2023-04-11 00:00:00", "certificateValidDate": "2022-09-20 00:00:00", "professionCode": "A0202002", "address": "fsfsfs", "email": "<EMAIL>", "mobileTelephone": "13343546456", "clientNo": "CL000000000000000128", "linkModeType": "03", "professionClass": "1", "riskPersonNo": "", "riskDetail": "", "opertaionGrade": "", "isSociaSecurity": "1", "pregnancyWeek": 0, "partnerName": "", "vehicleNum": 0, "hospitalGrade": "", "hospitalDepart": "", "opertaionName": "", "surgeonLevel": "", "anaesthesiaType": "", "anaesthesiaGrade": "", "inpatientOutpatient": "", "isMajorSurgical": "", "vaccinesType": "", "vaccinesName": "", "brandModel": "", "ownerDriver": "", "vehicleSeats": "", "vehicleTonnages": "", "buildProjectCalculationType": "", "buildProjectName": "", "buildProjectType": "", "buildProjectTotalFund": "100.00", "buildProjectAreaFund": "20.00", "province": "140000", "city": "140300", "county": "140311", "departureAddress": "", "destinationCountry": "", "yearlySalaries": 120, "workUnit": "35354", "nationalityName": "156", "ageUnit": 0}], "planInfoList": [{"id": "182541fe3088497fb68bdd5c117fb12c", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "totalStandardPremium": 14.4, "totalAgreePremium": 14.4, "totalActualPremium": 1.2, "totalInsuredAmount": 6000000, "totalVATExcludedPremium": 14.4, "totalValueAddedTax": 0, "dutyInfoList": [{"id": "a64df88bbff1439b985c7701314f6751", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "premiumRate": 2e-07, "totalStandardPremium": 14.4, "totalAgreePremium": 14.4, "totalActualPremium": 1.2, "totalInsuredAmount": 6000000, "discountProportion": 1, "totalVATExcludedPremium": 14.4, "totalValueAddedTax": 0, "discountPermium": 14.4, "commissionRate": 0.13, "commissionAmount": 22.46, "saleRate": 1, "performanceRate": 1, "discount": 100, "dutyDetailInfoList": [{"id": "a4df3278050711eeb6059ea3550221fc", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyDetailCode": "DD00462", "dutyDetailName": "住院前后门急诊保险金", "claimProportion": 1, "effectiveDate": "2023-04-10 11:38:56", "dutyDetailType": "04", "fixedLimit": 300}, {"id": "a4df64d5050711eeb6059ea3550221fc", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyDetailCode": "DD00463", "dutyDetailName": "指定门急诊医疗保险金", "claimProportion": 1, "effectiveDate": "2023-04-10 11:39:28", "dutyDetailType": "04", "fixedLimit": 300.0}, {"id": "a4df919c050711eeb6059ea3550221fc", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyDetailCode": "DD00464", "dutyDetailName": "疾病门急诊", "claimProportion": 1, "effectiveDate": "2023-04-10 11:39:50", "dutyDetailType": "04", "fixedLimit": 300.0}], "riskDutyRelationInfoList": [{"id": "1666351275644280832", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:47:58", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "riskInfoId": "023f444e11434ea1a7d70ee0bd119da9", "insuredAmount": 6000000, "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "agreePremium": 14.4, "standardPremium": 14.4, "insuranceBeginDate": "2023-06-08 00:00:00", "insuranceEndDate": "2024-06-07 23:59:59", "totalInsuredAmount": 6000000, "totalActualPremium": 14.4}], "dutyAttributeInfoList": [{"id": "0f28cd4da6ab46d9a99993654c38bf05", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyAttrCode": "174", "dutyAttrAmountValue": "30", "dutyAttributeName": "等待期", "dutyAttributeUnit": "天"}, {"id": "d3b8a75b79a34734a63a97c5622bdcbc", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyAttrCode": "271", "dutyAttrAmountValue": "10000", "dutyAttributeName": "免赔额"}, {"id": "087bb5cb028d473390643a38694e83e2", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyAttrCode": "360", "dutyAttrAmountValue": "0", "dutyAttributeName": "是否区分社保"}, {"id": "7a58bee59d444131b01afc689fb76231", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyAttrCode": "361", "dutyAttributeName": "有社保", "dutyAttrDetailList": [{"id": "09cb4424a6bf4f1b8d56ff9cf0960011", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyAttrInfoId": "7a58bee59d444131b01afc689fb76231", "dutyAttrDetailCode": "361_4001", "dutyAttrDetailValue": "60", "attrRowNo": "0", "attrColumnNo": "0", "dutyAttributeDetailName": "未经医保结算赔付比例(%)"}, {"id": "224eace9b75946f2a0938fc9125b69e3", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyAttrInfoId": "7a58bee59d444131b01afc689fb76231", "dutyAttrDetailCode": "361_4000", "dutyAttrDetailValue": "100", "attrRowNo": "0", "attrColumnNo": "0", "dutyAttributeDetailName": "经医保结算赔付比例(%)"}, {"id": "2f253eb391e9466c90add0d89a35c72a", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyAttrInfoId": "7a58bee59d444131b01afc689fb76231", "dutyAttrDetailCode": "361_1003", "dutyAttrDetailValue": "3", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例类型"}]}, {"id": "5d2fd361f7bb4075809f03699122aa51", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyAttrCode": "362", "dutyAttributeName": "无社保", "dutyAttrDetailList": [{"id": "c5250af969d048fe846f9e10155caf3b", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyAttrInfoId": "5d2fd361f7bb4075809f03699122aa51", "dutyAttrDetailCode": "362_1003", "dutyAttrDetailValue": "0", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例类型"}, {"id": "e6d28e49c8dd423ab7e73aca2ed41b3b", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a64df88bbff1439b985c7701314f6751", "dutyAttrInfoId": "5d2fd361f7bb4075809f03699122aa51", "dutyAttrDetailCode": "362_1004", "dutyAttrDetailValue": "100", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例(%)"}]}], "planInfoId": "182541fe3088497fb68bdd5c117fb12c", "dutyCode": "C00011", "dutyName": "一般医疗保险金", "insuredAmount": 6000000, "waitPeriod": 15, "dutySharedAmountMerge": "C00011,C00012,C00013,C00014,C00015", "isDutySharedAmount": "0", "sort": 1}], "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "planCode": "PL0200185", "planName": "一般医疗保险金test-CJB", "isMain": "1", "regulatorType": "YL", "sort": 1, "productClass": "02", "taxRate": 0}, {"id": "38fe5cb90a07421083f817bae17ea3e1", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "totalStandardPremium": 14.4, "totalAgreePremium": 14.4, "totalActualPremium": 1.2, "totalInsuredAmount": 6000000, "totalVATExcludedPremium": 14.4, "totalValueAddedTax": 0, "dutyInfoList": [{"id": "a34be9dab5734d48ba9ff3ab3d677242", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "premiumRate": 2e-07, "totalStandardPremium": 14.4, "totalAgreePremium": 14.4, "totalActualPremium": 1.2, "totalInsuredAmount": 6000000, "discountProportion": 1, "totalVATExcludedPremium": 14.4, "totalValueAddedTax": 0, "discountPermium": 14.4, "commissionRate": 0.13, "commissionAmount": 22.46, "saleRate": 1, "performanceRate": 1, "discount": 100, "dutyDetailInfoList": [{"id": "a4c3164b050711eeb6059ea3550221fc", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "dutyDetailCode": "DD00465", "dutyDetailName": "意外门急诊", "claimProportion": 1, "effectiveDate": "2023-04-10 11:40:06", "dutyDetailType": "04", "fixedLimit": 300.0}], "riskDutyRelationInfoList": [{"id": "1666351274180468736", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:47:58", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "riskInfoId": "023f444e11434ea1a7d70ee0bd119da9", "insuredAmount": 6000000, "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "agreePremium": 14.4, "standardPremium": 14.4, "insuranceBeginDate": "2023-06-08 00:00:00", "insuranceEndDate": "2024-06-07 23:59:59", "totalInsuredAmount": 6000000, "totalActualPremium": 14.4}], "dutyAttributeInfoList": [{"id": "ed0291fbf5424016b3e1cdb5e7918f33", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "dutyAttrCode": "174", "dutyAttrAmountValue": "30", "dutyAttributeName": "等待期", "dutyAttributeUnit": "天"}, {"id": "5feb3835f8424e30a5b2b7335c1ed552", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "dutyAttrCode": "271", "dutyAttrAmountValue": "10000", "dutyAttributeName": "免赔额"}, {"id": "f346064750b6429dbf7e012f0db06313", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "dutyAttrCode": "360", "dutyAttrAmountValue": "0", "dutyAttributeName": "是否区分社保"}, {"id": "3ee0fa6e450c4720a59411a26a15916c", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "dutyAttrCode": "361", "dutyAttributeName": "有社保", "dutyAttrDetailList": [{"id": "a4a958c5e8b346139436e925212c54fb", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "dutyAttrInfoId": "3ee0fa6e450c4720a59411a26a15916c", "dutyAttrDetailCode": "361_4001", "dutyAttrDetailValue": "60", "attrRowNo": "0", "attrColumnNo": "0", "dutyAttributeDetailName": "未经医保结算赔付比例(%)"}, {"id": "b3286ae8c2334b28ac8595d073f7590d", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "dutyAttrInfoId": "3ee0fa6e450c4720a59411a26a15916c", "dutyAttrDetailCode": "361_4000", "dutyAttrDetailValue": "100", "attrRowNo": "0", "attrColumnNo": "0", "dutyAttributeDetailName": "经医保结算赔付比例(%)"}, {"id": "d5bac9e7715647d6ba64ebeaf2a66ce2", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "dutyAttrInfoId": "3ee0fa6e450c4720a59411a26a15916c", "dutyAttrDetailCode": "361_1003", "dutyAttrDetailValue": "3", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例类型"}]}, {"id": "97cf9fe3d89a46acbc1c133986fe3b9b", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "dutyAttrCode": "362", "dutyAttributeName": "无社保", "dutyAttrDetailList": [{"id": "564ded3fa6d94324bda344589f295913", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "dutyAttrInfoId": "97cf9fe3d89a46acbc1c133986fe3b9b", "dutyAttrDetailCode": "362_1003", "dutyAttrDetailValue": "0", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例类型"}, {"id": "99117296699b4fb8a4bc3fe9c8eaa3bb", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "a34be9dab5734d48ba9ff3ab3d677242", "dutyAttrInfoId": "97cf9fe3d89a46acbc1c133986fe3b9b", "dutyAttrDetailCode": "362_1004", "dutyAttrDetailValue": "100", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例(%)"}]}], "planInfoId": "38fe5cb90a07421083f817bae17ea3e1", "dutyCode": "C00012", "dutyName": "传染病医疗保险金test", "insuredAmount": 6000000, "waitPeriod": 15, "dutySharedAmountMerge": "C00011,C00012,C00013,C00014,C00015", "isDutySharedAmount": "0", "sort": 1}], "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "planCode": "PL0200194", "planName": "传染病医疗保险金test", "isMain": "1", "regulatorType": "YL", "sort": 2, "productClass": "02", "taxRate": 0}, {"id": "81e8ee4c7ba44da3a112b42349ad53d4", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "totalStandardPremium": 14.4, "totalAgreePremium": 14.4, "totalActualPremium": 1.2, "totalInsuredAmount": 6000000, "totalVATExcludedPremium": 13.56, "totalValueAddedTax": 0.84, "dutyInfoList": [{"id": "3bd89dde26b24e268d18ba46d3701a94", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "premiumRate": 2e-07, "totalStandardPremium": 14.4, "totalAgreePremium": 14.4, "totalActualPremium": 1.2, "totalInsuredAmount": 6000000, "discountProportion": 1, "totalVATExcludedPremium": 13.56, "totalValueAddedTax": 0.84, "discountPermium": 14.4, "commissionRate": 0.13, "commissionAmount": 22.46, "saleRate": 1, "performanceRate": 1, "discount": 100, "dutyDetailInfoList": [{"id": "a4c9dfba050711eeb6059ea3550221fc", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyDetailCode": "DD00466", "dutyDetailName": "意外门急诊费用test", "claimProportion": 1, "effectiveDate": "2023-04-10 11:40:25", "dutyDetailType": "04", "fixedLimit": 300.0}, {"id": "a4ca0ead050711eeb6059ea3550221fc", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyDetailCode": "DD00499", "dutyDetailName": "意外住院费用test", "claimProportion": 1, "effectiveDate": "2023-04-26 09:20:04", "dutyDetailType": "04", "fixedLimit": 300.0}], "riskDutyRelationInfoList": [{"id": "1666351274574733312", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:47:58", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "riskInfoId": "023f444e11434ea1a7d70ee0bd119da9", "insuredAmount": 6000000, "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "agreePremium": 14.4, "standardPremium": 14.4, "insuranceBeginDate": "2023-06-08 00:00:00", "insuranceEndDate": "2024-06-07 23:59:59", "totalInsuredAmount": 6000000, "totalActualPremium": 14.4}], "dutyAttributeInfoList": [{"id": "ab51da774fe74888b6a9e5a0fea2bd97", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyAttrCode": "174", "dutyAttrAmountValue": "30", "dutyAttributeName": "等待期", "dutyAttributeUnit": "天"}, {"id": "44370ef5bc3940dd815831b46cb818db", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyAttrCode": "271", "dutyAttrAmountValue": "10000", "dutyAttributeName": "免赔额"}, {"id": "942bd85f2dc5489ea58cba6ab8c133fd", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyAttrCode": "360", "dutyAttrAmountValue": "0", "dutyAttributeName": "是否区分社保"}, {"id": "70900da617204db2802a395582377019", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyAttrCode": "361", "dutyAttributeName": "有社保", "dutyAttrDetailList": [{"id": "5743130d7c524715a171032851a0be7b", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyAttrInfoId": "70900da617204db2802a395582377019", "dutyAttrDetailCode": "361_4001", "dutyAttrDetailValue": "60", "attrRowNo": "0", "attrColumnNo": "0", "dutyAttributeDetailName": "未经医保结算赔付比例(%)"}, {"id": "a95fe8f17e584b8da20399bd52891bb2", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyAttrInfoId": "70900da617204db2802a395582377019", "dutyAttrDetailCode": "361_4000", "dutyAttrDetailValue": "100", "attrRowNo": "0", "attrColumnNo": "0", "dutyAttributeDetailName": "经医保结算赔付比例(%)"}, {"id": "fa98c00be6ea4e1f9cb8f0fc57ef6073", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyAttrInfoId": "70900da617204db2802a395582377019", "dutyAttrDetailCode": "361_1003", "dutyAttrDetailValue": "3", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例类型"}]}, {"id": "134601bd11e945ef84b8f505ab75bf9f", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyAttrCode": "362", "dutyAttributeName": "无社保", "dutyAttrDetailList": [{"id": "6fad31edeb204076ab189251588775dd", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyAttrInfoId": "134601bd11e945ef84b8f505ab75bf9f", "dutyAttrDetailCode": "362_1003", "dutyAttrDetailValue": "0", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例类型"}, {"id": "8b972be4f81c4912a3611a27dbfad4bd", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "3bd89dde26b24e268d18ba46d3701a94", "dutyAttrInfoId": "134601bd11e945ef84b8f505ab75bf9f", "dutyAttrDetailCode": "362_1004", "dutyAttrDetailValue": "100", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例(%)"}]}], "planInfoId": "81e8ee4c7ba44da3a112b42349ad53d4", "dutyCode": "C00013", "dutyName": "一般门诊急诊医疗保险金", "insuredAmount": 6000000, "waitPeriod": 15, "dutySharedAmountMerge": "C00011,C00012,C00013,C00014,C00015", "isDutySharedAmount": "0", "sort": 1}], "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "planCode": "PL0200195", "planName": "意外门急诊保险金test", "isMain": "1", "regulatorType": "YY", "sort": 3, "productClass": "02", "taxRate": 0.06}, {"id": "d7c91293c15f4749860c523266c593b8", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "totalStandardPremium": 14.4, "totalAgreePremium": 14.4, "totalActualPremium": 1.2, "totalInsuredAmount": 6000000, "totalVATExcludedPremium": 14.4, "totalValueAddedTax": 0, "dutyInfoList": [{"id": "8539402b83d14f07820020f99d2c43a3", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "premiumRate": 2e-07, "totalStandardPremium": 14.4, "totalAgreePremium": 14.4, "totalActualPremium": 1.2, "totalInsuredAmount": 6000000, "discountProportion": 1, "totalVATExcludedPremium": 14.4, "totalValueAddedTax": 0, "discountPermium": 14.4, "commissionRate": 0.13, "commissionAmount": 22.46, "saleRate": 1, "performanceRate": 1, "discount": 100, "dutyDetailInfoList": [{"id": "a4d10ebe050711eeb6059ea3550221fc", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "dutyDetailCode": "DD00467", "dutyDetailName": "特定疾病含特需医疗保险金test", "claimProportion": 1, "effectiveDate": "2023-04-10 11:40:37", "dutyDetailType": "04", "fixedLimit": 300.0}], "riskDutyRelationInfoList": [{"id": "1666351275069661184", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:47:58", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "riskInfoId": "023f444e11434ea1a7d70ee0bd119da9", "insuredAmount": 6000000, "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "agreePremium": 14.4, "standardPremium": 14.4, "insuranceBeginDate": "2023-06-08 00:00:00", "insuranceEndDate": "2024-06-07 23:59:59", "totalInsuredAmount": 6000000, "totalActualPremium": 14.4}], "dutyAttributeInfoList": [{"id": "a738674abd4a4bb68ed11560dd502cec", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "dutyAttrCode": "174", "dutyAttrAmountValue": "30", "dutyAttributeName": "等待期", "dutyAttributeUnit": "天"}, {"id": "7bba6010719f4192b37e612c3ac43b4d", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "dutyAttrCode": "271", "dutyAttrAmountValue": "10000", "dutyAttributeName": "免赔额"}, {"id": "f4326af51a3c49edb28f5aba2c6186c5", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "dutyAttrCode": "360", "dutyAttrAmountValue": "0", "dutyAttributeName": "是否区分社保"}, {"id": "aa2e8ce590714d8c91ee518e12a341e7", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "dutyAttrCode": "361", "dutyAttributeName": "有社保", "dutyAttrDetailList": [{"id": "4c5633d50c074d91b5c0f0053270067c", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "dutyAttrInfoId": "aa2e8ce590714d8c91ee518e12a341e7", "dutyAttrDetailCode": "361_4000", "dutyAttrDetailValue": "100", "attrRowNo": "0", "attrColumnNo": "0", "dutyAttributeDetailName": "经医保结算赔付比例(%)"}, {"id": "5d1e41b391a24284bddb7b360302cba8", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "dutyAttrInfoId": "aa2e8ce590714d8c91ee518e12a341e7", "dutyAttrDetailCode": "361_4001", "dutyAttrDetailValue": "60", "attrRowNo": "0", "attrColumnNo": "0", "dutyAttributeDetailName": "未经医保结算赔付比例(%)"}, {"id": "6f0e8593f1c34851b149c21a56376a21", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "dutyAttrInfoId": "aa2e8ce590714d8c91ee518e12a341e7", "dutyAttrDetailCode": "361_1003", "dutyAttrDetailValue": "3", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例类型"}]}, {"id": "eaff6cea4ec94967a8b05a7c9ad66596", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "dutyAttrCode": "362", "dutyAttributeName": "无社保", "dutyAttrDetailList": [{"id": "2b3980326d8d42fbbb3ceb7017fde9db", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "dutyAttrInfoId": "eaff6cea4ec94967a8b05a7c9ad66596", "dutyAttrDetailCode": "362_1003", "dutyAttrDetailValue": "0", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例类型"}, {"id": "f955103c6fde4d91bd7a54bab29c9175", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "8539402b83d14f07820020f99d2c43a3", "dutyAttrInfoId": "eaff6cea4ec94967a8b05a7c9ad66596", "dutyAttrDetailCode": "362_1004", "dutyAttrDetailValue": "100", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例(%)"}]}], "planInfoId": "d7c91293c15f4749860c523266c593b8", "dutyCode": "C00014", "dutyName": "特定疾病住院保险金", "insuredAmount": 6000000, "waitPeriod": 15, "dutySharedAmountMerge": "C00011,C00012,C00013,C00014,C00015", "isDutySharedAmount": "0", "sort": 1}], "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "planCode": "PL0200196", "planName": "特定疾病（含特需）医疗保险金test", "isMain": "1", "regulatorType": "YL", "sort": 4, "productClass": "02", "taxRate": 0}, {"id": "5e5623b11ff04051b0686d27e5aef2fe", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "totalStandardPremium": 14.4, "totalAgreePremium": 14.4, "totalActualPremium": 1.2, "totalInsuredAmount": 6000000, "totalVATExcludedPremium": 14.4, "totalValueAddedTax": 0, "dutyInfoList": [{"id": "f81548bda56b4ea693a21db42e75ed66", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "premiumRate": 2e-07, "totalStandardPremium": 14.4, "totalAgreePremium": 14.4, "totalActualPremium": 1.2, "totalInsuredAmount": 6000000, "discountProportion": 1, "totalVATExcludedPremium": 14.4, "totalValueAddedTax": 0, "discountPermium": 14.4, "commissionRate": 0.13, "commissionAmount": 22.46, "saleRate": 1, "performanceRate": 1, "discount": 100, "dutyDetailInfoList": [{"id": "a4d812cf050711eeb6059ea3550221fc", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyDetailCode": "DD00468", "dutyDetailName": "特定药品费用test", "claimProportion": 1, "effectiveDate": "2023-04-10 11:40:49", "dutyDetailType": "04", "fixedLimit": 300.0}], "riskDutyRelationInfoList": [{"id": "1666351275291959296", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:47:58", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "riskInfoId": "023f444e11434ea1a7d70ee0bd119da9", "insuredAmount": 6000000, "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "agreePremium": 14.4, "standardPremium": 14.4, "insuranceBeginDate": "2023-06-08 00:00:00", "insuranceEndDate": "2024-06-07 23:59:59", "totalInsuredAmount": 6000000, "totalActualPremium": 14.4}], "dutyAttributeInfoList": [{"id": "dde3279b00cc42d58d9111d856aef312", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyAttrCode": "174", "dutyAttrAmountValue": "30", "dutyAttributeName": "等待期", "dutyAttributeUnit": "天"}, {"id": "cb1cb8c18e2c4151be5eb4df75f3e110", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyAttrCode": "271", "dutyAttrAmountValue": "10000", "dutyAttributeName": "免赔额"}, {"id": "3df67b90b12a4db9b67b625d36d42e69", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyAttrCode": "6", "dutyAttrAmountValue": "2000000", "dutyAttributeName": "赔偿限额"}, {"id": "36a7f7550c2846e688546aefd7d30b5a", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyAttrCode": "360", "dutyAttrAmountValue": "0", "dutyAttributeName": "是否区分社保"}, {"id": "45a4d1a9e16446458c678e9a937352a9", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyAttrCode": "361", "dutyAttributeName": "有社保", "dutyAttrDetailList": [{"id": "00017551da6549bf82256cda4e16625f", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyAttrInfoId": "45a4d1a9e16446458c678e9a937352a9", "dutyAttrDetailCode": "361_1003", "dutyAttrDetailValue": "3", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例类型"}, {"id": "2b2a88621a5f4ae3a078ac2f3c8b72c7", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyAttrInfoId": "45a4d1a9e16446458c678e9a937352a9", "dutyAttrDetailCode": "361_4000", "dutyAttrDetailValue": "100", "attrRowNo": "0", "attrColumnNo": "0", "dutyAttributeDetailName": "经医保结算赔付比例(%)"}, {"id": "9557b9e92d1d4c679a7b916195df5ff7", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyAttrInfoId": "45a4d1a9e16446458c678e9a937352a9", "dutyAttrDetailCode": "361_4001", "dutyAttrDetailValue": "60", "attrRowNo": "0", "attrColumnNo": "0", "dutyAttributeDetailName": "未经医保结算赔付比例(%)"}]}, {"id": "404c5cea7cc04f078b7cc9ace0ff351b", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyAttrCode": "362", "dutyAttributeName": "无社保", "dutyAttrDetailList": [{"id": "159cb26c57304f7bb69625bece7c774a", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyAttrInfoId": "404c5cea7cc04f078b7cc9ace0ff351b", "dutyAttrDetailCode": "362_1003", "dutyAttrDetailValue": "0", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例类型"}, {"id": "a84ab49ee6bc45be918c16138c41d2e6", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "dutyInfoId": "f81548bda56b4ea693a21db42e75ed66", "dutyAttrInfoId": "404c5cea7cc04f078b7cc9ace0ff351b", "dutyAttrDetailCode": "362_1004", "dutyAttrDetailValue": "100", "attrRowNo": "0", "attrColumnNo": "1", "dutyAttributeDetailName": "赔付比例(%)"}]}], "planInfoId": "5e5623b11ff04051b0686d27e5aef2fe", "dutyCode": "C00015", "dutyName": "院外特定药品费用医疗保险金", "insuredAmount": 6000000, "waitPeriod": 15, "dutySharedAmountMerge": "C00011,C00012,C00013,C00014,C00015", "isDutySharedAmount": "0", "sort": 1}], "riskGroupId": "d4681bd716f043ee9e1c2b0baa5a2705", "planCode": "PL0200197", "planName": "特定药品费用保险金test", "isMain": "1", "regulatorType": "YL", "sort": 5, "productClass": "02", "taxRate": 0}], "productClass": "02", "riskGroupType": "15", "baseInfoId": "1666351272397889536", "applyNum": 1, "totalStandardPremium": 72, "totalAgreePremium": 72, "totalActualPremium": 6, "totalInsuredAmount": 1.07, "discountProportion": 1, "discountPermium": 72, "commissionRate": 9.35, "commissionAmount": 1.56, "saleRate": 1, "saleAmount": 0.23, "performanceRate": 1, "performanceAmount": 0.23, "discount": 100, "averagePremium": 6, "averagePremiumCheck": 664, "combinedProductCode": "MP02P00002", "combinedProductName": "少儿医疗（渠道联调）", "productPackageType": "PMP02P00002020", "productPackageName": "微保版", "applyRiskNum": 1, "combinedProductVersion": "1.13", "riskGroupName": "方案一", "virtualRiskNum": 0, "specialPromiseList": [{"id": "1666351316987535360", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "promiseCode": "-", "promiseDesc": "颠三倒四", "promiseType": "N", "sortIndex": 1, "riskGroupName": "方案一", "promisShortName": "测试-李占川052903"}], "riskGroupNo": "1", "succorServerList": [{"createdBy": "SYSTEM", "createdDate": "2023-06-07 15:48:08", "updatedBy": "SYSTEM", "updatedDate": "2023-06-07 15:48:08", "idPlyHealthServices": "2d88e790153f4fbf8ac5cf6f3c40978e", "idPlyRiskGroup": "d4681bd716f043ee9e1c2b0baa5a2705", "combinedCode": "S00000", "combinedName": "工单产品套餐", "combinedDetailDesc": "在线购药（3次）", "applyNum": 1, "price": 55.55, "combinedType": "2"}, {"createdBy": "SYSTEM", "createdDate": "2023-06-07 15:48:08", "updatedBy": "SYSTEM", "updatedDate": "2023-06-07 15:48:08", "idPlyHealthServices": "bed9124806d545f0b189b404fa53a52c", "idPlyRiskGroup": "d4681bd716f043ee9e1c2b0baa5a2705", "combinedCode": "S99999", "combinedName": "测试核销服务", "combinedDetailDesc": "在线购药（2次）", "applyNum": 1, "price": 2, "combinedType": "2"}, {"createdBy": "SYSTEM", "createdDate": "2023-06-07 15:48:08", "updatedBy": "SYSTEM", "updatedDate": "2023-06-07 15:48:08", "idPlyHealthServices": "e5f595a918c84ecebc458c4842cd8f45", "idPlyRiskGroup": "d4681bd716f043ee9e1c2b0baa5a2705", "combinedCode": "S00001", "combinedName": "无忧保百万医疗健康管理服务", "combinedDetailDesc": "电话咨询（1次）、图文咨询（1次）、视频咨询（1次）、健康档案建立（不限次）、报告解读（不限次）、挂号小秘书（不限次）、重疾门诊绿通（含陪诊）（1次）、重疾手术/住院加快（含陪诊）（1次）、名医二诊（升级版）（1次）、重疾国内住院垫付（1次）、重疾就医通勤协助（1次）", "applyNum": 1, "price": 0, "combinedType": "2"}], "termContentList": []}], "applicantInfoList": [{"id": "1a807a6ae960493fb8d2e7773a431ac3", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "endorseNo": "", "index": 0, "baseInfoId": "1666351272397889536", "name": "ZYQ测试47181", "birthday": "2000-02-03 00:00:00", "age": 23, "sexCode": "M", "nationality": "156", "certificateNo": "**********", "certificateType": "02", "certificateIssueDate": "2023-04-11 00:00:00", "certificateValidDate": "2022-09-20 00:00:00", "professionCode": "A0102001", "address": "北京市东城区334543", "postcode": "353567", "email": "<EMAIL>", "homeTelephone": "", "mobileTelephone": "***********", "officeTelephone": "", "linkManName": "nffsd", "personnelType": "1", "clientNo": "********************", "isConfirmName": "0", "professionName": "A0102001", "linkModeType": "03", "billingDepositBank": "7006", "billingDepositBankAccount": "********", "province": "140000", "city": "140300", "county": "140311", "superviseInfoList": [{"createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "personId": "1a807a6ae960493fb8d2e7773a431ac3", "personType": "1", "businessScope": "经营范围", "otherCertificateNo": "4364535", "superviseExtendList": [{"createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "superviseInfoId": "6c53387fde3545339a6e764c566978d0", "companyRelationType": "2", "name": "法定代表人", "certificateNo": "*************", "certificateType": "01", "certificateIssueDate": "2022-09-20 00:00:00", "certificateValidDate": "2022-09-20 00:00:00", "address": "法定代表人地址详细信息", "benefitMode": "", "personnelType": "1", "superviseExtendId": "23e554bea72a45e09d07d2df82ad851b", "province": "140000", "city": "140300", "county": "140311"}, {"createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "superviseInfoId": "6c53387fde3545339a6e764c566978d0", "companyRelationType": "1", "name": "控股股东", "certificateNo": "1223232323111", "certificateType": "01", "certificateIssueDate": "2022-09-20 00:00:00", "certificateValidDate": "2022-09-20 00:00:00", "address": "豆腐干大概", "benefitMode": "", "personnelType": "1", "superviseExtendId": "5c15824a91a0499c804f673d1156478a", "province": "140000", "city": "140300", "county": "140311"}, {"createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "superviseInfoId": "6c53387fde3545339a6e764c566978d0", "companyRelationType": "4", "name": "受益人", "certificateNo": "1223232323222", "certificateType": "01", "certificateIssueDate": "2022-09-20 00:00:00", "certificateValidDate": "2022-09-20 00:00:00", "address": "豆腐干大概", "benefitMode": "01", "equityRatio": 20, "personnelType": "1", "superviseExtendId": "98b774a6463e48f182e69f6c346a6270", "province": "140000", "city": "140300", "county": "140311"}, {"createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "superviseInfoId": "6c53387fde3545339a6e764c566978d0", "companyRelationType": "3", "name": "授权办理业务人", "certificateNo": "**************", "certificateType": "01", "certificateIssueDate": "2022-09-20 00:00:00", "certificateValidDate": "2022-09-20 00:00:00", "address": "授权办理业务地址详细信息", "benefitMode": "", "personnelType": "1", "superviseExtendId": "b4ce1165a2754047b392674bc54cb2de", "province": "140000", "city": "140300", "county": "140311"}], "enterpriseType": "01", "registeredFund": 10000, "regfundCurrencyCode": "CNY", "billingDepositBank": "308", "billingDepositBankAccount": "**********", "unitPersonNum": "12", "industryType": "A0142", "organizationType": "270"}], "applicantType": "1", "isConfirmNotification": "0", "workUnit": "fdgfdfg", "yearlySalaries": 30, "ageUnit": 0}], "specialPromiseList": [{"id": "1666351316987535360", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "promiseCode": "-", "promiseDesc": "颠三倒四", "promiseType": "N", "sortIndex": 1, "riskGroupName": "方案一", "promisShortName": "测试-李占川052903"}], "attachmentGroupList": [{"id": "e241403063d1491ab8256dec0353dbf5", "createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SYSTEM", "updatedBy": "SYSTEM", "archiveDate": "2023-06-07 15:48:08", "policyNo": "Z7899522048264177147", "index": 0, "baseInfoId": "1666351272397889536", "documentGroupId": "**********", "documentGroupName": "icore_pnbs", "documentGroupType": "02", "documentGroupDesc": "icore_pnbs", "businessCode": "icore_pnbs", "dataSource": "3", "attachmentList": [{"createdDate": "2023-06-07", "updatedDate": "2023-06-07", "createdBy": "SX1", "updatedBy": "SX1ER", "index": 0, "documentId": "2000003316", "documentType": "T01", "documentName": "大饭店", "documentFormat": "35", "documentDesc": "dfgf", "uploadPersonnel": "renren", "uploadDate": "Fri Apr 14 00:00:00 CST 2023", "uploadPath": "sdff", "documentClass": "T01", "documentSize": "1.00", "uploadIsDate": "Wed Jun 07 15:47:57 CST 2023", "documentStatus": "Y", "documentOrder": "1", "documentGroupId": "**********", "generatedDate": "Wed Jun 07 15:47:57 CST 2023", "certificateBeginDate": "Wed Jun 07 15:47:57 CST 2023", "certificateEndDate": "Wed Jun 07 15:47:57 CST 2023"}], "taskType": "3", "fileType": "T01", "operateName": "renren", "operateDate": "2023-04-14 00:00:00"}], "accountInfoList": [], "plyApplyFreezes": []}, "saleDepartmentName": "内部账户组", "saleDepartmentCode": "651", "technicProductCode": "TP9999999", "isGracePeriod": "N", "isProcess": "N"}}