<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsElecReportInfoMapper">

    <insert id="insertForReportTypeOne"
            parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity">
        begin
        insert into CLMS_ELEC_REPORT_INFO (
        CREATED_BY,CREATED_DATE,UPDATED_BY,UPDATED_DATE,CARD_NO,
        REPORT_NO,clms_POLICY_EXC_ID,REPORT_TYPE
        )
        values (
        #{createdBy,jdbcType=VARCHAR},now(),
        #{updatedBy,jdbcType=VARCHAR},now(),
        #{policyCerNo,jdbcType=VARCHAR},#{reportNo,jdbcType=VARCHAR},
        #{idAhcsPolicyInfo,jdbcType=VARCHAR},'1'
        );
        <if test="selfCardNo != null">
            insert into CLMS_ELEC_REPORT_INFO (
            CREATED_BY,CREATED_DATE,UPDATED_BY,UPDATED_DATE,CARD_NO,
            REPORT_NO,clms_POLICY_EXC_ID,REPORT_TYPE
            )
            values (
            #{createdBy,jdbcType=VARCHAR},now(),
            #{updatedBy,jdbcType=VARCHAR},now(),
            #{selfCardNo,jdbcType=VARCHAR},#{reportNo,jdbcType=VARCHAR},
            #{idAhcsPolicyInfo,jdbcType=VARCHAR},'1'
            );
        </if>
        end;
    </insert>

    <insert id="insertForReportTypeTwo"
            parameterType="com.paic.ncbs.claim.dao.entity.report.ElecReportExcEntity">
        insert into CLMS_ELEC_REPORT_INFO (
        CREATED_BY,CREATED_DATE,UPDATED_BY,UPDATED_DATE,CARD_NO,
        REPORT_NO,clms_POLICY_EXC_ID,REPORT_TYPE
        )
        values (
        #{createdBy,jdbcType=VARCHAR},now(),
        #{updatedBy,jdbcType=VARCHAR},now(),
        #{elecSubPolicyNo,jdbcType=VARCHAR},#{reportNo,jdbcType=VARCHAR},
        #{idReportExc,jdbcType=VARCHAR},'2'
        )
    </insert>
</mapper>