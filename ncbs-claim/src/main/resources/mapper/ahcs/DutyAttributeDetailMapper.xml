<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeDetailMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeDetailEntity">
        <id column="ID_AHCS_DUTY_ATTRIBUTE_DETAIL" property="idAhcsDutyAttributeDetail" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_AHCS_DUTY_ATTRIBUTE" property="idAhcsDutyAttribute" jdbcType="VARCHAR"/>
        <result column="ATTRIBUTE_DETAIL_CODE" property="attributeDetailCode" jdbcType="VARCHAR"/>
        <result column="ATTRIBUTE_DETAIL_VALUE" property="attributeDetailValue" jdbcType="VARCHAR"/>
        <result column="ATTRIBUTE_ROW_NO" property="attributeRowNo" jdbcType="VARCHAR"/>
        <result column="ATTRIBUTE_COLUMN_NO" property="attributeColumnNo" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO" id="result">
        <result property="attrName" column="ATTRIBUTE_DETAIL_NAME" jdbcType="VARCHAR"/>
        <result property="attrCode" column="ATTRIBUTE_DETAIL_CODE" jdbcType="VARCHAR"/>
        <result property="attrValue" column="ATTRIBUTE_DETAIL_VALUE" jdbcType="VARCHAR"/>
        <result property="attrUnit" column="UNIT" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_DUTY_ATTRIBUTE_DETAIL, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_AHCS_DUTY_ATTRIBUTE, ATTRIBUTE_DETAIL_CODE, ATTRIBUTE_DETAIL_VALUE, ATTRIBUTE_ROW_NO,
        ATTRIBUTE_COLUMN_NO
    </sql>

    <select id="getAllAttributeDetails" resultMap="result" parameterType="java.util.Map">
        select b.ATTRIBUTE_DETAIL_NAME,
               a.ATTRIBUTE_DETAIL_CODE,
               a.ATTRIBUTE_DETAIL_VALUE,
               b.UNIT
        from clms_duty_attribute_detail a,duty_attr_def_detail b
        where a.ATTRIBUTE_DETAIL_CODE = b.ATTRIBUTE_DETAIL_CODE
        and ID_AHCS_DUTY_ATTRIBUTE= #{idAhcsDutyAttribute,jdbcType=VARCHAR}
    </select>

    <select id="getAttributeByDutyId" resultType="java.util.HashMap" parameterType="java.lang.String">
        select
            ATTRIBUTE_DETAIL_CODE, ATTRIBUTE_DETAIL_VALUE
        from CLMS_DUTY_ATTRIBUTE_DETAIL
        where ID_AHCS_DUTY_ATTRIBUTE= #{idAhcsDutyAttribute,jdbcType=VARCHAR}
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_DUTY_ATTRIBUTE_DETAIL
        where ID_AHCS_DUTY_ATTRIBUTE_DETAIL = #{idAhcsDutyAttributeDetail,jdbcType=VARCHAR}
    </select>
    <select id="getInfoByDutyAttributeId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
            ID_AHCS_DUTY_ATTRIBUTE, ATTRIBUTE_DETAIL_CODE, ATTRIBUTE_DETAIL_VALUE, ATTRIBUTE_ROW_NO,
            ATTRIBUTE_COLUMN_NO
        from CLMS_DUTY_ATTRIBUTE_DETAIL
        where ID_AHCS_DUTY_ATTRIBUTE = #{idAhcsDutyAttribute,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_DUTY_ATTRIBUTE_DETAIL
        where ID_AHCS_DUTY_ATTRIBUTE_DETAIL = #{idAhcsDutyAttributeDetail,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeDetailEntity">
        insert into CLMS_DUTY_ATTRIBUTE_DETAIL (ID_AHCS_DUTY_ATTRIBUTE_DETAIL, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_AHCS_DUTY_ATTRIBUTE, ATTRIBUTE_DETAIL_CODE,
        ATTRIBUTE_DETAIL_VALUE, ATTRIBUTE_ROW_NO, ATTRIBUTE_COLUMN_NO
        )
        values (#{idAhcsDutyAttributeDetail,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{idAhcsDutyAttribute,jdbcType=VARCHAR}, #{attributeDetailCode,jdbcType=VARCHAR},
        #{attributeDetailValue,jdbcType=VARCHAR}, #{attributeRowNo,jdbcType=VARCHAR},
        #{attributeColumnNo,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertList" parameterType="java.util.List">
        insert into /*+append_values */ CLMS_DUTY_ATTRIBUTE_DETAIL (ID_AHCS_DUTY_ATTRIBUTE_DETAIL, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_AHCS_DUTY_ATTRIBUTE, ATTRIBUTE_DETAIL_CODE,
        ATTRIBUTE_DETAIL_VALUE, ATTRIBUTE_ROW_NO, ATTRIBUTE_COLUMN_NO
        )
        <foreach collection="dutyAttributeDetailEntities" item="dutyAttributeDetail" index="index"
                 separator="union all">
            select #{dutyAttributeDetail.idAhcsDutyAttributeDetail,jdbcType=VARCHAR},
            #{dutyAttributeDetail.createdBy,jdbcType=VARCHAR},
            #{dutyAttributeDetail.createdDate,jdbcType=TIMESTAMP}, #{dutyAttributeDetail.updatedBy,jdbcType=VARCHAR},
            #{dutyAttributeDetail.updatedDate,jdbcType=TIMESTAMP},
            #{dutyAttributeDetail.idAhcsDutyAttribute,jdbcType=VARCHAR},
            #{dutyAttributeDetail.attributeDetailCode,jdbcType=VARCHAR},
            #{dutyAttributeDetail.attributeDetailValue,jdbcType=VARCHAR},
            #{dutyAttributeDetail.attributeRowNo,jdbcType=VARCHAR},
            #{dutyAttributeDetail.attributeColumnNo,jdbcType=VARCHAR}
        </foreach>
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeDetailEntity">
        insert into CLMS_DUTY_ATTRIBUTE_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsDutyAttributeDetail != null">
                ID_AHCS_DUTY_ATTRIBUTE_DETAIL,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idAhcsDutyAttribute != null">
                ID_AHCS_DUTY_ATTRIBUTE,
            </if>
            <if test="attributeDetailCode != null">
                ATTRIBUTE_DETAIL_CODE,
            </if>
            <if test="attributeDetailValue != null">
                ATTRIBUTE_DETAIL_VALUE,
            </if>
            <if test="attributeRowNo != null">
                ATTRIBUTE_ROW_NO,
            </if>
            <if test="attributeColumnNo != null">
                ATTRIBUTE_COLUMN_NO,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsDutyAttributeDetail != null">
                #{idAhcsDutyAttributeDetail,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsDutyAttribute != null">
                #{idAhcsDutyAttribute,jdbcType=VARCHAR},
            </if>
            <if test="attributeDetailCode != null">
                #{attributeDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="attributeDetailValue != null">
                #{attributeDetailValue,jdbcType=VARCHAR},
            </if>
            <if test="attributeRowNo != null">
                #{attributeRowNo,jdbcType=VARCHAR},
            </if>
            <if test="attributeColumnNo != null">
                #{attributeColumnNo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeDetailEntity">
        update CLMS_DUTY_ATTRIBUTE_DETAIL
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsDutyAttribute != null">
                ID_AHCS_DUTY_ATTRIBUTE = #{idAhcsDutyAttribute,jdbcType=VARCHAR},
            </if>
            <if test="attributeDetailCode != null">
                ATTRIBUTE_DETAIL_CODE = #{attributeDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="attributeDetailValue != null">
                ATTRIBUTE_DETAIL_VALUE = #{attributeDetailValue,jdbcType=VARCHAR},
            </if>
            <if test="attributeRowNo != null">
                ATTRIBUTE_ROW_NO = #{attributeRowNo,jdbcType=VARCHAR},
            </if>
            <if test="attributeColumnNo != null">
                ATTRIBUTE_COLUMN_NO = #{attributeColumnNo,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_AHCS_DUTY_ATTRIBUTE_DETAIL = #{idAhcsDutyAttributeDetail,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeDetailEntity">
        update CLMS_DUTY_ATTRIBUTE_DETAIL
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        ID_AHCS_DUTY_ATTRIBUTE = #{idAhcsDutyAttribute,jdbcType=VARCHAR},
        ATTRIBUTE_DETAIL_CODE = #{attributeDetailCode,jdbcType=VARCHAR},
        ATTRIBUTE_DETAIL_VALUE = #{attributeDetailValue,jdbcType=VARCHAR},
        ATTRIBUTE_ROW_NO = #{attributeRowNo,jdbcType=VARCHAR},
        ATTRIBUTE_COLUMN_NO = #{attributeColumnNo,jdbcType=VARCHAR}
        where ID_AHCS_DUTY_ATTRIBUTE_DETAIL = #{idAhcsDutyAttributeDetail,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete from CLMS_DUTY_ATTRIBUTE_DETAIL e
        where e.id_ahcs_duty_attribute in
        (select d.id_ahcs_duty_attribute
        from CLMS_DUTY_ATTRIBUTE d
        where d.id_ahcs_policy_duty in
        (select c.id_ahcs_policy_duty
        from CLMS_POLICY_DUTY c
        where c.id_ahcs_policy_plan in
        (select a.id_ahcs_policy_plan
        from CLMS_POLICY_PLAN a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR}))))
    </delete>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeDetailEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_DUTY_ATTRIBUTE_DETAIL
        where ID_AHCS_DUTY_ATTRIBUTE = #{idAhcsDutyAttribute,jdbcType=VARCHAR}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into CLMS_DUTY_ATTRIBUTE_DETAIL (ID_AHCS_DUTY_ATTRIBUTE_DETAIL, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_AHCS_DUTY_ATTRIBUTE, ATTRIBUTE_DETAIL_CODE,
        ATTRIBUTE_DETAIL_VALUE, ATTRIBUTE_ROW_NO, ATTRIBUTE_COLUMN_NO
        )
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsDutyAttributeDetail,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.idAhcsDutyAttribute,jdbcType=VARCHAR},
            #{item.attributeDetailCode,jdbcType=VARCHAR},
            #{item.attributeDetailValue,jdbcType=VARCHAR},
            #{item.attributeRowNo,jdbcType=VARCHAR},
            #{item.attributeColumnNo,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <select id="getAttributeByRepotNo" resultType="com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO">
        select d.attribute_code attrCode,d.ATTRIBUTE_VALUE attrValue,b.plan_code planCode ,c.duty_code dutyCode
        from clms_policy_info a ,clms_policy_plan b,clms_policy_duty c ,clms_duty_attribute d
        where a.ID_AHCS_POLICY_INFO=b.ID_AHCS_POLICY_INFO
        and b.ID_AHCS_POLICY_PLAN=c.ID_AHCS_POLICY_PLAN
        and d.ID_AHCS_POLICY_DUTY=c.ID_AHCS_POLICY_DUTY
        and  a.report_no=#{reportNo}
        and a.policy_no=#{policyNo}
    </select>
    <select id="getInfoByDutyAttributeDutyId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select b.ID_AHCS_DUTY_ATTRIBUTE, b.ATTRIBUTE_DETAIL_CODE, b.ATTRIBUTE_DETAIL_VALUE, b.ATTRIBUTE_ROW_NO,
        b.ATTRIBUTE_COLUMN_NO from clms_duty_attribute a,clms_duty_attribute_detail b
        where a.ID_AHCS_DUTY_ATTRIBUTE=b.ID_AHCS_DUTY_ATTRIBUTE
        and a.ATTRIBUTE_CODE='366'
        and a.ID_AHCS_POLICY_DUTY=#{idPolicyDuty}
    </select>
    
</mapper>