<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyDutyDataMapper" >
  <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDataEntity" >
    <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
    <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR" />
    <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP" />
    <result column="ID_AHCS_POLICY_DUTY_DATA" property="idAhcsPolicyDutyData" jdbcType="VARCHAR" />
    <result column="ID_AHCS_POLICY_DUTY" property="idAhcsPolicyDuty" jdbcType="VARCHAR" />
    <result column="ID_AHCS_POLICY_PLAN" property="idAhcsPolicyPlan" jdbcType="VARCHAR" />
    <result column="DUTY_NAME" property="dutyName" jdbcType="VARCHAR" />
    <result column="DUTY_CODE" property="dutyCode" jdbcType="VARCHAR" />
    <result column="DUTY_AMOUNT" property="dutyAmount" jdbcType="DECIMAL" />
    <result column="DUTY_DESC" property="dutyDesc" jdbcType="VARCHAR" />
    <result column="ORG_DUTY_CODE" property="orgDutyCode" jdbcType="VARCHAR" />
    <result column="ORG_DUTY_NAME" property="orgDutyName" jdbcType="VARCHAR" />
  </resultMap>
    <sql id="Base_Column_List" >
    ID_AHCS_POLICY_DUTY_DATA,ID_AHCS_POLICY_DUTY, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_PLAN, 
    DUTY_NAME, DUTY_CODE, DUTY_AMOUNT, DUTY_DESC, ORG_DUTY_CODE, ORG_DUTY_NAME
  </sql>
  <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDataEntity" >
    insert into CLMS_POLICY_DUTY_DATA (CREATED_BY, CREATED_DATE, UPDATED_BY,
      UPDATED_DATE, ID_AHCS_POLICY_DUTY_DATA, ID_AHCS_POLICY_DUTY, ID_AHCS_POLICY_PLAN, 
      DUTY_NAME, DUTY_CODE, DUTY_AMOUNT, 
      DUTY_DESC, ORG_DUTY_CODE, ORG_DUTY_NAME
      )
    values (#{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedDate,jdbcType=TIMESTAMP}, #{idAhcsPolicyDutyData,jdbcType=VARCHAR}, #{idAhcsPolicyDuty,jdbcType=VARCHAR}, #{idAhcsPolicyPlan,jdbcType=VARCHAR}, 
      #{dutyName,jdbcType=VARCHAR}, #{dutyCode,jdbcType=VARCHAR}, #{dutyAmount,jdbcType=DECIMAL}, 
      #{dutyDesc,jdbcType=VARCHAR}, #{orgDutyCode,jdbcType=VARCHAR}, #{orgDutyName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDataEntity" >
    insert into CLMS_POLICY_DUTY_DATA
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="createdDate != null" >
        CREATED_DATE,
      </if>
      <if test="updatedBy != null" >
        UPDATED_BY,
      </if>
      <if test="updatedDate != null" >
        UPDATED_DATE,
      </if>
      <if test="idAhcsPolicyDutyData != null" >
        ID_AHCS_POLICY_DUTY_DATA,
      </if>
      <if test="idAhcsPolicyDuty != null" >
        ID_AHCS_POLICY_DUTY,
      </if>
      <if test="idAhcsPolicyPlan != null" >
        ID_AHCS_POLICY_PLAN,
      </if>
      <if test="dutyName != null" >
        DUTY_NAME,
      </if>
      <if test="dutyCode != null" >
        DUTY_CODE,
      </if>
      <if test="dutyAmount != null" >
        DUTY_AMOUNT,
      </if>
      <if test="dutyDesc != null" >
        DUTY_DESC,
      </if>
      <if test="orgDutyCode != null" >
        ORG_DUTY_CODE,
      </if>
      <if test="orgDutyName != null" >
        ORG_DUTY_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null" >
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedDate != null" >
        #{updatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="idAhcsPolicyDutyData != null" >
        #{idAhcsPolicyDutyData,jdbcType=VARCHAR},
      </if>
      <if test="idAhcsPolicyDuty != null" >
        #{idAhcsPolicyDuty,jdbcType=VARCHAR},
      </if>
      <if test="idAhcsPolicyPlan != null" >
        #{idAhcsPolicyPlan,jdbcType=VARCHAR},
      </if>
      <if test="dutyName != null" >
        #{dutyName,jdbcType=VARCHAR},
      </if>
      <if test="dutyCode != null" >
        #{dutyCode,jdbcType=VARCHAR},
      </if>
      <if test="dutyAmount != null" >
        #{dutyAmount,jdbcType=DECIMAL},
      </if>
      <if test="dutyDesc != null" >
        #{dutyDesc,jdbcType=VARCHAR},
      </if>
      <if test="orgDutyCode != null" >
        #{orgDutyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgDutyName != null" >
        #{orgDutyName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
   <insert id="insertList" parameterType="java.util.List" >
    insert into /*+append_values */ CLMS_POLICY_DUTY_DATA (ID_AHCS_POLICY_DUTY_DATA,ID_AHCS_POLICY_DUTY, CREATED_BY, CREATED_DATE,
      UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_PLAN, 
      DUTY_NAME, DUTY_CODE, DUTY_AMOUNT, DUTY_DESC, ORG_DUTY_CODE, ORG_DUTY_NAME)
    <foreach collection="policyDutyDataEntities" item="policyDuty" index="index" separator="union all">     
     select #{policyDuty.idAhcsPolicyDutyData,jdbcType=VARCHAR},#{policyDuty.idAhcsPolicyDuty,jdbcType=VARCHAR}, #{policyDuty.createdBy,jdbcType=VARCHAR}, #{policyDuty.createdDate,jdbcType=TIMESTAMP}, 
      #{policyDuty.updatedBy,jdbcType=VARCHAR}, #{policyDuty.updatedDate,jdbcType=TIMESTAMP}, #{policyDuty.idAhcsPolicyPlan,jdbcType=VARCHAR}, 
      #{policyDuty.dutyName,jdbcType=VARCHAR}, #{policyDuty.dutyCode,jdbcType=VARCHAR}, #{policyDuty.dutyAmount,jdbcType=DECIMAL}, 
      #{policyDuty.dutyDesc,jdbcType=VARCHAR}, #{policyDuty.orgDutyCode,jdbcType=VARCHAR}, #{policyDuty.orgDutyName,jdbcType=VARCHAR}
  </foreach>
  </insert>
  <select id="getPolicyDutyInfo" resultMap="BaseResultMap" parameterType="java.lang.String" >
 	 select 
  		<include refid="Base_Column_List" />
     from 
     CLMS_POLICY_DUTY_DATA c
  	 where 
  	 c.duty_code = #{dutyCode,jdbcType=VARCHAR}
  	 and
  	 c.id_ahcs_policy_plan in
     (select a.id_ahcs_policy_plan
     from CLMS_POLICY_PLAN_DATA a
     where 
     a.plan_code = #{planCode,jdbcType=VARCHAR}
     and a.id_ahcs_policy_info in
     (select b.id_ahcs_policy_info
     from CLMS_Policy_Info b
     where  b.report_no = #{reportNo,jdbcType=VARCHAR}
     	and b.policy_no = #{policyNo,jdbcType=VARCHAR}
     <if test="selfCardNo !=null and selfCardNo != ''">
      	and b.self_card_no = #{selfCardNo,jdbcType=VARCHAR}
      </if>
     ))
  </select>
	<delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
		delete
		from CLMS_POLICY_DUTY_DATA c
		where c.id_ahcs_policy_plan in
		(select a.id_ahcs_policy_plan
		from CLMS_POLICY_PLAN a
		where a.id_ahcs_policy_info in
		(select b.id_ahcs_policy_info
		from CLMS_Policy_Info b
		where b.report_no = #{reportNo,jdbcType=VARCHAR}
		and b.policy_no =#{policyNo,jdbcType=VARCHAR}))
	</delete>
</mapper>