<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.BatchAutoCloseMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.ahcs.BatchAutoCloseDTO">
        <id column="ID_CLMS_BATCH_AUTO_CLOSE" property="idClmsBatchAutoClose" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="SERIAL_NO" property="serialNo" jdbcType="VARCHAR"/>
        <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"/>
        <result column="INSURED_NAME" property="insuredName" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_TYPE" property="certificateType" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_NO" property="certificateNo" jdbcType="VARCHAR"/>
        <result column="LINK_NAME" property="linkName" jdbcType="VARCHAR"/>
        <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR"/>
        <result column="CASE_CLASS" property="caseClass" jdbcType="VARCHAR"/>
        <result column="POLICY_NO" property="policyNo" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_TYPE" property="accidentType" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_DATE" property="accidentDate" jdbcType="TIMESTAMP"/>
        <result column="ACCIDENT_PROVINCE" property="accidentProvince" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_CITY" property="accidentCity" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_COUNTY" property="accidentCounty" jdbcType="VARCHAR"/>
        <result column="PLAN_CODE" property="planCode" jdbcType="VARCHAR"/>
        <result column="DUTY_CODE" property="dutyCode" jdbcType="VARCHAR"/>
        <result column="DUTY_DETAIL_CODE" property="dutyDetailCode" jdbcType="VARCHAR"/>
        <result column="DIAGNOSE_CODE" property="diagnoseCode" jdbcType="VARCHAR"/>
        <result column="DIAGNOSE_NAME" property="diagnoseName" jdbcType="VARCHAR"/>
        <result column="HOSPITAL" property="hospital" jdbcType="VARCHAR"/>
        <result column="BILL_AMOUNT" property="billAmount" jdbcType="DECIMAL"/>
        <result column="BILL_NO" property="billNo" jdbcType="VARCHAR"/>
        <result column="BILL_TYPE" property="billType" jdbcType="VARCHAR"/>
        <result column="APPLY_AMOUNT" property="applyAmount" jdbcType="DECIMAL"/>
        <result column="NON_MEDICAL_FEE" property="nonMedicalFee" jdbcType="DECIMAL"/>
        <result column="THREE_PAY_AMOUNT" property="threePayAmount" jdbcType="DECIMAL"/>
        <result column="REMIT_AMOUNT" property="remitAmount" jdbcType="DECIMAL"/>
        <result column="PAY_PROPORTION" property="payProportion" jdbcType="DECIMAL"/>
        <result column="PAY_AMOUNT" property="payAmount" jdbcType="DECIMAL"/>
        <result column="CLIENT_NAME" property="clientName" jdbcType="VARCHAR"/>
        <result column="CLIENT_CERTIFICATE_NO" property="clientCertificateNo" jdbcType="VARCHAR"/>
        <result column="CLIENT_BANK_ACCOUNT" property="clientBankAccount" jdbcType="VARCHAR"/>
        <result column="CLIENT_BANK_CODE" property="clientBankCode" jdbcType="VARCHAR"/>
        <result column="CLIENT_BANK_NAME" property="clientBankName" jdbcType="VARCHAR"/>
        <result column="BANK_DETAIL" property="bankDetail" jdbcType="VARCHAR"/>
        <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"/>
        <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"/>
        <result column="REGION_CODE" property="regionCode" jdbcType="VARCHAR"/>
        <result column="BANK_ACCOUNT_ATTRIBUTE" property="bankAccountAttribute" jdbcType="VARCHAR"/>
        <result column="THREE_SOURCE" property="threeSource" jdbcType="VARCHAR"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="THIRD_BATCH_NO" property="thirdBatchNo" jdbcType="VARCHAR"/>
        <result column="EXPRESS_COMPANY_CODE" property="expressCompanyCode" jdbcType="VARCHAR"/>
        <result column="REOPEN_NUM" property="reopenNum" jdbcType="DECIMAL"/>

    </resultMap>
    <sql id="Base_Column_List">
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_CLMS_BATCH_AUTO_CLOSE,
        SERIAL_NO,
        BATCH_NO,
        INSURED_NAME,
        CERTIFICATE_TYPE,
        CERTIFICATE_NO,
        REPORTER_NAME,
        LINK_NAME,
        LINK_PHONE,
        CASE_CLASS,
        POLICY_NO,
        ACCIDENT_TYPE,
        ACCIDENT_DATE,
        ACCIDENT_PROVINCE,
        ACCIDENT_CITY,
        ACCIDENT_COUNTY,
        PLAN_CODE,
        DUTY_CODE,
        DUTY_DETAIL_CODE,
        DIAGNOSE_CODE,
        DIAGNOSE_NAME,
        HOSPITAL,
        BILL_AMOUNT,
        BILL_NO,
        BILL_TYPE,
        APPLY_AMOUNT,
        NON_MEDICAL_FEE,
        THREE_PAY_AMOUNT,
        REMIT_AMOUNT,
        PAY_PROPORTION,
        PAY_AMOUNT,
        CLIENT_NAME,
        CLIENT_CERTIFICATE_NO,
        CLIENT_BANK_ACCOUNT,
        CLIENT_BANK_CODE,
        CLIENT_BANK_NAME,
        BANK_DETAIL,
        PROVINCE_CODE,
        CITY_CODE,
        REGION_CODE,
        BANK_ACCOUNT_ATTRIBUTE,
        THREE_SOURCE,
        REPORT_NO,
        SDB_MARK,
        THIRD_BATCH_NO,
        EXPRESS_COMPANY_CODE,
        REOPEN_NUM
    </sql>

    <insert id="addBatchCloseList" parameterType="java.util.List">
        INSERT INTO CLMS_BATCH_AUTO_CLOSE (
            CREATED_BY ,
            CREATED_DATE ,
            UPDATED_BY ,
            UPDATED_DATE ,
            ID_CLMS_BATCH_AUTO_CLOSE ,
            BATCH_NO ,
            SERIAL_NO,
            INSURED_NAME ,
            CERTIFICATE_TYPE ,
            CERTIFICATE_NO ,
            REPORTER_NAME ,
            LINK_NAME ,
            LINK_PHONE ,
            CASE_CLASS ,
            POLICY_NO ,
            ACCIDENT_TYPE ,
            ACCIDENT_DATE ,
            ACCIDENT_PROVINCE ,
            ACCIDENT_CITY ,
            ACCIDENT_COUNTY ,
            PLAN_CODE ,
            DUTY_CODE ,
            DUTY_DETAIL_CODE ,
            DIAGNOSE_CODE ,
            DIAGNOSE_NAME ,
            HOSPITAL ,
            BILL_AMOUNT ,
            BILL_NO ,
            BILL_TYPE ,
            APPLY_AMOUNT ,
            NON_MEDICAL_FEE ,
            THREE_PAY_AMOUNT ,
            REMIT_AMOUNT ,
            PAY_PROPORTION ,
            PAY_AMOUNT ,
            CLIENT_NAME ,
            CLIENT_CERTIFICATE_NO ,
            CLIENT_BANK_ACCOUNT ,
            CLIENT_BANK_CODE ,
            CLIENT_BANK_NAME ,
            BANK_DETAIL ,
            PROVINCE_CODE ,
            CITY_CODE ,
            REGION_CODE ,
            BANK_ACCOUNT_ATTRIBUTE ,
            THREE_SOURCE ,
            REPORT_NO,
            THIRD_BATCH_NO,
            EXPRESS_COMPANY_CODE,
            REOPEN_NUM)
        VALUES
        <foreach collection="batchCloseList" separator="," index="index" item="item">
            (#{item.createdBy,jdbcType=VARCHAR},
            now(),
            #{item.updatedBy,jdbcType=VARCHAR},
            now(),
            #{item.idClmsBatchAutoClose, jdbcType=VARCHAR},
            #{item.batchNo,jdbcType=VARCHAR},
            #{item.serialNo,jdbcType=VARCHAR},
            #{item.insuredName,jdbcType=VARCHAR},
            #{item.certificateType,jdbcType=VARCHAR},
            #{item.certificateNo,jdbcType=VARCHAR},
            #{item.reporterName,jdbcType=VARCHAR},
            #{item.linkName,jdbcType=VARCHAR},
            #{item.linkPhone,jdbcType=VARCHAR},
            #{item.caseClass,jdbcType=VARCHAR},
            #{item.policyNo,jdbcType=VARCHAR},
            #{item.accidentType,jdbcType=VARCHAR},
            #{item.accidentDate,jdbcType=TIMESTAMP},
            #{item.accidentProvince,jdbcType=VARCHAR},
            #{item.accidentCity,jdbcType=VARCHAR},
            #{item.accidentCounty,jdbcType=VARCHAR},
            #{item.planCode,jdbcType=VARCHAR},
            #{item.dutyCode,jdbcType=VARCHAR},
            #{item.dutyDetailCode,jdbcType=VARCHAR},
            #{item.diagnoseCode,jdbcType=VARCHAR},
            #{item.diagnoseName,jdbcType=VARCHAR},
            #{item.hospital,jdbcType=VARCHAR},
            #{item.billAmount,jdbcType=DECIMAL},
            #{item.billNo,jdbcType=VARCHAR},
            #{item.billType,jdbcType=VARCHAR},
            #{item.applyAmount,jdbcType=DECIMAL},
            #{item.nonMedicalFee,jdbcType=DECIMAL},
            #{item.threePayAmount,jdbcType=DECIMAL},
            #{item.remitAmount,jdbcType=DECIMAL},
            #{item.payProportion,jdbcType=DECIMAL},
            #{item.payAmount,jdbcType=DECIMAL},
            #{item.clientName,jdbcType=VARCHAR},
            #{item.clientCertificateNo,jdbcType=VARCHAR},
            #{item.clientBankAccount,jdbcType=VARCHAR},
            #{item.clientBankCode,jdbcType=VARCHAR},
            #{item.clientBankName,jdbcType=VARCHAR},
            #{item.bankDetail,jdbcType=VARCHAR},
            #{item.provinceCode,jdbcType=VARCHAR},
            #{item.cityCode,jdbcType=VARCHAR},
            #{item.regionCode,jdbcType=VARCHAR},
            #{item.bankAccountAttribute,jdbcType=VARCHAR},
            #{item.threeSource,jdbcType=VARCHAR},
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.thirdBatchNo},
            #{item.expressCompanyCode,jdbcType=VARCHAR},
            #{item.reopenNum,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="getBatchCloseList" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.dao.entity.report.BatchReportTempEntity">

        select SERIAL_NO orderNo,
            REPORT_NO reportNo,
            INSURED_NAME clientName,
            CERTIFICATE_TYPE certificateType,
            CERTIFICATE_NO certificateNo,
            ACCIDENT_DATE accidentDate
        from clms_batch_auto_close
        where BATCH_NO = #{batchNo,jdbcType=VARCHAR}
        order by (SERIAL_NO + 0)

    </select>

    <select id="getByThirdBatchNo" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.ahcs.OnlineBatchDTO">
        select  b.batch_no batchNo,
                b.report_no reportNo,
                b.policy_no policyNo,
                p.id_clm_payment_item paySerialNo,
                (CASE p.collect_pay_approach WHEN '02' THEN 'COMPANY_PAY' WHEN '03' THEN 'OFFLINE_PAY' WHEN '215' THEN 'COMPANY_PAY' END) paymentMode,
                b.express_company_code expressCompanyCode,
                b.reopen_num reopenNum
        FROM clms_batch_auto_close b
        LEFT JOIN clm_payment_item p ON b.report_no = p.report_no
        WHERE b.third_batch_no = #{thirdBatchNo}
        <if test="reopenNum != null">
            and b.reopen_num = #{reopenNum}
        </if>
    </select>

    <select id="getByReportNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM clms_batch_auto_close
        WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <select id="getByReportNos" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM clms_batch_auto_close
        WHERE REPORT_NO IN
        <foreach collection="reportNos" item="reportNo" separator="," open="(" close=")">
            #{reportNo}
        </foreach>
    </select>

    <select id="getProblemBatchCloseList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM clms_batch_auto_close
        WHERE created_date >= DATE '2024-07-18'
          AND PAY_AMOUNT > 0
          AND three_source = '线上退运险'
          AND EXISTS (SELECT 1
                      FROM clm_policy_pay p
                      WHERE p.REPORT_NO = clms_batch_auto_close.REPORT_NO
                        AND (p.POLICY_PAY IS NULL OR p.POLICY_PAY = 0))
        ORDER BY created_date LIMIT 1000
    </select>

    <select id="compensationByThirdBatchNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        b.CREATED_BY,
        b.CREATED_DATE,
        b.UPDATED_BY,
        b.UPDATED_DATE,
        b.ID_CLMS_BATCH_AUTO_CLOSE,
        b.SERIAL_NO,
        b.BATCH_NO,
        b.INSURED_NAME,
        b.CERTIFICATE_TYPE,
        b.CERTIFICATE_NO,
        b.REPORTER_NAME,
        b.LINK_NAME,
        b.LINK_PHONE,
        b.CASE_CLASS,
        b.POLICY_NO,
        b.ACCIDENT_TYPE,
        b.ACCIDENT_DATE,
        b.ACCIDENT_PROVINCE,
        b.ACCIDENT_CITY,
        b.ACCIDENT_COUNTY,
        b.PLAN_CODE,
        b.DUTY_CODE,
        b.DUTY_DETAIL_CODE,
        b.DIAGNOSE_CODE,
        b.DIAGNOSE_NAME,
        b.HOSPITAL,
        b.BILL_AMOUNT,
        b.BILL_NO,
        b.BILL_TYPE,
        b.APPLY_AMOUNT,
        b.NON_MEDICAL_FEE,
        b.THREE_PAY_AMOUNT,
        b.REMIT_AMOUNT,
        b.PAY_PROPORTION,
        b.PAY_AMOUNT,
        b.CLIENT_NAME,
        b.CLIENT_CERTIFICATE_NO,
        b.CLIENT_BANK_ACCOUNT,
        b.CLIENT_BANK_CODE,
        b.CLIENT_BANK_NAME,
        b.BANK_DETAIL,
        b.PROVINCE_CODE,
        b.CITY_CODE,
        b.REGION_CODE,
        b.BANK_ACCOUNT_ATTRIBUTE,
        b.THREE_SOURCE,
        b.REPORT_NO,
        b.SDB_MARK,
        b.THIRD_BATCH_NO,
        b.EXPRESS_COMPANY_CODE,
        b.REOPEN_NUM
        FROM clm_whole_case_base a
        join clms_batch_auto_close b on a.report_no = b.report_no
        where a.case_times = 1 and a.IS_REGISTER is null and a.WHOLE_CASE_STATUS != '0'
        and b.third_batch_no = #{thirdBatchNo, jdbcType=VARCHAR};
    </select>

    <select id="compensationByReportNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        b.CREATED_BY,
        b.CREATED_DATE,
        b.UPDATED_BY,
        b.UPDATED_DATE,
        b.ID_CLMS_BATCH_AUTO_CLOSE,
        b.SERIAL_NO,
        b.BATCH_NO,
        b.INSURED_NAME,
        b.CERTIFICATE_TYPE,
        b.CERTIFICATE_NO,
        b.REPORTER_NAME,
        b.LINK_NAME,
        b.LINK_PHONE,
        b.CASE_CLASS,
        b.POLICY_NO,
        b.ACCIDENT_TYPE,
        b.ACCIDENT_DATE,
        b.ACCIDENT_PROVINCE,
        b.ACCIDENT_CITY,
        b.ACCIDENT_COUNTY,
        b.PLAN_CODE,
        b.DUTY_CODE,
        b.DUTY_DETAIL_CODE,
        b.DIAGNOSE_CODE,
        b.DIAGNOSE_NAME,
        b.HOSPITAL,
        b.BILL_AMOUNT,
        b.BILL_NO,
        b.BILL_TYPE,
        b.APPLY_AMOUNT,
        b.NON_MEDICAL_FEE,
        b.THREE_PAY_AMOUNT,
        b.REMIT_AMOUNT,
        b.PAY_PROPORTION,
        b.PAY_AMOUNT,
        b.CLIENT_NAME,
        b.CLIENT_CERTIFICATE_NO,
        b.CLIENT_BANK_ACCOUNT,
        b.CLIENT_BANK_CODE,
        b.CLIENT_BANK_NAME,
        b.BANK_DETAIL,
        b.PROVINCE_CODE,
        b.CITY_CODE,
        b.REGION_CODE,
        b.BANK_ACCOUNT_ATTRIBUTE,
        b.THREE_SOURCE,
        b.REPORT_NO,
        b.SDB_MARK,
        b.THIRD_BATCH_NO,
        b.EXPRESS_COMPANY_CODE,
        b.REOPEN_NUM
        FROM clm_whole_case_base a
        join clms_batch_auto_close b on a.report_no = b.report_no
        where a.case_times = 1 and a.IS_REGISTER is null and a.WHOLE_CASE_STATUS != '0'
        and b.report_no = #{reportNo, jdbcType=VARCHAR};
    </select>
</mapper>