<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyDutyDetailMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity">
        <id column="ID_AHCS_POLICY_DUTY_DETAIL" property="idAhcsPolicyDutyDetail" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_AHCS_POLICY_DUTY" property="idAhcsPolicyDuty" jdbcType="VARCHAR"/>
        <result column="DUTY_DETAIL_NAME" property="dutyDetailName" jdbcType="VARCHAR"/>
        <result column="DUTY_DETAIL_CODE" property="dutyDetailCode" jdbcType="VARCHAR"/>
        <result column="DUTY_DETAIL_TYPE" property="dutyDetailtype" jdbcType="VARCHAR"/>
        <result column="DUTY_AMOUNT" property="dutyAmount" jdbcType="DECIMAL"/>
        <result column="NOCLAIM_PROPERTY" property="noclaimProperty" jdbcType="VARCHAR"/>
        <result column="NOCLAIM_AMOUNT" property="noclaimAmount" jdbcType="DECIMAL"/>
        <result column="NOCLAIM_DAYS" property="noclaimDays" jdbcType="DECIMAL"/>
        <result column="NOCLAIM_TIMES" property="noclaimTimes" jdbcType="DECIMAL"/>
        <result column="AMOUNT_TYPE" property="amountType" jdbcType="VARCHAR"/>
        <result column="OBSERVED_DAYS" property="observedDays" jdbcType="DECIMAL"/>
        <result column="CLAIM_PROPORTION" property="claimProportion" jdbcType="DECIMAL"/>
        <result column="ALLOWANCE_EVERYDAY" property="allowanceEveryday" jdbcType="DECIMAL"/>
        <result column="ALLOWANCE_DAYS_LIMIT" property="allowanceDaysLimit" jdbcType="DECIMAL"/>
        <result column="SECOND_TYPE" property="secondType" jdbcType="VARCHAR"/>
        <result column="ORG_DUTY_DETAIL_CODE" property="orgDutyDetailCode" jdbcType="VARCHAR"/>
        <result column="ORG_DUTY_DETAIL_NAME" property="orgDutyDetailName" jdbcType="VARCHAR"/>
        <result column="detail_limit_amount" property="detailLimitAmount" jdbcType="DECIMAL"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_POLICY_DUTY_DETAIL, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_DUTY,
        DUTY_DETAIL_NAME, DUTY_DETAIL_CODE, DUTY_AMOUNT, NOCLAIM_PROPERTY, NOCLAIM_AMOUNT,
        NOCLAIM_DAYS, NOCLAIM_TIMES, OBSERVED_DAYS, CLAIM_PROPORTION,DUTY_DETAIL_TYPE,
        ALLOWANCE_EVERYDAY, ALLOWANCE_DAYS_LIMIT, SECOND_TYPE, ORG_DUTY_DETAIL_CODE, ORG_DUTY_DETAIL_NAME,detail_limit_amount
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_DUTY_DETAIL
        where ID_AHCS_POLICY_DUTY_DETAIL = #{idAhcsPolicyDutyDetail,jdbcType=VARCHAR}
    </select>
    <select id="getInfoByDutyId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_DUTY_DETAIL
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_POLICY_DUTY_DETAIL
        where ID_AHCS_POLICY_DUTY_DETAIL = #{idAhcsPolicyDutyDetail,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByIdAhcsPolicyDuty" parameterType="java.lang.String">
        delete from CLMS_POLICY_DUTY_DETAIL
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity">
        insert into CLMS_POLICY_DUTY_DETAIL (ID_AHCS_POLICY_DUTY_DETAIL, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,DUTY_DETAIL_TYPE,
        ID_AHCS_POLICY_DUTY, DUTY_DETAIL_NAME, DUTY_DETAIL_CODE,
        DUTY_AMOUNT, NOCLAIM_PROPERTY, NOCLAIM_AMOUNT,
        NOCLAIM_DAYS, NOCLAIM_TIMES,
        AMOUNT_TYPE, OBSERVED_DAYS, CLAIM_PROPORTION,
        ALLOWANCE_EVERYDAY, ALLOWANCE_DAYS_LIMIT,
        SECOND_TYPE, ORG_DUTY_DETAIL_CODE, ORG_DUTY_DETAIL_NAME,detail_limit_amount,FIXED_LIMIT)
        values (#{idAhcsPolicyDutyDetail,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},#{dutyDetailtype,jdbcType=VARCHAR},
        #{idAhcsPolicyDuty,jdbcType=VARCHAR}, #{dutyDetailName,jdbcType=VARCHAR}, #{dutyDetailCode,jdbcType=VARCHAR},
        #{dutyAmount,jdbcType=DECIMAL}, #{noclaimProperty,jdbcType=VARCHAR}, #{noclaimAmount,jdbcType=DECIMAL},
        #{noclaimDays,jdbcType=DECIMAL}, #{noclaimTimes,jdbcType=DECIMAL},
        #{amountType,jdbcType=VARCHAR}, #{observedDays,jdbcType=DECIMAL}, #{claimProportion,jdbcType=DECIMAL},
        #{allowanceEveryday,jdbcType=DECIMAL}, #{allowanceDaysLimit,jdbcType=DECIMAL},
        #{secondType,jdbcType=VARCHAR}, #{orgDutyDetailCode,jdbcType=VARCHAR} ,#{orgDutyDetailName,jdbcType=VARCHAR},
        #{detailLimitAmount,jdbcType=DECIMAL},
        #{fixedLimit,jdbcType=DECIMAL}
              )
    </insert>
    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_POLICY_DUTY_DETAIL (ID_AHCS_POLICY_DUTY_DETAIL, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,DUTY_DETAIL_TYPE,
        ID_AHCS_POLICY_DUTY, DUTY_DETAIL_NAME, DUTY_DETAIL_CODE,
        DUTY_AMOUNT, NOCLAIM_PROPERTY, NOCLAIM_AMOUNT,
        NOCLAIM_DAYS, NOCLAIM_TIMES,
        AMOUNT_TYPE, OBSERVED_DAYS, CLAIM_PROPORTION,
        ALLOWANCE_EVERYDAY, ALLOWANCE_DAYS_LIMIT,
        SECOND_TYPE, ORG_DUTY_DETAIL_CODE, ORG_DUTY_DETAIL_NAME,detail_limit_amount,FIXED_LIMIT)
        <foreach collection="policyDutyDetailEntities" item="policyDutyDetail" index="index" separator="union all">
            select #{policyDutyDetail.idAhcsPolicyDutyDetail,jdbcType=VARCHAR},
            #{policyDutyDetail.createdBy,jdbcType=VARCHAR},
            #{policyDutyDetail.createdDate,jdbcType=TIMESTAMP}, #{policyDutyDetail.updatedBy,jdbcType=VARCHAR},
            #{policyDutyDetail.updatedDate,jdbcType=TIMESTAMP}, #{policyDutyDetail.dutyDetailtype,jdbcType=VARCHAR},
            #{policyDutyDetail.idAhcsPolicyDuty,jdbcType=VARCHAR}, #{policyDutyDetail.dutyDetailName,jdbcType=VARCHAR},
            #{policyDutyDetail.dutyDetailCode,jdbcType=VARCHAR},
            #{policyDutyDetail.dutyAmount,jdbcType=DECIMAL}, #{policyDutyDetail.noclaimProperty,jdbcType=VARCHAR},
            #{policyDutyDetail.noclaimAmount,jdbcType=DECIMAL},
            #{policyDutyDetail.noclaimDays,jdbcType=DECIMAL}, #{policyDutyDetail.noclaimTimes,jdbcType=DECIMAL},
            #{policyDutyDetail.amountType,jdbcType=VARCHAR}, #{policyDutyDetail.observedDays,jdbcType=DECIMAL},
            #{policyDutyDetail.claimProportion,jdbcType=DECIMAL},
            #{policyDutyDetail.allowanceEveryday,jdbcType=DECIMAL},
            #{policyDutyDetail.allowanceDaysLimit,jdbcType=DECIMAL},
            #{policyDutyDetail.secondType,jdbcType=VARCHAR}, #{policyDutyDetail.orgDutyDetailCode,jdbcType=VARCHAR},
            #{policyDutyDetail.orgDutyDetailName,jdbcType=VARCHAR},
            #{policyDutyDetail.detailLimitAmount,jdbcType=DECIMAL},
            #{policyDutyDetail.fixedLimit,jdbcType=DECIMAL}

        </foreach>
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity">
        insert into CLMS_POLICY_DUTY_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsPolicyDutyDetail != null">
                ID_AHCS_POLICY_DUTY_DETAIL,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idAhcsPolicyDuty != null">
                ID_AHCS_POLICY_DUTY,
            </if>
            <if test="dutyDetailName != null">
                DUTY_DETAIL_NAME,
            </if>
            <if test="dutyDetailCode != null">
                DUTY_DETAIL_CODE,
            </if>
            <if test="dutyAmount != null">
                DUTY_AMOUNT,
            </if>
            <if test="noclaimProperty != null">
                NOCLAIM_PROPERTY,
            </if>
            <if test="noclaimAmount != null">
                NOCLAIM_AMOUNT,
            </if>
            <if test="noclaimDays != null">
                NOCLAIM_DAYS,
            </if>
            <if test="noclaimTimes != null">
                NOCLAIM_TIMES,
            </if>
            <if test="amountType != null">
                AMOUNT_TYPE,
            </if>
            <if test="observedDays != null">
                OBSERVED_DAYS,
            </if>
            <if test="claimProportion != null">
                CLAIM_PROPORTION,
            </if>
            <if test="allowanceEveryday != null">
                ALLOWANCE_EVERYDAY,
            </if>
            <if test="allowanceDaysLimit != null">
                ALLOWANCE_DAYS_LIMIT,
            </if>
            <if test="secondType != null">
                SECOND_TYPE,
            </if>
            <if test="orgDutyDetailCode != null">
                ORG_DUTY_DETAIL_CODE,
            </if>
            <if test="orgDutyDetailName != null">
                ORG_DUTY_DETAIL_NAME,
            </if>
            <if test="dutyDetailtype != null">
                DUTY_DETAIL_TYPE,
            </if>
            <if test="detailLimitAmount != null">
                detail_limit_amount,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsPolicyDutyDetail != null">
                #{idAhcsPolicyDutyDetail,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyDuty != null">
                #{idAhcsPolicyDuty,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyAmount != null">
                #{dutyAmount,jdbcType=DECIMAL},
            </if>
            <if test="noclaimProperty != null">
                #{noclaimProperty,jdbcType=VARCHAR},
            </if>
            <if test="noclaimAmount != null">
                #{noclaimAmount,jdbcType=DECIMAL},
            </if>
            <if test="noclaimDays != null">
                #{noclaimDays,jdbcType=DECIMAL},
            </if>
            <if test="noclaimTimes != null">
                #{noclaimTimes,jdbcType=DECIMAL},
            </if>
            <if test="amountType != null">
                #{amountType,jdbcType=VARCHAR},
            </if>
            <if test="observedDays != null">
                #{observedDays,jdbcType=DECIMAL},
            </if>
            <if test="claimProportion != null">
                #{claimProportion,jdbcType=DECIMAL},
            </if>
            <if test="allowanceEveryday != null">
                #{allowanceEveryday,jdbcType=DECIMAL},
            </if>
            <if test="allowanceDaysLimit != null">
                #{allowanceDaysLimit,jdbcType=DECIMAL},
            </if>
            <if test="secondType != null">
                #{secondType,jdbcType=VARCHAR},
            </if>
            <if test="orgDutyDetailCode != null">
                #{orgDutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="orgDutyDetailName != null">
                #{orgDutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailtype != null">
                #{dutyDetailtype,jdbcType=VARCHAR},
            </if>
            <if test="detailLimitAmount != null">
                #{detailLimitAmount,jdbcType=DECIMAL}
            </if>

        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity">
        update CLMS_POLICY_DUTY_DETAIL
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyDuty != null">
                ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                DUTY_DETAIL_NAME = #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyAmount != null">
                DUTY_AMOUNT = #{dutyAmount,jdbcType=DECIMAL},
            </if>
            <if test="noclaimProperty != null">
                NOCLAIM_PROPERTY = #{noclaimProperty,jdbcType=VARCHAR},
            </if>
            <if test="noclaimAmount != null">
                NOCLAIM_AMOUNT = #{noclaimAmount,jdbcType=DECIMAL},
            </if>
            <if test="noclaimDays != null">
                NOCLAIM_DAYS = #{noclaimDays,jdbcType=DECIMAL},
            </if>
            <if test="noclaimTimes != null">
                NOCLAIM_TIMES = #{noclaimTimes,jdbcType=DECIMAL},
            </if>
            <if test="amountType != null">
                AMOUNT_TYPE = #{amountType,jdbcType=VARCHAR},
            </if>
            <if test="observedDays != null">
                OBSERVED_DAYS = #{observedDays,jdbcType=DECIMAL},
            </if>
            <if test="claimProportion != null">
                CLAIM_PROPORTION = #{claimProportion,jdbcType=DECIMAL},
            </if>
            <if test="allowanceEveryday != null">
                ALLOWANCE_EVERYDAY = #{allowanceEveryday,jdbcType=DECIMAL},
            </if>
            <if test="allowanceDaysLimit != null">
                ALLOWANCE_DAYS_LIMIT = #{allowanceDaysLimit,jdbcType=DECIMAL},
            </if>
            <if test="secondType != null">
                SECOND_TYPE = #{secondType,jdbcType=VARCHAR},
            </if>
            <if test="orgDutyDetailCode != null">
                ORG_DUTY_DETAIL_CODE = #{orgDutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="orgDutyDetailName != null">
                ORG_DUTY_DETAIL_NAME = #{orgDutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailtype != null">
                DUTY_DETAIL_TYPE = #{dutyDetailtype,jdbcType=VARCHAR},
            </if>
            <if test="detailLimitAmount != null">
                detail_limit_amount = #{detailLimitAmount,jdbcType=DECIMAL},
            </if>
        </set>
        where ID_AHCS_POLICY_DUTY_DETAIL = #{idAhcsPolicyDutyDetail,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity">
        update CLMS_POLICY_DUTY_DETAIL
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR},
        DUTY_DETAIL_NAME = #{dutyDetailName,jdbcType=VARCHAR},
        DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR},
        DUTY_AMOUNT = #{dutyAmount,jdbcType=DECIMAL},
        NOCLAIM_PROPERTY = #{noclaimProperty,jdbcType=VARCHAR},
        NOCLAIM_AMOUNT = #{noclaimAmount,jdbcType=DECIMAL},
        NOCLAIM_DAYS = #{noclaimDays,jdbcType=DECIMAL},
        NOCLAIM_TIMES = #{noclaimTimes,jdbcType=DECIMAL},
        AMOUNT_TYPE = #{amountType,jdbcType=VARCHAR},
        OBSERVED_DAYS = #{observedDays,jdbcType=DECIMAL},
        CLAIM_PROPORTION = #{claimProportion,jdbcType=DECIMAL},
        ALLOWANCE_EVERYDAY = #{allowanceEveryday,jdbcType=DECIMAL},
        ALLOWANCE_DAYS_LIMIT = #{allowanceDaysLimit,jdbcType=DECIMAL},
        SECOND_TYPE = #{secondType,jdbcType=VARCHAR},
        ORG_DUTY_DETAIL_CODE = #{orgDutyDetailCode,jdbcType=VARCHAR},
        DUTY_DETAIL_TYPE = #{dutyDetailtype,jdbcType=VARCHAR},
        ORG_DUTY_DETAIL_NAME = #{orgDutyDetailName,jdbcType=VARCHAR},
        detail_limit_amount = #{detailLimitAmount,jdbcType=DECIMAL}
        where ID_AHCS_POLICY_DUTY_DETAIL = #{idAhcsPolicyDutyDetail,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete from CLMS_POLICY_DUTY_DETAIL d where d.id_ahcs_policy_duty in( select
        c.id_ahcs_policy_duty
        from CLMS_POLICY_DUTY c
        where c.id_ahcs_policy_plan in
        (select a.id_ahcs_policy_plan
        from CLMS_POLICY_PLAN a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR})))
    </delete>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_DUTY_DETAIL
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into CLMS_POLICY_DUTY_DETAIL (ID_AHCS_POLICY_DUTY_DETAIL, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,DUTY_DETAIL_TYPE,
        ID_AHCS_POLICY_DUTY, DUTY_DETAIL_NAME, DUTY_DETAIL_CODE,
        DUTY_AMOUNT, NOCLAIM_PROPERTY, NOCLAIM_AMOUNT,
        NOCLAIM_DAYS, NOCLAIM_TIMES,
        AMOUNT_TYPE, OBSERVED_DAYS, CLAIM_PROPORTION,
        ALLOWANCE_EVERYDAY, ALLOWANCE_DAYS_LIMIT,
        SECOND_TYPE, ORG_DUTY_DETAIL_CODE, ORG_DUTY_DETAIL_NAME,detail_limit_amount)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsPolicyDutyDetail,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedDate,jdbcType=TIMESTAMP}, #{item.dutyDetailtype,jdbcType=VARCHAR},
            #{item.idAhcsPolicyDuty,jdbcType=VARCHAR}, #{item.dutyDetailName,jdbcType=VARCHAR},
            #{item.dutyDetailCode,jdbcType=VARCHAR},
            #{item.dutyAmount,jdbcType=DECIMAL}, #{item.noclaimProperty,jdbcType=VARCHAR},
            #{item.noclaimAmount,jdbcType=DECIMAL},
            #{item.noclaimDays,jdbcType=DECIMAL}, #{item.noclaimTimes,jdbcType=DECIMAL},
            #{item.amountType,jdbcType=VARCHAR}, #{item.observedDays,jdbcType=DECIMAL},
            #{item.claimProportion,jdbcType=DECIMAL},
            #{item.allowanceEveryday,jdbcType=DECIMAL},
            #{item.allowanceDaysLimit,jdbcType=DECIMAL},
            #{item.secondType,jdbcType=VARCHAR}, #{item.orgDutyDetailCode,jdbcType=VARCHAR},
            #{item.orgDutyDetailName,jdbcType=VARCHAR},
            #{item.detailLimitAmount,jdbcType=DECIMAL})

        </foreach>
    </insert>
    
</mapper>