<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsInsuredPersonExtMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPersonExtEntity">
        <id column="ID_AHCS_INSURED_PERSON_EXT" property="idAhcsInsuredPersonExt"
            jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_AHCS_INSURED_PERSON" property="idAhcsInsuredPerson"
                jdbcType="VARCHAR"/>
        <result column="BANK_ACCOUNT" property="bankAccount" jdbcType="VARCHAR"/>
        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"/>
        <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
        <result column="BANK_HEADQUARTERS_CODE" property="bankHeadquartersCode"
                jdbcType="VARCHAR"/>
        <result column="VEHICLE_LICENCE_CODE" property="vehicleLicenceCode"
                jdbcType="VARCHAR"/>
        <result column="VEHICLE_FRAME_NO" property="vehicleFrameNo"
                jdbcType="VARCHAR"/>
        <result column="ENGINE_NO" property="engineNo" jdbcType="VARCHAR"/>
        <result column="FLIGHT_NO" property="flightNo" jdbcType="VARCHAR"/>
        <result column="FLIGHT_DATE" property="flightDate" jdbcType="TIMESTAMP"/>
        <result column="ORIGINAL" property="original" jdbcType="VARCHAR"/>
        <result column="DESTINATION" property="destination" jdbcType="VARCHAR"/>
        <result column="TRANSACTION_NO" property="transactionNo"
                jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_INSURED_PERSON_EXT, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_AHCS_INSURED_PERSON,
        BANK_ACCOUNT, BANK_CODE, BANK_NAME, BANK_HEADQUARTERS_CODE, VEHICLE_LICENCE_CODE,
        VEHICLE_FRAME_NO,
        ENGINE_NO, FLIGHT_NO, FLIGHT_DATE, ORIGINAL, DESTINATION, TRANSACTION_NO
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_INSURED_PERSON_EXT
        where ID_AHCS_INSURED_PERSON_EXT =
        #{idAhcsInsuredPersonExt,jdbcType=VARCHAR}
    </select>
    <select id="selectByIdAhcsInsuredPerson" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_INSURED_PERSON_EXT
        where ID_AHCS_INSURED_PERSON = #{idAhcsInsuredPerson,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_INSURED_PERSON_EXT
        where ID_AHCS_INSURED_PERSON_EXT =
        #{idAhcsInsuredPersonExt,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByIdAhcsInsuredPerson" parameterType="java.lang.String">
        delete from CLMS_INSURED_PERSON_EXT
        where ID_AHCS_INSURED_PERSON = #{idAhcsInsuredPerson,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPersonExtEntity">
        insert into CLMS_INSURED_PERSON_EXT (ID_AHCS_INSURED_PERSON_EXT,
        CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_AHCS_INSURED_PERSON, BANK_ACCOUNT, BANK_CODE, BANK_NAME,
        BANK_HEADQUARTERS_CODE, VEHICLE_LICENCE_CODE,
        VEHICLE_FRAME_NO, ENGINE_NO, FLIGHT_NO,
        FLIGHT_DATE, ORIGINAL, DESTINATION,
        TRANSACTION_NO)
        values (#{idAhcsInsuredPersonExt,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{idAhcsInsuredPerson,jdbcType=VARCHAR},
        #{bankAccount,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR},
        #{bankName,jdbcType=VARCHAR},
        #{bankHeadquartersCode,jdbcType=VARCHAR},
        #{vehicleLicenceCode,jdbcType=VARCHAR},
        #{vehicleFrameNo,jdbcType=VARCHAR}, #{engineNo,jdbcType=VARCHAR},
        #{flightNo,jdbcType=VARCHAR},
        #{flightDate,jdbcType=TIMESTAMP}, #{original,jdbcType=VARCHAR}, #{destination,jdbcType=VARCHAR},
        #{transactionNo,jdbcType=VARCHAR})
    </insert>

    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_INSURED_PERSON_EXT (ID_AHCS_INSURED_PERSON_EXT,
        CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_AHCS_INSURED_PERSON, BANK_ACCOUNT, BANK_CODE, BANK_NAME,
        BANK_HEADQUARTERS_CODE, VEHICLE_LICENCE_CODE,
        VEHICLE_FRAME_NO, ENGINE_NO, FLIGHT_NO,
        FLIGHT_DATE, ORIGINAL, DESTINATION,
        TRANSACTION_NO)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsInsuredPersonExt,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.idAhcsInsuredPerson,jdbcType=VARCHAR},
            #{item.bankAccount,jdbcType=VARCHAR},
            #{item.bankCode,jdbcType=VARCHAR},
            #{item.bankName,jdbcType=VARCHAR},
            #{item.bankHeadquartersCode,jdbcType=VARCHAR},
            #{item.vehicleLicenceCode,jdbcType=VARCHAR},
            #{item.vehicleFrameNo,jdbcType=VARCHAR},
            #{item.engineNo,jdbcType=VARCHAR},
            #{item.flightNo,jdbcType=VARCHAR},
            #{item.flightDate,jdbcType=TIMESTAMP},
            #{item.original,jdbcType=VARCHAR},
            #{item.destination,jdbcType=VARCHAR},
            #{item.transactionNo,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPersonExtEntity">
        insert into CLMS_INSURED_PERSON_EXT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsInsuredPersonExt != null">
                ID_AHCS_INSURED_PERSON_EXT,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idAhcsInsuredPerson != null">
                ID_AHCS_INSURED_PERSON,
            </if>
            <if test="bankAccount != null">
                BANK_ACCOUNT,
            </if>
            <if test="bankCode != null">
                BANK_CODE,
            </if>
            <if test="bankName != null">
                BANK_NAME,
            </if>
            <if test="bankHeadquartersCode != null">
                BANK_HEADQUARTERS_CODE,
            </if>
            <if test="vehicleLicenceCode != null">
                VEHICLE_LICENCE_CODE,
            </if>
            <if test="vehicleFrameNo != null">
                VEHICLE_FRAME_NO,
            </if>
            <if test="engineNo != null">
                ENGINE_NO,
            </if>
            <if test="flightNo != null">
                FLIGHT_NO,
            </if>
            <if test="flightDate != null">
                FLIGHT_DATE,
            </if>
            <if test="original != null">
                ORIGINAL,
            </if>
            <if test="destination != null">
                DESTINATION,
            </if>
            <if test="transactionNo != null">
                TRANSACTION_NO,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsInsuredPersonExt != null">
                #{idAhcsInsuredPersonExt,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsInsuredPerson != null">
                #{idAhcsInsuredPerson,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null">
                #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null">
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="bankHeadquartersCode != null">
                #{bankHeadquartersCode,jdbcType=VARCHAR},
            </if>
            <if test="vehicleLicenceCode != null">
                #{vehicleLicenceCode,jdbcType=VARCHAR},
            </if>
            <if test="vehicleFrameNo != null">
                #{vehicleFrameNo,jdbcType=VARCHAR},
            </if>
            <if test="engineNo != null">
                #{engineNo,jdbcType=VARCHAR},
            </if>
            <if test="flightNo != null">
                #{flightNo,jdbcType=VARCHAR},
            </if>
            <if test="flightDate != null">
                #{flightDate,jdbcType=TIMESTAMP},
            </if>
            <if test="original != null">
                #{original,jdbcType=VARCHAR},
            </if>
            <if test="destination != null">
                #{destination,jdbcType=VARCHAR},
            </if>
            <if test="transactionNo != null">
                #{transactionNo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPersonExtEntity">
        update CLMS_INSURED_PERSON_EXT
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsInsuredPerson != null">
                ID_AHCS_INSURED_PERSON = #{idAhcsInsuredPerson,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null">
                BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null">
                BANK_CODE = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null">
                BANK_NAME = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="bankHeadquartersCode != null">
                BANK_HEADQUARTERS_CODE = #{bankHeadquartersCode,jdbcType=VARCHAR},
            </if>
            <if test="vehicleLicenceCode != null">
                VEHICLE_LICENCE_CODE = #{vehicleLicenceCode,jdbcType=VARCHAR},
            </if>
            <if test="vehicleFrameNo != null">
                VEHICLE_FRAME_NO = #{vehicleFrameNo,jdbcType=VARCHAR},
            </if>
            <if test="engineNo != null">
                ENGINE_NO = #{engineNo,jdbcType=VARCHAR},
            </if>
            <if test="flightNo != null">
                FLIGHT_NO = #{flightNo,jdbcType=VARCHAR},
            </if>
            <if test="flightDate != null">
                FLIGHT_DATE = #{flightDate,jdbcType=TIMESTAMP},
            </if>
            <if test="original != null">
                ORIGINAL = #{original,jdbcType=VARCHAR},
            </if>
            <if test="destination != null">
                DESTINATION = #{destination,jdbcType=VARCHAR},
            </if>
            <if test="transactionNo != null">
                TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_AHCS_INSURED_PERSON_EXT =
        #{idAhcsInsuredPersonExt,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPersonExtEntity">
        update CLMS_INSURED_PERSON_EXT
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        ID_AHCS_INSURED_PERSON = #{idAhcsInsuredPerson,jdbcType=VARCHAR},
        BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
        BANK_CODE = #{bankCode,jdbcType=VARCHAR},
        BANK_NAME = #{bankName,jdbcType=VARCHAR},
        BANK_HEADQUARTERS_CODE = #{bankHeadquartersCode,jdbcType=VARCHAR},
        VEHICLE_LICENCE_CODE = #{vehicleLicenceCode,jdbcType=VARCHAR},
        VEHICLE_FRAME_NO = #{vehicleFrameNo,jdbcType=VARCHAR},
        ENGINE_NO = #{engineNo,jdbcType=VARCHAR},
        FLIGHT_NO = #{flightNo,jdbcType=VARCHAR},
        FLIGHT_DATE = #{flightDate,jdbcType=TIMESTAMP},
        ORIGINAL = #{original,jdbcType=VARCHAR},
        DESTINATION = #{destination,jdbcType=VARCHAR},
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
        where ID_AHCS_INSURED_PERSON_EXT =
        #{idAhcsInsuredPersonExt,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete from CLMS_INSURED_PERSON_EXT d where d.id_ahcs_insured_person
        in(select a.id_ahcs_insured_person
        from CLMS_INSURED_PERSON a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR}))
    </delete>
</mapper>