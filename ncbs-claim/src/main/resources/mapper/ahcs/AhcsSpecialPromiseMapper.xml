<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsSpecialPromiseMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsSpecialPromiseEntity">
        <id column="ID_AHCS_SPECIAL_PROMISE" property="idAhcsSpecialPromise" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_AHCS_POLICY_INFO" property="idAhcsPolicyInfo" jdbcType="VARCHAR"/>
        <result column="PROMISE_CODE" property="promiseCode" jdbcType="VARCHAR"/>
        <result column="PROMISE_DESC" property="promiseDesc" jdbcType="VARCHAR"/>
        <result column="PROMISE_TYPE" property="promiseType" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="CONTENT_TYPE" property="contentType" jdbcType="VARCHAR"/>
        <result column="SORT_INDEX" property="sortIndex" jdbcType="DECIMAL"/>
        <result column="RISK_GROUP_NAME" property="riskGroupName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_SPECIAL_PROMISE, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        PROMISE_CODE, PROMISE_DESC, PROMISE_TYPE, BUSINESS_TYPE, CONTENT_TYPE, SORT_INDEX,RISK_GROUP_NAME
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_SPECIAL_PROMISE
        where ID_AHCS_SPECIAL_PROMISE = #{idAhcsSpecialPromise,jdbcType=VARCHAR}
    </select>
    <select id="getInfoByPolicyId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_SPECIAL_PROMISE
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_SPECIAL_PROMISE
        where ID_AHCS_SPECIAL_PROMISE = #{idAhcsSpecialPromise,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByIdAhcsPolicyInfo" parameterType="java.lang.String">
        delete from CLMS_SPECIAL_PROMISE
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsSpecialPromiseEntity">
        insert into CLMS_SPECIAL_PROMISE (ID_AHCS_SPECIAL_PROMISE, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_AHCS_POLICY_INFO, PROMISE_CODE, PROMISE_DESC,
        PROMISE_TYPE, BUSINESS_TYPE, CONTENT_TYPE,
        SORT_INDEX,RISK_GROUP_NAME)
        values (#{idAhcsSpecialPromise,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{idAhcsPolicyInfo,jdbcType=VARCHAR}, #{promiseCode,jdbcType=VARCHAR}, #{promiseDesc,jdbcType=VARCHAR},
        #{promiseType,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, #{contentType,jdbcType=VARCHAR},
        #{sortIndex,jdbcType=DECIMAL}, #{riskGroupName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_SPECIAL_PROMISE (ID_AHCS_SPECIAL_PROMISE, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_AHCS_POLICY_INFO, PROMISE_CODE, PROMISE_DESC,
        PROMISE_TYPE, BUSINESS_TYPE, CONTENT_TYPE,
        SORT_INDEX,RISK_GROUP_NAME)
        <foreach collection="specialPromiseEntities" item="specialPromise" index="index" separator="union all">
            select #{specialPromise.idAhcsSpecialPromise,jdbcType=VARCHAR},
            #{specialPromise.createdBy,jdbcType=VARCHAR},
            #{specialPromise.createdDate,jdbcType=TIMESTAMP}, #{specialPromise.updatedBy,jdbcType=VARCHAR},
            #{specialPromise.updatedDate,jdbcType=TIMESTAMP},
            #{specialPromise.idAhcsPolicyInfo,jdbcType=VARCHAR}, #{specialPromise.promiseCode,jdbcType=VARCHAR},
            #{specialPromise.promiseDesc,jdbcType=VARCHAR},
            #{specialPromise.promiseType,jdbcType=VARCHAR}, #{specialPromise.businessType,jdbcType=VARCHAR},
            #{specialPromise.contentType,jdbcType=VARCHAR},
            #{specialPromise.sortIndex,jdbcType=DECIMAL},#{specialPromise.riskGroupName,jdbcType=VARCHAR}
        </foreach>
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsSpecialPromiseEntity">
        insert into CLMS_SPECIAL_PROMISE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsSpecialPromise != null">
                ID_AHCS_SPECIAL_PROMISE,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO,
            </if>
            <if test="promiseCode != null">
                PROMISE_CODE,
            </if>
            <if test="promiseDesc != null">
                PROMISE_DESC,
            </if>
            <if test="promiseType != null">
                PROMISE_TYPE,
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE,
            </if>
            <if test="contentType != null">
                CONTENT_TYPE,
            </if>
            <if test="sortIndex != null">
                SORT_INDEX,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsSpecialPromise != null">
                #{idAhcsSpecialPromise,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyInfo != null">
                #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="promiseCode != null">
                #{promiseCode,jdbcType=VARCHAR},
            </if>
            <if test="promiseDesc != null">
                #{promiseDesc,jdbcType=VARCHAR},
            </if>
            <if test="promiseType != null">
                #{promiseType,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null">
                #{contentType,jdbcType=VARCHAR},
            </if>
            <if test="sortIndex != null">
                #{sortIndex,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsSpecialPromiseEntity">
        update CLMS_SPECIAL_PROMISE
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="promiseCode != null">
                PROMISE_CODE = #{promiseCode,jdbcType=VARCHAR},
            </if>
            <if test="promiseDesc != null">
                PROMISE_DESC = #{promiseDesc,jdbcType=VARCHAR},
            </if>
            <if test="promiseType != null">
                PROMISE_TYPE = #{promiseType,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null">
                CONTENT_TYPE = #{contentType,jdbcType=VARCHAR},
            </if>
            <if test="sortIndex != null">
                SORT_INDEX = #{sortIndex,jdbcType=DECIMAL},
            </if>
        </set>
        where ID_AHCS_SPECIAL_PROMISE = #{idAhcsSpecialPromise,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsSpecialPromiseEntity">
        update CLMS_SPECIAL_PROMISE
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR},
        PROMISE_CODE = #{promiseCode,jdbcType=VARCHAR},
        PROMISE_DESC = #{promiseDesc,jdbcType=VARCHAR},
        PROMISE_TYPE = #{promiseType,jdbcType=VARCHAR},
        BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
        CONTENT_TYPE = #{contentType,jdbcType=VARCHAR},
        SORT_INDEX = #{sortIndex,jdbcType=DECIMAL}
        where ID_AHCS_SPECIAL_PROMISE = #{idAhcsSpecialPromise,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete
        from CLMS_SPECIAL_PROMISE a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR})
    </delete>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsSpecialPromiseEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_SPECIAL_PROMISE
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into CLMS_SPECIAL_PROMISE (
        ID_AHCS_SPECIAL_PROMISE,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_POLICY_INFO,
        PROMISE_CODE,
        PROMISE_DESC,
        PROMISE_TYPE,
        BUSINESS_TYPE,
        CONTENT_TYPE,
        SORT_INDEX,
        RISK_GROUP_NAME)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsSpecialPromise,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.idAhcsPolicyInfo,jdbcType=VARCHAR},
            #{item.promiseCode,jdbcType=VARCHAR},
            #{item.promiseDesc,jdbcType=VARCHAR},
            #{item.promiseType,jdbcType=VARCHAR},
            #{item.businessType,jdbcType=VARCHAR},
            #{item.contentType,jdbcType=VARCHAR},
            #{item.sortIndex,jdbcType=DECIMAL},
            #{item.riskGroupName,jdbcType=VARCHAR})
        </foreach>
    </insert>
    
</mapper>