<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyDutyMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyEntity">
        <id column="ID_AHCS_POLICY_DUTY" property="idAhcsPolicyDuty" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_AHCS_POLICY_PLAN" property="idAhcsPolicyPlan" jdbcType="VARCHAR"/>
        <result column="DUTY_NAME" property="dutyName" jdbcType="VARCHAR"/>
        <result column="DUTY_CODE" property="dutyCode" jdbcType="VARCHAR"/>
        <result column="DUTY_AMOUNT" property="dutyAmount" jdbcType="DECIMAL"/>
        <result column="DUTY_DESC" property="dutyDesc" jdbcType="VARCHAR"/>
        <result column="ORG_DUTY_CODE" property="orgDutyCode" jdbcType="VARCHAR"/>
        <result column="ORG_DUTY_NAME" property="orgDutyName" jdbcType="VARCHAR"/>
        <result column="IS_DUTY_SHARED_AMOUNT" property="isDutySharedAmount" jdbcType="VARCHAR"/>
        <result column="DUTY_SHARED_AMOUNT_MERGE" property="dutySharedAmountMerge" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_POLICY_DUTY, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_PLAN,
        DUTY_NAME, DUTY_CODE, DUTY_AMOUNT, DUTY_DESC, ORG_DUTY_CODE, ORG_DUTY_NAME,IS_DUTY_SHARED_AMOUNT,DUTY_SHARED_AMOUNT_MERGE
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_DUTY
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </select>
    <select id="selectByIdAhcsPolicyPlan" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_DUTY
        where ID_AHCS_POLICY_PLAN = #{idAhcsPolicyPlan,jdbcType=VARCHAR}
    </select>
    <select id="getInfoByReportInfo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from
        CLMS_POLICY_DUTY c
        where
        c.duty_code = #{dutyCode,jdbcType=VARCHAR}
        and
        c.id_ahcs_policy_plan in
        (select a.id_ahcs_policy_plan
        from CLMS_POLICY_PLAN a
        where
        a.plan_code = #{planCode,jdbcType=VARCHAR}
        and a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where 1=1
        <if test="reportNo !=null and reportNo != ''">
            and b.report_no = #{reportNo,jdbcType=VARCHAR}
        </if>
        <if test="policyNo !=null and policyNo != ''">
            and b.policy_no = #{policyNo,jdbcType=VARCHAR}))
        </if>
    </select>
    <select id="getPolicyDutyInfo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from
        CLMS_POLICY_DUTY c
        where
        c.duty_code = #{dutyCode,jdbcType=VARCHAR}
        and
        c.id_ahcs_policy_plan in
        (select a.id_ahcs_policy_plan
        from CLMS_POLICY_PLAN a
        where
        a.plan_code = #{planCode,jdbcType=VARCHAR}
        and a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no = #{policyNo,jdbcType=VARCHAR}
        <if test="selfCardNo !=null and selfCardNo != ''">
            and b.self_card_no = #{selfCardNo,jdbcType=VARCHAR}
        </if>
        ))
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_POLICY_DUTY
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByIdAhcsPolicyPlan" parameterType="java.lang.String">
        delete from CLMS_POLICY_DUTY
        where ID_AHCS_POLICY_PLAN = #{idAhcsPolicyPlan,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyEntity">
        insert into CLMS_POLICY_DUTY (ID_AHCS_POLICY_DUTY, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_PLAN,
        DUTY_NAME, DUTY_CODE, DUTY_AMOUNT, DUTY_DESC, ORG_DUTY_CODE, ORG_DUTY_NAME,
        IS_DUTY_SHARED_AMOUNT,DUTY_SHARED_AMOUNT_MERGE,insurance_begin_date,insurance_end_date)
        values (#{idAhcsPolicyDuty,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{idAhcsPolicyPlan,jdbcType=VARCHAR},
        #{dutyName,jdbcType=VARCHAR}, #{dutyCode,jdbcType=VARCHAR}, #{dutyAmount,jdbcType=DECIMAL},
        #{dutyDesc,jdbcType=VARCHAR},
        #{orgDutyCode,jdbcType=VARCHAR}, #{orgDutyName,jdbcType=VARCHAR},
        #{isDutySharedAmount,jdbcType=VARCHAR},#{dutySharedAmountMerge,jdbcType=VARCHAR},
        #{insuranceBeginDate,jdbcType=TIMESTAMP},#{insuranceEndDate,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_POLICY_DUTY (ID_AHCS_POLICY_DUTY, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_PLAN,
        DUTY_NAME, DUTY_CODE, DUTY_AMOUNT, DUTY_DESC, ORG_DUTY_CODE, ORG_DUTY_NAME,
        IS_DUTY_SHARED_AMOUNT,DUTY_SHARED_AMOUNT_MERGE,insurance_begin_date,insurance_end_date)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsPolicyDuty,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.idAhcsPolicyPlan,jdbcType=VARCHAR},
            #{item.dutyName,jdbcType=VARCHAR}, #{item.dutyCode,jdbcType=VARCHAR},
            #{item.dutyAmount,jdbcType=DECIMAL},
            #{item.dutyDesc,jdbcType=VARCHAR}, #{item.orgDutyCode,jdbcType=VARCHAR},
            #{item.orgDutyName,jdbcType=VARCHAR},
            #{item.isDutySharedAmount,jdbcType=VARCHAR},#{item.dutySharedAmountMerge,jdbcType=VARCHAR},
            #{item.insuranceBeginDate,jdbcType=TIMESTAMP},#{item.insuranceEndDate,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyEntity">
        insert into CLMS_POLICY_DUTY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsPolicyDuty != null">
                ID_AHCS_POLICY_DUTY,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idAhcsPolicyPlan != null">
                ID_AHCS_POLICY_PLAN,
            </if>
            <if test="dutyName != null">
                DUTY_NAME,
            </if>
            <if test="dutyCode != null">
                DUTY_CODE,
            </if>
            <if test="dutyAmount != null">
                DUTY_AMOUNT,
            </if>
            <if test="dutyDesc != null">
                DUTY_DESC,
            </if>
            <if test="orgDutyCode != null">
                ORG_DUTY_CODE,
            </if>
            <if test="orgDutyName != null">
                ORG_DUTY_NAME,
            </if>
            <if test="isDutySharedAmount != null">
                IS_DUTY_SHARED_AMOUNT,
            </if>
            <if test="dutySharedAmountMerge != null">
                DUTY_SHARED_AMOUNT_MERGE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsPolicyDuty != null">
                #{idAhcsPolicyDuty,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyPlan != null">
                #{idAhcsPolicyPlan,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyAmount != null">
                #{dutyAmount,jdbcType=DECIMAL},
            </if>
            <if test="dutyDesc != null">
                #{dutyDesc,jdbcType=VARCHAR},
            </if>
            <if test="orgDutyCode != null">
                #{orgDutyCode,jdbcType=VARCHAR},
            </if>
            <if test="orgDutyName != null">
                #{orgDutyName,jdbcType=VARCHAR},
            </if>
            <if test="isDutySharedAmount != null">
                #{isDutySharedAmount,jdbcType=VARCHAR},
            </if>
            <if test="dutySharedAmountMerge != null">
                #{dutySharedAmountMerge,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyEntity">
        update CLMS_POLICY_DUTY
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyPlan != null">
                ID_AHCS_POLICY_PLAN = #{idAhcsPolicyPlan,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                DUTY_NAME = #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                DUTY_CODE = #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyAmount != null">
                DUTY_AMOUNT = #{dutyAmount,jdbcType=DECIMAL},
            </if>
            <if test="dutyDesc null">
                DUTY_DESC = #{dutyDesc,jdbcType=VARCHAR},
            </if>
            <if test="orgDutyCode null">
                ORG_DUTY_CODE = #{orgDutyCode,jdbcType=VARCHAR},
            </if>
            <if test="orgDutyName null">
                ORG_DUTY_NAME = #{orgDutyName,jdbcType=VARCHAR},
            </if>
            <if test="isDutySharedAmount != null">
                IS_DUTY_SHARED_AMOUNT = #{isDutySharedAmount,jdbcType=VARCHAR},
            </if>
            <if test="dutySharedAmountMerge != null">
                DUTY_SHARED_AMOUNT_MERGE = #{dutySharedAmountMerge,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyEntity">
        update CLMS_POLICY_DUTY
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        ID_AHCS_POLICY_PLAN = #{idAhcsPolicyPlan,jdbcType=VARCHAR},
        DUTY_NAME = #{dutyName,jdbcType=VARCHAR},
        DUTY_CODE = #{dutyCode,jdbcType=VARCHAR},
        DUTY_AMOUNT = #{dutyAmount,jdbcType=DECIMAL},
        DUTY_DESC = #{dutyDesc,jdbcType=VARCHAR},
        ORG_DUTY_CODE = #{orgDutyCode,jdbcType=VARCHAR},
        ORG_DUTY_NAME = #{orgDutyName,jdbcType=VARCHAR},
        IS_DUTY_SHARED_AMOUNT = #{isDutySharedAmount,jdbcType=VARCHAR},
        DUTY_SHARED_AMOUNT_MERGE = #{dutySharedAmountMerge,jdbcType=VARCHAR}
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete
        from CLMS_POLICY_DUTY c
        where c.id_ahcs_policy_plan in
        (select a.id_ahcs_policy_plan
        from CLMS_POLICY_PLAN a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR}))
    </delete>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_DUTY
        where ID_AHCS_POLICY_PLAN = #{idAhcsPolicyPlan,jdbcType=VARCHAR}
    </select>

    <select id="getPlanCodeByDuty"  resultType="java.lang.String">
        SELECT DISTINCT
            b.PLAN_CODE planCode
        FROM
            clms_policy_info a,
            clms_policy_plan b,
            clms_policy_duty c
        WHERE
            a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          AND a.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
          AND a.ID_AHCS_POLICY_INFO = b.ID_AHCS_POLICY_INFO
          AND c.ID_AHCS_POLICY_PLAN = b.ID_AHCS_POLICY_PLAN
          AND c.DUTY_CODE =  #{dutyCode,jdbcType=VARCHAR}
        LIMIT 1
    </select>
    <select id="getDutyList" resultType="com.paic.ncbs.claim.dao.entity.duty.ReportDutyVo">
        select * from clms_policy_duty where id_ahcs_policy_plan =#{idAhcsPolicyPlan};
    </select>
    <select id="getDutyDetailList" resultType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity">
        select * from clms_policy_duty_detail where id_ahcs_policy_duty = #{idAhcsPolicyDuty};
    </select>

</mapper>