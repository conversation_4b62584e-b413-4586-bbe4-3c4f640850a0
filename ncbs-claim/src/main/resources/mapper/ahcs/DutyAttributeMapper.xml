<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeEntity">
        <id column="ID_AHCS_DUTY_ATTRIBUTE" property="idAhcsDutyAttribute" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_AHCS_POLICY_DUTY" property="idAhcsPolicyDuty" jdbcType="VARCHAR"/>
        <result column="ATTRIBUTE_CODE" property="attributeCode" jdbcType="VARCHAR"/>
        <result column="ATTRIBUTE_VALUE" property="attributeValue" jdbcType="VARCHAR"/>
        <result column="ATTR_RATE_VALUE" property="attrRateValue" jdbcType="DECIMAL"/>
        <result column="ATTRIBUTE_UNIT" property="attributeUnit" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO" id="result">
        <result property="attrName" column="ATTRIBUTE_NAME" jdbcType="VARCHAR"/>
        <result property="attrCode" column="ATTRIBUTE_CODE" jdbcType="VARCHAR"/>
        <result property="attrValue" column="ATTRIBUTE_VALUE" jdbcType="VARCHAR"/>
        <result property="attrUnit" column="UNIT" jdbcType="VARCHAR"/>
    <!-- 关联 责任属性明细-->
    <collection property="attrDetails"
                ofType="com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO"
                select="com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeDetailMapper.getAllAttributeDetails"
                column="{idAhcsDutyAttribute = ID_AHCS_DUTY_ATTRIBUTE}">
    </collection>
    </resultMap>

    <resultMap id="dutyAttributeValue" type="com.paic.ncbs.claim.model.dto.settle.DutyAttributeValueDTO">
        <result property="attributeCode" column="attribute_code" />
        <result property="attributeValue" column='attribute_value' />
        <result property="planCode" column="plan_code" />
        <result property="dutyCode" column="duty_code" />
        <result property="policyNo" column="policy_no" />
        <result property="productCode" column="product_code" />
        <result property="dutyName" column="duty_name" />
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_DUTY_ATTRIBUTE, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_DUTY,
        ATTRIBUTE_CODE, ATTRIBUTE_VALUE, ATTR_RATE_VALUE, ATTRIBUTE_UNIT
    </sql>

    <select id="getAllAttributes" resultMap="result" parameterType="java.lang.String">
        select a.ID_AHCS_DUTY_ATTRIBUTE,
               b.ATTRIBUTE_NAME,
               a.ATTRIBUTE_CODE,
               a.ATTRIBUTE_VALUE,
               b.UNIT
        from CLMS_DUTY_ATTRIBUTE a ,duty_attr_def b
        where a.ATTRIBUTE_CODE = b.ATTRIBUTE_CODE
        and a.ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </select>


    <select id="getAttributeByDutyId" resultType="java.util.HashMap" parameterType="java.lang.String">
        select
            ATTRIBUTE_CODE, ATTRIBUTE_VALUE,ID_AHCS_DUTY_ATTRIBUTE
        from CLMS_DUTY_ATTRIBUTE
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </select>

    <select id="getAttributeByAttrCode" resultType="java.util.HashMap">
        select
            ATTRIBUTE_CODE, ATTRIBUTE_VALUE,ID_AHCS_DUTY_ATTRIBUTE
        from CLMS_DUTY_ATTRIBUTE
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
        and ATTRIBUTE_CODE in
            <foreach collection="attributeCode" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>

    </select>
    <select id="getAttributeDetailByidPolicyDuty" resultType="java.util.HashMap">
        select b.ATTRIBUTE_DETAIL_CODE,b.ATTRIBUTE_DETAIL_VALUE from clms_duty_attribute a , clms_duty_attribute_detail b
        where a.ID_AHCS_DUTY_ATTRIBUTE=b.ID_AHCS_DUTY_ATTRIBUTE
        and a.ID_AHCS_POLICY_DUTY= #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </select>



    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_DUTY_ATTRIBUTE
        where ID_AHCS_DUTY_ATTRIBUTE = #{idAhcsDutyAttribute,jdbcType=VARCHAR}
    </select>
    <select id="getInfoByDutyId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_DUTY_ATTRIBUTE
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_DUTY_ATTRIBUTE
        where ID_AHCS_DUTY_ATTRIBUTE = #{idAhcsDutyAttribute,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeEntity">
        insert into /*+append_values */ CLMS_DUTY_ATTRIBUTE (ID_AHCS_DUTY_ATTRIBUTE, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_DUTY,
        ATTRIBUTE_CODE, ATTRIBUTE_VALUE, ATTR_RATE_VALUE, ATTRIBUTE_UNIT
        )
        values (#{idAhcsDutyAttribute,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{idAhcsPolicyDuty,jdbcType=VARCHAR},
        #{attributeCode,jdbcType=VARCHAR}, #{attributeValue,jdbcType=VARCHAR}, #{attrRateValue,jdbcType=DECIMAL},
        #{attributeUnit,jdbcType=VARCHAR})
    </insert>
    
    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_DUTY_ATTRIBUTE (ID_AHCS_DUTY_ATTRIBUTE, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_DUTY,
        ATTRIBUTE_CODE, ATTRIBUTE_VALUE, ATTR_RATE_VALUE, ATTRIBUTE_UNIT
        )
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsDutyAttribute,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.idAhcsPolicyDuty,jdbcType=VARCHAR},
            #{item.attributeCode,jdbcType=VARCHAR}, #{item.attributeValue,jdbcType=VARCHAR},
            #{item.attrRateValue,jdbcType=DECIMAL},
            #{item.attributeUnit,jdbcType=VARCHAR})
        </foreach>
    </insert>
    
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeEntity">
        insert into CLMS_DUTY_ATTRIBUTE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsDutyAttribute != null">
                ID_AHCS_DUTY_ATTRIBUTE,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idAhcsPolicyDuty != null">
                ID_AHCS_POLICY_DUTY,
            </if>
            <if test="attributeCode != null">
                ATTRIBUTE_CODE,
            </if>
            <if test="attributeValue != null">
                ATTRIBUTE_VALUE,
            </if>
            <if test="attrRateValue != null">
                ATTR_RATE_VALUE,
            </if>
            <if test="attributeUnit != null">
                ATTRIBUTE_UNIT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsDutyAttribute != null">
                #{idAhcsDutyAttribute,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyDuty != null">
                #{idAhcsPolicyDuty,jdbcType=VARCHAR},
            </if>
            <if test="attributeCode != null">
                #{attributeCode,jdbcType=VARCHAR},
            </if>
            <if test="attributeValue != null">
                #{attributeValue,jdbcType=VARCHAR},
            </if>
            <if test="attrRateValue != null">
                #{attrRateValue,jdbcType=DECIMAL},
            </if>
            <if test="attributeUnit != null">
                #{attributeUnit,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeEntity">
        update CLMS_DUTY_ATTRIBUTE
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyDuty != null">
                ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR},
            </if>
            <if test="attributeCode != null">
                ATTRIBUTE_CODE = #{attributeCode,jdbcType=VARCHAR},
            </if>
            <if test="attributeValue != null">
                ATTRIBUTE_VALUE = #{attributeValue,jdbcType=VARCHAR},
            </if>
            <if test="attrRateValue != null">
                ATTR_RATE_VALUE = #{attrRateValue,jdbcType=DECIMAL},
            </if>
            <if test="attributeUnit != null">
                ATTRIBUTE_UNIT = #{attributeUnit,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_AHCS_DUTY_ATTRIBUTE = #{idAhcsDutyAttribute,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeEntity">
        update CLMS_DUTY_ATTRIBUTE
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR},
        ATTRIBUTE_CODE = #{attributeCode,jdbcType=VARCHAR},
        ATTRIBUTE_VALUE = #{attributeValue,jdbcType=VARCHAR},
        ATTR_RATE_VALUE = #{attrRateValue,jdbcType=DECIMAL},
        ATTRIBUTE_UNIT = #{attributeUnit,jdbcType=VARCHAR}
        where ID_AHCS_DUTY_ATTRIBUTE = #{idAhcsDutyAttribute,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete
        from CLMS_DUTY_ATTRIBUTE d where d.id_ahcs_policy_duty in( select
        c.id_ahcs_policy_duty
        from CLMS_POLICY_DUTY c
        where c.id_ahcs_policy_plan in
        (select a.id_ahcs_policy_plan
        from CLMS_POLICY_PLAN a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR})))
    </delete>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_DUTY_ATTRIBUTE
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty,jdbcType=VARCHAR}
    </select>

    <select id="getAttributeValue" resultMap="dutyAttributeValue" parameterType="java.lang.String">
        SELECT
            a.POLICY_NO AS policy_no,
            a.PRODUCT_CODE AS product_code,
            b.PLAN_CODE AS plan_code,
            c.DUTY_CODE AS duty_code,
            d.ATTRIBUTE_CODE AS attribute_code,
            d.ATTRIBUTE_VALUE AS attribute_value,
            c.DUTY_NAME as duty_name
        FROM
            CLMS_POLICY_INFO a
                INNER JOIN CLMS_POLICY_PLAN b ON a.ID_AHCS_POLICY_INFO = b.ID_AHCS_POLICY_INFO
                INNER JOIN CLMS_POLICY_DUTY c ON b.ID_AHCS_POLICY_PLAN = c.ID_AHCS_POLICY_PLAN
                INNER JOIN CLMS_DUTY_ATTRIBUTE d ON c.ID_AHCS_POLICY_DUTY = d.ID_AHCS_POLICY_DUTY
        WHERE
            a.report_no = #{reportNo,jdbcType=VARCHAR}
        and
            d.attribute_code in('541','151')
    </select>
    <!--ATTRIBUTE_CODE：'12'表示 属性配置了限额类型，6-表示配置了赔偿限额   -->
    <select id="getDutyAttribute" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.settle.DutyAttributeValueDTO">
        select d.ATTRIBUTE_CODE attributeCode,
        d.ATTRIBUTE_VALUE attributeValue,
        a.duty_code dutyCode,
        b.PLAN_CODE planCode,
        c.policy_no policyNo
        from clms_duty_attribute d, clms_policy_duty a, clms_policy_plan b, clms_policy_info c
        where  b.ID_AHCS_POLICY_INFO=c.ID_AHCS_POLICY_INFO and a.ID_AHCS_POLICY_PLAN=b.ID_AHCS_POLICY_PLAN
        and d.ID_AHCS_POLICY_DUTY=a.ID_AHCS_POLICY_DUTY
        and d.ATTRIBUTE_CODE in('12','6')
        and c.report_no=#{reportNo}
    </select>
    <select id="getAttributeByAttrCodeInfo" resultType="java.lang.String">
        select ATTRIBUTE_VALUE attributeValue
        from CLMS_DUTY_ATTRIBUTE
        where ID_AHCS_POLICY_DUTY = #{idAhcsPolicyDuty}
        and ATTRIBUTE_CODE=#{attributeCode}
    </select>
    <!--ATTRIBUTE_CODE：'615'表示 属性配置了每月赔付天数，  -->
    <select id="getDutyAttributePayDays" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.settle.DutyAttributeValueDTO">
        select d.ATTRIBUTE_CODE attributeCode,
        d.ATTRIBUTE_VALUE attributeValue,
        a.duty_code dutyCode,
        b.PLAN_CODE planCode,
        c.policy_no policyNo
        from clms_duty_attribute d, clms_policy_duty a, clms_policy_plan b, clms_policy_info c
        where  b.ID_AHCS_POLICY_INFO=c.ID_AHCS_POLICY_INFO and a.ID_AHCS_POLICY_PLAN=b.ID_AHCS_POLICY_PLAN
        and d.ID_AHCS_POLICY_DUTY=a.ID_AHCS_POLICY_DUTY
        and d.ATTRIBUTE_CODE='615'
        and c.report_no=#{reportNo}
    </select>
    <!--ATTRIBUTE_CODE：'614'表示 年度赔付天数  -->
    <select id="getDutyAttributeYearlyPayDay" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.settle.DutyAttributeValueDTO">
        select d.ATTRIBUTE_CODE attributeCode,
        d.ATTRIBUTE_VALUE attributeValue,
        a.duty_code dutyCode,
        b.PLAN_CODE planCode,
        c.policy_no policyNo
        from clms_duty_attribute d, clms_policy_duty a, clms_policy_plan b, clms_policy_info c
        where  b.ID_AHCS_POLICY_INFO=c.ID_AHCS_POLICY_INFO and a.ID_AHCS_POLICY_PLAN=b.ID_AHCS_POLICY_PLAN
        and d.ID_AHCS_POLICY_DUTY=a.ID_AHCS_POLICY_DUTY
        and d.ATTRIBUTE_CODE='614'
        and c.report_no=#{reportNo}
    </select>
    <!--查询保单下所有责任的属性 -->
    <select id="getPolicyDutyAttributes" resultType="com.paic.ncbs.claim.model.dto.settle.factor.PolicyDutyAttributeDTO">
        select a.ID_AHCS_POLICY_DUTY idAhcsPolicyDuty, a.ATTRIBUTE_CODE attributeCode,a.ATTRIBUTE_VALUE attributeValue
        ,a.ID_AHCS_DUTY_ATTRIBUTE idAhcsDutyAttribute
        from clms_duty_attribute a,clms_policy_duty b,clms_policy_plan c,clms_policy_info d
        where a.ID_AHCS_POLICY_DUTY=b.ID_AHCS_POLICY_DUTY
        and b.ID_AHCS_POLICY_PLAN=c.ID_AHCS_POLICY_PLAN
        and c.ID_AHCS_POLICY_INFO=d.ID_AHCS_POLICY_INFO
        and d.REPORT_NO=#{reportNo}
        and d.policy_no=#{policyNo}
    </select>
    <select id="getDutyAttributeRemiamount" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.settle.DutyAttributeValueDTO">
        select d.ATTRIBUTE_CODE attributeCode,
        d.ATTRIBUTE_VALUE attributeValue,
        a.duty_code dutyCode,
        b.PLAN_CODE planCode,
        c.policy_no policyNo
        from clms_duty_attribute d, clms_policy_duty a, clms_policy_plan b, clms_policy_info c
        where  b.ID_AHCS_POLICY_INFO=c.ID_AHCS_POLICY_INFO and a.ID_AHCS_POLICY_PLAN=b.ID_AHCS_POLICY_PLAN
        and d.ID_AHCS_POLICY_DUTY=a.ID_AHCS_POLICY_DUTY
        and d.ATTRIBUTE_CODE in('271','363')
        and c.report_no=#{reportNo}
    </select>
    <select id="getDeductibleToDuty" resultType="java.math.BigDecimal">
        select ifnull(sum(cda.ATTRIBUTE_VALUE),0) from clms_policy_info cpi
                inner join clms_policy_plan_data cppd on cppd.ID_AHCS_POLICY_INFO = cpi.ID_AHCS_POLICY_INFO
                inner join clms_policy_duty_data cpdd on cpdd.ID_AHCS_POLICY_PLAN  = cppd.ID_AHCS_POLICY_PLAN
                inner join CLMS_DUTY_ATTRIBUTE cda on cda.ID_AHCS_POLICY_DUTY  = cpdd.ID_AHCS_POLICY_DUTY
                where report_no=#{reportNo} and cda.ATTRIBUTE_CODE ='271' and cpdd.DUTY_CODE =#{dutyCode};
    </select>
</mapper>