<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyHolderMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity">
        <id column="ID_AHCS_POLICY_HOLDER" property="idAhcsPolicyHolder" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_AHCS_POLICY_INFO" property="idAhcsPolicyInfo" jdbcType="VARCHAR"/>
        <result column="NAME" property="name" jdbcType="VARCHAR"/>
        <result column="ADDRESS" property="address" jdbcType="VARCHAR"/>
        <result column="TELEPHONE" property="telephone" jdbcType="VARCHAR"/>
        <result column="PERSONNEL_TYPE" property="personnelType" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_TYPE" property="certificateType" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_NO" property="certificateNo" jdbcType="VARCHAR"/>
        <result column="CLIENT_NO" property="clientNo" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_POLICY_HOLDER, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        NAME, ADDRESS, TELEPHONE, CLIENT_NO, PERSONNEL_TYPE, CERTIFICATE_TYPE, CERTIFICATE_NO
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_HOLDER
        where ID_AHCS_POLICY_HOLDER = #{idAhcsPolicyHolder,jdbcType=VARCHAR}
    </select>
    <select id="getInfoByPolicyId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_HOLDER
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>
    <select id="getInfoByReportNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_HOLDER a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR})
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_POLICY_HOLDER
        where ID_AHCS_POLICY_HOLDER = #{idAhcsPolicyHolder,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByIdAhcsPolicyInfo" parameterType="java.lang.String">
        delete from CLMS_POLICY_HOLDER
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity">
        insert into CLMS_POLICY_HOLDER (ID_AHCS_POLICY_HOLDER, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        NAME, ADDRESS, TELEPHONE, PERSONNEL_TYPE, EMAIL, CERTIFICATE_TYPE, CERTIFICATE_NO, CLIENT_NO
        )
        values (#{idAhcsPolicyHolder,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{idAhcsPolicyInfo,jdbcType=VARCHAR},
        #{name,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR},
        #{personnelType,jdbcType=VARCHAR}
        , #{email,jdbcType=VARCHAR}, #{certificateType,jdbcType=VARCHAR}, #{certificateNo,jdbcType=VARCHAR},
        #{clientNo,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_POLICY_HOLDER (ID_AHCS_POLICY_HOLDER, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        NAME, ADDRESS, TELEPHONE, PERSONNEL_TYPE, EMAIL, CERTIFICATE_TYPE, CERTIFICATE_NO, CLIENT_NO
        )
        <foreach collection="holderEntities" item="holder" index="index" separator="union all">
            select #{holder.idAhcsPolicyHolder,jdbcType=VARCHAR}, #{holder.createdBy,jdbcType=VARCHAR},
            #{holder.createdDate,jdbcType=TIMESTAMP},
            #{holder.updatedBy,jdbcType=VARCHAR}, #{holder.updatedDate,jdbcType=TIMESTAMP},
            #{holder.idAhcsPolicyInfo,jdbcType=VARCHAR},
            #{holder.name,jdbcType=VARCHAR}, #{holder.address,jdbcType=VARCHAR}, #{holder.telephone,jdbcType=VARCHAR},
            #{holder.personnelType,jdbcType=VARCHAR}
            , #{holder.email,jdbcType=VARCHAR}, #{holder.certificateType,jdbcType=VARCHAR},
            #{holder.certificateNo,jdbcType=VARCHAR}, #{holder.clientNo,jdbcType=VARCHAR}
        </foreach>

    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity">
        insert into CLMS_POLICY_HOLDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsPolicyHolder != null">
                ID_AHCS_POLICY_HOLDER,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO,
            </if>
            <if test="name != null">
                NAME,
            </if>
            <if test="address != null">
                ADDRESS,
            </if>
            <if test="telephone != null">
                TELEPHONE,
            </if>
            <if test="personnelType != null">
                PERSONNEL_TYPE,
            </if>
            <if test="email != null">
                EMAIL,
            </if>
            <if test="certificateType != null">
                CERTIFICATE_TYPE,
            </if>
            <if test="certificateNo != null">
                CERTIFICATE_NO,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsPolicyHolder != null">
                #{idAhcsPolicyHolder,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyInfo != null">
                #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="personnelType != null">
                #{personnelType,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="certificateType != null">
                #{certificateType,jdbcType=VARCHAR},
            </if>
            <if test="certificateNo != null">
                #{certificateNo,jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null">
                #{clientNo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity">
        update CLMS_POLICY_HOLDER
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                ADDRESS = #{address,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                TELEPHONE = #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="personnelType != null">
                PERSONNEL_TYPE = #{personnelType,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                EMAIL = #{email,jdbcType=VARCHAR},
            </if>
            <if test="certificateType != null">
                CERTIFICATE_TYPE = #{certificateType,jdbcType=VARCHAR},
            </if>
            <if test="certificateNo != null">
                CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_AHCS_POLICY_HOLDER = #{idAhcsPolicyHolder,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity">
        update CLMS_POLICY_HOLDER
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR},
        NAME = #{name,jdbcType=VARCHAR},
        ADDRESS = #{address,jdbcType=VARCHAR},
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
        PERSONNEL_TYPE = #{personnelType,jdbcType=VARCHAR},
        EMAIL = #{email,jdbcType=VARCHAR},
        CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR}
        where ID_AHCS_POLICY_HOLDER = #{idAhcsPolicyHolder,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete
        from CLMS_POLICY_HOLDER a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR})
    </delete>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_HOLDER
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into CLMS_POLICY_HOLDER (ID_AHCS_POLICY_HOLDER, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        NAME, ADDRESS, TELEPHONE, PERSONNEL_TYPE, EMAIL, CERTIFICATE_TYPE, CERTIFICATE_NO, CLIENT_NO
        )
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsPolicyHolder,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.idAhcsPolicyInfo,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR}, #{item.telephone,jdbcType=VARCHAR},
            #{item.personnelType,jdbcType=VARCHAR} , 
            #{item.email,jdbcType=VARCHAR}, #{item.certificateType,jdbcType=VARCHAR},
            #{item.certificateNo,jdbcType=VARCHAR}, #{item.clientNo,jdbcType=VARCHAR})
        </foreach>

    </insert>
    
</mapper>