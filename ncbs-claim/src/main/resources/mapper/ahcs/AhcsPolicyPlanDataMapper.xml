<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyPlanDataMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyPlanDataEntity">
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_AHCS_POLICY_PLAN_DATA" property="idAhcsPolicyPlanData" jdbcType="VARCHAR"/>
        <result column="ID_AHCS_POLICY_PLAN" property="idAhcsPolicyPlan" jdbcType="VARCHAR"/>
        <result column="ID_AHCS_POLICY_INFO" property="idAhcsPolicyInfo" jdbcType="VARCHAR"/>
        <result column="PLAN_NAME" property="planName" jdbcType="VARCHAR"/>
        <result column="PLAN_CODE" property="planCode" jdbcType="VARCHAR"/>
        <result column="RESCUE_COMPANY" property="rescueCompany" jdbcType="VARCHAR"/>
        <result column="IS_MAIN" property="isMain" jdbcType="VARCHAR"/>
        <result column="GROUP_CODE" property="groupCode" jdbcType="VARCHAR"/>
        <result column="ORG_PLAN_CODE" property="orgPlanCode" jdbcType="VARCHAR"/>
        <result column="ORG_PLAN_NAME" property="orgPlanName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_POLICY_PLAN_DATA, ID_AHCS_POLICY_PLAN, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_AHCS_POLICY_INFO,
        PLAN_NAME, PLAN_CODE, RESCUE_COMPANY, IS_MAIN ,GROUP_CODE, ORG_PLAN_CODE, ORG_PLAN_NAME
    </sql>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyPlanDataEntity">
        insert into CLMS_POLICY_PLAN_DATA (CREATED_BY, CREATED_DATE, UPDATED_BY,
        UPDATED_DATE, ID_AHCS_POLICY_PLAN_DATA,ID_AHCS_POLICY_PLAN, ID_AHCS_POLICY_INFO,
        PLAN_NAME, PLAN_CODE, RESCUE_COMPANY,
        IS_MAIN, GROUP_CODE, ORG_PLAN_CODE,
        ORG_PLAN_NAME)
        values (#{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR},
        #{updatedDate,jdbcType=TIMESTAMP}, #{idAhcsPolicyPlanData,jdbcType=VARCHAR},
        #{idAhcsPolicyPlan,jdbcType=VARCHAR}, #{idAhcsPolicyInfo,jdbcType=VARCHAR},
        #{planName,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{rescueCompany,jdbcType=VARCHAR},
        #{isMain,jdbcType=VARCHAR}, #{groupCode,jdbcType=VARCHAR}, #{orgPlanCode,jdbcType=VARCHAR},
        #{orgPlanName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyPlanDataEntity">
        insert into CLMS_POLICY_PLAN_DATA
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idAhcsPolicyPlanData != null">
                ID_AHCS_POLICY_PLAN_DATA,
            </if>
            <if test="idAhcsPolicyPlan != null">
                ID_AHCS_POLICY_PLAN,
            </if>
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO,
            </if>
            <if test="planName != null">
                PLAN_NAME,
            </if>
            <if test="planCode != null">
                PLAN_CODE,
            </if>
            <if test="rescueCompany != null">
                RESCUE_COMPANY,
            </if>
            <if test="isMain != null">
                IS_MAIN,
            </if>
            <if test="groupCode != null">
                GROUP_CODE,
            </if>
            <if test="orgPlanCode != null">
                ORG_PLAN_CODE,
            </if>
            <if test="orgPlanName != null">
                ORG_PLAN_NAME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyPlanData != null">
                #{idAhcsPolicyPlanData,jdbcType=VARCHAR},
            </if>
            <if test="idAhcsPolicyPlan != null">
                #{idAhcsPolicyPlan,jdbcType=VARCHAR},
            </if>
            <if test="idAhcsPolicyInfo != null">
                #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                #{planName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="rescueCompany != null">
                #{rescueCompany,jdbcType=VARCHAR},
            </if>
            <if test="isMain != null">
                #{isMain,jdbcType=VARCHAR},
            </if>
            <if test="groupCode != null">
                #{groupCode,jdbcType=VARCHAR},
            </if>
            <if test="orgPlanCode != null">
                #{orgPlanCode,jdbcType=VARCHAR},
            </if>
            <if test="orgPlanName != null">
                #{orgPlanName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertList" parameterType="java.util.List">
        insert into /*+append_values */ CLMS_POLICY_PLAN_DATA (ID_AHCS_POLICY_PLAN_DATA, ID_AHCS_POLICY_PLAN,
        CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        PLAN_NAME, PLAN_CODE, RESCUE_COMPANY,
        IS_MAIN, GROUP_CODE, ORG_PLAN_CODE, ORG_PLAN_NAME)
        <foreach collection="policyPlanDataEntities" item="policyPlan" index="index" separator="union all">
            select #{policyPlan.idAhcsPolicyPlanData,jdbcType=VARCHAR},#{policyPlan.idAhcsPolicyPlan,jdbcType=VARCHAR},
            #{policyPlan.createdBy,jdbcType=VARCHAR}, #{policyPlan.createdDate,jdbcType=TIMESTAMP},
            #{policyPlan.updatedBy,jdbcType=VARCHAR}, #{policyPlan.updatedDate,jdbcType=TIMESTAMP},
            #{policyPlan.idAhcsPolicyInfo,jdbcType=VARCHAR},
            #{policyPlan.planName,jdbcType=VARCHAR}, #{policyPlan.planCode,jdbcType=VARCHAR},
            #{policyPlan.rescueCompany,jdbcType=VARCHAR},
            #{policyPlan.isMain,jdbcType=VARCHAR}, #{policyPlan.groupCode,jdbcType=VARCHAR},
            #{policyPlan.orgPlanCode,jdbcType=VARCHAR}, #{policyPlan.orgPlanName,jdbcType=VARCHAR}
        </foreach>
    </insert>
    <!--批量保存小条款 -->
    <insert id="insertTermList" parameterType="java.util.List">
        insert into clms_plan_term(id,report_no,case_times,risk_group_id,sort_no,term_code,term_name,term_content,created_by,created_date,updated_by,updated_date)
        <foreach collection="planTermContentEntities" item="term" index="index" separator="union all">
            select #{term.id,jdbcType=VARCHAR},#{term.reportNo,jdbcType=VARCHAR},
            #{term.caseTimes,jdbcType=INTEGER},#{term.riskGroupId,jdbcType=VARCHAR},
            #{term.sortNo,jdbcType=INTEGER}, #{term.termCode,jdbcType=VARCHAR},
            #{term.termName,jdbcType=VARCHAR}, #{term.termContent,jdbcType=VARCHAR},
            #{term.createdBy,jdbcType=VARCHAR}, #{term.createdDate,jdbcType=TIMESTAMP},
            #{term.updatedBy,jdbcType=VARCHAR}, #{term.updatedDate,jdbcType=TIMESTAMP}
        </foreach>
    </insert>
    <select id="selectPolicyInfo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_PLAN_DATA a
        where
        a.plan_code = #{planCode,jdbcType=VARCHAR}
        and a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no = #{policyNo,jdbcType=VARCHAR}
        <if test="selfCardNo !=null and selfCardNo != ''">
            and b.self_card_no = #{selfCardNo,jdbcType=VARCHAR}
        </if>
        )
    </select>
    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete from CLMS_POLICY_PLAN_DATA a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR})
    </delete>
    <resultMap id="resultTermMap" type="com.paic.ncbs.claim.dao.entity.ahcs.PlanTermContentEntity">
        <result column="risk_group_id" property="riskGroupId"/>
        <result column="term_code" property="termCode"/>
        <result column="term_name" property="termName"/>
        <result column="term_content" property="termContent"/>
    </resultMap>
    <!--根据报案号 赔付次数查询小条款信息 小条款查询不要带赔付次数作为条件，重开案件不会重新抄单所以不能带 -->
    <select id="getPlanTermContentInfo" resultMap="resultTermMap">
        select risk_group_id,term_code,term_name,term_content from clms_plan_term cpt
        where report_no=#{reportNo}
    </select>
</mapper>