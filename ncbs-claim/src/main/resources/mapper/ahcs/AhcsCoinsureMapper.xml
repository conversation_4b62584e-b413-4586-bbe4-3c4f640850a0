<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsCoinsureMapper">

    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsCoinsureEntity">
        <id column="ID_AHCS_COINSURE" property="idAhcsCoinsure" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_AHCS_POLICY_INFO" property="idAhcsPolicyInfo" jdbcType="VARCHAR"/>
        <result column="COINSURANCE_TYPE" property="coinsuranceType" jdbcType="VARCHAR"/>
        <result column="ACCEPT_INSURANCE_FLAG" property="acceptInsuranceFlag" jdbcType="VARCHAR"/>
        <result column="REINSURE_COMPANY_CODE" property="reinsureCompanyCode" jdbcType="VARCHAR"/>
        <result column="REINSURE_COMPANY_NAME" property="reinsureCompanyName" jdbcType="VARCHAR"/>
        <result column="REINSURE_SCALE" property="reinsureScale" jdbcType="DECIMAL"/>
        <result column="INSURED_AMOUNT" property="insuredAmount" jdbcType="DECIMAL"/>
        <result column="PREMIUM" property="premium" jdbcType="DECIMAL"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_COINSURE, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        COINSURANCE_TYPE, ACCEPT_INSURANCE_FLAG, REINSURE_COMPANY_CODE, REINSURE_COMPANY_NAME,
        REINSURE_SCALE, INSURED_AMOUNT, PREMIUM
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_COINSURE
        where ID_AHCS_COINSURE = #{idAhcsCoinsure,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_COINSURE
        where ID_AHCS_COINSURE = #{idAhcsCoinsure,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByIdAhcsPolicyInfo" parameterType="java.lang.String">
        delete from CLMS_COINSURE
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsCoinsureEntity">
        insert into CLMS_COINSURE (ID_AHCS_COINSURE, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        COINSURANCE_TYPE, ACCEPT_INSURANCE_FLAG, REINSURE_COMPANY_CODE,
        REINSURE_COMPANY_NAME, REINSURE_SCALE, INSURED_AMOUNT,
        PREMIUM)
        values (#{idAhcsCoinsure,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{idAhcsPolicyInfo,jdbcType=VARCHAR},
        #{coinsuranceType,jdbcType=VARCHAR}, #{acceptInsuranceFlag,jdbcType=VARCHAR},
        #{reinsureCompanyCode,jdbcType=VARCHAR},
        #{reinsureCompanyName,jdbcType=VARCHAR}, #{reinsureScale,jdbcType=DECIMAL}, #{insuredAmount,jdbcType=DECIMAL},
        #{premium,jdbcType=DECIMAL})
    </insert>
    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_COINSURE (ID_AHCS_COINSURE, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        COINSURANCE_TYPE, ACCEPT_INSURANCE_FLAG, REINSURE_COMPANY_CODE,
        REINSURE_COMPANY_NAME, REINSURE_SCALE, INSURED_AMOUNT,
        PREMIUM)
        <foreach collection="coinsureEntities" item="coinsure" index="index" separator="union all">
            select
            #{coinsure.idAhcsCoinsure,jdbcType=VARCHAR}, #{coinsure.createdBy,jdbcType=VARCHAR},
            #{coinsure.createdDate,jdbcType=TIMESTAMP},
            #{coinsure.updatedBy,jdbcType=VARCHAR}, #{coinsure.updatedDate,jdbcType=TIMESTAMP},
            #{coinsure.idAhcsPolicyInfo,jdbcType=VARCHAR},
            #{coinsure.coinsuranceType,jdbcType=VARCHAR}, #{coinsure.acceptInsuranceFlag,jdbcType=VARCHAR},
            #{coinsure.reinsureCompanyCode,jdbcType=VARCHAR},
            #{coinsure.reinsureCompanyName,jdbcType=VARCHAR}, #{coinsure.reinsureScale,jdbcType=DECIMAL},
            #{coinsure.insuredAmount,jdbcType=DECIMAL},
            #{coinsure.premium,jdbcType=DECIMAL}
        </foreach>
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsCoinsureEntity">
        insert into CLMS_COINSURE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsCoinsure != null">
                ID_AHCS_COINSURE,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO,
            </if>
            <if test="coinsuranceType != null">
                COINSURANCE_TYPE,
            </if>
            <if test="acceptInsuranceFlag != null">
                ACCEPT_INSURANCE_FLAG,
            </if>
            <if test="reinsureCompanyCode != null">
                REINSURE_COMPANY_CODE,
            </if>
            <if test="reinsureCompanyName != null">
                REINSURE_COMPANY_NAME,
            </if>
            <if test="reinsureScale != null">
                REINSURE_SCALE,
            </if>
            <if test="insuredAmount != null">
                INSURED_AMOUNT,
            </if>
            <if test="premium != null">
                PREMIUM,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsCoinsure != null">
                #{idAhcsCoinsure,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyInfo != null">
                #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="coinsuranceType != null">
                #{coinsuranceType,jdbcType=VARCHAR},
            </if>
            <if test="acceptInsuranceFlag != null">
                #{acceptInsuranceFlag,jdbcType=VARCHAR},
            </if>
            <if test="reinsureCompanyCode != null">
                #{reinsureCompanyCode,jdbcType=VARCHAR},
            </if>
            <if test="reinsureCompanyName != null">
                #{reinsureCompanyName,jdbcType=VARCHAR},
            </if>
            <if test="reinsureScale != null">
                #{reinsureScale,jdbcType=DECIMAL},
            </if>
            <if test="insuredAmount != null">
                #{insuredAmount,jdbcType=DECIMAL},
            </if>
            <if test="premium != null">
                #{premium,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsCoinsureEntity">
        update CLMS_COINSURE
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="coinsuranceType != null">
                COINSURANCE_TYPE = #{coinsuranceType,jdbcType=VARCHAR},
            </if>
            <if test="acceptInsuranceFlag != null">
                ACCEPT_INSURANCE_FLAG = #{acceptInsuranceFlag,jdbcType=VARCHAR},
            </if>
            <if test="reinsureCompanyCode != null">
                REINSURE_COMPANY_CODE = #{reinsureCompanyCode,jdbcType=VARCHAR},
            </if>
            <if test="reinsureCompanyName != null">
                REINSURE_COMPANY_NAME = #{reinsureCompanyName,jdbcType=VARCHAR},
            </if>
            <if test="reinsureScale != null">
                REINSURE_SCALE = #{reinsureScale,jdbcType=DECIMAL},
            </if>
            <if test="insuredAmount != null">
                INSURED_AMOUNT = #{insuredAmount,jdbcType=DECIMAL},
            </if>
            <if test="premium != null">
                PREMIUM = #{premium,jdbcType=DECIMAL},
            </if>
        </set>
        where ID_AHCS_COINSURE = #{idAhcsCoinsure,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsCoinsureEntity">
        update CLMS_COINSURE
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR},
        COINSURANCE_TYPE = #{coinsuranceType,jdbcType=VARCHAR},
        ACCEPT_INSURANCE_FLAG = #{acceptInsuranceFlag,jdbcType=VARCHAR},
        REINSURE_COMPANY_CODE = #{reinsureCompanyCode,jdbcType=VARCHAR},
        REINSURE_COMPANY_NAME = #{reinsureCompanyName,jdbcType=VARCHAR},
        REINSURE_SCALE = #{reinsureScale,jdbcType=DECIMAL},
        INSURED_AMOUNT = #{insuredAmount,jdbcType=DECIMAL},
        PREMIUM = #{premium,jdbcType=DECIMAL}
        where ID_AHCS_COINSURE = #{idAhcsCoinsure,jdbcType=VARCHAR}
    </update>
    <select id="selectByIdAhcsPolicyInfo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_COINSURE
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete
        from CLMS_COINSURE a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR})
    </delete>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsCoinsureEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_COINSURE
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into CLMS_COINSURE (ID_AHCS_COINSURE, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        COINSURANCE_TYPE, ACCEPT_INSURANCE_FLAG, REINSURE_COMPANY_CODE,
        REINSURE_COMPANY_NAME, REINSURE_SCALE, INSURED_AMOUNT,
        PREMIUM)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsCoinsure,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.idAhcsPolicyInfo,jdbcType=VARCHAR},
            #{item.coinsuranceType,jdbcType=VARCHAR}, #{item.acceptInsuranceFlag,jdbcType=VARCHAR},
            #{item.reinsureCompanyCode,jdbcType=VARCHAR},
            #{item.reinsureCompanyName,jdbcType=VARCHAR}, #{item.reinsureScale,jdbcType=DECIMAL},
            #{item.insuredAmount,jdbcType=DECIMAL},
            #{item.premium,jdbcType=DECIMAL})
        </foreach>
    </insert>
    
</mapper>