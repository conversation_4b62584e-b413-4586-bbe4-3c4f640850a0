<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportAccidentTravelMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportAccidentTravelEntity">
        <id column="ID_AHCS_REPORT_ACCIDENT_TRAVEL" property="idAhcsReportAccidentTravel" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="CHANGE_REASON" property="changeReason" jdbcType="VARCHAR"/>
        <result column="CHANGE_TYPE" property="changeType" jdbcType="VARCHAR"/>
        <result column="COST_ESTIMATE" property="costEstimate" jdbcType="DECIMAL"/>
        <result column="TRAVEL_EXTEND" property="travelExtend" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_REPORT_ACCIDENT_TRAVEL, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, CHANGE_REASON, CHANGE_TYPE, COST_ESTIMATE,TRAVEL_EXTEND
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_TRAVEL
        where ID_AHCS_REPORT_ACCIDENT_TRAVEL = #{idAhcsReportAccidentTravel,jdbcType=VARCHAR}
    </select>

    <select id="getReportAccidentTravelByReportNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_TRAVEL
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_REPORT_ACCIDENT_TRAVEL
        where ID_AHCS_REPORT_ACCIDENT_TRAVEL = #{idAhcsReportAccidentTravel,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentTravelEntity">
        insert into CLMS_REPORT_ACCIDENT_TRAVEL (ID_AHCS_REPORT_ACCIDENT_TRAVEL, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, CHANGE_REASON, CHANGE_TYPE,
        COST_ESTIMATE,TRAVEL_EXTEND)
        values (#{idAhcsReportAccidentTravel,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{reportNo,jdbcType=VARCHAR}, #{changeReason,jdbcType=VARCHAR}, #{changeType,jdbcType=VARCHAR},
        #{costEstimate,jdbcType=DECIMAL},#{travelExtend,jdbcType=DECIMAL})
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentTravelEntity">
        insert into CLMS_REPORT_ACCIDENT_TRAVEL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentTravel != null">
                ID_AHCS_REPORT_ACCIDENT_TRAVEL,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="reportNo != null">
                REPORT_NO,
            </if>
            <if test="changeReason != null">
                CHANGE_REASON,
            </if>
            <if test="changeType != null">
                CHANGE_TYPE,
            </if>
            <if test="costEstimate != null">
                COST_ESTIMATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentTravel != null">
                #{idAhcsReportAccidentTravel,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="changeReason != null">
                #{changeReason,jdbcType=VARCHAR},
            </if>
            <if test="changeType != null">
                #{changeType,jdbcType=VARCHAR},
            </if>
            <if test="costEstimate != null">
                #{costEstimate,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentTravelEntity">
        update CLMS_REPORT_ACCIDENT_TRAVEL
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="changeReason != null">
                CHANGE_REASON = #{changeReason,jdbcType=VARCHAR},
            </if>
            <if test="changeType != null">
                CHANGE_TYPE = #{changeType,jdbcType=VARCHAR},
            </if>
            <if test="costEstimate != null">
                COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL},
            </if>
        </set>
        where ID_AHCS_REPORT_ACCIDENT_TRAVEL = #{idAhcsReportAccidentTravel,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentTravelEntity">
        update CLMS_REPORT_ACCIDENT_TRAVEL
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        CHANGE_REASON = #{changeReason,jdbcType=VARCHAR},
        CHANGE_TYPE = #{changeType,jdbcType=VARCHAR},
        COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL}
        where ID_AHCS_REPORT_ACCIDENT_TRAVEL = #{idAhcsReportAccidentTravel,jdbcType=VARCHAR}
    </update>
</mapper>