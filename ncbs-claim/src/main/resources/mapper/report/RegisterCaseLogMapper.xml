<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
		    
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.RegisterCaseLogMapper">

	<insert id="addRegisterCaseLog" parameterType="com.paic.ncbs.claim.model.dto.report.RegisterCaseLogDTO">
		INSERT INTO CLMS_REGISTER_CASE_LOG(
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_REGISTER_CASE_LOG,
			REPORT_NO,
			CASE_TIMES,
			IS_REGISTER,
			REPORT_DATE,
			REGISTER_DATE,
			WAIT_REGISTER_DATE,
			IS_DEAL,
			FAIL_REASON_CODE,
			ARCHIVE_TIME
		)VALUES(
			'SYSTEM',
			NOW(),
			'SYST<PERSON>',
			NOW(),
			#{idRegisterCaseLog, jdbcType=VARCHAR},
			#{reportNo, jdbcType=VARCHAR},
			#{caseTimes, jdbcType=INTEGER},
			#{isRegister, jdbcType=VARCHAR},
			#{reportDate, jdbcType=TIMESTAMP},
			#{registerDate, jdbcType=TIMESTAMP},
			#{waitRegisterDate, jdbcType=TIMESTAMP},
			#{isDeal, jdbcType=VARCHAR},
			#{failReasonCode, jdbcType=VARCHAR},
			NOW()
		)
	</insert>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO CLMS_REGISTER_CASE_LOG (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_REGISTER_CASE_LOG,
			REPORT_NO,
			CASE_TIMES,
			IS_REGISTER,
			REPORT_DATE,
			REGISTER_DATE,
			WAIT_REGISTER_DATE,
			IS_DEAL,
			FAIL_REASON_CODE,
			ARCHIVE_TIME
		)
		SELECT
			#{userId},
			NOW(),
			#{userId},
			NOW(),
			MD5(UUID()),
			REPORT_NO,
			#{reopenCaseTimes},
			IS_REGISTER,
			REPORT_DATE,
			REGISTER_DATE,
			WAIT_REGISTER_DATE,
			IS_DEAL,
			FAIL_REASON_CODE,
			NOW()
		FROM CLMS_REGISTER_CASE_LOG
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
	</insert>
</mapper>