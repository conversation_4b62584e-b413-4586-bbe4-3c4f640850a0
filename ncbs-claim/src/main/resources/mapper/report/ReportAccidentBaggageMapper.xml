<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportAccidentBaggageMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportAccidentBaggageEntity">
        <id column="ID_AHCS_REPORT_ACCIDENT_BAG" property="idAhcsReportAccidentBag" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="FLIGHT_NO" property="flightNo" jdbcType="VARCHAR"/>
        <result column="OVERSEAS_OCCUR" property="overseasOccur" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_PLACE" property="departurePlace" jdbcType="VARCHAR"/>
        <result column="DESTINATION" property="destination" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_PROVINCE" property="departureProvince" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_CITY" property="departureCity" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_AIRPORT" property="departureAirport" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_AREA" property="departureArea" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_NATION" property="departureNation" jdbcType="VARCHAR"/>
        <result column="DESTINATION_PROVINCE" property="destinationProvince" jdbcType="VARCHAR"/>
        <result column="DESTINATION_CITY" property="destinationCity" jdbcType="VARCHAR"/>
        <result column="DESTINATION_AIRPORT" property="destinationAirport" jdbcType="VARCHAR"/>
        <result column="DESTINATION_AREA" property="destinationArea" jdbcType="VARCHAR"/>
        <result column="DESTINATION_NATION" property="destinationNation" jdbcType="VARCHAR"/>
        <result column="PLAN_ARRIVAL_DATE" property="planArrivalDate" jdbcType="TIMESTAMP"/>
        <result column="SIGN_DATE" property="signDate" jdbcType="TIMESTAMP"/>
        <result column="BAGGAGE_DELAY_TYPE" property="baggageDelayType" jdbcType="VARCHAR"/>
        <result column="COST_ESTIMATE" property="costEstimate" jdbcType="DECIMAL"/>
        <result column="DELAY_TIME" property="delayTime" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_REPORT_ACCIDENT_BAG, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, FLIGHT_NO, OVERSEAS_OCCUR, DEPARTURE_PLACE, DESTINATION, DEPARTURE_PROVINCE,
        DEPARTURE_CITY, DEPARTURE_AIRPORT, DEPARTURE_AREA, DEPARTURE_NATION, DESTINATION_PROVINCE,
        DESTINATION_CITY, DESTINATION_AIRPORT, DESTINATION_AREA, DESTINATION_NATION, PLAN_ARRIVAL_DATE,
        SIGN_DATE, BAGGAGE_DELAY_TYPE, COST_ESTIMATE, DELAY_TIME
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_BAGGAGE
        where ID_AHCS_REPORT_ACCIDENT_BAG = #{idAhcsReportAccidentBag,jdbcType=VARCHAR}
    </select>

    <select id="getReportAccidentBaggageByReportNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_BAGGAGE
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_REPORT_ACCIDENT_BAGGAGE
        where ID_AHCS_REPORT_ACCIDENT_BAG = #{idAhcsReportAccidentBag,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentBaggageEntity">
        insert into CLMS_REPORT_ACCIDENT_BAGGAGE (ID_AHCS_REPORT_ACCIDENT_BAG, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, FLIGHT_NO, OVERSEAS_OCCUR,
        DEPARTURE_PLACE, DESTINATION, DEPARTURE_PROVINCE,
        DEPARTURE_CITY, DEPARTURE_AIRPORT, DEPARTURE_AREA,
        DEPARTURE_NATION, DESTINATION_PROVINCE, DESTINATION_CITY,
        DESTINATION_AIRPORT, DESTINATION_AREA, DESTINATION_NATION,
        PLAN_ARRIVAL_DATE, SIGN_DATE, BAGGAGE_DELAY_TYPE,
        COST_ESTIMATE, DELAY_TIME)
        values (#{idAhcsReportAccidentBag,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{reportNo,jdbcType=VARCHAR}, #{flightNo,jdbcType=VARCHAR}, #{overseasOccur,jdbcType=VARCHAR},
        #{departurePlace,jdbcType=VARCHAR}, #{destination,jdbcType=VARCHAR}, #{departureProvince,jdbcType=VARCHAR},
        #{departureCity,jdbcType=VARCHAR}, #{departureAirport,jdbcType=VARCHAR}, #{departureArea,jdbcType=VARCHAR},
        #{departureNation,jdbcType=VARCHAR}, #{destinationProvince,jdbcType=VARCHAR},
        #{destinationCity,jdbcType=VARCHAR},
        #{destinationAirport,jdbcType=VARCHAR}, #{destinationArea,jdbcType=VARCHAR},
        #{destinationNation,jdbcType=VARCHAR},
        #{planArrivalDate,jdbcType=TIMESTAMP}, #{signDate,jdbcType=TIMESTAMP}, #{baggageDelayType,jdbcType=VARCHAR},
        #{costEstimate,jdbcType=DECIMAL}, #{delayTime,jdbcType=DECIMAL})
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentBaggageEntity">
        insert into CLMS_REPORT_ACCIDENT_BAGGAGE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentBag != null">
                ID_AHCS_REPORT_ACCIDENT_BAG,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="reportNo != null">
                REPORT_NO,
            </if>
            <if test="flightNo != null">
                FLIGHT_NO,
            </if>
            <if test="overseasOccur != null">
                OVERSEAS_OCCUR,
            </if>
            <if test="departurePlace != null">
                DEPARTURE_PLACE,
            </if>
            <if test="destination != null">
                DESTINATION,
            </if>
            <if test="departureProvince != null">
                DEPARTURE_PROVINCE,
            </if>
            <if test="departureCity != null">
                DEPARTURE_CITY,
            </if>
            <if test="departureAirport != null">
                DEPARTURE_AIRPORT,
            </if>
            <if test="departureArea != null">
                DEPARTURE_AREA,
            </if>
            <if test="departureNation != null">
                DEPARTURE_NATION,
            </if>
            <if test="destinationProvince != null">
                DESTINATION_PROVINCE,
            </if>
            <if test="destinationCity != null">
                DESTINATION_CITY,
            </if>
            <if test="destinationAirport != null">
                DESTINATION_AIRPORT,
            </if>
            <if test="destinationArea != null">
                DESTINATION_AREA,
            </if>
            <if test="destinationNation != null">
                DESTINATION_NATION,
            </if>
            <if test="planArrivalDate != null">
                PLAN_ARRIVAL_DATE,
            </if>
            <if test="signDate != null">
                SIGN_DATE,
            </if>
            <if test="baggageDelayType != null">
                BAGGAGE_DELAY_TYPE,
            </if>
            <if test="costEstimate != null">
                COST_ESTIMATE,
            </if>
            <if test="delayTime != null">
                DELAY_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentBag != null">
                #{idAhcsReportAccidentBag,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="flightNo != null">
                #{flightNo,jdbcType=VARCHAR},
            </if>
            <if test="overseasOccur != null">
                #{overseasOccur,jdbcType=VARCHAR},
            </if>
            <if test="departurePlace != null">
                #{departurePlace,jdbcType=VARCHAR},
            </if>
            <if test="destination != null">
                #{destination,jdbcType=VARCHAR},
            </if>
            <if test="departureProvince != null">
                #{departureProvince,jdbcType=VARCHAR},
            </if>
            <if test="departureCity != null">
                #{departureCity,jdbcType=VARCHAR},
            </if>
            <if test="departureAirport != null">
                #{departureAirport,jdbcType=VARCHAR},
            </if>
            <if test="departureArea != null">
                #{departureArea,jdbcType=VARCHAR},
            </if>
            <if test="departureNation != null">
                #{departureNation,jdbcType=VARCHAR},
            </if>
            <if test="destinationProvince != null">
                #{destinationProvince,jdbcType=VARCHAR},
            </if>
            <if test="destinationCity != null">
                #{destinationCity,jdbcType=VARCHAR},
            </if>
            <if test="destinationAirport != null">
                #{destinationAirport,jdbcType=VARCHAR},
            </if>
            <if test="destinationArea != null">
                #{destinationArea,jdbcType=VARCHAR},
            </if>
            <if test="destinationNation != null">
                #{destinationNation,jdbcType=VARCHAR},
            </if>
            <if test="planArrivalDate != null">
                #{planArrivalDate,jdbcType=TIMESTAMP},
            </if>
            <if test="signDate != null">
                #{signDate,jdbcType=TIMESTAMP},
            </if>
            <if test="baggageDelayType != null">
                #{baggageDelayType,jdbcType=VARCHAR},
            </if>
            <if test="costEstimate != null">
                #{costEstimate,jdbcType=DECIMAL},
            </if>
            <if test="delayTime != null">
                #{delayTime,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentBaggageEntity">
        update CLMS_REPORT_ACCIDENT_BAGGAGE
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="flightNo != null">
                FLIGHT_NO = #{flightNo,jdbcType=VARCHAR},
            </if>
            <if test="overseasOccur != null">
                OVERSEAS_OCCUR = #{overseasOccur,jdbcType=VARCHAR},
            </if>
            <if test="departurePlace != null">
                DEPARTURE_PLACE = #{departurePlace,jdbcType=VARCHAR},
            </if>
            <if test="destination != null">
                DESTINATION = #{destination,jdbcType=VARCHAR},
            </if>
            <if test="departureProvince != null">
                DEPARTURE_PROVINCE = #{departureProvince,jdbcType=VARCHAR},
            </if>
            <if test="departureCity != null">
                DEPARTURE_CITY = #{departureCity,jdbcType=VARCHAR},
            </if>
            <if test="departureAirport != null">
                DEPARTURE_AIRPORT = #{departureAirport,jdbcType=VARCHAR},
            </if>
            <if test="departureArea != null">
                DEPARTURE_AREA = #{departureArea,jdbcType=VARCHAR},
            </if>
            <if test="departureNation != null">
                DEPARTURE_NATION = #{departureNation,jdbcType=VARCHAR},
            </if>
            <if test="destinationProvince != null">
                DESTINATION_PROVINCE = #{destinationProvince,jdbcType=VARCHAR},
            </if>
            <if test="destinationCity != null">
                DESTINATION_CITY = #{destinationCity,jdbcType=VARCHAR},
            </if>
            <if test="destinationAirport != null">
                DESTINATION_AIRPORT = #{destinationAirport,jdbcType=VARCHAR},
            </if>
            <if test="destinationArea != null">
                DESTINATION_AREA = #{destinationArea,jdbcType=VARCHAR},
            </if>
            <if test="destinationNation != null">
                DESTINATION_NATION = #{destinationNation,jdbcType=VARCHAR},
            </if>
            <if test="planArrivalDate != null">
                PLAN_ARRIVAL_DATE = #{planArrivalDate,jdbcType=TIMESTAMP},
            </if>
            <if test="signDate != null">
                SIGN_DATE = #{signDate,jdbcType=TIMESTAMP},
            </if>
            <if test="baggageDelayType != null">
                BAGGAGE_DELAY_TYPE = #{baggageDelayType,jdbcType=VARCHAR},
            </if>
            <if test="costEstimate != null">
                COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL},
            </if>
            <if test="delayTime != null">
                DELAY_TIME = #{delayTime,jdbcType=DECIMAL},
            </if>
        </set>
        where ID_AHCS_REPORT_ACCIDENT_BAG = #{idAhcsReportAccidentBag,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentBaggageEntity">
        update CLMS_REPORT_ACCIDENT_BAGGAGE
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        FLIGHT_NO = #{flightNo,jdbcType=VARCHAR},
        OVERSEAS_OCCUR = #{overseasOccur,jdbcType=VARCHAR},
        DEPARTURE_PLACE = #{departurePlace,jdbcType=VARCHAR},
        DESTINATION = #{destination,jdbcType=VARCHAR},
        DEPARTURE_PROVINCE = #{departureProvince,jdbcType=VARCHAR},
        DEPARTURE_CITY = #{departureCity,jdbcType=VARCHAR},
        DEPARTURE_AIRPORT = #{departureAirport,jdbcType=VARCHAR},
        DEPARTURE_AREA = #{departureArea,jdbcType=VARCHAR},
        DEPARTURE_NATION = #{departureNation,jdbcType=VARCHAR},
        DESTINATION_PROVINCE = #{destinationProvince,jdbcType=VARCHAR},
        DESTINATION_CITY = #{destinationCity,jdbcType=VARCHAR},
        DESTINATION_AIRPORT = #{destinationAirport,jdbcType=VARCHAR},
        DESTINATION_AREA = #{destinationArea,jdbcType=VARCHAR},
        DESTINATION_NATION = #{destinationNation,jdbcType=VARCHAR},
        PLAN_ARRIVAL_DATE = #{planArrivalDate,jdbcType=TIMESTAMP},
        SIGN_DATE = #{signDate,jdbcType=TIMESTAMP},
        BAGGAGE_DELAY_TYPE = #{baggageDelayType,jdbcType=VARCHAR},
        COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL},
        DELAY_TIME = #{delayTime,jdbcType=DECIMAL}
        where ID_AHCS_REPORT_ACCIDENT_BAG = #{idAhcsReportAccidentBag,jdbcType=VARCHAR}
    </update>

</mapper>