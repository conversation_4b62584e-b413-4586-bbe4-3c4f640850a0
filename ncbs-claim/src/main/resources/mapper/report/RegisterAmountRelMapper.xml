<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.RegisterAmountRelMapper">

    <insert id="addRegisterAmountRel">
        INSERT INTO CLMS_REGISTER_AMOUNT_REL(
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_REGISTER_AMOUNT_REL,
        ID_REGISTER_CASE_APPLY,
        ID_ESTIMATE_DUTY_RECORD,
        ARCHIVE_TIME
        )VALUES
        <foreach collection="list" item="registerAmountRelDTO" index="i" separator=",">
            (
            #{registerAmountRelDTO.createdBy, jdbcType = VARCHAR},
            NOW(),
            #{registerAmountRelDTO.updatedBy, jdbcType = VARCHAR},
            NOW(),
            #{registerAmountRelDTO.idAhcsRegisterAmountRel, jdbcType=VARCHAR},
            #{registerAmountRelDTO.idRegisterCaseApply, jdbcType=VARCHAR},
            #{registerAmountRelDTO.idEstimateDutyRecord, jdbcType=VARCHAR},
            NOW()
            )
        </foreach>

    </insert>

</mapper>