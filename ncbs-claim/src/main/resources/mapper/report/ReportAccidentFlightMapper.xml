<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportAccidentFlightMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportAccidentFlightEntity">
        <id column="ID_AHCS_REPORT_ACCIDENT_FLIGHT" property="idAhcsReportAccidentFlight" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_OVERSEAS_OCCUR" property="departureOverseasOccur" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_PLACE" property="departurePlace" jdbcType="VARCHAR"/>
        <result column="DESTINATION_OVERSEAS_OCCUR" property="destinationOverseasOccur" jdbcType="VARCHAR"/>
        <result column="DESTINATION" property="destination" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_PROVINCE" property="departureProvince" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_CITY" property="departureCity" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_AIRPORT" property="departureAirport" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_AREA" property="departureArea" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_NATION" property="departureNation" jdbcType="VARCHAR"/>
        <result column="DESTINATION_PROVINCE" property="destinationProvince" jdbcType="VARCHAR"/>
        <result column="DESTINATION_CITY" property="destinationCity" jdbcType="VARCHAR"/>
        <result column="DESTINATION_AIRPORT" property="destinationAirport" jdbcType="VARCHAR"/>
        <result column="DESTINATION_AREA" property="destinationArea" jdbcType="VARCHAR"/>
        <result column="DESTINATION_NATION" property="destinationNation" jdbcType="VARCHAR"/>
        <result column="IS_FLIGHT_CANCELLATION" property="isFlightCancellation" jdbcType="VARCHAR"/>
        <result column="IS_FLIGHT_LAND" property="isFlightLand" jdbcType="VARCHAR"/>
        <result column="LANDING_AIRPORT" property="landingAirport" jdbcType="VARCHAR"/>
        <result column="REPLACE_FLIGHT" property="replaceFlight" jdbcType="VARCHAR"/>
        <result column="TAKEOFF_DATE" property="takeoffDate" jdbcType="TIMESTAMP"/>
        <result column="ELE_TICKET_NO" property="eleTicketNo" jdbcType="VARCHAR"/>
        <result column="FLIGHT_NO" property="flightNo" jdbcType="VARCHAR"/>
        <result column="IS_JOURNEY_CANCEL" property="isJourneyCancel" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_REASON" property="accidentReason" jdbcType="VARCHAR"/>
        <result column="DELAY_TIME" property="delayTime" jdbcType="DECIMAL"/>
        <result column="IS_FLIGHT_DELAY" property="isFlightDelay" jdbcType="VARCHAR"/>
        <result column="DELAY_TIME_USER" property="delayTimeUser" jdbcType="DECIMAL"/>
        <result column="TICKET_STATUS" property="ticketStatus" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_REPORT_ACCIDENT_FLIGHT, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, DEPARTURE_PLACE, DESTINATION, DEPARTURE_PROVINCE, DEPARTURE_CITY,
        DEPARTURE_AIRPORT, DEPARTURE_AREA, DEPARTURE_NATION, DESTINATION_PROVINCE, DESTINATION_CITY,
        DESTINATION_AIRPORT, DESTINATION_AREA, DESTINATION_NATION, IS_FLIGHT_CANCELLATION,
        IS_FLIGHT_LAND, LANDING_AIRPORT, REPLACE_FLIGHT, TAKEOFF_DATE, ELE_TICKET_NO, FLIGHT_NO,
        IS_JOURNEY_CANCEL, DEPARTURE_OVERSEAS_OCCUR, DESTINATION_OVERSEAS_OCCUR, ACCIDENT_REASON, DELAY_TIME,
        IS_FLIGHT_DELAY, DELAY_TIME_USER, TICKET_STATUS
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_FLIGHT
        where ID_AHCS_REPORT_ACCIDENT_FLIGHT = #{idAhcsReportAccidentFlight,jdbcType=VARCHAR}
    </select>

    <select id="getReportAccidentFlightByReportNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_FLIGHT
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_REPORT_ACCIDENT_FLIGHT
        where ID_AHCS_REPORT_ACCIDENT_FLIGHT = #{idAhcsReportAccidentFlight,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentFlightEntity">
        insert into CLMS_REPORT_ACCIDENT_FLIGHT (ID_AHCS_REPORT_ACCIDENT_FLIGHT, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, DEPARTURE_PLACE,
        DESTINATION, DEPARTURE_PROVINCE, DEPARTURE_CITY,
        DEPARTURE_AIRPORT, DEPARTURE_AREA, DEPARTURE_NATION,
        DESTINATION_PROVINCE, DESTINATION_CITY, DESTINATION_AIRPORT,
        DESTINATION_AREA, DESTINATION_NATION, IS_FLIGHT_CANCELLATION,
        IS_FLIGHT_LAND, LANDING_AIRPORT, REPLACE_FLIGHT,
        TAKEOFF_DATE, ELE_TICKET_NO, FLIGHT_NO,
        IS_JOURNEY_CANCEL, DEPARTURE_OVERSEAS_OCCUR,
        DESTINATION_OVERSEAS_OCCUR, ACCIDENT_REASON, DELAY_TIME, IS_FLIGHT_DELAY, DELAY_TIME_USER, TICKET_STATUS)
        values (#{idAhcsReportAccidentFlight,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{reportNo,jdbcType=VARCHAR}, #{departurePlace,jdbcType=VARCHAR},
        #{destination,jdbcType=VARCHAR}, #{departureProvince,jdbcType=VARCHAR}, #{departureCity,jdbcType=VARCHAR},
        #{departureAirport,jdbcType=VARCHAR}, #{departureArea,jdbcType=VARCHAR}, #{departureNation,jdbcType=VARCHAR},
        #{destinationProvince,jdbcType=VARCHAR}, #{destinationCity,jdbcType=VARCHAR},
        #{destinationAirport,jdbcType=VARCHAR},
        #{destinationArea,jdbcType=VARCHAR}, #{destinationNation,jdbcType=VARCHAR},
        #{isFlightCancellation,jdbcType=VARCHAR},
        #{isFlightLand,jdbcType=VARCHAR}, #{landingAirport,jdbcType=VARCHAR}, #{replaceFlight,jdbcType=VARCHAR},
        #{takeoffDate,jdbcType=TIMESTAMP}, #{eleTicketNo,jdbcType=VARCHAR}, #{flightNo,jdbcType=VARCHAR},
        #{isJourneyCancel,jdbcType=VARCHAR}, #{departureOverseasOccur,jdbcType=VARCHAR},
        #{destinationOverseasOccur,jdbcType=VARCHAR},
        #{accidentReason,jdbcType=VARCHAR}, #{delayTime,jdbcType=DECIMAL}, #{isFlightDelay,jdbcType=VARCHAR},
        #{delayTimeUser,jdbcType=DECIMAL}, #{ticketStatus,jdbcType=DECIMAL})
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentFlightEntity">
        insert into CLMS_REPORT_ACCIDENT_FLIGHT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentFlight != null">
                ID_AHCS_REPORT_ACCIDENT_FLIGHT,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="reportNo != null">
                REPORT_NO,
            </if>
            <if test="departurePlace != null">
                DEPARTURE_PLACE,
            </if>
            <if test="destination != null">
                DESTINATION,
            </if>
            <if test="departureProvince != null">
                DEPARTURE_PROVINCE,
            </if>
            <if test="departureCity != null">
                DEPARTURE_CITY,
            </if>
            <if test="departureAirport != null">
                DEPARTURE_AIRPORT,
            </if>
            <if test="departureArea != null">
                DEPARTURE_AREA,
            </if>
            <if test="departureNation != null">
                DEPARTURE_NATION,
            </if>
            <if test="destinationProvince != null">
                DESTINATION_PROVINCE,
            </if>
            <if test="destinationCity != null">
                DESTINATION_CITY,
            </if>
            <if test="destinationAirport != null">
                DESTINATION_AIRPORT,
            </if>
            <if test="destinationArea != null">
                DESTINATION_AREA,
            </if>
            <if test="destinationNation != null">
                DESTINATION_NATION,
            </if>
            <if test="isFlightCancellation != null">
                IS_FLIGHT_CANCELLATION,
            </if>
            <if test="isFlightLand != null">
                IS_FLIGHT_LAND,
            </if>
            <if test="landingAirport != null">
                LANDING_AIRPORT,
            </if>
            <if test="replaceFlight != null">
                REPLACE_FLIGHT,
            </if>
            <if test="takeoffDate != null">
                TAKEOFF_DATE,
            </if>
            <if test="eleTicketNo != null">
                ELE_TICKET_NO,
            </if>
            <if test="flightNo != null">
                FLIGHT_NO,
            </if>
            <if test="isJourneyCancel != null">
                IS_JOURNEY_CANCEL,
            </if>
            <if test="departureOverseasOccur != null">
                DEPARTURE_OVERSEAS_OCCUR,
            </if>
            <if test="destinationOverseasOccur != null">
                DESTINATION_OVERSEAS_OCCUR,
            </if>
            <if test="accidentReason != null">
                ACCIDENT_REASON,
            </if>
            <if test="isFlightDelay != null">
                IS_FLIGHT_DELAY,
            </if>
            <if test="delayTimeUser != null">
                DElAY_TIME_USER,
            </if>
            <if test="ticketStatus != null">
                TICKET_STATUS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentFlight != null">
                #{idAhcsReportAccidentFlight,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="departurePlace != null">
                #{departurePlace,jdbcType=VARCHAR},
            </if>
            <if test="destination != null">
                #{destination,jdbcType=VARCHAR},
            </if>
            <if test="departureProvince != null">
                #{departureProvince,jdbcType=VARCHAR},
            </if>
            <if test="departureCity != null">
                #{departureCity,jdbcType=VARCHAR},
            </if>
            <if test="departureAirport != null">
                #{departureAirport,jdbcType=VARCHAR},
            </if>
            <if test="departureArea != null">
                #{departureArea,jdbcType=VARCHAR},
            </if>
            <if test="departureNation != null">
                #{departureNation,jdbcType=VARCHAR},
            </if>
            <if test="destinationProvince != null">
                #{destinationProvince,jdbcType=VARCHAR},
            </if>
            <if test="destinationCity != null">
                #{destinationCity,jdbcType=VARCHAR},
            </if>
            <if test="destinationAirport != null">
                #{destinationAirport,jdbcType=VARCHAR},
            </if>
            <if test="destinationArea != null">
                #{destinationArea,jdbcType=VARCHAR},
            </if>
            <if test="destinationNation != null">
                #{destinationNation,jdbcType=VARCHAR},
            </if>
            <if test="isFlightCancellation != null">
                #{isFlightCancellation,jdbcType=VARCHAR},
            </if>
            <if test="isFlightLand != null">
                #{isFlightLand,jdbcType=VARCHAR},
            </if>
            <if test="landingAirport != null">
                #{landingAirport,jdbcType=VARCHAR},
            </if>
            <if test="replaceFlight != null">
                #{replaceFlight,jdbcType=VARCHAR},
            </if>
            <if test="takeoffDate != null">
                #{takeoffDate,jdbcType=TIMESTAMP},
            </if>
            <if test="eleTicketNo != null">
                #{eleTicketNo,jdbcType=VARCHAR},
            </if>
            <if test="flightNo != null">
                #{flightNo,jdbcType=VARCHAR},
            </if>
            <if test="isJourneyCancel != null">
                #{isJourneyCancel,jdbcType=VARCHAR},
            </if>
            <if test="departureOverseasOccur != null">
                #{departureOverseasOccur,jdbcType=VARCHAR},
            </if>
            <if test="destinationOverseasOccur != null">
                #{destinationOverseasOccur,jdbcType=VARCHAR},
            </if>
            <if test="accidentReason != null">
                #{accidentReason,jdbcType=VARCHAR},
            </if>
            <if test="delayTime != null">
                #{delayTime,jdbcType=DECIMAL},
            </if>
            <if test="isFlightDelay != null">
                #{isFlightDelay,jdbcType=VARCHAR},
            </if>
            <if test="delayTimeUser != null">
                #{delayTimeUser,jdbcType=DECIMAL},
            </if>
            <if test="ticketStatus != null">
                #{ticketStatus,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentFlightEntity">
        update CLMS_REPORT_ACCIDENT_FLIGHT
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="departurePlace != null">
                DEPARTURE_PLACE = #{departurePlace,jdbcType=VARCHAR},
            </if>
            <if test="destination != null">
                DESTINATION = #{destination,jdbcType=VARCHAR},
            </if>
            <if test="departureProvince != null">
                DEPARTURE_PROVINCE = #{departureProvince,jdbcType=VARCHAR},
            </if>
            <if test="departureCity != null">
                DEPARTURE_CITY = #{departureCity,jdbcType=VARCHAR},
            </if>
            <if test="departureAirport != null">
                DEPARTURE_AIRPORT = #{departureAirport,jdbcType=VARCHAR},
            </if>
            <if test="departureArea != null">
                DEPARTURE_AREA = #{departureArea,jdbcType=VARCHAR},
            </if>
            <if test="departureNation != null">
                DEPARTURE_NATION = #{departureNation,jdbcType=VARCHAR},
            </if>
            <if test="destinationProvince != null">
                DESTINATION_PROVINCE = #{destinationProvince,jdbcType=VARCHAR},
            </if>
            <if test="destinationCity != null">
                DESTINATION_CITY = #{destinationCity,jdbcType=VARCHAR},
            </if>
            <if test="destinationAirport != null">
                DESTINATION_AIRPORT = #{destinationAirport,jdbcType=VARCHAR},
            </if>
            <if test="destinationArea != null">
                DESTINATION_AREA = #{destinationArea,jdbcType=VARCHAR},
            </if>
            <if test="destinationNation != null">
                DESTINATION_NATION = #{destinationNation,jdbcType=VARCHAR},
            </if>
            <if test="isFlightCancellation != null">
                IS_FLIGHT_CANCELLATION = #{isFlightCancellation,jdbcType=VARCHAR},
            </if>
            <if test="isFlightLand != null">
                IS_FLIGHT_LAND = #{isFlightLand,jdbcType=VARCHAR},
            </if>
            <if test="landingAirport != null">
                LANDING_AIRPORT = #{landingAirport,jdbcType=VARCHAR},
            </if>
            <if test="replaceFlight != null">
                REPLACE_FLIGHT = #{replaceFlight,jdbcType=VARCHAR},
            </if>
            <if test="takeoffDate != null">
                TAKEOFF_DATE = #{takeoffDate,jdbcType=TIMESTAMP},
            </if>
            <if test="eleTicketNo != null">
                ELE_TICKET_NO = #{eleTicketNo,jdbcType=VARCHAR},
            </if>
            <if test="flightNo != null">
                FLIGHT_NO = #{flightNo,jdbcType=VARCHAR},
            </if>
            <if test="isJourneyCancel != null">
                IS_JOURNEY_CANCEL = #{isJourneyCancel,jdbcType=VARCHAR},
            </if>
            <if test="departureOverseasOccur != null">
                DEPARTURE_OVERSEAS_OCCUR = #{departureOverseasOccur,jdbcType=VARCHAR},
            </if>
            <if test="destinationOverseasOccur != null">
                DESTINATION_OVERSEAS_OCCUR = #{destinationOverseasOccur,jdbcType=VARCHAR},
            </if>
            <if test="accidentReason != null">
                ACCIDENT_REASON = #{accidentReason,jdbcType=VARCHAR},
            </if>
            <if test="delayTime != null">
                DELAY_TIME = #{delayTime,jdbcType=DECIMAL},
            </if>
            <if test="isFlightDelay != null">
                IS_FLIGHT_DELAY = #{isFlightDelay,jdbcType=VARCHAR},
            </if>
            <if test="delayTimeUser != null">
                DELAY_TIME_USER = #{delayTimeUser,jdbcType=DECIMAL},
            </if>
            <if test="ticketStatus != null">
                TICKET_STATUS = #{ticketStatus,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_AHCS_REPORT_ACCIDENT_FLIGHT = #{idAhcsReportAccidentFlight,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentFlightEntity">
        update CLMS_REPORT_ACCIDENT_FLIGHT
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        DEPARTURE_PLACE = #{departurePlace,jdbcType=VARCHAR},
        DESTINATION = #{destination,jdbcType=VARCHAR},
        DEPARTURE_PROVINCE = #{departureProvince,jdbcType=VARCHAR},
        DEPARTURE_CITY = #{departureCity,jdbcType=VARCHAR},
        DEPARTURE_AIRPORT = #{departureAirport,jdbcType=VARCHAR},
        DEPARTURE_AREA = #{departureArea,jdbcType=VARCHAR},
        DEPARTURE_NATION = #{departureNation,jdbcType=VARCHAR},
        DESTINATION_PROVINCE = #{destinationProvince,jdbcType=VARCHAR},
        DESTINATION_CITY = #{destinationCity,jdbcType=VARCHAR},
        DESTINATION_AIRPORT = #{destinationAirport,jdbcType=VARCHAR},
        DESTINATION_AREA = #{destinationArea,jdbcType=VARCHAR},
        DESTINATION_NATION = #{destinationNation,jdbcType=VARCHAR},
        IS_FLIGHT_CANCELLATION = #{isFlightCancellation,jdbcType=VARCHAR},
        IS_FLIGHT_LAND = #{isFlightLand,jdbcType=VARCHAR},
        LANDING_AIRPORT = #{landingAirport,jdbcType=VARCHAR},
        REPLACE_FLIGHT = #{replaceFlight,jdbcType=VARCHAR},
        TAKEOFF_DATE = #{takeoffDate,jdbcType=TIMESTAMP},
        ELE_TICKET_NO = #{eleTicketNo,jdbcType=VARCHAR},
        FLIGHT_NO = #{flightNo,jdbcType=VARCHAR},
        IS_JOURNEY_CANCEL = #{isJourneyCancel,jdbcType=VARCHAR},
        DEPARTURE_OVERSEAS_OCCUR = #{departureOverseasOccur,jdbcType=VARCHAR},
        DESTINATION_OVERSEAS_OCCUR = #{destinationOverseasOccur,jdbcType=VARCHAR},
        ACCIDENT_REASON = #{accidentReason,jdbcType=VARCHAR},
        DELAY_TIME = #{delayTime,jdbcType=DECIMAL},
        IS_FLIGHT_DELAY = #{isFlightDelay,jdbcType=VARCHAR},
        DELAY_TIME_USER = #{delayTimeUser,jdbcType=DECIMAL},
        TICKET_STATUS = #{ticketStatus,jdbcType=VARCHAR}
        where ID_AHCS_REPORT_ACCIDENT_FLIGHT = #{idAhcsReportAccidentFlight,jdbcType=VARCHAR}
    </update>

</mapper>