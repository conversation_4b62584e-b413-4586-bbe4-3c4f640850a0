<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportAccidentTrafficMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportAccidentTrafficEntity">
        <id column="ID_AHCS_REPORT_ACCIDENT_TRA" property="idAhcsReportAccidentTra" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="DEPARTURE_PLACE" property="departurePlace" jdbcType="VARCHAR"/>
        <result column="DESTINATION" property="destination" jdbcType="VARCHAR"/>
        <result column="IS_TRAFFIC_DELAY" property="isTrafficDelay" jdbcType="VARCHAR"/>
        <result column="CHANGED_PORT" property="changedPort" jdbcType="VARCHAR"/>
        <result column="STEAMER_DELAY_CASE" property="steamerDelayCase" jdbcType="VARCHAR"/>
        <result column="ORIGINAL_DEPARTURE_DATE" property="originalDepartureDate" jdbcType="TIMESTAMP"/>
        <result column="ACTUAL_DEPARTURE_DATE" property="actualDepartureDate" jdbcType="TIMESTAMP"/>
        <result column="ORIGINAL_ARRIVAL_DATE" property="originalArrivalDate" jdbcType="TIMESTAMP"/>
        <result column="ACTUAL_ARRIVAL_DATE" property="actualArrivalDate" jdbcType="TIMESTAMP"/>
        <result column="COST_ESTIMATE" property="costEstimate" jdbcType="DECIMAL"/>
        <result column="TRANSPORTATION" property="transportation" jdbcType="VARCHAR"/>
        <result column="DELAY_TIME" property="delayTime" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_REPORT_ACCIDENT_TRA, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, DEPARTURE_PLACE, DESTINATION, IS_TRAFFIC_DELAY, CHANGED_PORT, STEAMER_DELAY_CASE,
        ORIGINAL_DEPARTURE_DATE, ACTUAL_DEPARTURE_DATE, ORIGINAL_ARRIVAL_DATE, ACTUAL_ARRIVAL_DATE,
        COST_ESTIMATE, TRANSPORTATION, DELAY_TIME
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_TRAFFIC
        where ID_AHCS_REPORT_ACCIDENT_TRA = #{idAhcsReportAccidentTra,jdbcType=VARCHAR}
    </select>

    <select id="getReportAccidentTrafficByReportNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_TRAFFIC
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_REPORT_ACCIDENT_TRAFFIC
        where ID_AHCS_REPORT_ACCIDENT_TRA = #{idAhcsReportAccidentTra,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentTrafficEntity">
        insert into CLMS_REPORT_ACCIDENT_TRAFFIC (ID_AHCS_REPORT_ACCIDENT_TRA, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, DEPARTURE_PLACE, DESTINATION,
        IS_TRAFFIC_DELAY, CHANGED_PORT, STEAMER_DELAY_CASE,
        ORIGINAL_DEPARTURE_DATE, ACTUAL_DEPARTURE_DATE,
        ORIGINAL_ARRIVAL_DATE, ACTUAL_ARRIVAL_DATE,
        COST_ESTIMATE, TRANSPORTATION, DELAY_TIME)
        values (#{idAhcsReportAccidentTra,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{reportNo,jdbcType=VARCHAR}, #{departurePlace,jdbcType=VARCHAR}, #{destination,jdbcType=VARCHAR},
        #{isTrafficDelay,jdbcType=VARCHAR}, #{changedPort,jdbcType=VARCHAR}, #{steamerDelayCase,jdbcType=VARCHAR},
        #{originalDepartureDate,jdbcType=TIMESTAMP}, #{actualDepartureDate,jdbcType=TIMESTAMP},
        #{originalArrivalDate,jdbcType=TIMESTAMP}, #{actualArrivalDate,jdbcType=TIMESTAMP},
        #{costEstimate,jdbcType=DECIMAL}, #{transportation,jdbcType=VARCHAR}, #{delayTime,jdbcType=DECIMAL})
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentTrafficEntity">
        insert into CLMS_REPORT_ACCIDENT_TRAFFIC
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentTra != null">
                ID_AHCS_REPORT_ACCIDENT_TRA,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="reportNo != null">
                REPORT_NO,
            </if>
            <if test="departurePlace != null">
                DEPARTURE_PLACE,
            </if>
            <if test="destination != null">
                DESTINATION,
            </if>
            <if test="isTrafficDelay != null">
                IS_TRAFFIC_DELAY,
            </if>
            <if test="changedPort != null">
                CHANGED_PORT,
            </if>
            <if test="steamerDelayCase != null">
                STEAMER_DELAY_CASE,
            </if>
            <if test="originalDepartureDate != null">
                ORIGINAL_DEPARTURE_DATE,
            </if>
            <if test="actualDepartureDate != null">
                ACTUAL_DEPARTURE_DATE,
            </if>
            <if test="originalArrivalDate != null">
                ORIGINAL_ARRIVAL_DATE,
            </if>
            <if test="actualArrivalDate != null">
                ACTUAL_ARRIVAL_DATE,
            </if>
            <if test="costEstimate != null">
                COST_ESTIMATE,
            </if>
            <if test="transportation != null">
                TRANSPORTATION,
            </if>
            <if test="delayTime != null">
                DELAY_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentTra != null">
                #{idAhcsReportAccidentTra,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="departurePlace != null">
                #{departurePlace,jdbcType=VARCHAR},
            </if>
            <if test="destination != null">
                #{destination,jdbcType=VARCHAR},
            </if>
            <if test="isTrafficDelay != null">
                #{isTrafficDelay,jdbcType=VARCHAR},
            </if>
            <if test="changedPort != null">
                #{changedPort,jdbcType=VARCHAR},
            </if>
            <if test="steamerDelayCase != null">
                #{steamerDelayCase,jdbcType=VARCHAR},
            </if>
            <if test="originalDepartureDate != null">
                #{originalDepartureDate,jdbcType=TIMESTAMP},
            </if>
            <if test="actualDepartureDate != null">
                #{actualDepartureDate,jdbcType=TIMESTAMP},
            </if>
            <if test="originalArrivalDate != null">
                #{originalArrivalDate,jdbcType=TIMESTAMP},
            </if>
            <if test="actualArrivalDate != null">
                #{actualArrivalDate,jdbcType=TIMESTAMP},
            </if>
            <if test="costEstimate != null">
                #{costEstimate,jdbcType=DECIMAL},
            </if>
            <if test="transportation != null">
                #{transportation,jdbcType=VARCHAR},
            </if>
            <if test="delayTime != null">
                #{delayTime,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentTrafficEntity">
        update CLMS_REPORT_ACCIDENT_TRAFFIC
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="departurePlace != null">
                DEPARTURE_PLACE = #{departurePlace,jdbcType=VARCHAR},
            </if>
            <if test="destination != null">
                DESTINATION = #{destination,jdbcType=VARCHAR},
            </if>
            <if test="isTrafficDelay != null">
                IS_TRAFFIC_DELAY = #{isTrafficDelay,jdbcType=VARCHAR},
            </if>
            <if test="changedPort != null">
                CHANGED_PORT = #{changedPort,jdbcType=VARCHAR},
            </if>
            <if test="steamerDelayCase != null">
                STEAMER_DELAY_CASE = #{steamerDelayCase,jdbcType=VARCHAR},
            </if>
            <if test="originalDepartureDate != null">
                ORIGINAL_DEPARTURE_DATE = #{originalDepartureDate,jdbcType=TIMESTAMP},
            </if>
            <if test="actualDepartureDate != null">
                ACTUAL_DEPARTURE_DATE = #{actualDepartureDate,jdbcType=TIMESTAMP},
            </if>
            <if test="originalArrivalDate != null">
                ORIGINAL_ARRIVAL_DATE = #{originalArrivalDate,jdbcType=TIMESTAMP},
            </if>
            <if test="actualArrivalDate != null">
                ACTUAL_ARRIVAL_DATE = #{actualArrivalDate,jdbcType=TIMESTAMP},
            </if>
            <if test="costEstimate != null">
                COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL},
            </if>
            <if test="transportation != null">
                TRANSPORTATION = #{transportation,jdbcType=VARCHAR},
            </if>
            <if test="delayTime != null">
                DELAY_TIME = #{delayTime,jdbcType=DECIMAL},
            </if>
        </set>
        where ID_AHCS_REPORT_ACCIDENT_TRA = #{idAhcsReportAccidentTra,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentTrafficEntity">
        update CLMS_REPORT_ACCIDENT_TRAFFIC
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        DEPARTURE_PLACE = #{departurePlace,jdbcType=VARCHAR},
        DESTINATION = #{destination,jdbcType=VARCHAR},
        IS_TRAFFIC_DELAY = #{isTrafficDelay,jdbcType=VARCHAR},
        CHANGED_PORT = #{changedPort,jdbcType=VARCHAR},
        STEAMER_DELAY_CASE = #{steamerDelayCase,jdbcType=VARCHAR},
        ORIGINAL_DEPARTURE_DATE = #{originalDepartureDate,jdbcType=TIMESTAMP},
        ACTUAL_DEPARTURE_DATE = #{actualDepartureDate,jdbcType=TIMESTAMP},
        ORIGINAL_ARRIVAL_DATE = #{originalArrivalDate,jdbcType=TIMESTAMP},
        ACTUAL_ARRIVAL_DATE = #{actualArrivalDate,jdbcType=TIMESTAMP},
        COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL},
        TRANSPORTATION = #{transportation,jdbcType=VARCHAR},
        DELAY_TIME = #{delayTime,jdbcType=DECIMAL}
        where ID_AHCS_REPORT_ACCIDENT_TRA = #{idAhcsReportAccidentTra,jdbcType=VARCHAR}
    </update>
</mapper>