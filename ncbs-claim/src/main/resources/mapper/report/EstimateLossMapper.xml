<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.EstimateLossMapper">

	<select id="getAllEstimatLossConfig" resultType="com.paic.ncbs.claim.model.dto.report.EstimateLossDTO">
		select
			dept_code           deptCode,
			insured_apply_type  insuredApplyType,
			config_amount       configAmount
		from clms_estimate_loss
		where is_effective = 'Y'
	</select>

	<update id="setAllEstimatLossConfig" parameterType="java.util.List">
		<foreach collection="estimateLossList" item="item"  open="" separator=";" close="">
			update clms_estimate_loss
			set updated_by = #{item.updatedBy,jdbcType=VARCHAR},
				updated_date = now(),
				config_amount = #{item.configAmount,jdbcType=DECIMAL}
			where insured_apply_type = #{item.insuredApplyType,jdbcType=VARCHAR}
			and   is_effective  = 'Y'
			and   config_amount != #{item.configAmount,jdbcType=DECIMAL}
		</foreach>
	</update>

	<select id="getEstimatLossConfig" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.report.EstimateLossDTO">
		select
			 dept_code           deptCode,
			 insured_apply_type  insuredApplyType,
			 config_amount       configAmount
		from clms_estimate_loss
		where insured_apply_type= #{insuredApplyType,jdbcType=VARCHAR}
		and   is_effective = 'Y'
	</select>

	<select id="sumDutyAmount" parameterType="java.lang.String" resultType="java.math.BigDecimal">
		select
			 ifnull(sum(d.duty_amount),0)
		from clms_policy_info        a,
			 clms_policy_plan        b,
			 clms_policy_duty        c,
			 clms_policy_duty_detail d
		where  a.id_ahcs_policy_info = b.id_ahcs_policy_info
		and    b.id_ahcs_policy_plan = c.id_ahcs_policy_plan
		and    c.id_ahcs_policy_duty = d.id_ahcs_policy_duty
		and    a.report_no = #{reportNo,jdbcType=VARCHAR}
		and    d.duty_detail_type = #{dutyDetailType,jdbcType=VARCHAR}

	</select>

</mapper>