<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.LinkManMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.LinkManEntity">
        <id column="ID_AHCS_LINK_MAN" property="idAhcsLinkMan" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="LINK_MAN_NO" property="linkManNo" jdbcType="DECIMAL"/>
        <result column="CASE_TIMES" property="caseTimes" jdbcType="DECIMAL"/>
        <result column="LINK_MAN_NAME" property="linkManName" jdbcType="VARCHAR"/>
        <result column="LINK_MAN_RELATION" property="linkManRelation" jdbcType="VARCHAR"/>
        <result column="LINK_MAN_TELEPHONE" property="linkManTelephone" jdbcType="VARCHAR"/>
        <result column="SEND_MESSAGE" property="sendMessage" jdbcType="VARCHAR"/>
        <result column="IS_REPORT" property="isReport" jdbcType="VARCHAR"/>
        <result column="APPLICANT_PERSON" property="applicantPerson" jdbcType="VARCHAR"/>
        <result column="APPLICANT_TYPE" property="applicantType" jdbcType="VARCHAR"/>
        <result column="APPLICANT_CERTIFICATE_TYPE" property="certificateType" jdbcType="VARCHAR"/>
        <result column="APPLICANT_CERTIFICATE_NO" property="certificateNo" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_LINK_MAN, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, REPORT_NO,
        LINK_MAN_NO, CASE_TIMES, LINK_MAN_NAME, LINK_MAN_RELATION, LINK_MAN_TELEPHONE, SEND_MESSAGE,
        IS_REPORT,APPLICANT_PERSON,APPLICANT_TYPE,APPLICANT_CERTIFICATE_TYPE,APPLICANT_CERTIFICATE_NO
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_LINK_MAN
        where ID_AHCS_LINK_MAN = #{idAhcsLinkMan}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_LINK_MAN
        where ID_AHCS_LINK_MAN = #{idAhcsLinkMan}
    </delete>

    <select id="getLinkMans" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_LINK_MAN
        where REPORT_NO = #{reportNo}
        <if test="caseTimes != null and caseTimes != '' ">
            and CASE_TIMES = #{caseTimes}
        </if>
        order by created_date desc
    </select>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.LinkManEntity">
        insert into CLMS_LINK_MAN (ID_AHCS_LINK_MAN, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO, CASE_TIMES,
        LINK_MAN_NO, LINK_MAN_NAME, LINK_MAN_RELATION,
        LINK_MAN_TELEPHONE, SEND_MESSAGE,
        IS_REPORT,APPLICANT_PERSON,APPLICANT_TYPE,APPLICANT_CERTIFICATE_TYPE,APPLICANT_CERTIFICATE_NO,ARCHIVE_DATE)
        values (#{idAhcsLinkMan}, #{createdBy}, #{createdDate},
        #{updatedBy}, #{updatedDate},
        #{reportNo}, #{caseTimes},
        #{linkManNo}, #{linkManName}, #{linkManRelation},
        #{linkManTelephone}, #{sendMessage}, #{isReport},
        #{applicantPerson},#{applicantType},#{certificateType},#{certificateNo},now())
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.report.LinkManEntity">
        insert into CLMS_LINK_MAN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsLinkMan != null">
                ID_AHCS_LINK_MAN,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="reportNo != null">
                REPORT_NO,
            </if>
            <if test="linkManNo != null">
                LINK_MAN_NO,
            </if>
            <if test="caseTimes != null">
                CASE_TIMES,
            </if>
            <if test="linkManName != null">
                LINK_MAN_NAME,
            </if>
            <if test="linkManRelation != null">
                LINK_MAN_RELATION,
            </if>
            <if test="linkManTelephone != null">
                LINK_MAN_TELEPHONE,
            </if>
            <if test="sendMessage != null">
                SEND_MESSAGE,
            </if>
            <if test="isReport != null">
                IS_REPORT,
            </if>
            <if test="applicantPerson != null">
                APPLICANT_PERSON,
            </if>
            <if test="applicantType != null">
                APPLICANT_TYPE,
            </if>
            <if test="certificateType != null">
                APPLICANT_CERTIFICATE_TYPE,
            </if>
            <if test="certificateNo != null">
                APPLICANT_CERTIFICATE_NO,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsLinkMan != null">
                #{idAhcsLinkMan},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDate != null">
                #{createdDate},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDate != null">
                #{updatedDate},
            </if>
            <if test="reportNo != null">
                #{reportNo},
            </if>
            <if test="linkManNo != null">
                #{linkManNo},
            </if>
            <if test="caseTimes != null">
                #{CASE_TIMES},
            </if>
            <if test="linkManName != null">
                #{linkManName},
            </if>
            <if test="linkManRelation != null">
                #{linkManRelation},
            </if>
            <if test="linkManTelephone != null">
                #{linkManTelephone},
            </if>
            <if test="sendMessage != null">
                #{sendMessage},
            </if>
            <if test="isReport != null">
                #{isReport},
            </if>
            <if test="applicantPerson != null">
                #{applicantPerson},
            </if>
            <if test="applicantType != null">
                #{applicantType},
            </if>
            <if test="certificateType != null">
                #{certificateType},
            </if>
            <if test="certificateNo != null">
                #{certificateNo},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.LinkManEntity">
        update CLMS_LINK_MAN
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo},
            </if>
            <if test="linkManNo != null">
                LINK_MAN_NO = #{linkManNo},
            </if>
            <if test="caseTimes != null">
                CASE_TIMES = #{caseTimes},
            </if>
            <if test="linkManName != null">
                LINK_MAN_NAME = #{linkManName},
            </if>
            <if test="linkManRelation != null">
                LINK_MAN_RELATION = #{linkManRelation},
            </if>
            <if test="linkManTelephone != null">
                LINK_MAN_TELEPHONE = #{linkManTelephone},
            </if>
            <if test="sendMessage != null">
                SEND_MESSAGE = #{sendMessage},
            </if>
            <if test="isReport != null">
                IS_REPORT = #{isReport},
            </if>
            <if test="applicantPerson != null">
                APPLICANT_PERSON = #{applicantPerson},
            </if>
            <if test="applicantType != null">
                APPLICANT_TYPE = #{applicantType},
            </if>
            <if test="certificateType != null">
                APPLICANT_CERTIFICATE_TYPE = #{certificateType},
            </if>
            <if test="certificateNo != null">
                APPLICANT_CERTIFICATE_NO = #{certificateNo},
            </if>
        </set>
        where ID_AHCS_LINK_MAN = #{idAhcsLinkMan}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.LinkManEntity">
        update CLMS_LINK_MAN
        set CREATED_BY = #{createdBy},
        CREATED_DATE = #{createdDate},
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = #{updatedDate},
        REPORT_NO = #{reportNo},
        LINK_MAN_NO = #{linkManNo},
        CASE_TIMES = #{caseTimes},
        LINK_MAN_NAME = #{linkManName},
        LINK_MAN_RELATION = #{linkManRelation},
        LINK_MAN_TELEPHONE = #{linkManTelephone},
        SEND_MESSAGE = #{sendMessage},
        IS_REPORT = #{isReport},
        APPLICANT_PERSON = #{applicantPerson},
        APPLICANT_TYPE = #{applicantType},
        APPLICANT_CERTIFICATE_TYPE = #{certificateType},
        APPLICANT_CERTIFICATE_NO = #{certificateNo}
        where ID_AHCS_LINK_MAN = #{idAhcsLinkMan}
    </update>

    <update id="updateByReportNo" parameterType="com.paic.ncbs.claim.dao.entity.report.LinkManEntity">
        update CLMS_LINK_MAN
        <set>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="linkManName != null">
                LINK_MAN_NAME = #{linkManName},
            </if>
            <if test="linkManTelephone != null">
                LINK_MAN_TELEPHONE = #{linkManTelephone},
            </if>
            <if test="sendMessage != null">
                SEND_MESSAGE = #{sendMessage},
            </if>
            UPDATED_DATE = now(),
            IS_REPORT = 'N'
        </set>
        where   REPORT_NO = #{reportNo}
        <if test='caseTimes != null and caseTimes != "" '>
            and  CASE_TIMES = #{caseTimes}
        </if>
        <if test='idAhcsLinkMan != null and idAhcsLinkMan != "" '>
            and  ID_AHCS_LINK_MAN = #{idAhcsLinkMan}
        </if>
    </update>

    <select id="isLinkManInfoChanged" parameterType="com.paic.ncbs.claim.dao.entity.report.LinkManEntity" resultType="java.lang.Integer">
        select count(1) from clms_link_man
        where REPORT_NO = #{reportNo}
        and CASE_TIMES = #{caseTimes}
        and LINK_MAN_NAME = #{linkManName}
        and LINK_MAN_TELEPHONE = #{linkManTelephone}
        and SEND_MESSAGE = #{sendMessage}
    </select>

    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_LINK_MAN (ID_AHCS_LINK_MAN, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO, CASE_TIMES,
        LINK_MAN_NO, LINK_MAN_NAME, LINK_MAN_RELATION,
        LINK_MAN_TELEPHONE, SEND_MESSAGE,
        IS_REPORT,APPLICANT_PERSON,APPLICANT_TYPE,APPLICANT_CERTIFICATE_TYPE,APPLICANT_CERTIFICATE_NO,ARCHIVE_DATE)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsLinkMan}, #{item.createdBy}, #{item.createdDate},
            #{item.updatedBy}, #{item.updatedDate},
            #{item.reportNo}, #{item.caseTimes},
            #{item.linkManNo}, #{item.linkManName}, #{item.linkManRelation},
            #{item.linkManTelephone}, #{item.sendMessage}, #{item.isReport},
            #{item.applicantPerson},#{item.applicantType},#{item.certificateType},#{item.certificateNo},now())
        </foreach>
    </insert>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_LINK_MAN (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_LINK_MAN,
            REPORT_NO,
            CASE_TIMES,
            LINK_MAN_NO,
            LINK_MAN_NAME,
            LINK_MAN_RELATION,
            LINK_MAN_TELEPHONE,
            SEND_MESSAGE,
            IS_EFFECTIVE,
            IS_REPORT,
            APPLICANT_PERSON,
            APPLICANT_TYPE,
            APPLICANT_CERTIFICATE_TYPE,
            APPLICANT_CERTIFICATE_NO,
            ARCHIVE_DATE
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            MD5(UUID()),
            REPORT_NO,
            #{reopenCaseTimes},
            LINK_MAN_NO,
            LINK_MAN_NAME,
            LINK_MAN_RELATION,
            LINK_MAN_TELEPHONE,
            SEND_MESSAGE,
            IS_EFFECTIVE,
            IS_REPORT,
            APPLICANT_PERSON,
            APPLICANT_TYPE,
            APPLICANT_CERTIFICATE_TYPE,
            APPLICANT_CERTIFICATE_NO,
            NOW()
        FROM CLMS_LINK_MAN
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
        AND IS_EFFECTIVE='Y'
    </insert>
</mapper>