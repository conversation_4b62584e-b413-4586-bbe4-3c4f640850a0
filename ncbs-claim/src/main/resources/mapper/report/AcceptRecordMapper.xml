<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.AcceptRecordMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.report.AcceptRecordDTO" id="acceptRecord">
			<id property="idAhcsAcceptRecord" column="ID_AHCS_ACCEPT_RECORD" />
			<result property="reportNo" column="REPORT_NO" />
			<result property="caseTimes" column="CASE_TIMES" />
			<result property="receiveVoucherUm" column="RECEIVE_VOUCHER_UM" />
			<result property="isSuffice" column="IS_SUFFICE" />
			<result property="createdBy" column="CREATED_BY" />
			<result property="updatedBy" column="UPDATED_BY" />
			<result property="remark" column="REMARK" />
			<result property="receiveVoucherDate" column="RECEIVE_VOUCHER_DATE" />
			<result property="claimdocRedayDate" column="CLAIMDOC_READY_DATE" />
			<result property="claimantApplyDate" column="CLAIMANT_APPLY_DATE" />
			<result property="disposableToldDate" column="DISPOSABLE_TOLD_DATE" />
			<result property="priorityReason" column="PRIORITY_REASON" />
			<result property="departmentCode" column="department_code" />
	</resultMap>

	<insert id="insertAcceptRecord"   >
	INSERT INTO CLMS_ACCEPT_RECORD (
	CREATED_BY,
	CREATED_DATE,
	UPDATED_BY,
	UPDATED_DATE,
	ID_AHCS_ACCEPT_RECORD,
	REPORT_NO,
	CASE_TIMES,
	IS_SUFFICE,
	REMARK,
	RECEIVE_VOUCHER_UM,
	RECEIVE_VOUCHER_DATE,
	CLAIMDOC_READY_DATE,
	CLAIMANT_APPLY_DATE,
	DISPOSABLE_TOLD_DATE,
	PRIORITY_REASON,
	department_code,
	ARCHIVE_TIME
	)
	VALUES (
	#{createdBy ,jdbcType=VARCHAR},
	SYSDATE(),
	#{updatedBy ,jdbcType=VARCHAR},
	SYSDATE(),
	#{idAhcsAcceptRecord ,jdbcType=VARCHAR},
	#{reportNo ,jdbcType=VARCHAR},
	#{caseTimes ,jdbcType=NUMERIC},
	#{isSuffice ,jdbcType=VARCHAR},
	#{remark ,jdbcType=VARCHAR},
	#{receiveVoucherUm ,jdbcType=VARCHAR},
	SYSDATE(),
	SYSDATE(),
	#{claimantApplyDate ,jdbcType=TIMESTAMP},
	#{disposableToldDate,jdbcType=TIMESTAMP},
	#{priorityReason ,jdbcType=VARCHAR},
	#{departmentCode ,jdbcType=VARCHAR},
	sysdate()
	)
	</insert>

	<select id="getAcceptRecord" resultMap="acceptRecord">
	 select A.ID_AHCS_ACCEPT_RECORD,
       		A.REPORT_NO,
      		A.CASE_TIMES,
       		A.RECEIVE_VOUCHER_UM,
       		A.IS_SUFFICE,
     		(  select C.VALUE_CHINESE_NAME 
     				from CLM_COMMON_PARAMETER c 
     				where A.PRIORITY_REASON = C.VALUE_CODE AND C.COLLECTION_CODE = 'AHCS_PRIORITY_REASO')
     				PRIORITY_REASON,
       		A.REMARK,
      		A.RECEIVE_VOUCHER_DATE,
       		A.CLAIMDOC_READY_DATE,
       		A.CLAIMANT_APPLY_DATE,
      		A.DISPOSABLE_TOLD_DATE,
      		A.department_code
  		from CLMS_ACCEPT_RECORD A
	     WHERE A.REPORT_NO = #{reportNo} 
                    and A.CASE_TIMES = #{caseTimes}
                    and IS_SUFFICE = 'Y'
		</select>

	<select id="getAcceptRecordByReportNo" resultMap="acceptRecord">
		select A.ID_AHCS_ACCEPT_RECORD,
		A.REPORT_NO,
		A.CASE_TIMES,
		A.RECEIVE_VOUCHER_UM,
		A.IS_SUFFICE,
		A.REMARK,
		A.RECEIVE_VOUCHER_DATE,
		A.CLAIMDOC_READY_DATE,
		A.CLAIMANT_APPLY_DATE,
		A.DISPOSABLE_TOLD_DATE
		from CLMS_ACCEPT_RECORD A
		WHERE A.REPORT_NO = #{reportNo}
		and A.CASE_TIMES = #{caseTimes}
	</select>

	<update id="modifyAcceptRecordSuffice" >
		update CLMS_ACCEPT_RECORD t
		<trim prefix="set" suffixOverrides=",">
			t.UPDATED_BY = #{updatedBy ,jdbcType=VARCHAR},
			t.UPDATED_DATE = SYSDATE(),
			<if test="isSuffice != null and isSuffice != '' ">
				t.IS_SUFFICE = #{isSuffice ,jdbcType=VARCHAR},
			</if>
			<if test="remark != null and remark != '' ">
				t.REMARK = #{remark ,jdbcType=VARCHAR},
			</if>
			<if test="receiveVoucherUm != null and receiveVoucherUm != '' ">
				t.RECEIVE_VOUCHER_UM = #{receiveVoucherUm ,jdbcType=VARCHAR},
			</if>
			<if test="priorityReason != null and priorityReason != '' ">
				t.PRIORITY_REASON = #{priorityReason ,jdbcType=VARCHAR},
			</if>
			<if test="receiveVoucherDate != null ">
				t.RECEIVE_VOUCHER_DATE = #{receiveVoucherDate},
			</if>
			<if test="claimdocRedayDate != null ">
				t.CLAIMDOC_READY_DATE = #{claimdocRedayDate },
			</if>
			<if test="claimantApplyDate != null ">
				t.CLAIMANT_APPLY_DATE = #{claimantApplyDate },
			</if>
			<if test="disposableToldDate != null ">
				t.DISPOSABLE_TOLD_DATE = #{disposableToldDate },
			</if>
			<if test="departmentCode != null ">
				t.department_code = #{departmentCode },
			</if>
		</trim>
		where t.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		and t.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
	</update>

	<select id="getAcceptRecordByAsc" resultMap="acceptRecord">
		select
		A.ID_AHCS_ACCEPT_RECORD,
		A.REPORT_NO,
		A.CASE_TIMES,
		A.RECEIVE_VOUCHER_UM,
		A.IS_SUFFICE,
		A.REMARK,
		A.RECEIVE_VOUCHER_DATE,
		A.CLAIMDOC_READY_DATE,
		A.CLAIMANT_APPLY_DATE,
		A.DISPOSABLE_TOLD_DATE
		from CLMS_ACCEPT_RECORD A
		WHERE  A.REPORT_NO=#{reportNo} and  A.CASE_TIMES =#{caseTimes}
		ORDER BY A.RECEIVE_VOUCHER_DATE
		LIMIT 1
	</select>

</mapper>