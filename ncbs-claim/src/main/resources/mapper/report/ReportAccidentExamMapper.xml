<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportAccidentExamMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportAccidentExamEntity">
        <id column="ID_AHCS_REPORT_ACCIDENT_EXAM" property="idAhcsReportAccidentExam" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="NO_PASS_TYPE" property="noPassType" jdbcType="VARCHAR"/>
        <result column="COST_ESTIMATE" property="costEstimate" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_REPORT_ACCIDENT_EXAM, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, NO_PASS_TYPE, COST_ESTIMATE
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_EXAM
        where ID_AHCS_REPORT_ACCIDENT_EXAM = #{idAhcsReportAccidentExam,jdbcType=VARCHAR}
    </select>

    <select id="getReportAccidentExamByReportNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_EXAM
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_REPORT_ACCIDENT_EXAM
        where ID_AHCS_REPORT_ACCIDENT_EXAM = #{idAhcsReportAccidentExam,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentExamEntity">
        insert into CLMS_REPORT_ACCIDENT_EXAM (ID_AHCS_REPORT_ACCIDENT_EXAM, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, NO_PASS_TYPE, COST_ESTIMATE
        )
        values (#{idAhcsReportAccidentExam,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{reportNo,jdbcType=VARCHAR}, #{noPassType,jdbcType=VARCHAR}, #{costEstimate,jdbcType=DECIMAL}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentExamEntity">
        insert into CLMS_REPORT_ACCIDENT_EXAM
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentExam != null">
                ID_AHCS_REPORT_ACCIDENT_EXAM,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="reportNo != null">
                REPORT_NO,
            </if>
            <if test="noPassType != null">
                NO_PASS_TYPE,
            </if>
            <if test="costEstimate != null">
                COST_ESTIMATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentExam != null">
                #{idAhcsReportAccidentExam,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="noPassType != null">
                #{noPassType,jdbcType=VARCHAR},
            </if>
            <if test="costEstimate != null">
                #{costEstimate,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentExamEntity">
        update CLMS_REPORT_ACCIDENT_EXAM
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="noPassType != null">
                NO_PASS_TYPE = #{noPassType,jdbcType=VARCHAR},
            </if>
            <if test="costEstimate != null">
                COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL},
            </if>
        </set>
        where ID_AHCS_REPORT_ACCIDENT_EXAM = #{idAhcsReportAccidentExam,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentExamEntity">
        update CLMS_REPORT_ACCIDENT_EXAM
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        NO_PASS_TYPE = #{noPassType,jdbcType=VARCHAR},
        COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL}
        where ID_AHCS_REPORT_ACCIDENT_EXAM = #{idAhcsReportAccidentExam,jdbcType=VARCHAR}
    </update>

</mapper>