<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportAccidentExMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportAccidentExEntity">
        <id column="ID_AHCS_REPORT_ACCIDENT_EX" property="idAhcsReportAccidentEx" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="INSURED_APPLY_STATUS" property="insuredApplyStatus" jdbcType="VARCHAR"/>
        <result column="INSURED_APPLY_TYPE" property="insuredApplyType" jdbcType="VARCHAR"/>
        <result column="MEDICAL_STATUS" property="medicalStatus" jdbcType="VARCHAR"/>
        <result column="THERAPY_TYPE" property="therapyType" jdbcType="VARCHAR"/>
        <result column="DOCTOR_DIAGNOSIS" property="doctorDiagnosis" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_TYPE" property="accidentType" jdbcType="VARCHAR"/>
        <result column="TRAFFIC_ACCIDENT_TYPE" property="trafficAccidentType" jdbcType="VARCHAR"/>
        <result column="INSURED_IDENTITY" property="insuredIdentity" jdbcType="VARCHAR"/>
        <result column="TRAFFIC_ACCIDENT_NATURE" property="trafficAccidentNature" jdbcType="VARCHAR"/>
        <result column="DIED_DATE" property="diedDate" jdbcType="TIMESTAMP"/>
        <result column="DIED_STATUS" property="diedStatus" jdbcType="VARCHAR"/>
        <result column="DIED_CAUSE" property="diedCause" jdbcType="VARCHAR"/>
        <result column="THIS_CARLICENSE" property="thisCarlicense" jdbcType="VARCHAR"/>
        <result column="HOSPITAL_NAME" property="hospitalName" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_STATUS_DETAILS" property="accidentStatusDetails" jdbcType="VARCHAR"/>
        <result column="BIG_DISEASE_CODE" property="bigDiseaseCode" jdbcType="VARCHAR"/>
        <result column="BIG_DISEASE_NAME" property="bigDiseaseName" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_EXTEND_INFO" property="accidentExtendInfo" jdbcType="VARCHAR"/>
        <result column="HOSPITAL_DAYS" property="hospitalDays" jdbcType="DECIMAL"/>
        <result column="CLOB_ACCIDENT_EXTEND" property="clobAccidentExtend" jdbcType="CLOB"/>

    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_REPORT_ACCIDENT_EX, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, REPORT_NO,
        INSURED_APPLY_STATUS, INSURED_APPLY_TYPE,MEDICAL_STATUS, THERAPY_TYPE, DOCTOR_DIAGNOSIS, ACCIDENT_TYPE,
        TRAFFIC_ACCIDENT_TYPE, INSURED_IDENTITY,
        TRAFFIC_ACCIDENT_NATURE,DIED_DATE,DIED_STATUS,DIED_CAUSE,THIS_CARLICENSE,HOSPITAL_NAME,
        ACCIDENT_STATUS_DETAILS, BIG_DISEASE_CODE, BIG_DISEASE_NAME, ACCIDENT_EXTEND_INFO, HOSPITAL_DAYS,
        CLOB_ACCIDENT_EXTEND
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_EX
        where ID_AHCS_REPORT_ACCIDENT_EX = #{idAhcsReportAccidentEx,jdbcType=VARCHAR}
    </select>

    <select id="getReportAccidentEx" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_EX
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_REPORT_ACCIDENT_EX
        where ID_AHCS_REPORT_ACCIDENT_EX = #{idAhcsReportAccidentEx,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentExEntity">
        insert into CLMS_REPORT_ACCIDENT_EX (ID_AHCS_REPORT_ACCIDENT_EX,
        CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO,
        INSURED_APPLY_STATUS,INSURED_APPLY_TYPE, MEDICAL_STATUS,
        THERAPY_TYPE, DOCTOR_DIAGNOSIS,
        ACCIDENT_TYPE,
        TRAFFIC_ACCIDENT_TYPE, INSURED_IDENTITY,
        TRAFFIC_ACCIDENT_NATURE,
        DIED_DATE,DIED_STATUS,DIED_CAUSE,THIS_CARLICENSE,HOSPITAL_NAME,
        ACCIDENT_STATUS_DETAILS,
        BIG_DISEASE_CODE, BIG_DISEASE_NAME, ACCIDENT_EXTEND_INFO, HOSPITAL_DAYS, CLOB_ACCIDENT_EXTEND,ARCHIVE_DATE
        )
        values
        (#{idAhcsReportAccidentEx,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{reportNo,jdbcType=VARCHAR}, #{insuredApplyStatus,jdbcType=VARCHAR},#{insuredApplyType,jdbcType=VARCHAR},
        #{medicalStatus,jdbcType=VARCHAR},
        #{therapyType,jdbcType=VARCHAR},
        #{doctorDiagnosis,jdbcType=VARCHAR}, #{accidentType,jdbcType=VARCHAR},
        #{trafficAccidentType,jdbcType=VARCHAR},
        #{insuredIdentity,jdbcType=VARCHAR},
        #{trafficAccidentNature,jdbcType=VARCHAR},
        #{diedDate,jdbcType=TIMESTAMP}, #{diedStatus,jdbcType=VARCHAR},
        #{diedCause,jdbcType=VARCHAR}, #{thisCarlicense,jdbcType=VARCHAR},
        #{hospitalName,jdbcType=VARCHAR},
        #{accidentStatusDetails,jdbcType=VARCHAR},
        #{bigDiseaseCode,jdbcType=VARCHAR},
        #{bigDiseaseName,jdbcType=VARCHAR},
        #{accidentExtendInfo,jdbcType=VARCHAR},
        #{hospitalDays,jdbcType=VARCHAR},
        #{clobAccidentExtend,jdbcType=CLOB},
         now()
        )
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentExEntity">
        update CLMS_REPORT_ACCIDENT_EX
        <set>
            <if test="thisCarLicense != null">
                THIS_CARLICENSE = #{thisCarlicense,jdbcType=VARCHAR},
            </if>
            <if test="diedDate != null">
                DIED_DATE = #{diedDate,jdbcType=VARCHAR},
            </if>
            <if test="diedStatus != null">
                DIED_STATUS = #{diedStatus,jdbcType=VARCHAR},
            </if>
            <if test="diedCause != null">
                DIED_CAUSE = #{diedCause,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="insuredApplyStatus != null">
                INSURED_APPLY_STATUS = #{insuredApplyStatus,jdbcType=VARCHAR},
            </if>
            <if test="insuredApplyType != null">
                INSURED_APPLY_TYPE = #{insuredApplyType,jdbcType=VARCHAR},
            </if>
            <if test="medicalStatus != null">
                MEDICAL_STATUS = #{medicalStatus,jdbcType=VARCHAR},
            </if>
            <if test="therapyType != null">
                THERAPY_TYPE = #{therapyType,jdbcType=VARCHAR},
            </if>
            <if test="doctorDiagnosis != null">
                DOCTOR_DIAGNOSIS = #{doctorDiagnosis,jdbcType=VARCHAR},
            </if>
            <if test="accidentType != null">
                ACCIDENT_TYPE = #{accidentType,jdbcType=VARCHAR},
            </if>
            <if test="trafficAccidentType != null">
                TRAFFIC_ACCIDENT_TYPE = #{trafficAccidentType,jdbcType=VARCHAR},
            </if>
            <if test="insuredIdentity != null">
                INSURED_IDENTITY = #{insuredIdentity,jdbcType=VARCHAR},
            </if>
            <if test="trafficAccidentNature != null">
                TRAFFIC_ACCIDENT_NATURE = #{trafficAccidentNature,jdbcType=VARCHAR},
            </if>
            <if test="diedDate != null">
                DIED_DATE = #{diedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="diedStatus != null">
                DIED_STATUS = #{diedStatus,jdbcType=VARCHAR},
            </if>
            <if test="diedCause != null">
                DIED_CAUSE = #{diedCause,jdbcType=VARCHAR},
            </if>
            <if test="thisCarlicense != null">
                THIS_CARLICENSE = #{thisCarlicense,jdbcType=VARCHAR},
            </if>
            <if test="hospitalName != null">
                HOSPITAL_NAME = #{hospitalName,jdbcType=VARCHAR},
            </if>
            <if test="accidentStatusDetails != null">
                ACCIDENT_STATUS_DETAILS = #{accidentStatusDetails,jdbcType=VARCHAR},
            </if>
            <if test="bigDiseaseCode != null">
                BIG_DISEASE_CODE = #{bigDiseaseCode,jdbcType=VARCHAR},
            </if>
            <if test="bigDiseaseName != null">
                BIG_DISEASE_NAME = #{bigDiseaseName,jdbcType=VARCHAR},
            </if>
            <if test="accidentExtendInfo != null">
                ACCIDENT_EXTEND_INFO = #{accidentExtendInfo,jdbcType=VARCHAR},
            </if>
            <if test="hospitalDays != null">
                HOSPITAL_DAYS = #{hospitalDays,jdbcType=DECIMAL},
            </if>
        </set>
        where ID_AHCS_REPORT_ACCIDENT_EX = #{idAhcsReportAccidentEx,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentExEntity">
        update CLMS_REPORT_ACCIDENT_EX
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        INSURED_APPLY_STATUS = #{insuredApplyStatus,jdbcType=VARCHAR},
        INSURED_APPLY_TYPE = #{insuredApplyType,jdbcType=VARCHAR},
        MEDICAL_STATUS = #{medicalStatus,jdbcType=VARCHAR},
        THERAPY_TYPE = #{therapyType,jdbcType=VARCHAR},
        DOCTOR_DIAGNOSIS = #{doctorDiagnosis,jdbcType=VARCHAR},
        ACCIDENT_TYPE = #{accidentType,jdbcType=VARCHAR},
        TRAFFIC_ACCIDENT_TYPE = #{trafficAccidentType,jdbcType=VARCHAR},
        INSURED_IDENTITY = #{insuredIdentity,jdbcType=VARCHAR},
        TRAFFIC_ACCIDENT_NATURE = #{trafficAccidentNature,jdbcType=VARCHAR},
        DIED_DATE = #{diedDate,jdbcType=VARCHAR},
        DIED_STATUS = #{diedStatus,jdbcType=VARCHAR},
        DIED_CAUSE = #{diedCause,jdbcType=VARCHAR},
        THIS_CARLICENSE = #{thisCarlicense,jdbcType=VARCHAR},
        HOSPITAL_NAME = #{hospitalName,jdbcType=VARCHAR},
        ACCIDENT_STATUS_DETAILS = #{accidentStatusDetails,jdbcType=VARCHAR},
        BIG_DISEASE_CODE = #{bigDiseaseCode,jdbcType=VARCHAR},
        BIG_DISEASE_NAME = #{bigDiseaseName,jdbcType=VARCHAR},
        ACCIDENT_EXTEND_INFO = #{accidentExtendInfo,jdbcType=VARCHAR},
        HOSPITAL_DAYS = #{hospitalDays,jdbcType=DECIMAL}
        where ID_AHCS_REPORT_ACCIDENT_EX = #{idAhcsReportAccidentEx,jdbcType=VARCHAR}
    </update>

    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_REPORT_ACCIDENT_EX (ID_AHCS_REPORT_ACCIDENT_EX,
        CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO,
        INSURED_APPLY_STATUS,INSURED_APPLY_TYPE, MEDICAL_STATUS,
        THERAPY_TYPE, DOCTOR_DIAGNOSIS,
        ACCIDENT_TYPE,
        TRAFFIC_ACCIDENT_TYPE, INSURED_IDENTITY,
        TRAFFIC_ACCIDENT_NATURE,
        DIED_DATE,DIED_STATUS,DIED_CAUSE,THIS_CARLICENSE,HOSPITAL_NAME,
        ACCIDENT_STATUS_DETAILS,
        BIG_DISEASE_CODE, BIG_DISEASE_NAME, ACCIDENT_EXTEND_INFO, HOSPITAL_DAYS, CLOB_ACCIDENT_EXTEND,ARCHIVE_DATE
        )
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsReportAccidentEx,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.reportNo,jdbcType=VARCHAR}, #{item.insuredApplyStatus,jdbcType=VARCHAR},#{item.insuredApplyType,jdbcType=VARCHAR},
            #{item.medicalStatus,jdbcType=VARCHAR},
            #{item.therapyType,jdbcType=VARCHAR},
            #{item.doctorDiagnosis,jdbcType=VARCHAR}, #{item.accidentType,jdbcType=VARCHAR},
            #{item.trafficAccidentType,jdbcType=VARCHAR},
            #{item.insuredIdentity,jdbcType=VARCHAR},
            #{item.trafficAccidentNature,jdbcType=VARCHAR},
            #{item.diedDate,jdbcType=TIMESTAMP}, #{item.diedStatus,jdbcType=VARCHAR},
            #{item.diedCause,jdbcType=VARCHAR}, #{item.thisCarlicense,jdbcType=VARCHAR},
            #{item.hospitalName,jdbcType=VARCHAR},
            #{item.accidentStatusDetails,jdbcType=VARCHAR},
            #{item.bigDiseaseCode,jdbcType=VARCHAR},
            #{item.bigDiseaseName,jdbcType=VARCHAR},
            #{item.accidentExtendInfo,jdbcType=VARCHAR},
            #{item.hospitalDays,jdbcType=VARCHAR},
            #{item.clobAccidentExtend,jdbcType=CLOB},
            now()
            )
        </foreach>
    </insert>
</mapper>