<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
		    
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.RegisterAmountConfigMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.report.BatchReportInfoDTO" id="BatchReportInfoDTOResult">
		<result column="ID_AHCS_BATCH_REPORT_TEMP" property="idAhcsBatchReportTemp"/>
		<result column="REPORT_BATCH_NO" property="reportBatchNo"/>
		<result column="ORDER_NO" property="orderNo"/>
		<result column="BATCH_STATUS" property="batchStatus"/>
		<result column="BATCH_RESULT" property="batchResult"/>
		<result column="POLICY_NO" property="policyNo"/>
		<result column="REPORT_NO" property="reportNo"/>
		<result column="CASE_TIMES" property="caseTimes"/>
		<result column="CASE_NO" property="caseNo"/>
		<result column="ACCIDENT_DATE" property="accidentDate"/>
		<result column="CLIENT_NAME" property="clientName"/>
		<result column="PLAN_CODE" property="planCode"/>
		<result column="DUTY_CODE" property="dutyCode"/>
		<result column="DUTY_AMOUNT" property="dutyAmount"/>
		<result column="CERTIFICATE_TYPE" property="certificateType"/>
		<result column="CERTIFICATE_NO" property="certificateNo"/>
		<result column="BANK_ACCOUNT" property="bankAccount"/>
		<result column="PAYEE_NAME" property="payeeName"/>
		<result column="PAYMENT_AMOUNT" property="paymentAmount"/>
		<result column="IS_SUPER_MAX_PAYMENT" property="isSuperMaxPayment"/>
		<result column="CREATED_BY" property="createdBy"/>
		<result column="DUTY_AMOUNT_LIST" property="dutyAmountList"/>
	</resultMap>

	<select id="getBatchReportInfoList" resultMap="BatchReportInfoDTOResult">
		SELECT ID_AHCS_BATCH_REPORT_TEMP,
		REPORT_BATCH_NO,
		ORDER_NO,
		BATCH_RESULT,
		BATCH_STATUS,
		POLICY_NO,
		REPORT_NO,
		CASE_NO,
		ACCIDENT_DATE,
		CLIENT_NAME,
		PLAN_CODE,
		DUTY_CODE,
		DUTY_AMOUNT,
		CERTIFICATE_NO,
		CASE_TIMES,
		BANK_ACCOUNT,
		PAYEE_NAME,
		CERTIFICATE_TYPE,
		PAYMENT_AMOUNT,
		IS_SUPER_MAX_PAYMENT,
		CREATED_BY,
		DUTY_AMOUNT_LIST
		FROM    CLMS_BATCH_REPORT_TEMP T
		WHERE  T.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND    T.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		and    T.BATCH_STATUS IN ('3','5')
	</select>

</mapper>