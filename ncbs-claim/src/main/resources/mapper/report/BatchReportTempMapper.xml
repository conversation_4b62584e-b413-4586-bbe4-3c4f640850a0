<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.BatchReportTempEntityMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.BatchReportTempEntity">
        <id column="ID_AHCS_BATCH_REPORT_TEMP" property="idAhcsBatchReportTemp" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_DATE" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_DATE" jdbcType="TIMESTAMP" property="updatedDate"/>
        <result column="REPORT_BATCH_NO" jdbcType="VARCHAR" property="reportBatchNo"/>
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo"/>
        <result column="BATCH_RESULT" jdbcType="VARCHAR" property="batchResult"/>
        <result column="BATCH_STATUS" jdbcType="VARCHAR" property="batchStatus"/>
        <result column="POLICY_NO" jdbcType="VARCHAR" property="policyNo"/>
        <result column="REPORT_NO" jdbcType="VARCHAR" property="reportNo"/>
        <result column="CASE_NO" jdbcType="VARCHAR" property="caseNo"/>
        <result column="ACCIDENT_DATE" jdbcType="TIMESTAMP" property="accidentDate"/>
        <result column="CLIENT_NAME" jdbcType="VARCHAR" property="clientName"/>
        <result column="PLAN_CODE" jdbcType="VARCHAR" property="planCode"/>
        <result column="DUTY_CODE" jdbcType="VARCHAR" property="dutyCode"/>
        <result column="DUTY_AMOUNT" jdbcType="DECIMAL" property="dutyAmount"/>
        <result column="CERTIFICATE_TYPE" jdbcType="VARCHAR" property="certificateType"/>
        <result column="CERTIFICATE_NO" jdbcType="VARCHAR" property="certificateNo"/>
        <result column="CASE_TIMES" jdbcType="DECIMAL" property="caseTimes"/>
        <result column="BANK_ACCOUNT" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="PAYEE_NAME" jdbcType="VARCHAR" property="payeeName"/>
        <result column="PAYMENT_AMOUNT" jdbcType="VARCHAR" property="paymentAmount"/>
        <result column="IS_SUPER_MAX_PAYMENT" jdbcType="VARCHAR" property="isSuperMaxPayment"/>
        <result column="ACCIDENT_TYPE" jdbcType="VARCHAR" property="accidentType"/>
        <result column="MOBILE_NO" jdbcType="VARCHAR" property="mobileNo"/>
        <result column="DUTY_AMOUNT_LIST" jdbcType="VARCHAR" property="dutyAmountList"/>
        <result column="MERGE_COUNT" jdbcType="DECIMAL" property="mergeCount"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_BATCH_REPORT_TEMP, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, REPORT_BATCH_NO,
        ORDER_NO, BATCH_RESULT, BATCH_STATUS, POLICY_NO, REPORT_NO, CASE_NO, ACCIDENT_DATE,
        CLIENT_NAME, PLAN_CODE, DUTY_CODE, DUTY_AMOUNT, CERTIFICATE_TYPE, CERTIFICATE_NO,
        CASE_TIMES, BANK_ACCOUNT, PAYEE_NAME, PAYMENT_AMOUNT, IS_SUPER_MAX_PAYMENT, ACCIDENT_TYPE, MOBILE_NO,
        DUTY_AMOUNT_LIST, MERGE_COUNT
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_BATCH_REPORT_TEMP
        where ID_AHCS_BATCH_REPORT_TEMP = #{idAhcsBatchReportTemp,jdbcType=VARCHAR}
    </select>

    <insert id="insertBatch">
        insert into CLMS_BATCH_REPORT_TEMP
        (CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, REPORT_BATCH_NO,
        ORDER_NO, BATCH_RESULT, BATCH_STATUS, POLICY_NO, ACCIDENT_DATE, CLIENT_NAME, PLAN_CODE, DUTY_CODE,
        DUTY_AMOUNT, CERTIFICATE_TYPE, CERTIFICATE_NO, BANK_ACCOUNT, PAYEE_NAME, ACCIDENT_TYPE, MOBILE_NO,
        DUTY_AMOUNT_LIST, MERGE_COUNT,ARCHIVE_DATE,ID_AHCS_BATCH_REPORT_TEMP,REPORT_NO)
        values
        <foreach collection="record" item="item" index="index" separator=",">
            (
            #{item.createdBy,jdbcType=VARCHAR},now(), #{item.createdBy,jdbcType=VARCHAR},now(), #{item.reportBatchNo,jdbcType=VARCHAR},
            #{item.orderNo,jdbcType=VARCHAR},
            #{item.batchResult,jdbcType=VARCHAR},
            #{item.batchStatus,jdbcType=VARCHAR},#{item.policyNo,jdbcType=VARCHAR},
            #{item.accidentDate,jdbcType=TIMESTAMP},
            #{item.clientName,jdbcType=VARCHAR}, #{item.planCode,jdbcType=VARCHAR},
            #{item.dutyCode,jdbcType=VARCHAR},#{item.dutyAmount,jdbcType=DECIMAL},
            #{item.certificateType,jdbcType=VARCHAR}, #{item.certificateNo,jdbcType=VARCHAR},
            #{item.bankAccount,jdbcType=VARCHAR},
            #{item.payeeName,jdbcType=VARCHAR}, #{item.accidentType,jdbcType=VARCHAR},
            #{item.mobileNo,jdbcType=VARCHAR},
            #{item.dutyAmountList,jdbcType=VARCHAR}, #{item.mergeCount,jdbcType=DECIMAL},
            now(),
            replace(uuid(),'-',''),#{item.reportNo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insert">
        insert into CLMS_BATCH_REPORT_TEMP
        (CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, REPORT_BATCH_NO,
         ORDER_NO, BATCH_RESULT, BATCH_STATUS, POLICY_NO, ACCIDENT_DATE, CLIENT_NAME,
         PLAN_CODE, DUTY_CODE,
         DUTY_AMOUNT, CERTIFICATE_TYPE, CERTIFICATE_NO, BANK_ACCOUNT, PAYEE_NAME,
         ACCIDENT_TYPE, MOBILE_NO)
        values(
                  #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR},
                  #{updatedDate,jdbcType=TIMESTAMP}, #{reportBatchNo,jdbcType=VARCHAR},
                  #{orderNo,jdbcType=VARCHAR}, #{batchResult,jdbcType=VARCHAR},
                  #{batchStatus,jdbcType=VARCHAR},#{policyNo,jdbcType=VARCHAR},
                  #{accidentDate,jdbcType=TIMESTAMP},
                  #{clientName,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR},
                  #{dutyCode,jdbcType=VARCHAR},#{dutyAmount,jdbcType=DECIMAL},
                  #{certificateType,jdbcType=VARCHAR}, #{certificateNo,jdbcType=VARCHAR},
                  #{bankAccount,jdbcType=VARCHAR},
                  #{payeeName,jdbcType=VARCHAR}, #{accidentType,jdbcType=VARCHAR}, #{mobileNo,jdbcType=VARCHAR}
              )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.report.BatchReportTempEntity">
        insert into CLMS_BATCH_REPORT_TEMP
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsBatchReportTemp != null">
                ID_AHCS_BATCH_REPORT_TEMP,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="reportBatchNo != null">
                REPORT_BATCH_NO,
            </if>
            <if test="orderNo != null">
                ORDER_NO,
            </if>
            <if test="batchResult != null">
                BATCH_RESULT,
            </if>
            <if test="batchStatus != null">
                BATCH_STATUS,
            </if>
            <if test="policyNo != null">
                POLICY_NO,
            </if>
            <if test="reportNo != null">
                REPORT_NO,
            </if>
            <if test="caseNo != null">
                CASE_NO,
            </if>
            <if test="accidentDate != null">
                ACCIDENT_DATE,
            </if>
            <if test="clientName != null">
                CLIENT_NAME,
            </if>
            <if test="planCode != null">
                PLAN_CODE,
            </if>
            <if test="dutyCode != null">
                DUTY_CODE,
            </if>
            <if test="dutyAmount != null">
                DUTY_AMOUNT,
            </if>
            <if test="certificateType != null">
                CERTIFICATE_TYPE,
            </if>
            <if test="certificateNo != null">
                CERTIFICATE_NO,
            </if>
            <if test="caseTimes != null">
                CASE_TIMES,
            </if>
            <if test="bankAccount != null">
                BANK_ACCOUNT,
            </if>
            <if test="payeeName != null">
                PAYEE_NAME,
            </if>
            <if test="paymentAmount != null">
                PAYMENT_AMOUNT,
            </if>
            <if test="isSuperMaxPayment != null">
                IS_SUPER_MAX_PAYMENT,
            </if>
            <if test="accidentType != null">
                ACCIDENT_TYPE,
            </if>
            <if test="mobileNo != null">
                MOBILE_NO,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsBatchReportTemp != null">
                #{idAhcsBatchReportTemp,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportBatchNo != null">
                #{reportBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="batchResult != null">
                #{batchResult,jdbcType=VARCHAR},
            </if>
            <if test="batchStatus != null">
                #{batchStatus,jdbcType=VARCHAR},
            </if>
            <if test="policyNo != null">
                #{policyNo,jdbcType=VARCHAR},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseNo != null">
                #{caseNo,jdbcType=VARCHAR},
            </if>
            <if test="accidentDate != null">
                #{accidentDate,jdbcType=TIMESTAMP},
            </if>
            <if test="clientName != null">
                #{clientName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyAmount != null">
                #{dutyAmount,jdbcType=DECIMAL},
            </if>
            <if test="certificateType != null">
                #{certificateType,jdbcType=VARCHAR},
            </if>
            <if test="certificateNo != null">
                #{certificateNo,jdbcType=VARCHAR},
            </if>
            <if test="caseTimes != null">
                #{caseTimes,jdbcType=DECIMAL},
            </if>
            <if test="bankAccount != null">
                #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="payeeName != null">
                #{payeeName,jdbcType=VARCHAR},
            </if>
            <if test="paymentAmount != null">
                #{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="isSuperMaxPayment != null">
                #{isSuperMaxPayment,jdbcType=VARCHAR},
            </if>
            <if test="accidentType != null">
                #{accidentType,jdbcType=VARCHAR},
            </if>
            <if test="mobileNo != null">
                #{mobileNo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.BatchReportTempEntity">
        update CLMS_BATCH_REPORT_TEMP
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            UPDATED_DATE = now(),
            <if test="reportBatchNo != null">
                REPORT_BATCH_NO = #{reportBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                ORDER_NO = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="batchResult != null">
                BATCH_RESULT = #{batchResult,jdbcType=VARCHAR},
            </if>
            <if test="batchStatus != null">
                BATCH_STATUS = #{batchStatus,jdbcType=VARCHAR},
            </if>
            <if test="policyNo != null">
                POLICY_NO = #{policyNo,jdbcType=VARCHAR},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseNo != null">
                CASE_NO = #{caseNo,jdbcType=VARCHAR},
            </if>
            <if test="accidentDate != null">
                ACCIDENT_DATE = #{accidentDate,jdbcType=TIMESTAMP},
            </if>
            <if test="clientName != null">
                CLIENT_NAME = #{clientName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                PLAN_CODE = #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                DUTY_CODE = #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyAmount != null">
                DUTY_AMOUNT = #{dutyAmount,jdbcType=DECIMAL},
            </if>
            <if test="certificateType != null">
                CERTIFICATE_TYPE = #{certificateType,jdbcType=VARCHAR},
            </if>
            <if test="certificateNo != null">
                CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR},
            </if>
            <if test="caseTimes != null">
                CASE_TIMES = #{caseTimes,jdbcType=DECIMAL},
            </if>
            <if test="bankAccount != null">
                BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="payeeName != null">
                PAYEE_NAME = #{payeeName,jdbcType=VARCHAR},
            </if>
            <if test="paymentAmount != null">
                PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="isSuperMaxPayment != null">
                IS_SUPER_MAX_PAYMENT = #{isSuperMaxPayment,jdbcType=VARCHAR},
            </if>
            <if test="accidentType != null">
                ACCIDENT_TYPE = #{accidentType,jdbcType=VARCHAR},
            </if>
            <if test="mobileNo != null">
                MOBILE_NO = #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="dutyAmountList != null">
                DUTY_AMOUNT_LIST = #{dutyAmountList,jdbcType=VARCHAR},
            </if>
            <if test="mergeCount != null">
                MERGE_COUNT = #{mergeCount,jdbcType=DECIMAL},
            </if>
        </set>
        where ID_AHCS_BATCH_REPORT_TEMP = #{idAhcsBatchReportTemp,jdbcType=VARCHAR}
        and BATCH_STATUS not in ('6','7','8')
    </update>

    <select id="queryRepeatOrderNo" resultType="java.lang.String" parameterType="java.util.Map">
        select t.order_no from CLMS_batch_report_temp t
        where t.report_batch_no = #{reportBatchNo,jdbcType=VARCHAR}
          and t.policy_no = #{policyNo,jdbcType=VARCHAR}
          and t.certificate_no = #{certificateNo,jdbcType=VARCHAR}
          and t.accident_date = str_to_date(#{accidentDate,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s')
          and t.client_name = #{clientName,jdbcType=VARCHAR}
          and t.plan_code = #{planCode,jdbcType=VARCHAR}
          and t.duty_code = #{dutyCode,jdbcType=VARCHAR}
    </select>

    <select id="queryReportInfoByPolicyNoAndDate" resultType="java.util.Map" parameterType="java.util.Map">
        select i.accident_date accidentDate, p.policy_no policyNo
        from clm_report_accident i, CLMS_policy_info p, CLMS_report_customer a
        where i.migrate_from = 'na'
          and i.report_no = p.report_no
          and i.report_no = a.report_no
          and a.certificate_no =#{certificateNo,jdbcType=VARCHAR}
          and a.name =#{name,jdbcType=VARCHAR}
          and p.policy_no = #{policyNo,jdbcType=VARCHAR}
          and i.accident_date between
            str_to_date(#{beginTime, jdbcType = VARCHAR}, '%Y-%m-%d %H:%i:%s') and
            str_to_date(#{endTime, jdbcType = VARCHAR}, '%Y-%m-%d %H:%i:%s')
    </select>

    <select id="queryUserNameByUserId" resultType="java.lang.String" parameterType="java.lang.String">
        select t.user_name from CLMS_user_info t where t.user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="queryMd5Count" resultType="java.lang.Integer">
        select count(1) from CLMS_batch_report t where t.file_md5 = #{md5,jdbcType=VARCHAR} and t.batch_status = 0
    </select>

    <select id="getIdListByReportBatchNo" resultType="java.lang.String">
        select t.id_ahcs_batch_report_temp from CLMS_batch_report_temp t where t.report_batch_no =
                                                                               #{reportBatchNo,jdbcType=VARCHAR}
    </select>

    <select id="getInfoByBatchNoAndType"
            resultType="com.paic.ncbs.claim.dao.entity.report.BatchReportTempEntity"
            parameterType="java.lang.String">
        select
            t.ID_AHCS_BATCH_REPORT_TEMP idAhcsBatchReportTemp,
            t.ORDER_NO orderNo,
            t.CERTIFICATE_TYPE certificateType,
            t.CERTIFICATE_NO certificateNo,
            t.ACCIDENT_TYPE accidentType,
            t.MOBILE_NO mobileNo,
            t.BATCH_RESULT batchResult,
            t.CLIENT_NAME clientName,
            t.REPORT_NO reportNo,
            t.ACCIDENT_DATE accidentDate
        from CLMS_BATCH_REPORT_TEMP t, CLMS_batch_report r
        where t.REPORT_BATCH_NO = #{reportBatchNo,jdbcType=VARCHAR}
          and r.BATCH_REPORT_TYPE =#{batchType,jdbcType=VARCHAR}
          and t.REPORT_BATCH_NO = r.REPORT_BATCH_NO
    </select>

    <select id="selectByReportNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_BATCH_REPORT_TEMP
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

</mapper>