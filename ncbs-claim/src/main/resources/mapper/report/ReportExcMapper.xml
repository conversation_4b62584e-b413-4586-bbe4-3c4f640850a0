<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportExcMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportExcEntity">
        <id column="ID_REPORT_EXC" property="idReportExc" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="POLICY_NO" property="policyNo" jdbcType="VARCHAR"/>
        <result column="DEPARTMENT_CODE" property="departmentCode" jdbcType="VARCHAR"/>
        <result column="IS_VIRTUAL" property="isVirtual" jdbcType="VARCHAR"/>
        <result column="BIRTHDAY" property="birthday" jdbcType="TIMESTAMP"/>
        <result column="CERTIFICATE_TYPE" property="certificateType" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_NO" property="certificateNo" jdbcType="VARCHAR"/>
        <result column="SEX" property="sex" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_NAME" property="accidentName" jdbcType="VARCHAR"/>
        <result column="TRANS_NAME" property="transName" jdbcType="VARCHAR"/>
        <result column="TRANS_DATE" property="transDate" jdbcType="TIMESTAMP"/>
        <result column="REASON_DESC" property="reasonDesc" jdbcType="VARCHAR"/>
        <result column="ELEC_SUB_POLICY_NO" property="elecSubPolicyNo" jdbcType="VARCHAR"/>
        <result column="REASON_CODE" property="reasonCode" jdbcType="VARCHAR"/>
        <result column="ABNORMAL_TYPE" property="abnormalType" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="getReportExcByReportNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from REPORT_EXC
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>
    <sql id="Base_Column_List">
        ID_REPORT_EXC, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, REPORT_NO, POLICY_NO,
        DEPARTMENT_CODE, IS_VIRTUAL, BIRTHDAY, CERTIFICATE_TYPE, CERTIFICATE_NO, SEX, ACCIDENT_NAME,
        TRANS_NAME, TRANS_DATE ,REASON_DESC ,ELEC_SUB_POLICY_NO ,REASON_CODE, ABNORMAL_TYPE
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from REPORT_EXC
        where ID_REPORT_EXC = #{idReportExc,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from REPORT_EXC
        where ID_REPORT_EXC = #{idReportExc,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportExcEntity">
        insert into REPORT_EXC (ID_REPORT_EXC, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        POLICY_NO, DEPARTMENT_CODE, IS_VIRTUAL,
        BIRTHDAY, CERTIFICATE_TYPE, CERTIFICATE_NO,
        SEX, ACCIDENT_NAME, TRANS_NAME,
        TRANS_DATE,REASON_DESC,ELEC_SUB_POLICY_NO,REASON_CODE,ABNORMAL_TYPE)
        values (#{idReportExc,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{reportNo,jdbcType=VARCHAR},
        #{policyNo,jdbcType=VARCHAR}, #{departmentCode,jdbcType=VARCHAR}, #{isVirtual,jdbcType=VARCHAR},
        #{birthday,jdbcType=TIMESTAMP}, #{certificateType,jdbcType=VARCHAR}, #{certificateNo,jdbcType=VARCHAR},
        #{sex,jdbcType=VARCHAR}, #{accidentName,jdbcType=VARCHAR}, #{transName,jdbcType=VARCHAR},
        #{transDate,jdbcType=TIMESTAMP},#{reasonDesc,jdbcType=VARCHAR},#{elecSubPolicyNo,jdbcType=VARCHAR},
        #{reasonCode,jdbcType=VARCHAR},
        #{abnormalType,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportExcEntity">
        insert into REPORT_EXC
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idReportExc != null">
                ID_REPORT_EXC,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="reportNo != null">
                REPORT_NO,
            </if>
            <if test="policyNo != null">
                POLICY_NO,
            </if>
            <if test="departmentCode != null">
                DEPARTMENT_CODE,
            </if>
            <if test="isVirtual != null">
                IS_VIRTUAL,
            </if>
            <if test="birthday != null">
                BIRTHDAY,
            </if>
            <if test="certificateType != null">
                CERTIFICATE_TYPE,
            </if>
            <if test="certificateNo != null">
                CERTIFICATE_NO,
            </if>
            <if test="sex != null">
                SEX,
            </if>
            <if test="accidentName != null">
                ACCIDENT_NAME,
            </if>
            <if test="transName != null">
                TRANS_NAME,
            </if>
            <if test="transDate != null">
                TRANS_DATE,
            </if>
            <if test="reasonDesc != null">
                REASON_DESC,
            </if>
            <if test="elecSubPolicyNo != null">
                ELEC_SUB_POLICY_NO,
            </if>
            <if test="reasonCode != null">
                REASON_CODE,
            </if>
            <if test="abnormalType != null">
                ABNORMAL_TYPE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idReportExc != null">
                #{idReportExc,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="policyNo != null">
                #{policyNo,jdbcType=VARCHAR},
            </if>
            <if test="departmentCode != null">
                #{departmentCode,jdbcType=VARCHAR},
            </if>
            <if test="isVirtual != null">
                #{isVirtual,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="certificateType != null">
                #{certificateType,jdbcType=VARCHAR},
            </if>
            <if test="certificateNo != null">
                #{certificateNo,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="accidentName != null">
                #{accidentName,jdbcType=VARCHAR},
            </if>
            <if test="transName != null">
                #{transName,jdbcType=VARCHAR},
            </if>
            <if test="transDate != null">
                #{transDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reasonDesc != null">
                #{reasonDesc,jdbcType=VARCHAR},
            </if>
            <if test="elecSubPolicyNo != null">
                #{elecSubPolicyNo,jdbcType=VARCHAR},
            </if>
            <if test="reasonCode != null">
                #{reasonCode,jdbcType=VARCHAR},
            </if>
            <if test="abnormalType != null">
                #{abnormalType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportExcEntity">
        update REPORT_EXC
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="policyNo != null">
                POLICY_NO = #{policyNo,jdbcType=VARCHAR},
            </if>
            <if test="departmentCode != null">
                DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR},
            </if>
            <if test="isVirtual != null">
                IS_VIRTUAL = #{isVirtual,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                BIRTHDAY = #{birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="certificateType != null">
                CERTIFICATE_TYPE = #{certificateType,jdbcType=VARCHAR},
            </if>
            <if test="certificateNo != null">
                CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                SEX = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="accidentName != null">
                ACCIDENT_NAME = #{accidentName,jdbcType=VARCHAR},
            </if>
            <if test="transName != null">
                TRANS_NAME = #{transName,jdbcType=VARCHAR},
            </if>
            <if test="transDate != null">
                TRANS_DATE = #{transDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reasonDesc != null">
                REASON_DESC = #{reasonDesc,jdbcType=VARCHAR},
            </if>
            <if test="elecSubPolicyNo != null">
                ELEC_SUB_POLICY_NO = #{elecSubPolicyNo,jdbcType=VARCHAR},
            </if>
            <if test="reasonCode != null">
                REASON_CODE = #{reasonCode,jdbcType=VARCHAR},
            </if>
            <if test="abnormalType != null">
                ABNORMAL_TYPE = #{abnormalType,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_REPORT_EXC = #{idReportExc,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportExcEntity">
        update REPORT_EXC
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        POLICY_NO = #{policyNo,jdbcType=VARCHAR},
        DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR},
        IS_VIRTUAL = #{isVirtual,jdbcType=VARCHAR},
        BIRTHDAY = #{birthday,jdbcType=TIMESTAMP},
        CERTIFICATE_TYPE = #{certificateType,jdbcType=VARCHAR},
        CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR},
        SEX = #{sex,jdbcType=VARCHAR},
        ACCIDENT_NAME = #{accidentName,jdbcType=VARCHAR},
        TRANS_NAME = #{transName,jdbcType=VARCHAR},
        TRANS_DATE = #{transDate,jdbcType=TIMESTAMP},
        REASON_DESC = #{reasonDesc,jdbcType=VARCHAR},
        ELEC_SUB_POLICY_NO = #{elecSubPolicyNo,jdbcType=VARCHAR},
        REASON_CODE = #{reasonCode,jdbcType=VARCHAR},
        ABNORMAL_TYPE = #{abnormalType,jdbcType=VARCHAR}
        where ID_REPORT_EXC = #{idReportExc,jdbcType=VARCHAR}
    </update>

</mapper>