<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.InsuredPersonMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.report.InsuredPersonDTO"
               id="InsuredPersonMap">
        <id property="idAhcsInsuredPerson" column="ID_AHCS_INSURED_PERSON"/>
        <result property="idAhcsPolicyInfo" column="ID_AHCS_POLICY_INFO"/>
        <result property="name" column="NAME"/>
        <result property="sexCode" column="SEX_CODE"/>
        <result property="birthday" column="BIRTHDAY"/>
        <result property="certificateNo" column="CERTIFICATE_NO"/>
        <result property="certificateType" column="CERTIFICATE_TYPE"/>
        <result property="professionCode" column="PROFESSION_CODE"/>
        <result property="telephone" column="TELEPHONE"/>
        <result property="mobileTelephone" column="MOBILE_TELEPHONE"/>
        <result property="address" column="ADDRESS"/>
        <result property="email" column="EMAIL"/>
        <result property="clientNo" column="CLIENT_NO"/>
        <result property="schemeNo" column="SCHEME_NO"/>
        <result property="schemeName" column="SCHEME_NAME"/>
        <result property="acceptNo" column="ACCEPT_NO"/>
        <result property="subPolicyno" column="SUB_POLICYNO"/>
        <result property="isSociaSecurity" column="IS_SOCIA_SECURITY"/>
<!--        <collection property="insuredPersonExtDTO"-->
<!--                    ofType="com.paic.ncbs.claim.model.dto.report.InsuredPersonExtDTO"-->
<!--                    select="getInsuredPersonExtDTO"-->
<!--                    column="{idAhcsInsuredPerson=ID_AHCS_INSURED_PERSON}">-->
<!--        </collection>-->
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.report.InsuredPersonExtDTO" id="InsuredPersonExtMap">
        <id property="idAhcsInsuredPersonExt" column="ID_AHCS_INSURED_PERSON_EXT"/>
        <result property="idAhcsInsuredPerson" column="ID_AHCS_INSURED_PERSON"/>
        <result property="bankAccount" column="BANK_ACCOUNT"/>
        <result property="bankCode" column="BANK_CODE"/>
        <result property="bankHeadquartersCode" column="BANK_HEADQUARTERS_CODE"/>
        <result property="vehicleLicenceCode" column="VEHICLE_LICENCE_CODE"/>
        <result property="vehicleFrameNo" column="VEHICLE_FRAME_NO"/>
        <result property="engineNo" column="ENGINE_NO"/>
        <result property="flightNo" column="FLIGHT_DATE"/>
        <result property="flightDate" column="MOBILE_TELEPHONE"/>
        <result property="original" column="ORIGINAL"/>
        <result property="destination" column="DESTINATION"/>
        <result property="transactionNo" column="TRANSACTION_NO"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.report.InsuredPersonDTO" id="resultInsuredPerson">
        <result property="name" column="NAME"/>
        <result property="clientNo" column="CLIENT_NO"/>
        <result property="sexCode" column="SEX_CODE"/>
        <result property="birthdayStr" column="BIRTHDAY"/>
        <result property="certificateNo" column="CERTIFICATE_NO"/>
        <result property="certificateType" column="CERTIFICATE_TYPE"/>
        <result property="reporterMobile" column="REPORTER_MOBILE"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="isOrganization" column="IS_ORGANIZATION"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.report.InsuredPersonDTO" id="resultCustomerNo">
        <result column="RISK_PERSON_NO" property="riskPersonNo"/>
        <result column="PERSONNEL_CODE" property="personnelCode"/>
        <result column="ACCEPT_NO" property="acceptNo"/>
        <result column="NAME" property="name"/>
        <result column="CERTIFICATE_NO" property="certificateNo"/>
        <result column="POLICY_NO" property="policyNo"/>
        <result column="CERTIFICATE_TYPE" property="certificateType"/>
        <result column="PLY_CERTIFICATE_TYPE" property="plyCertificateType"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.vo.report.InsuredPersonVO" id="resultInsuredInfo">
        <result property="memberName" column="NAME"/>
        <result property="gender" column="SEX_CODE"/>
        <result property="age" column="AGE"/>
    </resultMap>

    <select id="getInsuredPersonDTO" resultMap="InsuredPersonMap">
        SELECT T.ID_AHCS_INSURED_PERSON,
        T.ID_AHCS_POLICY_INFO,
        T.NAME,
        T.SEX_CODE,
        T.BIRTHDAY,
        T.CERTIFICATE_NO,
        T.CERTIFICATE_TYPE,
        T.PROFESSION_CODE,
        T.TELEPHONE,
        T.MOBILE_TELEPHONE,
        T.ADDRESS,
        T.EMAIL,
        T.CLIENT_NO,
        T.SCHEME_NO,
        T.SCHEME_NAME,
        T.ACCEPT_NO,
        T.SUB_POLICYNO,
        T.IS_SOCIA_SECURITY
        FROM CLMS_INSURED_PERSON T, CLMS_POLICY_INFO TT
        WHERE TT.ID_AHCS_POLICY_INFO = T.ID_AHCS_POLICY_INFO
        AND TT.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="getInsuredPersonDTOByPolicyId" resultMap="InsuredPersonMap">
        SELECT T.ID_AHCS_INSURED_PERSON,
        T.ID_AHCS_POLICY_INFO,
        T.NAME,
        T.SEX_CODE,
        T.BIRTHDAY,
        T.CERTIFICATE_NO,
        T.CERTIFICATE_TYPE,
        T.PROFESSION_CODE,
        T.TELEPHONE,
        T.MOBILE_TELEPHONE,
        T.ADDRESS,
        T.EMAIL,
        T.CLIENT_NO,
        T.SCHEME_NO,
        T.SCHEME_NAME,
        T.ACCEPT_NO,
        T.SUB_POLICYNO,
        T.IS_SOCIA_SECURITY
        FROM CLMS_INSURED_PERSON T
        WHERE T.ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="getInsuredPerson" resultMap="resultInsuredPerson">
        SELECT ip.NAME,
        ip.CLIENT_NO
        FROM CLMS_INSURED_PERSON ip
        WHERE ip.ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo}
        limit 1
    </select>

    <select id="getInsuredPersonInfo" resultMap="resultInsuredPerson">
        SELECT ip.NAME,
        ip.CLIENT_NO,
        ip.SEX_CODE,
        ip.BIRTHDAY,
        ip.IS_ORGANIZATION
        FROM CLMS_REPORT_CUSTOMER ip
        WHERE ip.report_no = #{reportNo}
        limit 1
    </select>

    <select id="getInsuredPersonList" resultMap="resultInsuredPerson">
        SELECT ip.NAME,
        ip.CLIENT_NO,
        decode(ip.SEX_CODE, 'M', '0', 'F' ,'1', '2') SEX_CODE,
        to_char(ip.BIRTHDAY, 'yyyymmdd') BIRTHDAY,
        ip.CERTIFICATE_NO,
        decode(ip.CERTIFICATE_TYPE, '01', '01', '02' ,'07', '03', '04', '06', '09', '99') CERTIFICATE_TYPE
        FROM CLMS_INSURED_PERSON ip
        WHERE ip.ID_AHCS_POLICY_INFO in
        <foreach collection="idAhcsPolicyInfoList" separator=" , " index="index" item="idAhcsPolicyInfo" open="("
                 close=")">
            #{idAhcsPolicyInfo}
        </foreach>
        <if test="birthday != null and birthday != '' ">
            and ip.BIRTHDAY is not null
        </if>
        <if test="birthday == null or birthday == '' ">
            and ip.BIRTHDAY is null
            and ip.CERTIFICATE_TYPE='01'
            and ip.CERTIFICATE_NO is not null
        </if>
    </select>

    <select id="getReportCustomer" parameterType="string" resultMap="resultInsuredPerson">
        SELECT ip.NAME,
        ip.CLIENT_NO,
        decode(ip.SEX_CODE, 'M', '0', 'F' ,'1', '2') SEX_CODE,
        to_char(ip.BIRTHDAY, 'yyyymmdd') BIRTHDAY,
        ip.CERTIFICATE_NO,
        decode(ip.CERTIFICATE_TYPE, '01', '01', '02' ,'07', '03', '04', '06', '09', '99') CERTIFICATE_TYPE
        FROM CLMS_REPORT_CUSTOMER ip
        WHERE ip.report_no = #{reportNo}
        and (ip.BIRTHDAY is not null or ip.CERTIFICATE_NO is not null)
        limit 1
    </select>

    <select id="getReportCustomerByInsuredPerson" resultMap="resultInsuredPerson">
        SELECT t.NAME,
        t.CLIENT_NO,
        decode(t.SEX_CODE, 'M', '0', 'F' ,'1', '2') SEX_CODE,
        to_char(t.BIRTHDAY, 'yyyymmdd') BIRTHDAY,
        t.CERTIFICATE_NO,
        decode(t.CERTIFICATE_TYPE, '01', '01', '02' ,'07', '03', '04', '06', '09', '99') CERTIFICATE_TYPE
        FROM CLMS_REPORT_CUSTOMER ip, CLMS_INSURED_PERSON t
        WHERE ip.report_no = #{reportNo}
        and t.id_ahcs_policy_info = #{idPolicyInfo,jdbcType=VARCHAR}
        and (t.BIRTHDAY is not null or t.CERTIFICATE_NO is not null)
        and ((ip.CERTIFICATE_NO = t.CERTIFICATE_NO and t.NAME=ip.NAME) or ip.CLIENT_NO=t.CLIENT_NO)
        limit 1
    </select>

    <select id="getSdInsuredPerson" resultMap="resultInsuredPerson">
        SELECT t.NAME,
        t.CLIENT_NO,
        decode(t.SEX_CODE, 'M', '0', 'F' ,'1', '2') SEX_CODE,
        to_char(t.BIRTHDAY, 'yyyymmdd') BIRTHDAY,
        t.CERTIFICATE_NO,
        decode(t.CERTIFICATE_TYPE, '01', '01', '02' ,'07', '03', '04', '06', '09', '99') CERTIFICATE_TYPE
        FROM CLMS_REPORT_CUSTOMER ip, CLMS_INSURED_PERSON t
        WHERE ip.report_no = #{reportNo}
        and t.id_ahcs_policy_info in
        <foreach collection="policyInfoIdList" item="policyInfoId" open="(" close=")" separator=",">
            #{policyInfoId}
        </foreach>
        and (t.BIRTHDAY is not null or t.CERTIFICATE_NO is not null)
        and ((ip.CERTIFICATE_NO = t.CERTIFICATE_NO and t.NAME=ip.NAME) or ip.CLIENT_NO=t.CLIENT_NO)
        limit 1
    </select>

    <select id="getReportCustomerNo" resultMap="resultInsuredPerson">
        SELECT ip.NAME,
        ip.CERTIFICATE_NO,
        decode(ip.CERTIFICATE_TYPE, '01', '1', '02' ,'7', '03', '4', '06', '41', '99') CERTIFICATE_TYPE
        FROM CLMS_REPORT_CUSTOMER ip
        WHERE ip.report_no = #{reportNo}
        limit 1
    </select>

    <select id="getReportCustomerNoByInsuredPerson" resultMap="resultInsuredPerson">
        SELECT t.NAME,
        t.CERTIFICATE_NO,
        decode(t.CERTIFICATE_TYPE, '01', '1', '02' ,'7', '03', '4', '06', '41', '99') CERTIFICATE_TYPE
        FROM CLMS_REPORT_CUSTOMER ip, CLMS_INSURED_PERSON t
        WHERE ip.report_no = #{reportNo}
        and t.ID_AHCS_POLICY_INFO = #{idPolicyInfo,jdbcType=VARCHAR}
        and ((ip.CERTIFICATE_NO = t.CERTIFICATE_NO and t.NAME=ip.NAME) or ip.CLIENT_NO=t.CLIENT_NO)
        limit 1
    </select>

    <select id="getCustomerNoByIdPolicy" parameterType="string" resultMap="resultCustomerNo">
        select t1.RISK_PERSON_NO,t1.PERSONNEL_CODE,t1.ACCEPT_NO
        from CLMS_insured_person t1 , CLMS_REPORT_CUSTOMER t2
        where t1.id_ahcs_policy_info = #{idPolicyInfo}
        and t2.report_no = #{reportNo}
        and ((t2.CERTIFICATE_NO = t1.CERTIFICATE_NO and t2.NAME=t1.NAME) or t2.CLIENT_NO=t1.CLIENT_NO)
        limit 1
    </select>

    <select id="getAcceptNoByIdPolicy" parameterType="string" resultMap="resultCustomerNo">
        select t1.ACCEPT_NO,
        t1.NAME,
        t1.CERTIFICATE_NO,
        t1.CERTIFICATE_TYPE,
        t1.PLY_CERTIFICATE_TYPE
        from CLMS_insured_person t1 , CLMS_REPORT_CUSTOMER t2
        where t1.id_ahcs_policy_info = #{idPolicyInfo,jdbcType=VARCHAR}
        and t2.report_no = #{reportNo}
        and ((t2.CERTIFICATE_NO = t1.CERTIFICATE_NO and t1.NAME=t2.NAME) or t2.CLIENT_NO=t1.CLIENT_NO)
        limit 1
    </select>

    <select id="getInsuredNameByReportNo" parameterType="string" resultType="string">
        select rc.NAME
        from CLMS_REPORT_CUSTOMER rc
        where rc.REPORT_NO = #{reportNo}
        limit 1
    </select>

    <select id="getReportCustomerInfo" resultMap="resultInsuredPerson">
        SELECT ip.NAME,
        ip.CERTIFICATE_NO,
        ip.CERTIFICATE_TYPE,
        IP.CLIENT_NO,
        ip.sex_code,
        (select t.reporter_call_no from clm_report_info t where t.report_no = ip.report_no limit 1)
        REPORTER_MOBILE
        FROM CLMS_REPORT_CUSTOMER ip
        WHERE ip.report_no = #{reportNo}
        limit 1
    </select>

    <select id="getReportCustomerByPolicy" resultMap="resultInsuredPerson">
        SELECT t.NAME,
        t.CLIENT_NO,
        decode(t.SEX_CODE, 'M', '0', 'F' ,'1', '2') SEX_CODE,
        to_char(t.BIRTHDAY, 'yyyymmdd') BIRTHDAY,
        t.CERTIFICATE_NO,
        decode(t.CERTIFICATE_TYPE, '01', '01', '02' ,'07', '03', '04', '06', '09', '99') CERTIFICATE_TYPE
        FROM CLMS_REPORT_CUSTOMER ip, CLMS_INSURED_PERSON t
        WHERE t.id_ahcs_policy_info = #{idPolicyInfo,jdbcType=VARCHAR}
        and (t.BIRTHDAY is not null or t.CERTIFICATE_NO is not null)
        and ((ip.CERTIFICATE_NO = t.CERTIFICATE_NO and t.NAME=ip.NAME) or ip.CLIENT_NO=t.CLIENT_NO)
        limit 1
    </select>

</mapper>
