<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ExReportRemarkMapper">
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ExReportRemark">
		<id column="ID_REPORT_TRANS" property="idReportTrans" jdbcType="VARCHAR" />
		<result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
		<result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP" />
		<result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR" />
		<result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP" />
		<result column="ID_REPORT_EXC" property="idReportExc" jdbcType="VARCHAR" />
		<result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR" />
		<result column="OPERATOR" property="operator" jdbcType="VARCHAR" />
		<result column="SEND_TIME" property="sendTime" jdbcType="VARCHAR" />
		<result column="REMARK" property="remark" jdbcType="VARCHAR" />
	</resultMap>

	<sql id="Base_Column_List">
		ID_REPORT_TRANS,CREATED_BY,CREATED_DATE,UPDATED_BY,UPDATED_DATE,ID_REPORT_EXC,REPORT_NO,OPERATOR,SEND_TIME,REMARK
	</sql>

	<select id="getExReportRemarkList" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from REPORT_EXC_TRANS
		where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		order by SEND_TIME asc
	</select>

	<insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ExReportRemark">
		insert into REPORT_EXC_TRANS (
		ID_REPORT_TRANS, 
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY, 
		UPDATED_DATE, 
		ID_REPORT_EXC, 
		REPORT_NO,
		OPERATOR,
		SEND_TIME,
		REMARK)
		values (
		#{idReportTrans,jdbcType=VARCHAR},
		#{createdBy,jdbcType=VARCHAR},
		sysdate(),
		#{updatedBy,jdbcType=VARCHAR},
		sysdate(),
		#{idReportExc,jdbcType=VARCHAR},
		#{reportNo,jdbcType=VARCHAR},
		#{operator,jdbcType=VARCHAR},
		sysdate(),
		#{remark,jdbcType=VARCHAR})
	</insert>

</mapper>