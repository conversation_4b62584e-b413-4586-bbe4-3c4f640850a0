<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.BatchReportEntityMapper" >
  <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.report.BatchReportEntity" >
    <id column="ID_AHCS_BATCH_REPORT" property="idAhcsBatchReport" jdbcType="VARCHAR" />
	<result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
    <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR" />
    <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP" />
    <result column="IMPORT_DATE" property="importDate" jdbcType="TIMESTAMP" />
    <result column="IMPORT_USER_ID" property="importUserId" jdbcType="VARCHAR" />
    <result column="COMMIT_DATE" property="commitDate" jdbcType="TIMESTAMP" />
    <result column="COMMIT_USER_ID" property="commitUserId" jdbcType="VARCHAR" />
    <result column="REVOKE_DATE" property="revokeDate" jdbcType="TIMESTAMP" />
    <result column="REVOKE_USER_ID" property="revokeUserId" jdbcType="VARCHAR" />
    <result column="BATCH_REPORT_TYPE" property="batchReportType" jdbcType="VARCHAR" />
    <result column="REPORT_BATCH_NO" property="reportBatchNo" jdbcType="VARCHAR" />
    <result column="BATCH_STATUS" property="batchStatus" jdbcType="VARCHAR" />
    <result column="FILE_MD5" property="fileMd5" jdbcType="VARCHAR" />
    <result column="IS_MERGE" property="isMerge" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    ID_AHCS_BATCH_REPORT, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, IMPORT_DATE, 
    IMPORT_USER_ID, COMMIT_DATE, COMMIT_USER_ID, REVOKE_DATE, REVOKE_USER_ID, BATCH_REPORT_TYPE, 
    REPORT_BATCH_NO, BATCH_STATUS, FILE_MD5, IS_MERGE
  </sql>

  <update id="revokeBatchReport" parameterType="java.lang.String" >
    update CLMS_batch_report t set t.batch_status = '2' where t.report_batch_no = #{reportBatchNo,jdbcType=VARCHAR}
  </update>

  <insert id="insertSelective" parameterType="com.paic.ncbs.claim.model.dto.report.BatchReportEntity" >
    insert into CLMS_BATCH_REPORT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      ID_AHCS_BATCH_REPORT,
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      CREATED_DATE,
      <if test="updatedBy != null" >
        UPDATED_BY,
      </if>
      UPDATED_DATE,
      IMPORT_DATE,
      <if test="importUserId != null" >
        IMPORT_USER_ID,
      </if>
      <if test="commitDate != null" >
        COMMIT_DATE,
      </if>
      <if test="commitUserId != null" >
        COMMIT_USER_ID,
      </if>
      <if test="revokeDate != null" >
        REVOKE_DATE,
      </if>
      <if test="revokeUserId != null" >
        REVOKE_USER_ID,
      </if>
      <if test="batchReportType != null" >
        BATCH_REPORT_TYPE,
      </if>
      <if test="reportBatchNo != null" >
        REPORT_BATCH_NO,
      </if>
      <if test="batchStatus != null" >
        BATCH_STATUS,
      </if>
      <if test="fileMd5 != null" >
        FILE_MD5,
      </if>
      <if test="isMerge != null" >
        IS_MERGE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      replace(uuid(),'-',''),
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      now(),
      <if test="updatedBy != null" >
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      now(),
      now(),
      <if test="importUserId != null" >
        #{importUserId,jdbcType=VARCHAR},
      </if>
      <if test="commitDate != null" >
        #{commitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="commitUserId != null" >
        #{commitUserId,jdbcType=VARCHAR},
      </if>
      <if test="revokeDate != null" >
        #{revokeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="revokeUserId != null" >
        #{revokeUserId,jdbcType=VARCHAR},
      </if>
      <if test="batchReportType != null" >
        #{batchReportType,jdbcType=VARCHAR},
      </if>
      <if test="reportBatchNo != null" >
        #{reportBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="batchStatus != null" >
        #{batchStatus,jdbcType=VARCHAR},
      </if>
      <if test="fileMd5 != null" >
        #{fileMd5,jdbcType=VARCHAR},
      </if>
      <if test="isMerge != null" >
        #{isMerge,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="queryIsMergeByReportBatchNo" parameterType="java.lang.String" resultType="java.lang.String">
    select t.IS_MERGE from CLMS_batch_report t where t.report_batch_no = #{reportBatchNo,jdbcType=VARCHAR}
  </select>

</mapper>