<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.PolicyMapper">

	<select id="getPolicyList" parameterType="com.paic.ncbs.claim.model.vo.report.PolicyQueryVO" resultType="com.paic.ncbs.claim.model.dto.ocas.OcasPolicyDTO">
		select
		p.policy_no policyNo,
		p.department_code departmentCode,
		(select DEPARTMENT_ABBR_NAME from department_define d where d.department_code=p.department_code limit 0,1)
		departmentName,
		(SELECT CHANNEL_SOURCE_NAME FROM channel_source_define r,ply_sale s WHERE
		r.CHANNEL_SOURCE_CODE=s.CHANNEL_SOURCE_CODE AND s.POLICY_NO=p.policy_no LIMIT 0,1) channelSourceName,
		p.insurance_begin_date insuranceBeginDate,
		p.insurance_end_date insuranceEndDate,
		p.status status,
		p.product_code productCode,
		x.name applicantName,
		(select marketproduct_name from marketproduct_info m where m.marketproduct_code=p.product_code order by
		CREATED_DATE desc limit 0,1) productName,
		rg.risk_group_name riskGroupName,
		pe.prosecution_period prosecutionPeriod,
		pe.extend_report_date extendReportDate,
		(select performance_attribution_code from ply_sale where POLICY_NO=p.policy_no) performanceAttributionCode,
		(select dd.DEPARTMENT_ABBR_NAME from ply_sale ps,department_define dd where ps.POLICY_NO=p.policy_no and
		ps.performance_attribution_code=dd.department_code limit 0,1) performanceAttributionName

		from ply_base_info p
		inner join ply_risk_group rg
		on p.policy_no = rg.policy_no
		inner join ply_extend pe
		on p.policy_no = pe.policy_no and p.ID_PLY_BASE_INFO = pe.ID_PLY_BASE_INFO
		inner join (
		select
		b.policy_no policy_no,
		c.name name,
		min(prg.risk_group_no) risk_group_no
		from ply_risk_person a, ply_base_info b , ply_applicant_info c, ply_risk_group prg
		where a.policy_no = b.policy_no and b.policy_no = c.policy_no
		and prg.id_ply_risk_group = a.id_ply_risk_group
		<if test="departmentCodes != null">
			and b.department_code in
			<foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="person">
			and (a.certificate_type in
			<foreach collection="insuredCertificateTypes" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
			or a.certificate_type is null or a.certificate_type = '')
		</if>
		<if test="!person">
			and a.certificate_type in
			<foreach collection="insuredCertificateTypes" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="certificateType != null and certificateType != '' ">
			and a.certificate_type = #{certificateType,jdbcType=VARCHAR}
		</if>
		<if test="certificateNo != null and certificateNo != '' ">
			and a.certificate_no = #{certificateNo,jdbcType=VARCHAR}
		</if>
		<if test="insuredName != null and insuredName != '' ">
			and a.name = #{insuredName,jdbcType=VARCHAR}
		</if>
		<!--				<if test="accidentDate != null ">-->
		<!--					and b.insurance_end_date >= #{accidentDate,jdbcType=TIMESTAMP} and b.insurance_begin_date <![CDATA[<=]]> #{accidentDate,jdbcType=TIMESTAMP}-->
		<!--				</if>				-->
		<if test="policyNo != null and policyNo != '' ">
			and a.policy_no = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="applicantName != null and applicantName != '' ">
			and c.name = #{applicantName,jdbcType=VARCHAR}
		</if>
		<if test="staffCertificateNo != null and staffCertificateNo != '' and staffCertificateType != null and staffCertificateType != '' ">
			and exists (select policy_no from ply_risk_propsub_group prgp where prgp.policy_no = a.policy_no and
			prgp.certificate_no = #{staffCertificateNo,jdbcType=VARCHAR} and prgp.certificate_type =
			#{staffCertificateType,jdbcType=VARCHAR})
		</if>
		group by b.policy_no
		) x on p.policy_no = x.policy_no and rg.risk_group_no = x.risk_group_no
		order by p.insurance_end_date desc
	</select>

	<select id="getPolicyInsuredList" parameterType="com.paic.ncbs.claim.model.vo.report.PolicyQueryVO"	resultType="com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO">
		select 
			   p.id_ply_risk_person    idPlyRiskPerson,
			   p.policy_no			   policyNo,
			   p.certificate_no        certificateNo,
			   p.certificate_type      certificateType,
			   p.name				   insuredName,
			   p.personnel_attribute   personnelAttribute,
			   p.birthday   		   birthday,
	 	       p.mobile_telephone      mobileTelephone,
			   (select value_chinese_name from acss_parameter t where collection_code ='ZK_ZYLX2022C' and t.value_code = p.profession_code  limit 0,1) profession
		from ply_risk_person p
		inner join ply_risk_group rg
		on p.id_ply_risk_group = rg.id_ply_risk_group 
		inner join (		
			select 				
				a.policy_no,
				a.name,
				a.certificate_type,				
				a.certificate_no,
	 			min(prg.risk_group_no) risk_group_no	
				from ply_risk_person a, ply_base_info c, ply_risk_group prg
				where a.policy_no = c.policy_no and prg.id_ply_risk_group = a.id_ply_risk_group
			<if test="person">
					and (a.certificate_type in
					<foreach collection="insuredCertificateTypes" item="item" open="(" close=")" separator=",">
						#{item}
					</foreach>	
						or a.certificate_type is null or a.certificate_type = '')
			</if>
			<if test="!person">
					and a.certificate_type in
					<foreach collection="insuredCertificateTypes" item="item" open="(" close=")" separator=",">
						#{item}
					</foreach>	
			</if>			
			<if test="certificateType != null and certificateType != '' ">
				and a.certificate_type = #{certificateType,jdbcType=VARCHAR}
			</if>	
			<if test="certificateNo != null and certificateNo != '' ">
				and a.certificate_no = #{certificateNo,jdbcType=VARCHAR}
			</if>
			<if test="insuredName != null and insuredName != '' " >
				and a.name = #{insuredName,jdbcType=VARCHAR}
			</if>
			<if test="policyNoList != null">
				and a.policy_no in
				<foreach collection="policyNoList" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="departmentCodes != null and departmentCodes != '' ">
				and c.department_code in
				<foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			group by a.policy_no, a.name, a.certificate_type, a.certificate_no
		) x
		on p.policy_no = x.policy_no and rg.risk_group_no = x.risk_group_no	and p.name = x.name
		<if test="!person">
		  and p.certificate_type = x.certificate_type and p.certificate_no = x.certificate_no
		</if>
		order by p.name 
	</select>
	
	<select id="getPolicyRiskSubPropList" parameterType="com.paic.ncbs.claim.model.vo.report.PolicyQueryVO"	resultType="com.paic.ncbs.claim.model.dto.report.PolicyRiskSubPropDTO">
		select 
			a.policy_no 		policyNo,
			a.name      		name,
			a.certificate_type  certificateType,
			a.certificate_no	certificateNo,
			a.birthday			birthday,
			a.sex				sex,
			a.age				age,
			a.subject_detail	subjectDetail,
			c.risk_group_no		riskGroupNo,
			c.risk_group_name 	riskGroupName
		from ply_risk_propsub_group a, ply_risk_property b, ply_risk_group c
		where a.id_ply_risk_property  = b.id_ply_risk_property and b.id_ply_risk_group = c.id_ply_risk_group
		<if test="certificateType != null and certificateType != '' ">
			and a.certificate_type = #{certificateType,jdbcType=VARCHAR}
		</if>	
		<if test="certificateNo != null and certificateNo != '' ">
			and a.certificate_no = #{certificateNo,jdbcType=VARCHAR}
		</if>
		<if test="riskSubPropName != null and riskSubPropName != '' " >
			and a.name = #{riskSubPropName,jdbcType=VARCHAR}
		</if>
		<if test="policyNo != null and policyNo != '' ">
			and a.policy_no = #{policyNo,jdbcType=VARCHAR}
		</if>
	</select>

	<select id="querySurrender" parameterType="java.lang.String" resultType="java.util.Date">
		SELECT
		d.EFFECTIVE_DATE
		FROM
		edr_apply_base_info d
		WHERE
		d.policy_no = #{policyNo}
		and d.DOCUMENT_STATUS='08'
		and d.SCENE_LIST ='20032'
		order by d.CREATED_DATE desc
		limit 1
	</select>

</mapper>