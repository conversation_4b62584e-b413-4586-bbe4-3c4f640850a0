<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportAccidentLossMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportAccidentLossEntity">
        <id column="ID_AHCS_REPORT_ACCIDENT_LOSS" property="idAhcsReportAccidentLoss" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="LOSS_TYPE" property="lossType" jdbcType="VARCHAR"/>
        <result column="COST_ESTIMATE" property="costEstimate" jdbcType="DECIMAL"/>
        <result column="ACCIDENT_REASON" property="accidentReason" jdbcType="VARCHAR"/>
        <result column="ACCIDENT_REASON_DESC" property="accidentReasonDesc" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_REPORT_ACCIDENT_LOSS, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, LOSS_TYPE, COST_ESTIMATE, ACCIDENT_REASON, ACCIDENT_REASON_DESC
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_LOSS
        where ID_AHCS_REPORT_ACCIDENT_LOSS = #{idAhcsReportAccidentLoss,jdbcType=VARCHAR}
    </select>

    <select id="getReportAccidentLossByReportNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_ACCIDENT_LOSS
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_REPORT_ACCIDENT_LOSS
        where ID_AHCS_REPORT_ACCIDENT_LOSS = #{idAhcsReportAccidentLoss,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentLossEntity">
        insert into CLMS_REPORT_ACCIDENT_LOSS (ID_AHCS_REPORT_ACCIDENT_LOSS, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        REPORT_NO, LOSS_TYPE, COST_ESTIMATE, ACCIDENT_REASON, ACCIDENT_REASON_DESC
        )
        values (#{idAhcsReportAccidentLoss,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{reportNo,jdbcType=VARCHAR}, #{lossType,jdbcType=VARCHAR}, #{costEstimate,jdbcType=DECIMAL},
        #{accidentReason,jdbcType=VARCHAR}, #{accidentReasonDesc,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentLossEntity">
        insert into CLMS_REPORT_ACCIDENT_LOSS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentLoss != null">
                ID_AHCS_REPORT_ACCIDENT_LOSS,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="reportNo != null">
                REPORT_NO,
            </if>
            <if test="lossType != null">
                LOSS_TYPE,
            </if>
            <if test="costEstimate != null">
                COST_ESTIMATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsReportAccidentLoss != null">
                #{idAhcsReportAccidentLoss,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="lossType != null">
                #{lossType,jdbcType=VARCHAR},
            </if>
            <if test="costEstimate != null">
                #{costEstimate,jdbcType=DECIMAL},
            </if>
            <if test="accidentReason != null">
                #{accidentReason,jdbcType=DECIMAL},
            </if>
            <if test="accidentReasonDesc != null">
                #{accidentReasonDesc,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentLossEntity">
        update CLMS_REPORT_ACCIDENT_LOSS
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="lossType != null">
                LOSS_TYPE = #{lossType,jdbcType=VARCHAR},
            </if>
            <if test="costEstimate != null">
                COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL},
            </if>
            <if test="accidentReason != null">
                ACCIDENT_REASON = #{accidentReason,jdbcType=DECIMAL},
            </if>
            <if test="accidentReasonDesc != null">
                ACCIDENT_REASON_DESC = #{accidentReasonDesc,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_AHCS_REPORT_ACCIDENT_LOSS = #{idAhcsReportAccidentLoss,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentLossEntity">
        update CLMS_REPORT_ACCIDENT_LOSS
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        LOSS_TYPE = #{lossType,jdbcType=VARCHAR},
        COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL},
        ACCIDENT_REASON = #{accidentReason,jdbcType=DECIMAL},
        ACCIDENT_REASON_DESC = #{accidentReasonDesc,jdbcType=VARCHAR}
        where ID_AHCS_REPORT_ACCIDENT_LOSS = #{idAhcsReportAccidentLoss,jdbcType=VARCHAR}
    </update>
</mapper>