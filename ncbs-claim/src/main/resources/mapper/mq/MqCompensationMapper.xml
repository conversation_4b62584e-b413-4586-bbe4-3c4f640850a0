<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.mqcompensation.MqCompensationMapper">

	<select id="getFailureRecordList" resultType="com.paic.ncbs.claim.dao.entity.mq.MqMessageRecordEntity">
		select
			id,
			topic,
			message_id,
			message_key,
			message_body,
			resend_count,
			send_status
		from mq_message_record
		where system_code = 'claim' and send_status = '2' and is_deleted = '0' limit 10
	</select>

	<update id="updateMqSendStatus" parameterType="com.paic.ncbs.claim.dao.entity.mq.MqMessageRecordEntity">
		UPDATE mq_message_record
		SET UPDATED_DATE = now(),
		UPDATED_BY = #{mqMessageRecordEntity.updatedBy,jdbcType=VARCHAR},
		send_status = #{mqMessageRecordEntity.sendStatus,jdbcType=NUMERIC},
		resend_count = #{mqMessageRecordEntity.resendCount,jdbcType=NUMERIC}
		WHERE id = #{mqMessageRecordEntity.id,jdbcType=NUMERIC}
	</update>

	<insert id="addEntity" parameterType="com.paic.ncbs.claim.dao.entity.mq.MqMessageRecordEntity">
		INSERT INTO mq_message_record
		(CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		topic,
		system_code,
		message_body,
		send_status,
		resend_count
		)VALUES(
		#{mqMessageRecordEntity.createdBy,jdbcType=VARCHAR},
		sysdate(),
		#{mqMessageRecordEntity.updatedBy,jdbcType=VARCHAR},
		sysdate(),
		#{mqMessageRecordEntity.topic,jdbcType=VARCHAR},
		#{mqMessageRecordEntity.systemCode,jdbcType=VARCHAR},
		#{mqMessageRecordEntity.messageBody,jdbcType=VARCHAR},
		#{mqMessageRecordEntity.sendStatus,jdbcType=NUMERIC} ,
		#{mqMessageRecordEntity.resendCount,jdbcType=NUMERIC}
		)
	</insert>



</mapper>