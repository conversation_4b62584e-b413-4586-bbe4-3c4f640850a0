<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.print.PrintTemplateMapper">
	
	<resultMap type="com.paic.ncbs.claim.model.dto.print.PrintTemplateDTO" id="printTemplateInfo">
		<result column="print_template_code" property="printTemplateCode" />
		<result column="print_template_name" property="printTemplateName" />
		<result column="print_template_type" property="printTemplateType" />
		<result column="print_mode" property="printMode" />
		<result column="display_no" property="displayNo" />
		<result column="print_status" property="printStatus" />
		<result column="sys_ctime" property="sysCtime" />
		<result column="sys_utime" property="sysUtime" />
		<result column="keyword" property="keyword" />
		<result column="print_template_value" property="printTemplateValue" />
		<result column="print_parameter_json" property="printParameterJson" />
	</resultMap>

	<select id="getPrintTemplate" resultMap="printTemplateInfo">
		SELECT t.print_template_value,
				t.print_mode,
				keyword
		FROM print_template t
		WHERE
		t.print_template_code = #{printTemplateCode,jdbcType=VARCHAR}
		AND t.print_template_type = #{printTemplateType,jdbcType=NUMERIC}
	</select>
</mapper>