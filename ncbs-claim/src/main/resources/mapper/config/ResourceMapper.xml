<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.config.ResourceMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.config.ResourceDTO" id="ResourceMap">
		<id property="idAhcsResource" column="ID_AHCS_RESOURCE" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="resourceCode" column="RESOURCE_CODE" />
		<result property="resourceName" column="RESOURCE_NAME" />
		<result property="remark" column="REMARK" />
	</resultMap>


	<update id="modifyResource" parameterType="com.paic.ncbs.claim.model.dto.config.ResourceDTO">
		UPDATE CLMS_RESOURCE
		<set>
			<if test="createdBy != null and createdBy != '' ">
				CREATED_BY  = #{createdBy}, 
			</if>
			<if test="createdDate != null ">
				CREATED_DATE  = #{createdDate}, 
			</if>
			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy}, 
			</if>
			UPDATED_DATE=sysdate,
			<if test="resourceCode != null and resourceCode != '' ">
				RESOURCE_CODE  = #{resourceCode}, 
			</if>
			<if test="resourceName != null and resourceName != '' ">
				RESOURCE_NAME  = #{resourceName}, 
			</if>
			<if test="remark != null and remark != '' ">
				REMARK  = #{remark}, 
			</if>
		</set>
		WHERE ID_AHCS_RESOURCE=#{idAhcsResource} 
	</update>
	
		

	<delete id="removeResourceById" parameterType="String">
		DELETE FROM CLMS_RESOURCE
		where  ID_AHCS_RESOURCE=#{idAhcsResource} 
	</delete>


	<select id="getResourceList" parameterType="java.lang.String" resultMap="ResourceMap">
		select 
		ID_AHCS_RESOURCE,
		RESOURCE_CODE,
		RESOURCE_NAME
		from CLMS_RESOURCE
		where RESOURCE_TYPE = #{resourceType}
	</select>
</mapper>