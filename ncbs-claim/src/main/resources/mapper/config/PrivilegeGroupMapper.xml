<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.config.PrivilegeGroupMapper">

	<resultMap type="com.paic.ncbs.claim.model.vo.user.ResourceDetailValueVO" id="ResourceDetailValueVO">
		<result property="userPrivGroupRelId" column="id_ahcs_user_priv_group_rel" />
		<result property="privilegeGroupId" column="id_ahcs_privilege_group" />
		<result property="resourceDetailValueId" column="id_ahcs_resource_detail_value" />
		<result property="userId" column="user_id" />
		<result property="privilegeGroupName" column="privilege_group_name" />
		<result property="remark" column="remark" />
		<result property="sortNo" column="sort_no" />
		<result property="privilegeType" column="privilege_type" />
		<result property="departmentCode" column="department_code" />
		<result property="resourceDetailCode" column="resource_detail_code" />
		<result property="resourceDetailType" column="resource_detail_type" />
		<result property="resourceDetailValue" column="resource_detail_value" />
		<result property="resourceDetailMinValue" column="resource_detail_min_value" />
		<result property="resourceDetailMaxValue" column="resource_detail_max_value" />
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.config.PrivilegeGroupDTO" id="PrivilegeGroupVOMap">
		<id property="idAhcsPrivilegeGroup" column="ID_AHCS_PRIVILEGE_GROUP" />
		<result property="privilegeGroupName" column="PRIVILEGE_GROUP_NAME" />
		<result property="sortNo" column="privilegeGroupSortNo" />
		<result property="privilegeType" column="privilege_type" />
		<result property="caseType" column="CASE_TYPE" />
		<collection property="resourceDetailValueList"
					ofType="com.paic.ncbs.claim.model.dto.config.ResourceDetailValueDTO">
			<id property="idAhcsResourceDetailValue" column="ID_AHCS_RESOURCE_DETAIL_VALUE" />
			<result property="idPrivilegeGroup" column="ID_PRIVILEGE_GROUP" />
			<result property="resourceDetailCode" column="RESOURCE_DETAIL_CODE" />
			<result property="resourceDetailType" column="RESOURCE_DETAIL_TYPE" />
			<result property="resourceDetailValue" column="RESOURCE_DETAIL_VALUE" />
			<result property="resourceDetailValueDesc" column="RESOURCE_DETAIL_VALUE_DESC" />
			<result property="resourceDetailMinValue" column="RESOURCE_DETAIL_MIN_VALUE" />
			<result property="resourceDetailMaxValue" column="RESOURCE_DETAIL_MAX_VALUE" />
			<result property="sortNo" column="resourceDetailSortNo" />
			<result property="resourceDetailName" column="RESOURCE_DETAIL_NAME" />
		</collection>
	</resultMap>
	

	<select id="getByResCodeAndDepCode" resultMap="PrivilegeGroupVOMap">
        select distinct rd.resource_detail_name,
        rd.sort_no resourceDetailSortNo,
        dv.id_ahcs_resource_detail_value,
        dv.id_privilege_group,
        dv.resource_detail_type,
        dv.resource_detail_value,
        dv.resource_detail_value_desc,
        dv.resource_detail_code,
        dv.resource_detail_min_value,
        dv.resource_detail_max_value,
        pg.privilege_group_name privilege_group_name,
        pg.privilege_type privilege_type,
        pg.sort_no privilegeGroupSortNo,
        pg.id_ahcs_privilege_group id_ahcs_privilege_group
        from CLMS_resource_detail_value dv
        inner join CLMS_resource_detail rd on dv.resource_detail_code = rd.resource_detail_code
        left join CLMS_privilege_group pg on dv.id_privilege_group = pg.id_ahcs_privilege_group
        where dv.department_code = #{departmentCode,jdbcType=VARCHAR}

        <if test='departmentCode != "2" '>
            and pg.privilege_type != '4'
        </if>

        and rd.resource_detail_code in
        (select rd.resource_detail_code from CLMS_resource_detail rd, CLMS_resource r
        where rd.resource_code = r.resource_code and r.resource_code = #{resourceCode,jdbcType=VARCHAR} )
        order by privilegeGroupSortNo desc
	</select>


	<insert id="addPrivilegeGroup" parameterType="com.paic.ncbs.claim.model.dto.config.PrivilegeGroupDTO">
		INSERT INTO CLMS_PRIVILEGE_GROUP (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_PRIVILEGE_GROUP,
			PRIVILEGE_GROUP_NAME,
			REMARK,
			SORT_NO
		) VALUES (
			#{createdBy,jdbcType=VARCHAR},
			now(),
			#{updatedBy,jdbcType=VARCHAR},
			now(),
			#{idAhcsPrivilegeGroup,jdbcType=VARCHAR},
			#{privilegeGroupName,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR},
			#{sortNo,jdbcType=VARCHAR}
		)
	</insert>


	<insert id="addPrivilegeGroupList" parameterType="java.util.List">
		<foreach collection="paramList" index="index" item="item"
			open="begin" separator=";" close=";end;">
			INSERT INTO CLMS_PRIVILEGE_GROUP (
				CREATED_BY,
				CREATED_DATE,
				UPDATED_BY,
				UPDATED_DATE,
				ID_AHCS_PRIVILEGE_GROUP,
				PRIVILEGE_GROUP_NAME,
				REMARK,
				SORT_NO
			) VALUES (
				#{item.createdBy,jdbcType=VARCHAR},
				now(),
				#{item.updatedBy,jdbcType=VARCHAR},
				now(),
				#{item.idAhcsPrivilegeGroup,jdbcType=VARCHAR},
				#{item.privilegeGroupName,jdbcType=VARCHAR},
				#{item.remark,jdbcType=VARCHAR},
				#{item.sortNo,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<select id="getPrivilegeGroupByUid" resultMap="PrivilegeGroupVOMap">
		select distinct
		PG.ID_AHCS_PRIVILEGE_GROUP,
		PG.PRIVILEGE_GROUP_NAME,
		PG.SORT_NO privilegeGroupSortNo,
		RDV.ID_AHCS_RESOURCE_DETAIL_VALUE,
		RDV.RESOURCE_DETAIL_MAX_VALUE,
		RDV.RESOURCE_DETAIL_VALUE,
		PG.PRIVILEGE_TYPE
		from CLMS_USER_PRIV_GROUP_REL UPR,
		CLMS_RESOURCE_DETAIL_VALUE RDV,
		CLMS_PRIVILEGE_GROUP PG
		where UPR.USER_ID = #{userPrivRel.userId}
		and UPR.ID_PRIVILEGE_GROUP = RDV.ID_PRIVILEGE_GROUP
		and PG.ID_AHCS_PRIVILEGE_GROUP = UPR.ID_PRIVILEGE_GROUP
		and RDV.DEPARTMENT_CODE = #{deptCode}
		and RDV.RESOURCE_DETAIL_CODE = #{resourceDetailCode}
		and UPR.RESOURCE_CODE = #{userPrivRel.resourceCode}
		and UPR.CASE_TYPE = #{userPrivRel.caseType}

		<if test='deptCode != "2" '>
			AND PG.PRIVILEGE_TYPE != '4'
		</if>

		order by PG.SORT_NO desc
	</select>


<!--	<insert id="addUserPrivilegeGroupList" parameterType="java.util.List">
		<foreach collection="paramList" index="index" item="item"
			open="begin" separator=";" close=";end;">
			INSERT INTO CLMS_USER_PRIV_GROUP_REL (
				CREATED_BY,
				CREATED_DATE,
				UPDATED_BY,
				UPDATED_DATE,
				ID_PRIVILEGE_GROUP,
				RESOURCE_CODE,
				CASE_TYPE,
				USER_ID
			) VALUES (
				#{item.createdBy},
				now(),
				#{item.updatedBy},
				now(),
				#{item.idPrivilegeGroup},
				#{item.resourceCode},
				#{item.caseType},
				#{item.userId}
			)
		</foreach>
	</insert>-->

	<select id="getPrivilegeGroupsList" resultType="java.lang.String">
		select distinct
			PG.PRIVILEGE_GROUP_NAME
		from CLMS_USER_PRIV_GROUP_REL UPR,
		CLMS_PRIVILEGE_GROUP PG
		where UPR.USER_ID in
		<foreach collection="list" index="index" item="item" open="("
			separator="," close=")">
			#{item}
		</foreach>
		and PG.ID_AHCS_PRIVILEGE_GROUP = UPR.ID_PRIVILEGE_GROUP
		and UPR.RESOURCE_CODE = #{resourceCode}
	</select>


	<delete id="removePrivilegeGroupByUserId">
		DELETE FROM CLMS_USER_PRIV_GROUP_REL
		WHERE   USER_ID = #{userId} 
				and CASE_TYPE in 
				 <foreach item="caseType" index="index" collection="caseTypeList" open="(" separator="," close=")">
						#{caseType} 
				  </foreach>
				and RESOURCE_CODE = #{resourceCode}
	</delete>
	

	<delete id="deleteUserPrivilegeRelationList">
		DELETE FROM CLMS_USER_PRIV_GROUP_REL
		WHERE	
			USER_ID in 
		  <foreach item="userId" index="index" collection="deleteUserIdList" open="(" separator="," close=")">
				#{userId} 
		  </foreach>
		AND
			ID_PRIVILEGE_GROUP in
		 <foreach item="idPrivilegeGroup" index="index" collection="deleteRDValueIdList" open="(" separator="," close=")">
				#{idPrivilegeGroup} 
		 </foreach>
	</delete>

<!--	<select id="getPrivilegeGroupDTOList" parameterType="String" resultMap="PrivilegeGroupMap">-->
<!--		select pg.* from CLMS_user_priv_group_rel up , CLMS_privilege_group pg-->
<!--		where up.id_privilege_group = pg.id_ahcs_privilege_group-->
<!--		and up.user_id=#{userId} -->
<!--	</select>-->

	<select id="getIsPrivilege" resultType="Integer">
		 select
		 		 count(1) 
		 from
			 CLMS_user_priv_group_rel t
		 where 
		 		t.case_type=#{caseType}
		 		and t.user_id=#{userId}
	</select>
	
	<select id="getResourceDetailValueVOListByDepartCode" resultMap="ResourceDetailValueVO">
		select t2.id_ahcs_privilege_group,
	           t3.id_ahcs_resource_detail_value,
	           t2.privilege_group_name,
	           t2.remark,
	           t2.sort_no,
	           t2.privilege_type,
	           t3.department_code,
	           t3.resource_detail_code,
	           t3.resource_detail_type,
	           t3.resource_detail_value,
	           t3.resource_detail_min_value,
	           t3.resource_detail_max_value
	      from CLMS_privilege_group t2, CLMS_resource_detail_value t3 ,CLMS_RESOURCE_DETAIL t4
	      where t2.id_ahcs_privilege_group = t3.id_privilege_group
		  and t3.RESOURCE_DETAIL_CODE = t4.RESOURCE_DETAIL_CODE
		  and t3.department_code=#{departmentCode}
	      and t2.privilege_type in    
	    <foreach item="item" index="index" collection="insuredApplyStatusList" open="(" separator="," close=")">
	   		#{item}
	    </foreach>
		<if test="resourceDetailCode != null and resourceDetailCode !='' ">
		    and t3.resource_detail_code = #{resourceDetailCode}
		</if>
	   <if test="policyPayTotal != null">
		   <![CDATA[
			   and (
			   		(t3.resource_detail_min_value*10000 < #{policyPayTotal,jdbcType = NUMERIC} and t3.resource_detail_max_value*10000 >= #{policyPayTotal,jdbcType = NUMERIC}) 
			   		or (t3.resource_detail_min_value*10000 = #{policyPayTotal,jdbcType = NUMERIC} and t3.resource_detail_max_value*10000 = #{policyPayTotal,jdbcType = NUMERIC})
			   	)
			]]>
	   </if>
	</select>
	
	<select id="isAssigneeOfPrivilegeParam" resultType="String">
		select distinct t1.user_id
 	    from CLMS_user_priv_group_rel t1
		where EXISTS (SELECT 1
          			  FROM  CLMS_privilege_group t2
         			  WHERE t1.id_privilege_group = t2.id_ahcs_privilege_group
           			  and t2.privilege_type in 
           			  <foreach item="item" index="index" collection="insuredApplyStatusList" open="(" separator="," close=")">
						  #{item}
					  </foreach>
		)
		and t1.resource_code = #{resourceCode,jdbcType=VARCHAR}
		<if test="caseType != null and caseType != ''">
				 and t1.case_type = #{caseType,jdbcType=VARCHAR}
			 </if>
   			 and t1.user_id in 
   			 <foreach item="item" index="index" collection="assignees" open="(" separator="," close=")">
	   		 #{item}
	    	 </foreach>
	</select>
	
<!--	<select id="permissionDetailsList" resultMap="PermissionDetailsListMap">-->
<!--		SELECT PI.RESOURCE_CODE as RESOURCE_CODE, PI.CASE_TYPE as CASE_TYPE, WM_CONCAT(PG.PRIVILEGE_GROUP_NAME) as PRIVILEGE_GROUP_NAME-->
<!--		FROM   CLMS_PRIVILEGE_GROUP PG,-->
<!--		       (SELECT WS.RESOURCE_CODE, WS.CASE_TYPE, WS.PRIVILEGE_TYPE, MIN(WS.SORT_NO) SORT_NO-->
<!--		        FROM   (SELECT T.PRIVILEGE_GROUP_NAME,-->
<!--		                       T.SORT_NO,-->
<!--		                       T.PRIVILEGE_TYPE,-->
<!--		                       TS.CASE_TYPE,-->
<!--		                       TS.RESOURCE_CODE,-->
<!--		                       TS.ID_PRIVILEGE_GROUP-->
<!--		                FROM   CLMS_PRIVILEGE_GROUP T, CLMS_USER_PRIV_GROUP_REL TS-->
<!--		                WHERE  T.ID_AHCS_PRIVILEGE_GROUP = TS.ID_PRIVILEGE_GROUP-->
<!--		                AND    TS.USER_ID = #{userId,jdbcType=VARCHAR}-->
<!--		                AND    TS.RESOURCE_CODE IS NOT NULL) WS-->
<!--		        GROUP  BY WS.CASE_TYPE, WS.PRIVILEGE_TYPE, WS.RESOURCE_CODE) PI-->
<!--		WHERE  PI.PRIVILEGE_TYPE = PG.PRIVILEGE_TYPE-->
<!--		AND    PI.SORT_NO = PG.SORT_NO-->
<!--		GROUP  BY PI.RESOURCE_CODE, PI.CASE_TYPE	-->
<!--	</select>-->
	
<!--	<select id="getMaxPrivilegeGroupByUid" resultMap="PrivilegeGroupVOMap">-->
<!--	select t2.ID_PRIVILEGE_GROUP ID_AHCS_PRIVILEGE_GROUP,-->
<!--			t2.PRIVILEGE_GROUP_NAME,-->
<!--			T2.SORT_NO privilegeGroupSortNo,-->
<!--			dv.ID_AHCS_RESOURCE_DETAIL_VALUE,-->
<!--			dv.RESOURCE_DETAIL_MAX_VALUE,-->
<!--		 	dv.RESOURCE_DETAIL_VALUE,-->
<!--		 	t2.PRIVILEGE_TYPE,-->
<!--      		t2.case_type-->
<!--  	 from CLMS_resource_detail_value dv, -->
<!--  	  		( SELECT T.PRIVILEGE_GROUP_NAME,-->
<!--		             TS.ID_PRIVILEGE_GROUP,-->
<!--                     TS.case_type,-->
<!--                     T.SORT_NO,-->
<!--                     T.PRIVILEGE_TYPE,-->
<!--                     ROW_NUMBER() OVER(PARTITION BY TS.case_type,T.PRIVILEGE_TYPE ORDER BY T.SORT_NO ) RN-->
<!--		       FROM  CLMS_PRIVILEGE_GROUP T, -->
<!--		         	 CLMS_USER_PRIV_GROUP_REL TS-->
<!--		       WHERE  T.ID_AHCS_PRIVILEGE_GROUP = TS.ID_PRIVILEGE_GROUP-->
<!--		              AND    TS.USER_ID =  #{userPrivRel.userId}-->
<!--		              AND    TS.RESOURCE_CODE= #{userPrivRel.resourceCode}-->
<!--                      and TS.case_type=#{userPrivRel.caseType}-->
<!--                      ) t2 -->
<!--     where dv.resource_detail_code=#{resourceDetailCode}-->
<!--     	   and dv.department_code=#{deptCode}-->
<!--           and dv.id_privilege_group=t2.ID_PRIVILEGE_GROUP  -->
<!--           and rn=1-->
<!--	</select>-->
	
<!--		<select id="getAssigneeDiffForCase" resultType="com.paic.icore.ahcs.taskdeal.vo.CasePrivilegeGroupVO">-->
<!--			select -->
<!--					t3.USER_ID userId, t3.diffAmount policyPayTotal-->
<!--		    from -->
<!--		    		(select -->
<!--		    				dv.RESOURCE_DETAIL_MAX_VALUE - #{policyPayTotal,jdbcType = NUMERIC}/10000 diffAmount,-->
<!--	              			t2.USER_ID-->
<!--	          		 from CLMS_resource_detail_value dv,-->
<!--			               (SELECT TS.ID_PRIVILEGE_GROUP,-->
<!--			               		   TS.USER_ID,-->
<!--			                       ROW_NUMBER() OVER(PARTITION BY TS.USER_ID, T.Privilege_Type ORDER BY T.SORT_NO) RN-->
<!--			                 FROM CLMS_PRIVILEGE_GROUP T, CLMS_USER_PRIV_GROUP_REL TS-->
<!--			                 WHERE T.ID_AHCS_PRIVILEGE_GROUP = TS.ID_PRIVILEGE_GROUP-->
<!--			                   		AND TS.USER_ID in-->
<!--				                       <foreach item="item" index="index" collection="assignees" open="(" separator="," close=")">-->
<!--								   			#{item}-->
<!--								    	</foreach>-->
<!--			                   		AND TS.RESOURCE_CODE = #{resourceCode,jdbcType=VARCHAR}-->
<!--			                   		and T.Privilege_Type in-->
<!--			                   			  <foreach item="item" index="index" collection="insuredApplyStatusList" open="(" separator="," close=")">-->
<!--										   		#{item}-->
<!--										    </foreach>-->
<!--			                   		and TS.case_type = #{caseType,jdbcType=VARCHAR}) t2-->
<!--	        	   where dv.resource_detail_code =  #{resourceDetailCode,jdbcType=VARCHAR}-->
<!--	           		    and dv.department_code =  #{departmentCode,jdbcType=VARCHAR}-->
<!--	                    and dv.id_privilege_group = t2.ID_PRIVILEGE_GROUP-->
<!--	                    and rn = 1) t3-->
<!--			 where <![CDATA[ t3.diffAmount >= 0]]>-->
<!--	 			   order by t3.diffAmount-->
<!--		</select>-->
		
		<select id="getMaxPrivilegeGroupByUidCase" resultMap="ResourceDetailValueVO">
			select t2.ID_PRIVILEGE_GROUP ID_AHCS_PRIVILEGE_GROUP,
					t2.PRIVILEGE_GROUP_NAME,
					T2.SORT_NO ,
					dv.ID_AHCS_RESOURCE_DETAIL_VALUE,
					dv.RESOURCE_DETAIL_MAX_VALUE,
				 	dv.RESOURCE_DETAIL_VALUE,
				 	t2.PRIVILEGE_TYPE,
		      		t2.case_type,
		      		dv.department_code
		  	 from CLMS_resource_detail_value dv,
		  	  		( SELECT T.PRIVILEGE_GROUP_NAME,
				             TS.ID_PRIVILEGE_GROUP,
		                     TS.case_type,
		                     T.SORT_NO,
		                     T.PRIVILEGE_TYPE,
		                     ROW_NUMBER() OVER(PARTITION BY TS.case_type,T.PRIVILEGE_TYPE ORDER BY T.SORT_NO ) RN
				       FROM  CLMS_PRIVILEGE_GROUP T,
				         	 CLMS_USER_PRIV_GROUP_REL TS
				       WHERE  T.ID_AHCS_PRIVILEGE_GROUP = TS.ID_PRIVILEGE_GROUP
				              AND    TS.USER_ID =  #{userId}
				              AND    TS.RESOURCE_CODE= #{resourceCode}
		                      and TS.case_type=#{caseType}
		                      and T.Privilege_Type in
                   			  <foreach item="item" index="index" collection="insuredApplyStatusList" open="(" separator="," close=")">
							   		#{item}
							    </foreach>
		                      ) t2 
		     where dv.resource_detail_code=#{resourceDetailCode}
		     	   and dv.department_code=#{departmentCode}
		           and dv.id_privilege_group=t2.ID_PRIVILEGE_GROUP  
		           and rn=1
	</select>
	
	<select id="getUserPrivilegeGroupForClear" resultType="String">
			select 
					distinct t.user_id 
			from
				CLMS_user_priv_group_rel t
		    where <![CDATA[ t.updated_date< now()-30 ]]>
	</select>
	

	<delete id="removeBatchPrivilegeGroup">
		DELETE FROM CLMS_USER_PRIV_GROUP_REL
		WHERE  <foreach collection="userIdList" index="index" item="userId" open="(" separator="or" close=")">
		            USER_ID = #{userId}
		       </foreach>
	</delete>
	
	<select id="getAllPrivilegeGroupByUid" resultType="java.util.Map">
		 select 
		 		t2.privilege_group_name,
		 		t1.case_type,
		 		t1.resource_code  
		 from
		CLMS_user_priv_group_rel t1,CLMS_privilege_group t2
		 where   t1.id_privilege_group=t2.id_ahcs_privilege_group
		 		and t1.user_id=#{userId} 
		 		<if test=" caseTypeList != null and caseTypeList.size>0 ">
			 		and t1.case_type in 
					 <foreach item="caseType" index="index" collection="caseTypeList" open="(" separator="," close=")">
							#{caseType} 
					  </foreach>
			    </if>
			    <if test=" resourceCode != null and resourceCode !=''">
					and t1.resource_code = #{resourceCode}
				</if>
	</select>
	

	<select id="getMaxPrivilegeLevel" resultType="Integer">

		SELECT MAX(A.PRIVILEGE_LEVEL) privilegeLevel
		FROM CLMS_PRIVILEGE_GROUP A, CLMS_USER_PRIV_GROUP_REL B, CLMS_RESOURCE C
		WHERE B.ID_PRIVILEGE_GROUP = A.ID_AHCS_PRIVILEGE_GROUP
		AND B.RESOURCE_CODE = C.RESOURCE_CODE
		AND B.USER_ID = #{userId}
		AND C.RESOURCE_TYPE = #{resourceType}
		
	</select>


	<select id="getMinPrivilegeLevel" resultType="Integer">
		
		SELECT MIN(A.PRIVILEGE_LEVEL) privilegeLevel
		FROM CLMS_PRIVILEGE_GROUP A, CLMS_USER_PRIV_GROUP_REL B, CLMS_RESOURCE C
		WHERE B.ID_PRIVILEGE_GROUP = A.ID_AHCS_PRIVILEGE_GROUP
		AND B.RESOURCE_CODE = C.RESOURCE_CODE
		AND B.USER_ID = #{userId}
		AND C.RESOURCE_TYPE = #{resourceType}
		
	</select>


	<select id="getPrivilegeLevelByUserId" resultType="Integer">

		SELECT DISTINCT A.PRIVILEGE_LEVEL privilegeLevel
		FROM CLMS_PRIVILEGE_GROUP A, CLMS_USER_PRIV_GROUP_REL B, CLMS_RESOURCE C
		WHERE B.ID_PRIVILEGE_GROUP = A.ID_AHCS_PRIVILEGE_GROUP
		AND B.RESOURCE_CODE = C.RESOURCE_CODE
		AND B.USER_ID = #{userId}
		AND C.RESOURCE_TYPE = #{resourceType}
		AND B.RESOURCE_CODE = #{resourceCode}

	</select>

	<select id="checkDeptHasCasePrivilege" resultType="Integer">
		select count(1)
		from CLMS_resource_detail_value t1, CLMS_privilege_group t2
		where t1.id_privilege_group = t2.id_ahcs_privilege_group
		and t2.privilege_type in
		<foreach item="item" index="index" collection="insuredApplyStatusList" open="(" separator="," close=")">
			#{item}
		</foreach>
		and t1.department_code = #{departmentCode,jdbcType=VARCHAR}
		and t1.resource_detail_code =  #{resourceDetailCode,jdbcType=VARCHAR}
		<![CDATA[ and t1.resource_detail_max_value >= #{policyPayTotal,jdbcType = NUMERIC}/10000 ]]>
	</select>
	
	<select id="checkDeptHasPrivilege" resultType="Integer">
		select count(1)
		from CLMS_resource_detail_value t1, CLMS_privilege_group t2
		where t1.id_privilege_group = t2.id_ahcs_privilege_group
		and t2.privilege_type in
		<foreach item="item" index="index" collection="insuredApplyStatusList" open="(" separator="," close=")">
			#{item}
		</foreach>
		and t1.department_code = #{departmentCode,jdbcType=VARCHAR}
		and t1.resource_detail_code =  #{resourceDetailCode,jdbcType=VARCHAR}
	</select>
</mapper>