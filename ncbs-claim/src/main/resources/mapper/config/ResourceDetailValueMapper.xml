<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.config.ResourceDetailValueMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.config.ResourceDetailValueDTO" id="ResourceDetailValueMap">
		<id property="idAhcsResourceDetailValue" column="ID_AHCS_RESOURCE_DETAIL_VALUE" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="departmentCode" column="DEPARTMENT_CODE" />
		<result property="idPrivilegeGroup" column="ID_PRIVILEGE_GROUP" />
		<result property="resourceDetailCode" column="RESOURCE_DETAIL_CODE" />
		<result property="resourceDetailType" column="RESOURCE_DETAIL_TYPE" />
		<result property="resourceDetailValue" column="RESOURCE_DETAIL_VALUE" />
		<result property="resourceDetailValueDesc" column="RESOURCE_DETAIL_VALUE_DESC" />
		<result property="resourceDetailMinValue" column="RESOURCE_DETAIL_MIN_VALUE" />
		<result property="resourceDetailMaxValue" column="RESOURCE_DETAIL_MAX_VALUE" />
	</resultMap>
		

	<resultMap type="com.paic.ncbs.claim.model.vo.config.PrivilegeGroupAndResourceVO" id="privilegeGroupAndResourceVO">
		<id property="idAhcsPrivilegeGroup" column="id_privilege_group" />
		<result property="privilegeGroupName" column="privilege_group_name" />
		<result property="privilegeType" column="privilege_type" />
		<result property="sortNo" column="sort_no" />
		<result property="privilegeLevel" column="privilege_level" />
		<result property="resourceDetailType" column="resource_detail_type" />
		<result property="resourceDetailCode" column="resource_detail_code" />
		<result property="resourceDetailValue" column="resource_detail_value" />
		<result property="resourceDetailMaxValue" column="resource_detail_max_value" />
	</resultMap>
	
	<select id="getPrivAndResVOList" parameterType="java.lang.String" resultMap="privilegeGroupAndResourceVO">
        select distinct pg.privilege_group_name privilege_group_name,
                    pg.privilege_type privilege_type,
                    pg.sort_no sort_no,
                    pg.privilege_level privilege_level,
                    dv.id_privilege_group,
                    dv.resource_detail_value,
                    dv.resource_detail_max_value,
                    dv.resource_detail_type,
                    dv.resource_detail_code
                    from CLMS_resource_detail_value dv,clms_privilege_group pg
                    where dv.id_privilege_group = pg.id_ahcs_privilege_group
                    and dv.department_code = #{departmentCode}
                    and dv.resource_detail_code = #{resourceDetailCode}
                    and dv.resource_detail_min_value is not null
                    and dv.resource_detail_max_value is not null
                    <if test='departmentCode != "2" '>
                        and pg.privilege_type != '4'
                    </if>
                    order by sort_no desc
	</select>	


	<insert id="addResourceDetailValueList" parameterType="java.util.List">
		  <foreach collection="paramList" index="index" item="item" open="begin" separator=";" close=";end;"> 
			INSERT INTO CLMS_RESOURCE_DETAIL_VALUE (
				CREATED_BY,
				CREATED_DATE,
				UPDATED_BY,
				UPDATED_DATE,
				ID_AHCS_RESOURCE_DETAIL_VALUE,
				DEPARTMENT_CODE,
				ID_PRIVILEGE_GROUP,
				RESOURCE_DETAIL_CODE,
				RESOURCE_DETAIL_TYPE,
				RESOURCE_DETAIL_VALUE,
				RESOURCE_DETAIL_MIN_VALUE,
				RESOURCE_DETAIL_MAX_VALUE
			)  VALUES (
				#{item.createdBy ,jdbcType=VARCHAR},
				now(),
				#{item.updatedBy ,jdbcType=VARCHAR},
				now(),
			    left(hex(uuid()),32),,
				#{item.departmentCode ,jdbcType=VARCHAR},
				#{item.idPrivilegeGroup ,jdbcType=VARCHAR},
				#{item.resourceDetailCode ,jdbcType=VARCHAR},
				#{item.resourceDetailType ,jdbcType=VARCHAR},
				#{item.resourceDetailValue ,jdbcType=VARCHAR},
				#{item.resourceDetailMinValue ,jdbcType=VARCHAR},
				#{item.resourceDetailMaxValue ,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>
	

	<update id="modifyResourceDetailValueList" parameterType="java.util.List">
		  <foreach collection="paramList" index="index" item="item" open="begin" separator=";" close=";end;">    
			UPDATE CLMS_RESOURCE_DETAIL_VALUE
			SET   UPDATED_BY  = #{item.updatedBy ,jdbcType=VARCHAR},
				  UPDATED_DATE=now(),
				  RESOURCE_DETAIL_VALUE = #{item.resourceDetailValue ,jdbcType=VARCHAR}, 
				  RESOURCE_DETAIL_MIN_VALUE = #{item.resourceDetailMinValue ,jdbcType=VARCHAR}, 
				  RESOURCE_DETAIL_MAX_VALUE = #{item.resourceDetailMaxValue ,jdbcType=VARCHAR}
			WHERE ID_AHCS_RESOURCE_DETAIL_VALUE = #{item.idAhcsResourceDetailValue ,jdbcType=VARCHAR} 
		  </foreach>   
	</update>
	
	
	<delete id="deleteResourceDetailValueList" parameterType="java.util.List">
		DELETE FROM CLMS_RESOURCE_DETAIL_VALUE WHERE ID_AHCS_RESOURCE_DETAIL_VALUE IN
		<foreach item="item" collection="paramList" open="(" separator="," close=")">
			#{item}
		</foreach>  
    </delete>
		

	<delete id="removeResourceDetailValueById" parameterType="String">
		DELETE FROM CLMS_RESOURCE_DETAIL_VALUE
		where  ID_AHCS_RESOURCE_DETAIL_VALUE=#{idAhcsResourceDetailValue} 
	</delete>
	
	<select id="getByResourceDetailCode" resultMap="ResourceDetailValueMap">
		select 
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_RESOURCE_DETAIL_VALUE,
			DEPARTMENT_CODE,
			ID_PRIVILEGE_GROUP,
			RESOURCE_DETAIL_CODE,
			RESOURCE_DETAIL_TYPE,
			RESOURCE_DETAIL_VALUE,
			RESOURCE_DETAIL_VALUE_DESC,
			RESOURCE_DETAIL_MIN_VALUE,
			RESOURCE_DETAIL_MAX_VALUE
		from CLMS_RESOURCE_DETAIL_VALUE
		where  RESOURCE_DETAIL_CODE=#{resourceDetailCode} 
	</select>
	
	<select id="queryAmountPrivRange" resultMap="ResourceDetailValueMap">
        select
        min(RDV.RESOURCE_DETAIL_MIN_VALUE) RESOURCE_DETAIL_MIN_VALUE ,
        max(RDV.RESOURCE_DETAIL_MAX_VALUE) RESOURCE_DETAIL_MAX_VALUE
        from CLMS_USER_PRIV_GROUP_REL UPR,
        CLMS_RESOURCE_DETAIL_VALUE RDV,
        CLMS_PRIVILEGE_GROUP PG
        where UPR.USER_ID =#{auditorUm}
        and UPR.ID_PRIVILEGE_GROUP = RDV.ID_PRIVILEGE_GROUP
        and PG.ID_AHCS_PRIVILEGE_GROUP = UPR.ID_PRIVILEGE_GROUP
        and UPR.RESOURCE_CODE = 'personPay'
        and RDV.DEPARTMENT_CODE =#{departmentCode}
        and UPR.CASE_TYPE = #{caseType}
        and PG.PRIVILEGE_TYPE =#{insuredApplyStatus}
	</select>

    <select id="getPrivMaxValueByUid"  resultType="java.math.BigDecimal">
        select ifnull(max(RDV.RESOURCE_DETAIL_MAX_VALUE),0) resourceDetailMaxValue
        from CLMS_USER_PRIV_GROUP_REL UPR,
        CLMS_RESOURCE_DETAIL_VALUE RDV,
        CLMS_PRIVILEGE_GROUP PG
        where UPR.USER_ID =  #{userPrivRel.userId}
        and UPR.ID_PRIVILEGE_GROUP = RDV.ID_PRIVILEGE_GROUP
        and PG.ID_AHCS_PRIVILEGE_GROUP = UPR.ID_PRIVILEGE_GROUP
        and  RDV.DEPARTMENT_CODE = #{deptCode}
        and RDV.RESOURCE_DETAIL_CODE = #{resourceDetailCode}
        and UPR.RESOURCE_CODE = #{userPrivRel.resourceCode}
        and UPR.CASE_TYPE = #{userPrivRel.caseType}
        and RDV.RESOURCE_DETAIL_MAX_VALUE>0
        order by PG.SORT_NO desc
    </select>

</mapper>