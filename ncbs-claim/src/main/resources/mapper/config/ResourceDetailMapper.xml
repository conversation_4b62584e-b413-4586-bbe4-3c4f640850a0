<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.config.ResourceDetailMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.config.ResourceDetailDTO" id="ResourceDetailMap">
		<id property="idAhcsResourceDetail" column="ID_AHCS_RESOURCE_DETAIL" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="resourceCode" column="RESOURCE_CODE" />
		<result property="resourceDetailCode" column="RESOURCE_DETAIL_CODE" />
		<result property="resourceDetailName" column="RESOURCE_DETAIL_NAME" />
		<result property="resourceDetailType" column="RESOURCE_DETAIL_TYPE" />
		<result property="sortNo" column="SORT_NO" />
	</resultMap>
	

	<insert id="addResourceDetailList" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" separator=";">
			INSERT INTO CLMS_RESOURCE_DETAIL (
				CREATED_BY,
				CREATED_DATE,
				UPDATED_BY,
				UPDATED_DATE,
				RESOURCE_CODE,
				RESOURCE_DETAIL_CODE,
				RESOURCE_DETAIL_NAME,
				RESOURCE_DETAIL_TYPE,
				SORT_NO
			) VALUES (
				#{item.createdBy ,jdbcType=VARCHAR},
				now(),
				#{item.updatedBy ,jdbcType=VARCHAR},
				now(),
				#{item.resourceCode ,jdbcType=VARCHAR},
				#{item.resourceDetailCode ,jdbcType=VARCHAR},
				#{item.resourceDetailName ,jdbcType=VARCHAR},
				#{item.resourceDetailType ,jdbcType=VARCHAR},
				#{item.sortNo ,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>
		

	<delete id="removeResourceDetailById" parameterType="String">
		DELETE FROM CLMS_RESOURCE_DETAIL
		where  ID_AHCS_RESOURCE_DETAIL=#{idAhcsResourceDetail} 
	</delete>
	
	<select id="getByResourceCode" resultMap="ResourceDetailMap">
		select 
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_RESOURCE_DETAIL,
			RESOURCE_CODE,
			RESOURCE_DETAIL_CODE,
			RESOURCE_DETAIL_NAME,
			RESOURCE_DETAIL_TYPE,
			SORT_NO
			from CLMS_RESOURCE_DETAIL
		where  RESOURCE_CODE=#{resourceCode}
		order by SORT_NO  
	</select>	
	
</mapper>