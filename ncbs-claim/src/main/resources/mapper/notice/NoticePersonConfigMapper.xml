<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.notice.NoticePersonConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.notice.NoticePersonConfigDTO">
        <id column="id" property="id" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="com_code" property="comCode" />
        <result column="valid_status" property="validStatus" />
        <result column="user_code" property="userCode" />
        <result column="user_name" property="userName" />
    </resultMap>

    <sql id="Base_Column_List">
        created_by,sys_ctime,updated_by,sys_utime,com_code,valid_status,user_code,user_name
    </sql>
    <select id="findByComCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from clms_notice_person_config
        where com_code = #{comCode}
    </select>

</mapper>
