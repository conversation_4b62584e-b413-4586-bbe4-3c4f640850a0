<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.notice.NoticeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.notice.NoticeConfigDTO">
        <id column="id" property="id" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="notice_class" property="noticeClass" />
        <result column="notice_sub_class" property="noticeSubClass" />
        <result column="source_system" property="sourceSystem" />
        <result column="notice_title" property="noticeTitle" />
        <result column="notice_content" property="noticeContent" />
        <result column="apply_company_code" property="applyCompanyCode" />
        <result column="apply_ins_class" property="applyInsClass" />
        <result column="threshold" property="threshold" />
        <result column="show_in_oc" property="showInOc" />
        <result column="valid_status" property="validStatus" />
    </resultMap>

</mapper>
