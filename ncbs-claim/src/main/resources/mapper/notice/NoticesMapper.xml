<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.notice.NoticesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.notice.NoticesDTO">
        <id column="id" property="id" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="notice_class" property="noticeClass" />
        <result column="notice_sub_class" property="noticeSubClass" />
        <result column="source_system" property="sourceSystem" />
        <result column="notice_title" property="noticeTitle" />
        <result column="notice_content" property="noticeContent" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="company_code" property="companyCode" />
        <result column="product_code" property="productCode" />
        <result column="product_name" property="productName" />
        <result column="risk_group_no" property="riskGroupNo" />
        <result column="valid_status" property="validStatus" />
        <result column="link_url" property="linkUrl" />
    </resultMap>
    <select id="getNoticeList" parameterType="com.paic.ncbs.claim.model.vo.notice.NoticesVO"
            resultType="com.paic.ncbs.claim.model.vo.notice.NoticesVO">
        SELECT
        n.id as noticesId,
        n.notice_class as noticeClass,
        n.report_no as reportNo,
        n.notice_content as noticeContent,
        n.company_code as companyCode,
        n.sys_Ctime as createdDate,
        p.id as noticePersonId,
        p.read_status as readStatus,
        decode(n.company_code,'0001','上海(总部)',dd.DEPARTMENT_ABBR_NAME) AS companyName,
        cpi.PRODUCT_CODE as productCode,
        cpi.PRODUCT_NAME AS productName,
        cip.SCHEME_NAME  AS riskGroupName
        FROM clms_notices n
        JOIN clms_notice_person p ON p.id_clms_notices=n.id
        left JOIN clms_policy_info cpi ON cpi.REPORT_NO = n.REPORT_NO
        left JOIN clms_insured_person cip ON cpi.ID_AHCS_POLICY_INFO = cip.ID_AHCS_POLICY_INFO
        LEFT JOIN department_define dd ON (dd.DEPARTMENT_CODE = n.company_code or (dd.global_code= n.company_code and dd.global_code <![CDATA[ <> ]]> '0001'))
        WHERE n.valid_status IN ('1') AND p.recipient = #{recipient} and p.read_status != '3'
        <if test="reportNo != null and reportNo != ''">
            AND n.REPORT_NO = TRIM(#{reportNo})
        </if>
        <if test='noticeClass != null and noticeClass != ""'>
            AND n.notice_class = #{noticeClass}
        </if>
        <if test="noticeStartTime != null and noticeEndTime != null">
            AND DATE_FORMAT(n.sys_utime, '%Y-%m-%d') BETWEEN DATE_FORMAT(#{noticeStartTime}, '%Y-%m-%d') AND DATE_FORMAT(#{noticeEndTime}, '%Y-%m-%d')
        </if>
        <if test='productCode != null and productCode != ""'>
            AND cpi.PRODUCT_CODE = #{productCode}
        </if>
        ORDER BY n.sys_utime DESC
    </select>
    <update id="updateNoticesStatus">
        update clms_notices n
        set n.valid_status=#{status},
        n.updated_by=#{userId},
        n.sys_utime=now()
        where n.id=#{noticesId}
    </update>
    <select id="getDefaultList" resultType="com.paic.ncbs.claim.model.vo.notice.NoticesVO">
        select n.id as noticesId,
            n.notice_class as noticeClass,
            n.report_no as reportNo,
            n.notice_content as noticeContent,
            n.company_code as companyCode,
            n.sys_Ctime as createdDate,
            n.case_times as caseTimes,
            p.id as noticePersonId,
            p.read_status as readStatus
          from clms_notices n, clms_notice_person p
         where n.id=p.id_clms_notices
           and n.valid_status='1'
           and p.recipient=#{recipient}
           and p.read_status='0'
         order by n.sys_utime desc
        limit 10
    </select>
    <delete id="deleteNoticesByDate">
        delete from clms_notices n
        where n.sys_Ctime <![CDATA[<=]]> #{dateTimeD}
    </delete>

    <select id="getPersonTraceDefaultList" resultType="com.paic.ncbs.claim.model.vo.notice.NoticesVO" parameterType="com.paic.ncbs.claim.model.vo.notice.NoticesVO">
        select n.id as noticesId,
        n.notice_class as noticeClass,
        n.report_no as reportNo,
        n.notice_content as noticeContent,
        n.company_code as companyCode,
        n.sys_Ctime as createdDate,
        n.case_times as caseTimes,
        p.id as noticePersonId,
        p.read_status as readStatus
        from clms_notices n, clms_notice_person p
        where n.id=p.id_clms_notices
        and n.valid_status='1'
        and p.recipient=#{recipient}
        and p.read_status='0'
        and n.notice_class ='7'
        <if test="reportNo != null and reportNo != ''">
            AND n.REPORT_NO = TRIM(#{reportNo})
        </if>
        <if test="caseTimes != null and caseTimes != ''">
            AND n.case_times = TRIM(#{caseTimes})
        </if>
        order by n.sys_utime desc
    </select>
</mapper>
