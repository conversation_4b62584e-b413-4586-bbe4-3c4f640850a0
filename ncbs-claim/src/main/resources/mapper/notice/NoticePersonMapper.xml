<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.notice.NoticePersonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.notice.NoticePersonDTO">
        <id column="id" property="id" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="id_clms_notices" property="idClmsNotices" />
        <result column="recipient" property="recipient" />
        <result column="read_status" property="readStatus" />
        <result column="send_info" property="sendInfo" />
    </resultMap>
    <update id="updatePersonStatus">
        update clms_notice_person p
        set p.read_status=#{readStatus},
        p.updated_by=#{userId},
        p.sys_utime=now()
        where p.id=#{id}
    </update>
    <delete id="deletePersonByDate">
        delete from clms_notice_person p
        where p.sys_Ctime <![CDATA[<=]]> #{dateTimeD}
    </delete>
</mapper>
