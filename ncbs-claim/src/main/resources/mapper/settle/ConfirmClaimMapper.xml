<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.ConfirmClaimMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.ahcs.ConfirmClaimDTO" id="confirmCliamDTO">
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="idAhcsConfirmClaiminfo" column="ID_AHCS_CONFIRM_CLAIM_INFO" />
		<result property="confirmState" column="CONFIRM_STATE" />
		<result property="startDate" column="START_DATE" />
		<result property="confirmDate" column="CONFIRM_DATE" />
		<result property="confirmPerson" column="CONFIRM_PERSON" />
		<result property="claimUmConfirmState" column="CLAIM_UM_CONFIRM_STATE" />
		<result property="remark" column="REMARK" />
	</resultMap>

	 <select id="getConfirmClaimDetail" resultMap="confirmCliamDTO">
		SELECT t.CONFIRM_STATE,
		t.START_DATE,
		t.CONFIRM_DATE,
		t.CONFIRM_PERSON,
		CLAIM_UM_CONFIRM_STATE,
		REMARK
		from CLMS_CONFIRM_CLAIM_INFO t where
		t.REPORT_NO = #{reportNo}
		and
		t.CASE_TIMES = #{caseTimes}
		order By t.START_DATE ASC
	</select>

</mapper>