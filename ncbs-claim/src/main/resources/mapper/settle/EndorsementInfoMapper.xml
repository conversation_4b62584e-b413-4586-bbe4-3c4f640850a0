<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.EndorsementMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.settle.EndorsementDTO"
		id="result">
			<id property="idAhcsEndorsement" column="ID_AHCS_ENDORSEMENT" />
			<result property="createdBy" column="CREATED_BY" />
			<result property="createdDate" column="CREATED_DATE" />
			<result property="updatedBy" column="UPDATED_BY" />
			<result property="updatedDate" column="UPDATED_DATE" />
			<result property="reportNo" column="REPORT_NO" />
			<result property="caseTimes" column="CASE_TIMES" />
			<result property="lossObjectNo" column="LOSS_OBJECT_NO" />
			<result property="endorsementRemark" column="ENDORSEMENT_REMARK" />
	</resultMap>

<!--  插入方法   -->
	<insert id="insertEndorsementInfo" parameterType="com.paic.ncbs.claim.model.dto.settle.EndorsementDTO" >
		INSERT INTO CLMS_ENDORSEMENT (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_ENDORSEMENT,
			REPORT_NO,
			CASE_TIMES,
			ENDORSEMENT_REMARK,
			LOSS_OBJECT_NO,
			ARCHIVE_TIME
		)
	VALUES (
		#{createdBy ,jdbcType=VARCHAR},
			sysdate(),
			#{updatedBy ,jdbcType=VARCHAR},
			sysdate(),
			#{idAhcsEndorsement ,jdbcType=VARCHAR},
			#{reportNo ,jdbcType=VARCHAR},
			#{caseTimes ,jdbcType=NUMERIC},
			#{endorsementRemark ,jdbcType=VARCHAR},
			#{lossObjectNo ,jdbcType=VARCHAR},
			sysdate()
	)
	</insert>

	<!-- 根据id查询-->
	<select id="selectByReportNoAndCaseTime" resultMap="result">
		select
		CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_ENDORSEMENT,
			REPORT_NO,
			CASE_TIMES,
			ENDORSEMENT_REMARK
		from CLMS_ENDORSEMENT
		where  REPORT_NO = #{reportNo} and  CASE_TIMES = #{caseTimes}
	</select>
	
	<!-- 根据id查询-->
	<select id="selectByReportNoAndCaseTimeWithLossObject" resultMap="result">
		select
		CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_ENDORSEMENT,
			REPORT_NO,
			CASE_TIMES,
			LOSS_OBJECT_NO,
			ENDORSEMENT_REMARK
		from CLMS_ENDORSEMENT
		where  REPORT_NO=#{reportNo} and  CASE_TIMES =#{caseTimes}
			   AND LOSS_OBJECT_NO = #{lossObjectNo}
	</select>

	<!-- 查询批单模板文本-->
	<select id="getEndorseTemplate" parameterType="java.lang.String" resultType="java.lang.String">
		select  template_content  from CLMS_text_template where  template_code = concat('ENDORSE_',#{ indemnityMode})
	</select>

	<!-- 删除-->
	<delete id="deleteByPolicyPayId">
		delete from CLMS_ENDORSEMENT where  REPORT_NO=#{reportNo} and  CASE_TIMES =#{caseTimes}
	</delete>

	<!--  根据对象更新 部分更新   -->
	<update id="updateEndorsementInfo" parameterType="com.paic.ncbs.claim.model.dto.settle.EndorsementDTO"  >
		UPDATE CLMS_ENDORSEMENT
		<set>
		UPDATED_BY  = #{record.updatedBy,jdbcType=VARCHAR}, 
		UPDATED_DATE  = sysdate(),
		ENDORSEMENT_REMARK  = #{record.endorsementRemark,jdbcType=VARCHAR}
		</set>
		WHERE REPORT_NO = #{record.reportNo} and  CASE_TIMES  = #{record.caseTimes}
	</update>
</mapper>