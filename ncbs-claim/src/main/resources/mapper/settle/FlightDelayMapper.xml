<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.FlightDelayMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.settle.FlightDelayDTO" id="flightDelayListResult">
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="ORIGINAL_FLIGHT_NO" property="originalFlightNo"/>
        <result column="ORIGINAL_DEPART_TIME" property="originalDepartTime"/>
        <result column="ORIGINAL_ARRIVAL_TIME" property="originalArrivalTime"/>
        <result column="QUERY_FLIGHT_STATUS" property="queryFlightStatus"/>
        <result column="DEPART_PLACE" property="departPlace"/>
        <result column="ARRIVAL_PLACE" property="arrivalPlace"/>
        <result column="TICKET_NO" property="ticketNo"/>
        <result column="QUERY_TICKET_STATUS" property="queryTicketStatus"/>
        <result column="FLIGHT_STATUS" property="flightStatus"/>
        <result column="REAL_FLIGHT_NO" property="realFlightNo"/>
        <result column="REAL_DEPART_TIME" property="realDepartTime"/>
        <result column="REAL_ARRIVAL_TIME" property="realArrivalTime"/>
        <result column="CALCULATE_MODE" property="calculateMode"/>
        <result column="DELAY_DURATION" property="delayDuration"/>
        <result column="DELAY_DURATION_UNIT" property="delayDurationUnit"/>
        <result column="PAY_PATTERN" property="payPattern"/>
        <result column="LOSS_AMOUNT" property="lossAmount"/>
        <result column="TASK_CODE" property="taskCode"/>
        <result column="CLAIMS_TYPE" property="claimsType"/>
        <result column="IS_TICKET_USED" property="isTicketUsed"/>
        <result column="IS_BUY_TICKET" property="isBuyTicket"/>
        <result column="STATUS" property="status"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
        <result column="TOTAL_TICKET_PRICE" property="totalTicketPrice"/>
        <result column="TICKET_PRICE" property="ticketPrice"/>
        <result column="MACHINE_BUILDING_FEE" property="machineBuildingFee"/>
        <result column="FLIGHT_TRAVEL_STATUS" property="flightTravelStatus"/>
        <result column="IS_REPLACE_FLIGHT" property="isReplaceFlight"/>
        <result column="DEPT_FLIGHT_TIME" property="deptFlightTime"/>
        <result column="ORIGINAL_TRAVEL_VERIFY" property="originalTravelVerify"/>
        <result column="REAL_TRAVEL_VERIFY" property="realTravelVerify"/>
    </resultMap>

    <insert parameterType="com.paic.ncbs.claim.model.dto.settle.FlightDelayDTO" id="addFlightDelay">
        INSERT INTO CLMS_FLIGHT_DELAY(
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_FLIGHT_DELAY,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        ORIGINAL_FLIGHT_NO,
        ORIGINAL_DEPART_TIME,
        ORIGINAL_ARRIVAL_TIME,
        QUERY_FLIGHT_STATUS,
        DEPART_PLACE,
        ARRIVAL_PLACE,
        TICKET_NO,
        QUERY_TICKET_STATUS,
        FLIGHT_STATUS,
        REAL_FLIGHT_NO,
        REAL_DEPART_TIME,
        REAL_ARRIVAL_TIME,
        CALCULATE_MODE,
        DELAY_DURATION,
        DELAY_DURATION_UNIT,
        PAY_PATTERN,
        LOSS_AMOUNT,
        TASK_CODE,
        STATUS,
        CLAIMS_TYPE,
        IS_TICKET_USED,
        IS_BUY_TICKET,
        TOTAL_TICKET_PRICE,
        TICKET_PRICE,
        MACHINE_BUILDING_FEE,
        FLIGHT_TRAVEL_STATUS,
        IS_REPLACE_FLIGHT,
        DEPT_FLIGHT_TIME,
        ORIGINAL_TRAVEL_VERIFY,
        REAL_TRAVEL_VERIFY,
        archive_time
        )
        VALUES(
        #{createdBy,jdbcType=VARCHAR},
        now(),
        #{updatedBy,jdbcType=VARCHAR},
        now(),
        #{idAhcsFlightDelay,jdbcType=VARCHAR},
        #{reportNo,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=NUMERIC},
        #{idAhcsChannelProcess,jdbcType=VARCHAR},
        #{originalFlightNo,jdbcType=VARCHAR},
        #{originalDepartTime,jdbcType=TIMESTAMP},
        #{originalArrivalTime,jdbcType=TIMESTAMP},
        #{queryFlightStatus,jdbcType=VARCHAR},
        #{departPlace,jdbcType=VARCHAR},
        #{arrivalPlace,jdbcType=VARCHAR},
        #{ticketNo,jdbcType=VARCHAR},
        #{queryTicketStatus,jdbcType=VARCHAR},
        #{flightStatus,jdbcType=VARCHAR},
        #{realFlightNo,jdbcType=VARCHAR},
        #{realDepartTime,jdbcType=TIMESTAMP},
        #{realArrivalTime,jdbcType=TIMESTAMP},
        #{calculateMode,jdbcType=VARCHAR},
        #{delayDuration,jdbcType=VARCHAR},
        #{delayDurationUnit,jdbcType=VARCHAR},
        #{payPattern,jdbcType=VARCHAR},
        #{lossAmount,jdbcType=NUMERIC},
        #{taskCode,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        #{claimsType,jdbcType=VARCHAR},
        #{isTicketUsed,jdbcType=VARCHAR},
        #{isBuyTicket,jdbcType=VARCHAR},
        #{totalTicketPrice,jdbcType=NUMERIC},
        #{ticketPrice,jdbcType=NUMERIC},
        #{machineBuildingFee,jdbcType=NUMERIC},
        #{flightTravelStatus,jdbcType=VARCHAR},
        #{isReplaceFlight,jdbcType=VARCHAR},
        #{deptFlightTime,jdbcType=TIMESTAMP},
        #{originalTravelVerify,jdbcType=VARCHAR},
        #{realTravelVerify,jdbcType=VARCHAR},
        now()
        )
    </insert>

    <delete id="removeFlightDelay">
        DELETE from CLMS_FLIGHT_DELAY WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test="channelProcessId != null and channelProcessId != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
    </delete>

    <select id="getFlightDelay" resultType="com.paic.ncbs.claim.model.dto.settle.FlightDelayDTO">
        SELECT REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        ORIGINAL_FLIGHT_NO originalFlightNo,
        ORIGINAL_DEPART_TIME originalDepartTime,
        ORIGINAL_ARRIVAL_TIME originalArrivalTime,
        QUERY_FLIGHT_STATUS queryFlightStatus,
        DEPART_PLACE departPlace,
        ARRIVAL_PLACE arrivalPlace,
        (SELECT AI.AIRPORT_NAME
        FROM CLMS_AIRPORT_INFO AI
        WHERE AI.AIRPORT_CODE = DEPART_PLACE) departPlaceName,
        (SELECT AI.AIRPORT_NAME
        FROM CLMS_AIRPORT_INFO AI
        WHERE AI.AIRPORT_CODE = ARRIVAL_PLACE) arrivalPlaceName,
        TICKET_NO ticketNo,
        QUERY_TICKET_STATUS queryTicketStatus,
        FLIGHT_STATUS flightStatus,
        REAL_FLIGHT_NO realFlightNo,
        REAL_DEPART_TIME realDepartTime,
        REAL_ARRIVAL_TIME realArrivalTime,
        CALCULATE_MODE calculateMode,
        DELAY_DURATION delayDuration,
        DELAY_DURATION_UNIT delayDurationUnit,
        PAY_PATTERN payPattern,
        LOSS_AMOUNT lossAmount,
        TASK_CODE taskCode,
        STATUS status,
        IS_TICKET_USED isTicketUsed,
        IS_BUY_TICKET isBuyTicket,
        TOTAL_TICKET_PRICE totalTicketPrice,
        TICKET_PRICE ticketPrice,
        FLIGHT_TRAVEL_STATUS flightTravelStatus,
        IS_REPLACE_FLIGHT isReplaceFlight,
        DEPT_FLIGHT_TIME deptFlightTime,
        ORIGINAL_TRAVEL_VERIFY originalTravelVerify,
        REAL_TRAVEL_VERIFY realTravelVerify,
        MACHINE_BUILDING_FEE machineBuildingFee
        FROM CLMS_FLIGHT_DELAY
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            AND STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test="channelProcessId != null and channelProcessId != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
        order by CREATED_DATE desc
        limit 1
    </select>

    <select id="getSettleFlightDelay" resultType="com.paic.ncbs.claim.model.vo.settle.SettleFlightDelayVO">
        SELECT
        REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        ORIGINAL_FLIGHT_NO originalFlightNo,
        ORIGINAL_DEPART_TIME originalDepartTime,
        ORIGINAL_ARRIVAL_TIME originalArrivalTime,
        QUERY_FLIGHT_STATUS queryFlightStatus,
        DEPART_PLACE departPlace,
        ARRIVAL_PLACE arrivalPlace,
        TICKET_NO ticketNo,
        QUERY_TICKET_STATUS queryTicketStatus,
        FLIGHT_STATUS flightStatus,
        REAL_FLIGHT_NO realFlightNo,
        REAL_DEPART_TIME realDepartTime,
        REAL_ARRIVAL_TIME realArrivalTime,
        CALCULATE_MODE calculateMode,
        DELAY_DURATION delayDuration,
        DELAY_DURATION_UNIT delayDurationUnit,
        PAY_PATTERN payPattern,
        LOSS_AMOUNT lossAmount,
        TASK_CODE taskCode,
        CONCAT(
        case IS_BUY_TICKET
        when 'YES' then '已购票'
        when 'NO' then '未购票'
        end ,
        CONCAT('; ',
        case IS_BUY_TICKET
        when 'YES' then '已使用'
        when 'NO' then '未使用'
        end
        )
        ) ticketInfo,
        STATUS status,
        FLIGHT_TRAVEL_STATUS flightTravelStatus,
        IS_REPLACE_FLIGHT isReplaceFlight,
        DEPT_FLIGHT_TIME deptFlightTime,
        ORIGINAL_TRAVEL_VERIFY originalTravelVerify,
        REAL_TRAVEL_VERIFY realTravelVerify,
        (SELECT CP.VALUE_CHINESE_NAME
        FROM CLM_COMMON_PARAMETER CP
        WHERE CP.COLLECTION_CODE = 'AHCS_FLIGHT_STATUS'
        AND FLIGHT_STATUS = CP.VALUE_CODE) flightStatusName,
        (SELECT CP.VALUE_CHINESE_NAME
        FROM CLM_COMMON_PARAMETER CP
        WHERE CP.COLLECTION_CODE = 'AHCS_DURATION_UNIT'
        AND DELAY_DURATION_UNIT = CP.VALUE_CODE) delayDurationUnitName
        FROM CLMS_FLIGHT_DELAY
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            AND STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test="idAhcsChannelProcess != null and idAhcsChannelProcess != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        </if>
        limit 1
    </select>

    <!-- 根据通道号、环节号获取航班延误信息-->
    <select id="getFlightDelayList" parameterType="string" resultMap="flightDelayListResult">
        select REPORT_NO,
        ORIGINAL_FLIGHT_NO,
        ORIGINAL_DEPART_TIME,
        ORIGINAL_ARRIVAL_TIME,
        QUERY_FLIGHT_STATUS,
        DEPART_PLACE,
        ARRIVAL_PLACE,
        TICKET_NO,
        QUERY_TICKET_STATUS,
        FLIGHT_STATUS,
        REAL_FLIGHT_NO,
        REAL_DEPART_TIME,
        REAL_ARRIVAL_TIME,
        CALCULATE_MODE,
        DELAY_DURATION,
        DELAY_DURATION_UNIT,
        PAY_PATTERN,
        LOSS_AMOUNT,
        TASK_CODE,
        CLAIMS_TYPE,
        IS_TICKET_USED,
        IS_BUY_TICKET,
        STATUS,
        ARCHIVE_TIME,
        TOTAL_TICKET_PRICE,
        TICKET_PRICE,
        FLIGHT_TRAVEL_STATUS,
        IS_REPLACE_FLIGHT,
        DEPT_FLIGHT_TIME,
        ORIGINAL_TRAVEL_VERIFY,
        REAL_TRAVEL_VERIFY,
        MACHINE_BUILDING_FEE
        from CLMS_FLIGHT_DELAY fd
        where fd.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        and fd.STATUS = '1'
        and fd.TASK_CODE = #{taskCode}
    </select>

    <!-- 新增多条 航班延误信息 -->
    <insert id="addFlightDelayList">
        insert into CLMS_FLIGHT_DELAY
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        ORIGINAL_FLIGHT_NO,
        ORIGINAL_DEPART_TIME,
        ORIGINAL_ARRIVAL_TIME,
        QUERY_FLIGHT_STATUS,
        DEPART_PLACE,
        ARRIVAL_PLACE,
        TICKET_NO,
        QUERY_TICKET_STATUS,
        FLIGHT_STATUS,
        REAL_FLIGHT_NO,
        REAL_DEPART_TIME,
        REAL_ARRIVAL_TIME,
        CALCULATE_MODE,
        DELAY_DURATION,
        DELAY_DURATION_UNIT,
        PAY_PATTERN,
        LOSS_AMOUNT,
        TASK_CODE,
        CLAIMS_TYPE,
        IS_TICKET_USED,
        IS_BUY_TICKET,
        STATUS,
        TOTAL_TICKET_PRICE,
        TICKET_PRICE,
        MACHINE_BUILDING_FEE,
        FLIGHT_TRAVEL_STATUS,
        IS_REPLACE_FLIGHT,
        DEPT_FLIGHT_TIME,
        ORIGINAL_TRAVEL_VERIFY,
        REAL_TRAVEL_VERIFY,
        archive_time)
        <foreach collection="flightDelayList" index="index" item="item" open="(" close=")" separator="union all">
            select #{userId},
            sysdate(),
            #{userId},
            sysdate(),
            #{item.reportNo},
            #{caseTimes},
            #{channelProcessId,jdbcType=VARCHAR},
            #{item.originalFlightNo,jdbcType=VARCHAR},
            #{item.originalDepartTime,jdbcType=TIMESTAMP},
            #{item.originalArrivalTime,jdbcType=TIMESTAMP},
            #{item.queryFlightStatus,jdbcType=VARCHAR},
            #{item.departPlace,jdbcType=VARCHAR},
            #{item.arrivalPlace,jdbcType=VARCHAR},
            #{item.ticketNo,jdbcType=VARCHAR},
            #{item.queryTicketStatus,jdbcType=VARCHAR},
            #{item.flightStatus,jdbcType=VARCHAR},
            #{item.realFlightNo,jdbcType=VARCHAR},
            #{item.realDepartTime,jdbcType=TIMESTAMP},
            #{item.realArrivalTime,jdbcType=TIMESTAMP},
            #{item.calculateMode,jdbcType=VARCHAR},
            #{item.delayDuration,jdbcType=VARCHAR},
            #{item.delayDurationUnit,jdbcType=VARCHAR},
            #{item.payPattern,jdbcType=VARCHAR},
            #{item.lossAmount,jdbcType=NUMERIC},
            #{item.taskCode,jdbcType=VARCHAR},
            #{item.claimsType,jdbcType=VARCHAR},
            #{item.isTicketUsed,jdbcType=VARCHAR},
            #{item.isBuyTicket,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR} ,
            #{item.totalTicketPrice,jdbcType=NUMERIC},
            #{item.ticketPrice,jdbcType=NUMERIC},
            #{item.machineBuildingFee,jdbcType=NUMERIC},
            #{item.flightTravelStatus,jdbcType=VARCHAR},
            #{item.isReplaceFlight,jdbcType=VARCHAR},
            #{item.deptFlightTime,jdbcType=TIMESTAMP},
            #{item.originalTravelVerify,jdbcType=VARCHAR},
            #{item.realTravelVerify,jdbcType=VARCHAR},
            <if test="item.archiveTime != null ">
                #{item.archiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="item.archiveTime == null ">
                sysdate()
            </if>
            from DUAL
        </foreach>
    </insert>

    <select id="getDutyAttrByReportNo" parameterType="string" resultType="Integer">
        select count(1)
        from CLMS_policy_info pi,
        CLMS_policy_plan pp,
        CLMS_policy_duty pd,
        CLMS_duty_attribute da
        where pi.id_ahcs_policy_info = pp.id_ahcs_policy_info
        and pp.id_ahcs_policy_plan = pd.id_ahcs_policy_plan
        and pd.id_ahcs_policy_duty = da.id_ahcs_policy_duty
        and pi.report_no = #{reportNo}
        and ((da.attribute_code = '7' and da.attribute_value = '3') or
        (da.attribute_code = 'delayType' and
        da.attribute_value = 'FYBC'))
    </select>

    <select id="getDutyAttrPolicyConfigByReportNo" parameterType="string" resultType="Integer">
        SELECT COUNT(1)
        FROM CLMS_POLICY_DUTY_ATTR A
        WHERE A.IS_EFFECTIVE = 'Y'
        AND A.ATTRIBUTE_CODE = '7'
        AND A.ATTRIBUTE_VALUE = '3'
        AND EXISTS (SELECT 1
        FROM CLMS_POLICY_INFO T
        WHERE T.REPORT_NO = #{reportNo}
        AND T.POLICY_NO = A.POLICY_NO)
    </select>

    <select id="getDutyAttrProductConfigByReportNo" parameterType="string" resultType="Integer">
        SELECT COUNT(1)
        FROM CLMS_PRODUCT_DUTY_ATTR A
        WHERE A.IS_EFFECTIVE = 'Y'
        AND A.ATTRIBUTE_CODE = '7'
        AND A.ATTRIBUTE_VALUE = '3'
        AND EXISTS (SELECT 1
        FROM CLMS_POLICY_INFO T
        WHERE T.REPORT_NO = #{reportNo}
        AND T.PRODUCT_CODE = A.PRODUCT_CODE)
    </select>

    <update id="updateAutoFlightDelayStatus">
        UPDATE CLMS_FLIGHT_DELAY T
        SET T.updated_date = sysdate(), T.STATUS = #{status}
        WHERE T.REPORT_NO = #{reportNo}
        AND T.CASE_TIMES = #{caseTimes}
        AND T.TASK_CODE = #{taskCode}
    </update>

    <update id="updateFlightDelayTicketInfo" parameterType="com.paic.ncbs.claim.model.dto.settle.FlightDelayDTO">
        UPDATE CLMS_FLIGHT_DELAY T
        SET
        T.IS_TICKET_USED = #{isTicketUsed,jdbcType=VARCHAR},
        T.IS_BUY_TICKET = #{isBuyTicket,jdbcType=VARCHAR},
        T.updated_date = sysdate(),
        T.TICKET_NO = #{ticketNo,jdbcType=VARCHAR}
        WHERE T.REPORT_NO = #{reportNo}
        AND T.CASE_TIMES =#{caseTimes}
        AND T.TASK_CODE =#{taskCode}
    </update>

    <update id="updateFlightDelay" parameterType="com.paic.ncbs.claim.model.dto.settle.FlightDelayDTO">
        update CLMS_FLIGHT_DELAY
        <trim prefix="set" suffixOverrides=",">
            <if test="updatedBy != null">UPDATED_BY=#{updatedBy},</if>
            UPDATED_DATE=sysdate(),
            <if test="flightStatus != null">FLIGHT_STATUS=#{flightStatus},</if>
            <if test="calculateMode != null">CALCULATE_MODE=#{calculateMode},</if>
            <if test="delayDuration != null">DELAY_DURATION=#{delayDuration},</if>
            <if test="delayDurationUnit != null">DELAY_DURATION_UNIT=#{delayDurationUnit},</if>
            <if test="payPattern != null">PAY_PATTERN=#{payPattern},</if>
            <if test="lossAmount != null">LOSS_AMOUNT=#{lossAmount},</if>
        </trim>
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        AND TASK_CODE = #{taskCode}
    </update>
</mapper>