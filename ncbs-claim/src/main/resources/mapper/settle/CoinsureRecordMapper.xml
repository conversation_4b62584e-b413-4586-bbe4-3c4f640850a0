<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.CoinsureRecordMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.CoinsureRecordDTO" id="coinsureRecord">
        <result property="idAhcsCoinsureRecord" column="ID_AHCS_COINSURE_RECORD"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="isCoinsureFee" column="IS_COINSURE_FEE"/>
        <result property="isCoinsurePay" column="IS_COINSURE_PAY"/>
    </resultMap>

    <select id="selectByParam" resultMap="coinsureRecord">
        select  id_ahcs_coinsure_record,
                report_no,
                case_times,
                policy_no,
                is_coinsure_fee,
                is_coinsure_pay
        from CLMS_COINSURE_RECORD
        where report_no=#{reportNo}
        <if test="caseTimes != null ">
            and case_times=#{caseTimes}
        </if>
        <if test="policyNo != null and policyNo != '' ">
            and policy_no=#{policyNo}
        </if>
    </select>

    <insert id="batchInsert">
        INSERT INTO CLMS_COINSURE_RECORD
        (CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_COINSURE_RECORD,
        REPORT_NO, CASE_TIMES, POLICY_NO,IS_COINSURE_FEE,IS_COINSURE_PAY,ARCHIVE_TIME)
        VALUES
        <foreach collection="list" item="item" index="i" separator=",">
            (#{item.createdBy},#{item.createdDate},#{item.updatedBy},#{item.updatedDate},#{item.idAhcsCoinsureRecord},
            #{item.reportNo},#{item.caseTimes},#{item.policyNo},#{item.isCoinsureFee},#{item.isCoinsurePay},#{item.archiveTime})
        </foreach>
    </insert>

    <update id="updateById">
        UPDATE CLMS_COINSURE_RECORD
        SET IS_COINSURE_FEE = #{isCoinsureFee},
        IS_COINSURE_PAY = #{isCoinsurePay},
        updated_by = #{updatedBy},
        updated_date = #{updatedDate}
        WHERE ID_AHCS_COINSURE_RECORD = #{idAhcsCoinsureRecord}
    </update>

</mapper>