<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.PolicyShareMapper">
	<!-- 定义PolicyClaimCase 的复杂关联map -->
	<resultMap type="com.paic.ncbs.claim.model.dto.settle.PolicyShareDTO" id="policyShareMap">
			<id property="idClmPolicyShare" column="ID_CLM_POLICY_SHARE" />
			<result property="createdBy" column="CREATED_BY" />
			<result property="createdDate" column="CREATED_DATE" />
			<result property="updatedBy" column="UPDATED_BY" />
			<result property="updatedDate" column="UPDATED_DATE" />
			<result property="idPolicyInfo" column="ID_POLICY_INFO"/>
			<result property="riskGroupId" column="RISK_GROUP_ID"/>
			<result property="planCode" column="PLAN_CODE"/>
			<result property="dutyCode" column="DUTY_CODE"/>
			<result property="groupCode" column="GROUP_CODE"/>
	</resultMap>
	
	<!-- 查询方法 -->
	<select id="getByPolicy" resultMap="policyShareMap">
		 SELECT CREATED_BY,
	       CREATED_DATE,
	       UPDATED_BY,
	       UPDATED_DATE,
	       ID_CLM_POLICY_SHARE,
	       ID_POLICY_INFO,
	       RISK_GROUP_ID,
	       PLAN_CODE,
	       DUTY_CODE,
	       GROUP_CODE,
	       ARCHIVE_DATE,
	       DELETED_TIME
	  	FROM CLM_POLICY_SHARE T
	  	WHERE T.ID_POLICY_INFO = #{idAhcsPolicyInfo}
	</select>

</mapper>