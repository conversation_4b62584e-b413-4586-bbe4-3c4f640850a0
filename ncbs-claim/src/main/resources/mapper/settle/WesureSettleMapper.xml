<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.WesureSettleMapper">
    <resultMap id="wesureSettleMap" type="com.paic.ncbs.claim.model.dto.settle.WesureSettleDTO">
        <id property="idClmsWesureSettle" column="ID_CLMS_WESURE_SETTLE" />
        <result property="wesureReportNo" column="WESURE_REPORT_NO" />
        <result property="reportNo" column="REPORT_NO" />
        <result property="calculateTime" column="CALCULATE_TIME" />
        <result property="auditTime" column="AUDIT_TIME" />
        <result property="auditMsg" column="AUDIT_MSG" />
        <result property="caseConclusion" column="CASE_CONCLUSION" />
        <result property="conclusionDetailCode" column="CONCLUSION_DETAIL_CODE" />
        <result property="conclusionDetailMsg" column="CONCLUSION_DETAIL_MSG" />
        <result property="casePayAmount" column="CASE_PAY_AMOUNT" />
        <collection property="policyBenefitList"
                    ofType="com.paic.ncbs.claim.model.dto.settle.WesurePolicyDTO"
                    select="getWesurePolicyById"
                    column="{idClmsWesureSettle = ID_CLMS_WESURE_SETTLE }">
        </collection>
    </resultMap>

    <resultMap id="wesurePolicyMap" type="com.paic.ncbs.claim.model.dto.settle.WesurePolicyDTO">
        <id property="idClmsWesurePolicy" column="ID_CLMS_WESURE_POLICY" />
        <result property="wesurePolicyNo" column="WESURE_POLICY_NO" />
        <result property="policyNo" column="POLICY_NO" />
        <result property="policyPayAmount" column="POLICY_PAY_AMOUNT" />
        <collection property="benefitPayList"
                    ofType="com.paic.ncbs.claim.model.dto.settle.WesureBenefitDTO"
                    select="getWesureBenefitById"
                    column="{idClmsWesurePolicy = ID_CLMS_WESURE_POLICY}">
        </collection>
    </resultMap>

    <resultMap id="wesureBenefitMap" type="com.paic.ncbs.claim.model.dto.settle.WesureBenefitDTO">
        <id property="idClmsWesureBenefit" column="ID_CLMS_WESURE_BENEFIT" />
        <result property="benefitCode" column="BENEFIT_CODE" />
        <result property="benefitName" column="BENEFIT_NAME" />
        <result property="benefitAdjustAmount" column="BENEFIT_ADJUST_AMOUNT" />
        <result property="benefitPayAmount" column="BENEFIT_PAY_AMOUNT" />
        <collection property="receiptList"
                    ofType="com.paic.ncbs.claim.model.dto.settle.WesureReceiptDetailDTO"
                    select="getWesureReceiptById"
                    column="{idClmsWesureBenefit = ID_CLMS_WESURE_BENEFIT }">
        </collection>
    </resultMap>

    <resultMap id="wesureReceiptMap" type="com.paic.ncbs.claim.model.dto.settle.WesureReceiptDetailDTO">
        <result property="receiptNo" column="RECEIPT_NO" />
        <result property="benefitCode" column="BENEFIT_CODE" />
        <result property="totalPay" column="TOTAL_PAY" />
        <result property="insurePay" column="INSURE_PAY" />
        <result property="insureSelfPay" column="INSURE_SELF_PAY" />
        <result property="classResponsibility" column="CLASS_RESPONSIBILITY" />
        <result property="personalResponsibility" column="PERSONAL_RESPONSIBILITY" />
        <result property="personalPay" column="PERSONAL_PAY" />
        <result property="selfPay" column="SELF_PAY" />
        <result property="otherPay" column="OTHER_PAY" />
        <result property="unreasonableAmount" column="UNREASONABLE_AMOUNT" />
    </resultMap>

    <resultMap id="wesureMedicalMap" type="com.paic.ncbs.claim.model.dto.settle.WesureMedicalDTO">
        <id property="idClmsWesureMedical" column="ID_CLMS_WESURE_MEDICAL" />
        <result property="visitType" column="VISIT_TYPE"/>
        <result property="outpatientDate" column="OUTPATINET_DATE"/>
        <result property="inHospitalDate" column="IN_HOSPITAL_DATE"/>
        <result property="outHospitalDate" column="OUT_HOSPITAL_DATE"/>
        <result property="inHospitalDays" column="IN_HOSPITAL_DAYS"/>
        <result property="visitAmount" column="VISIT_AMOUNT"/>
        <collection property="receiptList"
                    ofType="com.paic.ncbs.claim.model.dto.settle.WesureMedicalReceiptDTO"
                    select="getWesureMedicalReceiptById"
                    column="{idClmsWesureMedical = ID_CLMS_WESURE_MEDICAL}">
        </collection>
    </resultMap>

    <resultMap id="wesureMedicalReceiptMap" type="com.paic.ncbs.claim.model.dto.settle.WesureMedicalReceiptDTO">
        <result property="receiptNo" column="RECEIPT_NO" />
        <result property="receiptClass" column="RECEIPT_CLASS" />
        <result property="receiptType" column="RECEIPT_TYPE" />
        <result property="receiptCode" column="RECEIPT_CODE" />
        <result property="receiptDate" column="RECEIPT_DATE" />
        <result property="totalPay" column="TOTAL_PAY" />
        <result property="insurePay" column="INSURE_PAY" />
        <result property="insureSelfPay" column="INSURE_SELF_PAY" />
        <result property="classResponsibility" column="CLASS_RESPONSIBILITY" />
        <result property="personalResponsibility" column="PERSONAL_RESPONSIBILITY" />
        <result property="personalPay" column="PERSONAL_PAY" />
        <result property="selfPay" column="SELF_PAY" />
        <result property="otherPay" column="OTHER_PAY" />
        <result property="inputMode" column="INPUT_MODE" />
        <result property="costCode" column="COST_CODE" />
        <result property="prepaidAmount" column="PREPAID_AMOUNT" />
        <result property="prepaidType" column="PREPAID_TYPE" />
        <result property="unreasonableAmount" column="UNREASONABLE_AMOUNT" />
        <result property="billClass" column="BILL_CLASS" />
        <result property="billType" column="BILL_TYPE" />
        <result property="riskLabel" column="RISK_LABEL" />
        <result property="hospitalCode" column="HOSPITAL_CODE"/>
        <result property="hospitalName" column="HOSPITAL_NAME"/>
        <result property="hospitalGrade" column="HOSPITAL_GRADE"/>
    </resultMap>

    <!--查询已理赔金额-->
    <select id="getClaimedAmount" resultType="com.paic.ncbs.claim.model.dto.settle.WesureBenefitDTO"
            parameterType="com.paic.ncbs.claim.model.dto.settle.WesureClaimedDTO">
        select t.DUTY_DETAIL_CODE benefitCode,
        sum(ifnull(if(t.SETTLE_AMOUNT>0,t.SETTLE_AMOUNT,t.AUTO_SETTLE_AMOUNT),0)) claimedAmount
        from clms_duty_detail_pay t,
        (select b.CASE_NO,max(CASE_TIMES) CASE_TIMES
            from clms_report_customer a,clms_policy_claim_case b,clm_whole_case_base c
            where a.REPORT_NO = b.REPORT_NO
            and b.REPORT_NO = c.REPORT_NO
            and a.CERTIFICATE_NO = #{insurantCredentialNo}
            and b.POLICY_NO = #{policyNo}
            and c.WHOLE_CASE_STATUS = '0'
            and c.INDEMNITY_CONCLUSION = '1'
            group by CASE_NO) t0
        where t.case_times = t0.case_times
        and t.case_no = t0.case_no
        and t.claim_type in ('1','2')
        and t.IS_EFFECTIVE = 'Y'
        and t.DUTY_DETAIL_CODE in(
        <foreach collection="benefitList" separator="," index="index" item="item">
            #{item.benefitCode}
        </foreach>
        )
        group by t.DUTY_DETAIL_CODE

    </select>

    <!--查询核心作业中录入的发票，剔除零注拒案件-->
    <select id="getReceiptStatus" resultType="com.paic.ncbs.claim.model.dto.settle.WesureReceiptClaimDTO" parameterType="java.util.List">
        select t.BILL_NO receiptNo,
               min(t.WHOLE_CASE_STATUS) claimStatus
        from (
           select distinct a.BILL_NO ,
                           b.WHOLE_CASE_STATUS
           from clms_bill_info a,clm_whole_case_base b
           where a.REPORT_NO = b.REPORT_NO
             and (b.WHOLE_CASE_STATUS = '1' or (b.WHOLE_CASE_STATUS ='0' and b.INDEMNITY_CONCLUSION = '1'))
             and a.IS_EFFECTIVE = 'Y'
             and a.BILL_NO in(
                <foreach collection="receiptClaimList" separator="," index="index" item="item">
                    #{item.receiptNo}
                </foreach>
               )
           ) t
        group by t.BILL_NO

    </select>

    <!--查询微保同步给保司的发票，剔除零注拒案件-->
    <select id="getWesureSyncReceiptStatus" resultType="com.paic.ncbs.claim.model.dto.settle.WesureReceiptClaimDTO" parameterType="java.util.List">
        select t.RECEIPT_NO receiptNo,
               min(t.WHOLE_CASE_STATUS) claimStatus
        from (
            select distinct c.RECEIPT_NO ,
                            a.WHOLE_CASE_STATUS
            from clm_whole_case_base a,clms_wesure_medical b,clms_wesure_medical_receipt c
            where a.REPORT_NO =b.REPORT_NO
            and b.ID_CLMS_WESURE_MEDICAL = c.ID_CLMS_WESURE_MEDICAL
            and c.RECEIPT_NO in(
            <foreach collection="receiptClaimList" separator="," index="index" item="item">
                #{item.receiptNo}
            </foreach>
            )
            and b.IS_EFFECTIVE ='Y'
            and (a.WHOLE_CASE_STATUS = '1' or (a.WHOLE_CASE_STATUS ='0' and a.INDEMNITY_CONCLUSION = '1'))
        ) t
        group by t.RECEIPT_NO

    </select>

    <insert id="saveWesureSettle" parameterType="com.paic.ncbs.claim.model.dto.settle.WesureSettleDTO">
        insert into clms_wesure_settle
            (CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_CLMS_WESURE_SETTLE,
            WESURE_REPORT_NO,
            REPORT_NO,
            CASE_TIMES,
            CALCULATE_TIME,
            CASE_CONCLUSION,
            CONCLUSION_DETAIL_CODE,
            CONCLUSION_DETAIL_MSG,
            CASE_PAY_AMOUNT,
            SYNC_TYPE,
            AUDIT_TIME,
            AUDIT_MSG
            )
        values(#{createdBy,jdbcType=VARCHAR},
               now(),
               #{updatedBy,jdbcType=VARCHAR},
               now(),
               #{idClmsWesureSettle,jdbcType=VARCHAR},
               #{wesureReportNo,jdbcType=VARCHAR},
               #{reportNo,jdbcType=VARCHAR},
               #{caseTimes,jdbcType=INTEGER},
               #{calculateTime,jdbcType=VARCHAR},
               #{caseConclusion,jdbcType=VARCHAR},
               #{conclusionDetailCode,jdbcType=VARCHAR},
               #{conclusionDetailMsg,jdbcType=VARCHAR},
               #{casePayAmount,jdbcType=DECIMAL},
               #{syncType,jdbcType=VARCHAR},
               #{auditTime,jdbcType=VARCHAR},
               #{auditMsg,jdbcType=VARCHAR}
               )
    </insert>

    <insert id="saveWesurePolicy" parameterType="java.util.List">
        insert into clms_wesure_policy
            (CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_CLMS_WESURE_POLICY,
            ID_CLMS_WESURE_SETTLE,
            WESURE_POLICY_NO,
            POLICY_NO,
            POLICY_PAY_AMOUNT,
            REPORT_NO,
            CASE_TIMES)
        values
        <foreach collection="wesurePolicyList" separator="," index="index" item="item">
            (#{item.createdBy,jdbcType=VARCHAR},
            now(),
            #{item.updatedBy,jdbcType=VARCHAR},
            now(),
            #{item.idClmsWesurePolicy,jdbcType=VARCHAR},
            #{item.idClmsWesureSettle,jdbcType=VARCHAR},
            #{item.wesurePolicyNo,jdbcType=VARCHAR},
            #{item.policyNo,jdbcType=VARCHAR},
            #{item.policyPayAmount,jdbcType=DECIMAL},
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <insert id="saveWesureBenefit" parameterType="java.util.List">
        insert into clms_wesure_benefit
            (CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_CLMS_WESURE_BENEFIT,
            ID_CLMS_WESURE_POLICY,
            BENEFIT_CODE,
            BENEFIT_NAME,
            BENEFIT_ADJUST_AMOUNT,
            BENEFIT_PAY_AMOUNT)
        values
        <foreach collection="wesureBenefitList" separator="," index="index" item="item">
            (#{item.createdBy,jdbcType=VARCHAR},
            now(),
            #{item.updatedBy,jdbcType=VARCHAR},
            now(),
            #{item.idClmsWesureBenefit,jdbcType=VARCHAR},
            #{item.idClmsWesurePolicy,jdbcType=VARCHAR},
            #{item.benefitCode,jdbcType=VARCHAR},
            #{item.benefitName,jdbcType=VARCHAR},
            #{item.benefitAdjustAmount,jdbcType=DECIMAL},
            #{item.benefitPayAmount,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <insert id="saveWesureReceipt" parameterType="java.util.List">
        insert into clms_wesure_receipt
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_CLMS_WESURE_RECEIPT,
        ID_CLMS_WESURE_BENEFIT,
        POLICY_NO,
        RECEIPT_NO,
        BENEFIT_CODE,
        TOTAL_PAY,
        INSURE_PAY,
        INSURE_SELF_PAY,
        CLASS_RESPONSIBILITY,
        PERSONAL_RESPONSIBILITY,
        PERSONAL_PAY,
        SELF_PAY,
        OTHER_PAY,
        UNREASONABLE_AMOUNT)
        values
        <foreach collection="wesureReceiptList" separator="," index="index" item="item">
            (#{item.createdBy,jdbcType=VARCHAR},
            now(),
            #{item.updatedBy,jdbcType=VARCHAR},
            now(),
            #{item.idClmsWesureReceipt,jdbcType=VARCHAR},
            #{item.idClmsWesureBenefit,jdbcType=VARCHAR},
            #{item.policyNo,jdbcType=VARCHAR},
            #{item.receiptNo,jdbcType=VARCHAR},
            #{item.benefitCode,jdbcType=VARCHAR},
            #{item.totalPay,jdbcType=DECIMAL},
            #{item.insurePay,jdbcType=DECIMAL},
            #{item.insureSelfPay,jdbcType=DECIMAL},
            #{item.classResponsibility,jdbcType=DECIMAL},
            #{item.personalResponsibility,jdbcType=DECIMAL},
            #{item.personalPay,jdbcType=DECIMAL},
            #{item.selfPay,jdbcType=DECIMAL},
            #{item.otherPay,jdbcType=DECIMAL},
            #{item.unreasonableAmount,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <update id="removeWesureSettle" parameterType="com.paic.ncbs.claim.model.dto.settle.WesureSettleDTO">
        update clms_wesure_settle
        set UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            UPDATED_DATE = now(),
            IS_EFFECTIVE = 'N'
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        and SYNC_TYPE = #{syncType,jdbcType=VARCHAR}
        and IS_EFFECTIVE = 'Y'

    </update>

    <select id="getWesureSettle" parameterType="com.paic.ncbs.claim.model.dto.settle.WesureSettleDTO" resultMap="wesureSettleMap">
        select ID_CLMS_WESURE_SETTLE,
               WESURE_REPORT_NO,
               REPORT_NO,
               CALCULATE_TIME,
               AUDIT_TIME,
               AUDIT_MSG,
               CASE_CONCLUSION,
               CONCLUSION_DETAIL_CODE,
               CONCLUSION_DETAIL_MSG,
               CASE_PAY_AMOUNT
        from clms_wesure_settle
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        and CASE_TIMES = #{caseTimes,jdbcType=VARCHAR}
        and SYNC_TYPE = #{syncType,jdbcType=INTEGER}
        and IS_EFFECTIVE = 'Y'
        order by CREATED_DATE desc
        limit 1
    </select>

    <select id="getWesurePolicyById" resultMap="wesurePolicyMap">
        select ID_CLMS_WESURE_POLICY,
               WESURE_POLICY_NO,
               POLICY_NO,
               POLICY_PAY_AMOUNT
        from clms_wesure_policy
        where ID_CLMS_WESURE_SETTLE = #{idClmsWesureSettle,jdbcType=VARCHAR}
        and IS_EFFECTIVE = 'Y'
    </select>

    <select id="getWesureBenefitById" resultMap="wesureBenefitMap">
        select ID_CLMS_WESURE_BENEFIT,
               BENEFIT_CODE,
               BENEFIT_NAME,
               ifnull(BENEFIT_ADJUST_AMOUNT,0) BENEFIT_ADJUST_AMOUNT,
               ifnull(BENEFIT_PAY_AMOUNT,0) BENEFIT_PAY_AMOUNT
        from CLMS_WESURE_BENEFIT
        where ID_CLMS_WESURE_POLICY = #{idClmsWesurePolicy,jdbcType=VARCHAR}
          and IS_EFFECTIVE = 'Y'
    </select>

    <select id="getWesureReceiptById" resultMap="wesureReceiptMap">
        select RECEIPT_NO,
               BENEFIT_CODE,
               TOTAL_PAY,
               INSURE_PAY,
               INSURE_SELF_PAY,
               CLASS_RESPONSIBILITY,
               PERSONAL_RESPONSIBILITY,
               PERSONAL_PAY,
               SELF_PAY,
               OTHER_PAY,
               UNREASONABLE_AMOUNT
        from CLMS_WESURE_RECEIPT
        where ID_CLMS_WESURE_BENEFIT = #{idClmsWesureBenefit,jdbcType=VARCHAR}
          and IS_EFFECTIVE = 'Y'
    </select>

    <select id="getWesureMedicalList" parameterType="com.paic.ncbs.claim.model.dto.settle.WesureMedicalDTO" resultMap="wesureMedicalMap">
        select ID_CLMS_WESURE_MEDICAL,
               VISIT_TYPE,
               OUTPATINET_DATE,
               IN_HOSPITAL_DATE,
               OUT_HOSPITAL_DATE,
               IN_HOSPITAL_DAYS,
               VISIT_AMOUNT
        from CLMS_WESURE_MEDICAL
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        and CASE_TIMES = #{caseTimes, jdbcType=INTEGER}
        and IS_EFFECTIVE = 'Y'
        <if test="createdDate != null">
            and CREATED_DATE <![CDATA[ > ]]> #{createdDate}
        </if>
    </select>

    <select id="getWesureMedicalReceiptById" resultMap="wesureMedicalReceiptMap">
        select RECEIPT_NO,
               RECEIPT_CLASS,
               RECEIPT_TYPE,
               RECEIPT_CODE,
               RECEIPT_DATE,
               TOTAL_PAY,
               INSURE_PAY,
               INSURE_SELF_PAY,
               CLASS_RESPONSIBILITY,
               PERSONAL_RESPONSIBILITY,
               PERSONAL_PAY,
               SELF_PAY,
               OTHER_PAY,
               INPUT_MODE,
               COST_CODE,
               PREPAID_AMOUNT,
               PREPAID_TYPE,
               UNREASONABLE_AMOUNT,
               BILL_CLASS,
               BILL_TYPE,
               RISK_LABEL,
               HOSPITAL_CODE,
               HOSPITAL_NAME,
               HOSPITAL_GRADE
        from CLMS_WESURE_MEDICAL_RECEIPT
        where ID_CLMS_WESURE_MEDICAL = #{idClmsWesureMedical,jdbcType=VARCHAR}
          and IS_EFFECTIVE = 'Y'
    </select>

    <!--医疗单据-->
    <insert id="saveWesureMedical" parameterType="java.util.List">
        insert into clms_wesure_medical
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_CLMS_WESURE_MEDICAL,
        WESURE_REPORT_NO,
        REPORT_NO,
        CASE_TIMES,
        MEDICAL_SOURCE,
        VISIT_TYPE,
        OUTPATINET_DATE,
        IN_HOSPITAL_DATE,
        OUT_HOSPITAL_DATE,
        IN_HOSPITAL_DAYS,
        VISIT_AMOUNT
         )
        values
        <foreach collection="wesureMedicalList" separator="," index="index" item="item">
            (#{item.createdBy,jdbcType=VARCHAR},
            now(),
            #{item.updatedBy,jdbcType=VARCHAR},
            now(),
            #{item.idClmsWesureMedical,jdbcType=VARCHAR},
            #{item.wesureReportNo,jdbcType=VARCHAR},
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=INTEGER},
            #{item.medicalSource,jdbcType=VARCHAR},
            #{item.visitType,jdbcType=VARCHAR},
            #{item.outpatientDate,jdbcType=VARCHAR},
            #{item.inHospitalDate,jdbcType=VARCHAR},
            #{item.outHospitalDate,jdbcType=VARCHAR},
            #{item.inHospitalDays,jdbcType=VARCHAR},
            #{item.visitAmount,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <insert id="saveWesureMedicalReceipt" parameterType="java.util.List">
        insert into clms_wesure_medical_receipt
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_CLMS_WESURE_MEDICAL_RECEIPT,
        ID_CLMS_WESURE_MEDICAL,
        RECEIPT_NO,
        RECEIPT_CLASS,
        RECEIPT_TYPE,
        RECEIPT_CODE,
        RECEIPT_CHECK_CODE,
        RECEIPT_DATE,
        NATIONAL_HEALTH_CARE,
        TOTAL_PAY,
        INSURE_PAY,
        INSURE_SELF_PAY,
        CLASS_RESPONSIBILITY,
        PERSONAL_RESPONSIBILITY,
        PERSONAL_PAY,
        SELF_PAY,
        OTHER_PAY,
        INPUT_MODE,
        COST_CODE,
        PREPAID_AMOUNT,
        PREPAID_TYPE,
        UNREASONABLE_AMOUNT,
        BILL_CLASS,
        BILL_TYPE,
        RISK_LABEL,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        HOSPITAL_GRADE
         )
        values
        <foreach collection="wesureMedicalReceiptList" separator="," index="index" item="item">
            (#{item.createdBy,jdbcType=VARCHAR},
            now(),
            #{item.updatedBy,jdbcType=VARCHAR},
            now(),
            #{item.idClmsWesureMedicalReceipt,jdbcType=VARCHAR},
            #{item.idClmsWesureMedical,jdbcType=VARCHAR},
            #{item.receiptNo,jdbcType=VARCHAR},
            #{item.receiptClass,jdbcType=VARCHAR},
            #{item.receiptType,jdbcType=VARCHAR},
            #{item.receiptCode,jdbcType=VARCHAR},
            #{item.receiptCheckCode,jdbcType=VARCHAR},
            #{item.receiptDate,jdbcType=VARCHAR},
            #{item.nationalHealthCare,jdbcType=VARCHAR},
            #{item.totalPay,jdbcType=DECIMAL},
            #{item.insurePay,jdbcType=DECIMAL},
            #{item.insureSelfPay,jdbcType=DECIMAL},
            #{item.classResponsibility,jdbcType=DECIMAL},
            #{item.personalResponsibility,jdbcType=DECIMAL},
            #{item.personalPay,jdbcType=DECIMAL},
            #{item.selfPay,jdbcType=DECIMAL},
            #{item.otherPay,jdbcType=DECIMAL},
            #{item.inputMode,jdbcType=VARCHAR},
            #{item.costCode,jdbcType=VARCHAR},
            #{item.prepaidAmount,jdbcType=DECIMAL},
            #{item.prepaidType,jdbcType=VARCHAR},
            #{item.unreasonableAmount,jdbcType=DECIMAL},
            #{item.billClass,jdbcType=VARCHAR},
            #{item.billType,jdbcType=VARCHAR},
            #{item.riskLabel,jdbcType=VARCHAR},
            #{item.hospitalCode,jdbcType=VARCHAR},
            #{item.hospitalName,jdbcType=VARCHAR},
            #{item.hospitalGrade,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <insert id="saveWesureMedicalDisease" parameterType="java.util.List">
        insert into clms_wesure_medical_disease
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_CLMS_WESURE_MEDICAL_DISEASE,
        WESURE_REPORT_NO,
        REPORT_NO,
        CASE_TIMES,
        DISEASE_NAME,
        DISEASE_CODE)
        values
        <foreach collection="wesureMedicalDiseaseList" separator="," index="index" item="item">
            (#{item.createdBy,jdbcType=VARCHAR},
            now(),
            #{item.updatedBy,jdbcType=VARCHAR},
            now(),
            #{item.idClmsWesureMedicalDisease,jdbcType=VARCHAR},
            #{item.wesureReportNo,jdbcType=VARCHAR},
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=INTEGER},
            #{item.diseaseName,jdbcType=VARCHAR},
            #{item.diseaseCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="removeWesureMedical" parameterType="com.paic.ncbs.claim.model.dto.settle.WesureMedicalDTO">
        update clms_wesure_medical t
        set t.UPDATED_DATE = now(),
            t.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            t.IS_EFFECTIVE = 'N'
        where t.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </update>

    <update id="removeWesureMedicalReceipt" parameterType="com.paic.ncbs.claim.model.dto.settle.WesureMedicalDTO">
        update clms_wesure_medical_receipt t
        set t.UPDATED_DATE = now(),
            t.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            t.IS_EFFECTIVE = 'N'
        where t.ID_CLMS_WESURE_MEDICAL in
        (select ID_CLMS_WESURE_MEDICAL
        from clms_wesure_medical m
        where m.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        and m.IS_EFFECTIVE = 'Y')
    </update>

    <update id="removeWesureMedicalDisease" parameterType="com.paic.ncbs.claim.model.dto.settle.WesureMedicalDTO">
        update clms_wesure_medical_disease t
        set t.UPDATED_DATE = now(),
            t.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            t.IS_EFFECTIVE = 'N'
        where t.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </update>

    <select id="getWesureAutoClaim" parameterType="java.lang.String" resultType="java.lang.String">
        select wesure_auto_claim
        from clms_report_info_ex
        where report_no = #{reportNo,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="getDutyDetailPayList" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO"
            resultType="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO">
        select a.DUTY_DETAIL_CODE dutyDetailCode,
               a.DUTY_DETAIL_NAME dutyDetailName,
               ifnull(if(a.SETTLE_AMOUNT>0,a.SETTLE_AMOUNT,a.AUTO_SETTLE_AMOUNT),0) settleAmount,
               a.AUTO_SETTLE_AMOUNT autoSettleAmount,
               a.REASONABLE_AMOUNT reasonableAmount,
               a.POLICY_NO policyNo
        from clms_duty_detail_pay a,
             clm_policy_pay b
        where a.CASE_NO = b.CASE_NO
          and b.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          and b.CASE_TIMES = #{caseTimes, jdbcType=INTEGER}
          and a.IS_EFFECTIVE = 'Y'
    </select>

    <select id="getPolicyDutyDetailPayList" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO"
            resultType="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO">
        select distinct
               a.DUTY_DETAIL_CODE dutyDetailCode,
               a.DUTY_DETAIL_NAME dutyDetailName,
               0 settleAmount,
               0 reasonableAmount,
               d.POLICY_NO  policyNo
        from clms_policy_duty_detail a,
             clms_policy_duty b,
             clms_policy_plan c,
             clms_policy_info d
        where d.ID_AHCS_POLICY_INFO = c.ID_AHCS_POLICY_INFO
          and c.ID_AHCS_POLICY_PLAN = b.ID_AHCS_POLICY_PLAN
          and b.ID_AHCS_POLICY_DUTY = a.ID_AHCS_POLICY_DUTY
          and d.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <select id="getRefusePayDesc" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO" resultType="java.lang.String">
        select b.CONCLUSION_CAUSE_DESC
        from clms_verify_conclusion a ,clms_verify_cnclusion_cause b
        where a.CONLUSION_CAUSE_CODE = b.CONCLUSION_CAUSE_CODE
          and a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          and a.CASE_TIMES = #{caseTimes, jdbcType=INTEGER}
          and a.IS_EFFECTIVE = 'Y'
          and b.IS_EFFECTIVE = 'Y'
          and b.PARENT_CODE ='4'
          limit 1
    </select>

    <select id="getZeroCancelDesc" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO" resultType="java.lang.String">
        select b.VALUE_CHINESE_NAME
        from clm_common_parameter b
        where b.COLLECTION_CODE ='AHCS_CANCEL_REASON'
          and VALUE_CODE =
          (select a.REASON_CODE
            from clms_zero_cancel_apply a
            where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
            and CASE_TIMES = #{caseTimes, jdbcType=INTEGER}
            and VERIFY_OPTIONS ='1'
            and STATUS ='2'
            order by CREATED_DATE desc
            limit 1)
    </select>

    <select id="getBillAmounts" parameterType="com.paic.ncbs.claim.model.dto.settle.WesureMedicalDTO" resultType="com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO">
        select distinct BILL_NO      billNo,
               ifnull(BILL_AMOUNT,0) billAmount,
               DEDUCTIBLE_AMOUNT     deductibleAmount,
               PREPAID_AMOUNT        prepaidAmount,
               IMMODERATE_AMOUNT     immoderateAmount,
               PARTIAL_DEDUCTIBLE    partialDeductible,
               REASONABLE_AMOUNT     reasonableAmount
        from clms_bill_info
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          and CASE_TIMES = #{caseTimes, jdbcType=INTEGER}
          and IS_EFFECTIVE ='Y'
    </select>

    <select id="getWesureMedicalDisease" resultType="com.paic.ncbs.claim.model.dto.settle.WesureMedicalDiseaseDTO">
        select DISEASE_NAME,
               DISEASE_CODE
          from clms_wesure_medical_disease
         where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
           and CASE_TIMES = #{caseTimes, jdbcType=INTEGER}
           and IS_EFFECTIVE ='Y'
    </select>

</mapper>