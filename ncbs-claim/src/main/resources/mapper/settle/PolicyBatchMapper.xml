<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.PolicyBatchMapper">

    <!-- 保单保单理算批次 -->
    <resultMap id="policyBatch" type="com.paic.ncbs.claim.model.dto.settle.PolicyBatchPayDTO">
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="idAhcsPolicyBatchPay" column="ID_AHCS_POLICY_BATCH_PAY"/>
        <result property="idAhcsBatch" column="ID_AHCS_BATCH"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="migrateFrom" column="MIGRATE_FROM"/>
        <result property="policyPayAmount" column="POLICY_PAY_AMOUNT"/>
        <result property="policyFee" column="POLICY_FEE"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="claimType" column="CLAIM_TYPE"/>
        <result property="subTimes" column="SUB_TIMES"/>
        <result property="remark" column="REMARK"/>
        <result property="finalPayAmount" column="FINAL_PAY_AMOUNT"/>
        <result property="accommodationAmount" column="ACCOMMODATION_AMOUNT"/>
        <result property="finalFee" column="FINAL_FEE"/>
        <result property="policyDecreaseFee" column="DECREASE_FEE"/>
    </resultMap>

    <insert id="insertPolicyBatch"
            parameterType="com.paic.ncbs.claim.model.dto.settle.PolicyBatchPayDTO">
        insert into CLM_POLICY_BATCH_PAY (CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_CLM_POLICY_BATCH_PAY, POLICY_NO, CASE_NO,
        CLAIM_TYPE, CASE_TIMES, ID_CLM_BATCH, SUB_TIMES, POLICY_PAY_AMOUNT,
        POLICY_FEE, REMARK, MIGRATE_FROM,CONSULT_AMOUNT,FINAL_FEE,DECREASE_FEE,FINAL_PAY_AMOUNT,CURRENCY_CODE,ARCHIVE_DATE)
        values (
        #{policyBatch.createdBy},
        SYSDATE(),
        #{policyBatch.updatedBy},
        SYSDATE(),
        #{policyBatch.idAhcsPolicyBatchPay},
        #{policyBatch.policyNo},
        #{policyBatch.caseNo},
        #{policyBatch.claimType},
        #{policyBatch.caseTimes},
        #{policyBatch.idAhcsBatch},
        #{policyBatch.subTimes},
        #{policyBatch.policyPayAmount},
        #{policyBatch.policyFee},
        #{policyBatch.remark},
        'na',
        #{policyBatch.accommodationAmount},
        #{policyBatch.finalFee},
        #{policyBatch.policyDecreaseFee},
        #{policyBatch.finalPayAmount},
        '01',
        #{policyBatch.archiveDate}
        )

    </insert>

    <select id="getPolicyBatch" resultMap="policyBatch">
        SELECT P.CREATED_BY,
        P.CREATED_DATE,
        P.UPDATED_BY,
        P.UPDATED_DATE,
        P.ID_CLM_POLICY_BATCH_PAY ID_AHCS_POLICY_BATCH_PAY,
        P.ID_CLM_BATCH ID_AHCS_BATCH,
        P.CASE_TIMES,
        P.MIGRATE_FROM,
        P.POLICY_PAY_AMOUNT,
        P.POLICY_FEE,
        P.CASE_NO,
        P.POLICY_NO,
        P.CLAIM_TYPE,
        P.SUB_TIMES,
        P.REMARK,
        P.FINAL_PAY_AMOUNT,
        P.FINAL_FEE,
        P.DECREASE_FEE,
        P.CONSULT_AMOUNT ACCOMMODATION_AMOUNT
        FROM CLM_POLICY_BATCH_PAY P
        WHERE POLICY_NO = #{policyNo}
        AND CASE_TIMES = #{caseTimes}
        AND ID_CLM_BATCH = #{idAhcsBatch}
    </select>

    <select id="getPolicyBatchsByCliamType" resultMap="policyBatch">
        SELECT P.CREATED_BY,
        P.CREATED_DATE,
        P.UPDATED_BY,
        P.UPDATED_DATE,
        P.ID_CLM_POLICY_BATCH_PAY ID_AHCS_POLICY_BATCH_PAY,
        P.ID_CLM_BATCH ID_AHCS_BATCH,
        P.CASE_TIMES,
        P.MIGRATE_FROM,
        P.POLICY_PAY_AMOUNT,
        P.POLICY_FEE,
        P.CASE_NO,
        P.POLICY_NO,
        P.CLAIM_TYPE,
        P.SUB_TIMES,
        P.REMARK,
        P.FINAL_PAY_AMOUNT,
        P.CONSULT_AMOUNT ACCOMMODATION_AMOUNT
        FROM CLM_POLICY_BATCH_PAY P
        WHERE CASE_NO in (select PP.Case_No from clm_policy_pay PP where report_no =#{reportNo} and
        case_times = #{caseTimes})
        AND CASE_TIMES = #{caseTimes}
        AND EXISTS (SELECT 1
        FROM CLMS_POLICY_INFO A
        WHERE P.CASE_NO = A.CASE_NO
        AND P.POLICY_NO = A.POLICY_NO)
        <if test="claimType != null ">
            AND CLAIM_TYPE = #{claimType}
        </if>
    </select>

    <select id="listPolicyBatchs" resultMap="policyBatch">
        SELECT P.CREATED_BY,
        P.CREATED_DATE,
        P.UPDATED_BY,
        P.UPDATED_DATE,
        P.ID_CLM_POLICY_BATCH_PAY ID_AHCS_POLICY_BATCH_PAY,
        P.ID_CLM_BATCH ID_AHCS_BATCH,
        P.CASE_TIMES,
        P.MIGRATE_FROM,
        P.POLICY_PAY_AMOUNT,
        P.POLICY_FEE,
        P.CASE_NO,
        P.POLICY_NO,
        P.CLAIM_TYPE,
        P.SUB_TIMES,
        P.REMARK,
        P.FINAL_FEE,
        P.FINAL_PAY_AMOUNT,
        P.DECREASE_FEE,
        P.CONSULT_AMOUNT ACCOMMODATION_AMOUNT
        FROM CLM_POLICY_BATCH_PAY P
        WHERE CASE_NO in (select distinct PP.Case_No from clm_policy_pay PP where report_no =#{reportNo} and
        CASE_TIMES = #{caseTimes})
        AND CASE_TIMES = #{caseTimes}
        AND EXISTS (SELECT 1
        FROM CLMS_POLICY_INFO A
        WHERE P.CASE_NO = A.CASE_NO
        AND P.POLICY_NO = A.POLICY_NO)
    </select>

    <insert id="bathInsertPolicyBath" parameterType="java.util.List">
            INSERT INTO CLM_POLICY_BATCH_PAY
            (CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_CLM_POLICY_BATCH_PAY,
            POLICY_NO,
            CASE_NO,
            CLAIM_TYPE,
            CASE_TIMES,
            ID_CLM_BATCH,
            SUB_TIMES,
            POLICY_PAY_AMOUNT,
            POLICY_FEE,
            REMARK,
            MIGRATE_FROM,
            CONSULT_AMOUNT,
            FINAL_FEE,
            FINAL_PAY_AMOUNT,
            CURRENCY_CODE,
            DECREASE_FEE,
            CHASE_FEE_OUT,
            ARCHIVE_DATE
            )
            values
        <foreach collection="policyBathPayList" item="policyBatch" separator=",">
            (
            #{policyBatch.createdBy},
            now(),
            #{policyBatch.updatedBy},
            now(),
            #{policyBatch.idAhcsPolicyBatchPay},
            #{policyBatch.policyNo},
            #{policyBatch.caseNo},
            #{policyBatch.claimType},
            #{policyBatch.caseTimes},
            #{policyBatch.idAhcsBatch},
            #{policyBatch.subTimes},
            #{policyBatch.policyPayAmount},
            #{policyBatch.policyFee},
            #{policyBatch.remark},
            'na',
            #{policyBatch.accommodationAmount},
            #{policyBatch.finalFee},
            #{policyBatch.finalPayAmount},
            '01',
            #{policyBatch.policyDecreaseFee},
            #{policyBatch.chaseFeeOut},
            now()
            )
        </foreach>
    </insert>

    <update id="bathUpdatePolicyBath" parameterType="java.util.List">
            update CLM_POLICY_BATCH_PAY SET
            <if test="updatedBy != null and updatedBy != '' ">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="caseTimes != null ">
                CASE_TIMES = #{caseTimes},
            </if>
            <if test="caseNo != null and caseNo != '' ">
                CASE_NO = #{caseNo},
            </if>
            <if test="policyNo != null and policyNo != '' ">
                POLICY_NO = #{policyNo},
            </if>
            <if test="claimType != null ">
                CLAIM_TYPE = #{claimType},
            </if>
            <if test="subTimes != null ">
                SUB_TIMES = #{subTimes},
            </if>
            <if test="policyPayAmount != null ">
                POLICY_PAY_AMOUNT = #{policyPayAmount},
            </if>
            <if test="remark != null ">
                REMARK = #{remark},
            </if>
            <if test="policyFee != null ">
                POLICY_FEE = #{policyFee},
            </if>
            <if test="finalPayAmount != null ">
                FINAL_PAY_AMOUNT = #{finalPayAmount},
            </if>
            <if test="finalFee != null ">
                FINAL_FEE = #{finalFee},
            </if>
            <if test="policyDecreaseFee != null ">
                DECREASE_FEE = #{policyDecreaseFee},
            </if>
            <if test="migrateFrom != null ">
                MIGRATE_FROM = #{migrateFrom},
            </if>
            <if test="accommodationAmount != null ">
                CONSULT_AMOUNT = #{accommodationAmount},
            </if>
            <if test="claimCoinsuranceAmount != null ">
                CLAIM_COINSURANCE_AMOUNT = #{claimCoinsuranceAmount},
            </if>
            <if test="claimCoinsuranceFee != null ">
                CLAIM_COINSURANCE_FEE = #{claimCoinsuranceFee},
            </if>
            UPDATED_DATE = SYSDATE() where ID_CLM_POLICY_BATCH_PAY = #{idAhcsPolicyBatchPay}
    </update>


    <delete id="deleteByReportNo">
        delete from CLM_POLICY_BATCH_PAY where case_no in (select case_no from clm_case_base where
        report_no=#{reportNo}) and case_times=#{caseTimes}
    </delete>

    <update id="bathUpdatePolicy">
        <foreach collection="list"  item="item" index="index" open="" close="" separator=";">
        update CLM_POLICY_BATCH_PAY set
           <if test="item.updatedBy != null and item.updatedBy != '' ">
               UPDATED_BY = #{item.updatedBy},
           </if>
           <if test="item.caseTimes != null ">
               CASE_TIMES = #{item.caseTimes},
           </if>
           <if test="item.caseNo != null and item.caseNo != '' ">
               CASE_NO = #{item.caseNo},
           </if>
           <if test="item.policyNo != null and item.policyNo != '' ">
               POLICY_NO = #{item.policyNo},
           </if>
           <if test="item.claimType != null ">
               CLAIM_TYPE = #{item.claimType},
           </if>
           <if test="item.subTimes != null ">
               SUB_TIMES = #{item.subTimes},
           </if>
           <if test="item.policyPayAmount != null ">
               POLICY_PAY_AMOUNT = #{item.policyPayAmount},
           </if>
           <if test="item.remark != null ">
               REMARK = #{item.remark},
           </if>
           <if test="item.policyFee != null ">
               POLICY_FEE = #{item.policyFee},
           </if>
           <if test="item.finalPayAmount != null ">
               FINAL_PAY_AMOUNT = #{item.finalPayAmount},
           </if>
           <if test="item.finalFee != null ">
               FINAL_FEE = #{item.finalFee},
           </if>
           <if test="item.policyDecreaseFee != null ">
               DECREASE_FEE = #{item.policyDecreaseFee},
           </if>
           <if test="item.migrateFrom != null ">
               MIGRATE_FROM = #{item.migrateFrom},
           </if>
           <if test="item.accommodationAmount != null ">
               CONSULT_AMOUNT = #{item.accommodationAmount},
           </if>
           <if test="item.claimCoinsuranceAmount != null ">
               CLAIM_COINSURANCE_AMOUNT = #{item.claimCoinsuranceAmount},
           </if>
           <if test="item.claimCoinsuranceFee != null ">
               CLAIM_COINSURANCE_FEE = #{item.claimCoinsuranceFee},
           </if>
           UPDATED_DATE = SYSDATE() where ID_CLM_POLICY_BATCH_PAY = #{item.idAhcsPolicyBatchPay}
       </foreach>

    </update>

</mapper>