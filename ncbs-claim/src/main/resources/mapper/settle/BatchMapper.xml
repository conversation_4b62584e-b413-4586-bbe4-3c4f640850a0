<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.BatchMapper">
    <!-- 批次（整案） -->
    <resultMap type="com.paic.ncbs.claim.model.dto.settle.BatchDTO" id="batchDto">
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="idAhcsBatch" column="ID_CLM_BATCH"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="settleUserUm" column="SETTLE_USER_UM"/>
        <result property="settleTime" column="SETTLE_TIME"/>
        <result property="settleStatus" column="SETTLE_STATUS"/>
        <result property="batchSettleType" column="BATCH_SETTLE_TYPE"/>
        <result property="settleUserName" column="USER_NAME"/>
        <result property="migrateFrom" column="MIGRATE_FROM"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.BatchDTO" id="batchDTO">
        <result property="idAhcsBatch" column="idAhcsBatch"/>
        <result property="reportNo" column="reportNo"/>
        <result property="caseTimes" column="caseTimes"/>
        <result property="settleUserUm" column="settleUserUm"/>
        <result property="settleUserName" column="USER_NAME"/>
        <result property="settleTime" column="settleTime"/>
        <result property="settleStatus" column="settleStatus"/>
        <result property="batchSettleType" column="batchSettleType"/>
        <result property="migrateFrom" column="migrateFrom"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
    </resultMap>

    <insert id="insertBatch" parameterType="com.paic.ncbs.claim.model.dto.settle.BatchDTO">
        insert into clm_batch (CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_CLM_BATCH, REPORT_NO,
        CASE_TIMES, SETTLE_USER_UM, SETTLE_TIME, SETTLE_STATUS, BATCH_SETTLE_TYPE,
        MIGRATE_FROM,ARCHIVE_DATE) values (
        #{batchDto.createdBy,jdbcType=VARCHAR},
        sysdate(),
        #{batchDto.updatedBy,jdbcType=VARCHAR},
        sysdate(),
        #{batchDto.idAhcsBatch,jdbcType=VARCHAR},
        #{batchDto.reportNo,jdbcType=VARCHAR},
        #{batchDto.caseTimes,jdbcType=INTEGER},
        #{batchDto.settleUserUm,jdbcType=VARCHAR},
        sysdate(),
        #{batchDto.settleStatus,jdbcType=VARCHAR},
        #{batchDto.batchSettleType,jdbcType=VARCHAR},
        IFNULL((select t.migrate_from from clm_whole_case_base t where t.report_no=#{batchDto.reportNo,jdbcType=VARCHAR}
        and t.case_times=#{batchDto.caseTimes,jdbcType=INTEGER} limit 1),'na'),
        #{batchDto.archiveDate}
        )
    </insert>

    <select id="getBatch" resultMap="batchDTO">
        SELECT
        b.UPDATED_DATE updatedDate,
        b.ID_CLM_BATCH idAhcsBatch,
        b.REPORT_NO reportNo,
        b.CASE_TIMES caseTimes,
        b.SETTLE_USER_UM settleUserUm,
        (select USER_NAME from CLMS_USER_INFO where USER_ID=b.SETTLE_USER_UM
        and USER_STATUS = '1') USER_NAME,
        b.SETTLE_TIME settleTime,
        b.SETTLE_STATUS settleStatus,
        b.BATCH_SETTLE_TYPE batchSettleType,
        b.MIGRATE_FROM migrateFrom
        FROM CLM_BATCH b
        WHERE b.REPORT_NO=#{reportNo,jdbcType=VARCHAR}
        AND b.CASE_TIMES=#{caseTimes,jdbcType=INTEGER}
        AND IFNULL(b.batch_settle_type,'01') = '01'
        limit 1
        <!-- AND   b.BATCH_SETTLE_TYPE=#{batchSettleType,JDBCTYPE=VARCHAR} -->
    </select>

    <select id="getBatchInfo" resultMap="batchDto">
        select ID_CLM_BATCH ,
        REPORT_NO ,
        CASE_TIMES ,
        SETTLE_USER_UM,
        SETTLE_TIME,
        SETTLE_STATUS,
        QUALITY_CHECK_USER_UM ,
        QUALITY_CHECK_TIME ,
        QUALITY_CHECK_STATUS,
        PAY_USER_UM ,
        PAY_TIME ,
        PAY_STATUS ,
        MIGRATE_FROM ,
        BATCH_SETTLE_TYPE ,
        IS_SETTLE_RULE_PASS,
        IS_AUTO_SETTLE from clm_batch where report_no = #{reportNo,jdbcType=VARCHAR} and case_times =
        #{caseTimes,jdbcType=INTEGER}
        AND IFNULL(batch_settle_type,'01') = '01'
        limit 1;
    </select>

    <update id="updateBatch" parameterType="com.paic.ncbs.claim.model.dto.settle.BatchDTO">
        update CLM_BATCH SET
        SETTLE_TIME = SYSDATE(),
        <if test="batchDto.updatedBy != null and batchDto.updatedBy != '' ">
            UPDATED_BY = #{batchDto.updatedBy},
        </if>
        <if test="batchDto.settleUserUm != null and batchDto.settleUserUm != '' ">
            SETTLE_USER_UM = #{batchDto.settleUserUm},
        </if>
        <if test="batchDto.settleStatus != null ">
            SETTLE_STATUS = #{batchDto.settleStatus},
        </if>
        <if test="batchDto.batchSettleType != null ">
            BATCH_SETTLE_TYPE = #{batchDto.batchSettleType},
        </if>

        UPDATED_DATE = SYSDATE() where ID_CLM_BATCH = #{batchDto.idAhcsBatch}
    </update>

    <update id="updateSettleTime">
        update CLM_BATCH SET
        SETTLE_TIME = SYSDATE(),
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = SYSDATE() where REPORT_NO=#{reportNo,jdbcType=VARCHAR}
        AND CASE_TIMES=#{caseTimes,jdbcType=INTEGER} AND BATCH_SETTLE_TYPE=#{batchSettleType,jdbcType=VARCHAR}
    </update>

    <delete id="deleteBatch">
        DELETE FROM CLM_BATCH
        where REPORT_NO=#{reportNo,jdbcType=VARCHAR}
        and CASE_TIMES=#{caseTimes,jdbcType=INTEGER}
    </delete>

    <!-- 通过报案号和赔付次数获取理算人 -->
    <select id="getSettleUserUm" resultType="string">
        SELECT b.SETTLE_USER_UM
        FROM CLM_BATCH b
        WHERE b.REPORT_NO=#{reportNo}
        AND b.CASE_TIMES=#{caseTimes}
        AND IFNULL(b.batch_settle_type,'01') = '01'
        LIMIT 1;
    </select>

</mapper>