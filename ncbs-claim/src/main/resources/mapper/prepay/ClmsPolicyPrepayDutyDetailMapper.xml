<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.prepay.ClmsPolicyPrepayDutyDetailMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.prepay.ClmsPolicyPrepayDutyDetailEntity" id="ClmsPolicyPrepayDutyDetailMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="subTimes" column="sub_times" jdbcType="INTEGER"/>
        <result property="caseNo" column="case_no" jdbcType="VARCHAR"/>
        <result property="dutyCode" column="duty_code" jdbcType="VARCHAR"/>
        <result property="dutyDetailCode" column="duty_detail_code" jdbcType="VARCHAR"/>
        <result property="dutyDetailName" column="duty_detail_name" jdbcType="VARCHAR"/>
        <result property="dutyDetailType" column="duty_detail_type" jdbcType="VARCHAR"/>
        <result property="dutyDetailAmount" column="duty_detail_amount" jdbcType="NUMERIC"/>
        <result property="prepayAmount" column="prepay_amount" jdbcType="NUMERIC"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
        <result property="planCode" column="plan_Code" jdbcType="VARCHAR"/>
        <result property="planName" column="plan_Name" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="all_columns" >
        id,report_no,case_times,sub_times,sub_times,case_no,duty_code,duty_detail_code,duty_detail_name,duty_detail_type,duty_detail_amount,prepay_amount,created_by,created_date,updated_by,updated_date,plan_code,plan_name
    </sql>
    <!--查询单个-->
    <select id="queryById" resultMap="ClmsPolicyPrepayDutyDetailMap">
        select id,report_no,case_times,case_no,duty_code,duty_detail_code,duty_detail_name,duty_detail_type,duty_detail_amount,prepay_amount,created_by,created_date,updated_by,updated_date
        from clms_policy_prepay_duty_detail
        where id = #{id}
    </select>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_policy_prepay_duty_detail(id,report_no,case_times,sub_times,case_no,plan_code,plan_name,duty_code,duty_detail_code,duty_detail_name,duty_detail_type,duty_detail_amount,prepay_amount,created_by,created_date,updated_by,updated_date)
        values (#{id},#{reportNo},#{caseTimes},#{subTimes},#{caseNo},#{planCode},#{planName},#{dutyCode},#{dutyDetailCode},#{dutyDetailName},#{dutyDetailType},#{dutyDetailAmount},#{prepayAmount},#{createdBy},#{createdDate},#{updatedBy},#{updatedDate})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        clms_policy_prepay_duty_detail(id,report_no,case_times,sub_times,case_no,plan_code,plan_name,duty_code,duty_detail_code,duty_detail_name,duty_detail_type,duty_detail_amount,prepay_amount,created_by,created_date,updated_by,updated_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportNo},#{entity.caseTimes},#{entity.subTimes},#{entity.caseNo},#{entity.planCode},#{entity.planName},#{entity.dutyCode},#{entity.dutyDetailCode},#{entity.dutyDetailName},#{entity.dutyDetailType},#{entity.dutyDetailAmount},#{entity.prepayAmount},#{entity.createdBy},#{entity.createdDate},#{entity.updatedBy},#{entity.updatedDate})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        clms_policy_prepay_duty_detail(id,report_no,case_times,sub_times,case_no,plan_code,plan_name,duty_code,duty_detail_code,duty_detail_name,duty_detail_type,duty_detail_amount,prepay_amount,created_by,created_date,updated_by,updated_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportNo},#{entity.caseTimes},#{entity.subTimes},#{entity.caseNo},#{entity.planCode},#{entity.planName},#{entity.dutyCode},#{entity.dutyDetailCode},#{entity.dutyDetailName},#{entity.dutyDetailType},#{entity.dutyDetailAmount},#{entity.prepayAmount},#{entity.createdBy},#{entity.createdDate},#{entity.updatedBy},#{entity.updatedDate})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_policy_prepay_duty_detail
        <set>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="caseNo != null and caseNo != ''">
                case_no = #{caseNo},
            </if>
            <if test="dutyCode != null and dutyCode != ''">
                duty_code = #{dutyCode},
            </if>
            <if test="dutyDetailCode != null and dutyDetailCode != ''">
                duty_detail_code = #{dutyDetailCode},
            </if>
            <if test="dutyDetailName != null and dutyDetailName != ''">
                duty_detail_name = #{dutyDetailName},
            </if>
            <if test="dutyDetailType != null and dutyDetailType != ''">
                duty_detail_type = #{dutyDetailType},
            </if>
            <if test="dutyDetailAmount != null">
                duty_detail_amount = #{dutyDetailAmount},
            </if>
            <if test="prepayAmount != null">
                prepay_amount = #{prepayAmount},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_policy_prepay_duty_detail
        where id = #{id}
    </delete>
    <!--根据报案号，赔付次数，预赔次数查询 -->
    <select id="getDutyDetailInfo" resultMap="ClmsPolicyPrepayDutyDetailMap">
        select <include refid="all_columns"></include>
        from clms_policy_prepay_duty_detail
        where REPORT_NO=#{reportNo}
        and CASE_TIMES=#{caseTimes}
        and SUB_TIMES=#{subTimes}
    </select>
    <select id="checkPrePayAmount" resultType="java.lang.Integer">
        select count(*)
        from clms_policy_prepay_duty_detail cppdd
        inner join clms_duty_detail_pay cp on cppdd.CASE_NO = cp.CASE_NO and cppdd.CASE_TIMES =cp.CASE_TIMES and cp.DUTY_CODE = cppdd.duty_code and cp.DUTY_DETAIL_CODE = cppdd.duty_detail_code
        where cp.CLAIM_TYPE ='1' and (cp.SETTLE_AMOUNT <![CDATA[<=]]> cppdd.prepay_amount or (cp.SETTLE_AMOUNT is null and cppdd.prepay_amount is not null))
        and cppdd.report_no  = #{reportNo}
        and cppdd.case_times = #{caseTimes};
    </select>
    <select id="selectPrePayAmountAndCode" resultType="com.paic.ncbs.claim.dao.entity.prepay.ClmsPolicyPrepayDutyDetailEntity">
        select (select t3.duty_name from CLMS_policy_info t1,
                CLMS_policy_plan t2, CLMS_policy_duty t3
                where t1.id_ahcs_policy_info = t2.id_ahcs_policy_info
                and t2.id_ahcs_policy_plan = t3.id_ahcs_policy_plan
                and t1.report_no = #{reportNo} and t3.duty_code = cppdd.duty_code) as dutyCode,
                sum(cppdd.prepay_amount) as prepayAmount
                from clms_policy_prepay_duty_detail cppdd,clms_prepay_info cpi  where cppdd.report_no = #{reportNo} and cppdd.case_times =#{caseTimes}
                and cppdd.report_no =cpi.REPORT_NO and cppdd.case_times =cpi.CASE_TIMES and cpi.VERIFY_OPTIONS !='2' and cppdd.sub_times = cpi.SUB_TIMES
                and cppdd.prepay_amount is not null
                group by cppdd.duty_code ;
    </select>

</mapper>

