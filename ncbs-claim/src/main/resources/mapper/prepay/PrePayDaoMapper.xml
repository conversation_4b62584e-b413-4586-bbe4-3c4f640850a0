<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.prepay.PrePayMapper">
	
	<resultMap type="com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO" id="prePayInfo">
		<result property="reportNo" column="REPORT_NO"/>
		<result property="caseTimes" column="CASE_TIMES"/>
		<result property="prePayInfoId" column="ID_AHCS_PREPAY_INFO"/>
		<result property="applyBy" column="APPLY_BY"/>
		<result property="applyName" column="APPLY_BY_NAME"/>
		<result property="applyDate" column="APPLY_DATE"/>
		<result property="approverBy" column="APPROVER_BY"/>
		<result property="approverName" column="APPROVER_BY_NAME"/>
		<result property="approverDate" column="APPROVER_DATE"/>
		<result property="prePayAmount" column="PREPAY_AMOUNT"/>
		<result property="prePayCause" column="PREPAY_CAUSE"/>
	</resultMap>

	<select id="getNoFinishPrePay" resultType="java.lang.Integer">
		SELECT count(1) FROM CLMS_PREPAY_INFO t
		  WHERE t.report_No = #{reportNo,jdbcType=VARCHAR}
		    AND t.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		    AND t.STATUS !='2'
	</select>

	<insert id="savePrePayInfo" parameterType="com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO">
		 INSERT INTO CLMS_PREPAY_INFO
		    (CREATED_BY,
		     CREATED_DATE,
		     UPDATED_BY,
		     UPDATED_DATE,
		     ID_AHCS_PREPAY_INFO,
		     REPORT_NO,
		     CASE_TIMES,
		     PREPAY_TYPE,
		     PREPAY_AMOUNT,
		     APPROVER_CAUSE,
		     PREPAY_CAUSE,
		     STATUS,
		     APPLY_BY,
		     APPROVER_BY,
		     APPLY_DATE,
		     APPROVER_DATE,
		     APPLY_REMARK,
		     APPROVER_REMARK,
		     VERIFY_OPTIONS,
		     PREPAY_BIG_TYPE,
		     APPLY_AMOUNT,
		     APPLY_BY_NAME,
		     ARCHIVE_TIME,
			TASK_DEFINITION_BPM_KEY
			,SUB_TIMES)
		  VALUES
		    (
			 #{createdBy,jdbcType=VARCHAR},
		     NOW(),
		     #{updatedBy,jdbcType=VARCHAR},
		     NOW(),
		     #{prePayInfoId,jdbcType=VARCHAR},
		     #{reportNo,jdbcType=VARCHAR},
		     #{caseTimes,jdbcType=VARCHAR},
		     #{prePayType,jdbcType=VARCHAR},
		     #{prePayAmount,jdbcType=NUMERIC},
		     #{approverCause,jdbcType=VARCHAR},
		     #{prePayCause,jdbcType=VARCHAR},
		     #{status,jdbcType=VARCHAR},
		     #{applyBy,jdbcType=VARCHAR},
		     #{approverBy,jdbcType=VARCHAR},
		     NOW(),
		     #{approverDate,jdbcType=VARCHAR},
		     #{applyRemark,jdbcType=VARCHAR},
		     #{approverRemark,jdbcType=VARCHAR},
		     #{verifyOptions,jdbcType=VARCHAR},
		     #{prepayBigType,jdbcType=VARCHAR},
		     #{applyAmount,jdbcType=NUMERIC},
		     #{applyName,jdbcType=VARCHAR},
		     NOW(),
			#{taskDefinitionBpmKey}
			,#{subTimes}
        )
	</insert>

	<select id="getDutyPrepayInfoList" parameterType="com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO"
			resultType="com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO">
		SELECT t.case_no caseNo,
		t.report_no reportNo,
		t.case_times caseTimes,
		t.sub_times subTimes,
		t.policy_no policyNo,
		t.plan_code planCode,
		t.duty_code dutyCode,
		t.duty_pay_amount dutyPayAmount
		FROM CLMS_duty_prepay_info t, CLMS_prepay_info pi
		WHERE pi.id_ahcs_prepay_info =t.id_ahcs_prepay_info and T.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND t.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		<if test="subTimes!=null">
			AND T.SUB_TIMES = #{subTimes,jdbcType=NUMERIC}
		</if>
		<if test="policyNo!=null and policyNo!=''">
			AND T.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="planCode!=null and planCode!=''">
			AND T.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
		</if>
		<if test="dutyCode!=null and dutyCode!=''">
			AND T.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
		</if>

	</select>

	<insert id="saveDutyPrepayInfo" parameterType="java.util.List">
			INSERT INTO CLMS_DUTY_PREPAY_INFO
			  (CREATED_BY,
			   CREATED_DATE,
			   UPDATED_BY,
			   UPDATED_DATE,
		       ID_AHCS_DUTY_PREPAY_INFO,
			   REPORT_NO,
			   CASE_TIMES,
			   SUB_TIMES,
			   POLICY_NO,
			   PLAN_CODE,
			   DUTY_CODE,
			   DUTY_PAY_AMOUNT,
               ID_AHCS_PREPAY_INFO,
               CASE_NO,
		       ARCHIVE_TIME
            ) VALUES
		<foreach collection="dutyPrepayList" separator="," item="duty">
		(
			  #{duty.createdBy,jdbcType=VARCHAR},
		      NOW(),
		      #{duty.updatedBy,jdbcType=VARCHAR},
		      NOW(),
			 #{duty.idAhcsDutyPrepayInfo,jdbcType=VARCHAR},
		      #{duty.reportNo,jdbcType=VARCHAR},
		      #{duty.caseTimes,jdbcType=NUMERIC},
		      #{duty.subTimes,jdbcType=NUMERIC},
		      #{duty.policyNo,jdbcType=VARCHAR},
		      #{duty.planCode,jdbcType=VARCHAR},
		      #{duty.dutyCode,jdbcType=VARCHAR},
		      #{duty.dutyPayAmount,jdbcType=VARCHAR},
              #{duty.idAhcsPrepayInfo},
              #{duty.caseNo},
			  NOW()
            )
		</foreach>
	</insert>

	<select id="getApprovedSubTimes" resultType="java.lang.Integer">
		 SELECT IFNULL(T.SUB_TIMES,0)
	   FROM CLMS_PREPAY_INFO T
	  WHERE T.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
	    AND T.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		and T.STATUS = '2'
		order by T.created_date desc limit 1
	</select>

	<select id="getCurrentApprovedSubTimes" resultType="java.lang.Integer">
		SELECT IFNULL(T.SUB_TIMES,0)
		FROM CLMS_PREPAY_INFO T
		WHERE T.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND T.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		order by T.created_date desc limit 1
	</select>

	<select id="getPrePayDutyList" resultType="com.paic.ncbs.claim.model.vo.ahcs.PreDutyVO">
		select a.ID_AHCS_POLICY_INFO idAhcsPolicyInfo,
		a.POLICY_NO policyNo,
		a.CASE_NO caseNo,
		a.DEPARTMENT_CODE departmentCode,
		(select t3.DEPARTMENT_ABBR_NAME  from department_define t3 where t3 .DEPARTMENT_CODE = a.DEPARTMENT_CODE ) departmentName,
		a.COINSURANCE_MARK coinsuranceMark,
		b.PLAN_CODE planCode,
		b.PLAN_NAME planName,
		c.DUTY_CODE dutyCode,
		c.DUTY_NAME dutyName,
		c.DUTY_AMOUNT dutyAmount,
		c.INSURANCE_BEGIN_DATE insuranceBeginDate,
		c.INSURANCE_END_DATE insuranceEndDate,
		c.id_ahcs_policy_duty idPolicyDuty,
		c.IS_DUTY_SHARED_AMOUNT dutyShareAmount,
		c.DUTY_SHARED_AMOUNT_MERGE  shareDutyGroup
		from clms_policy_info a,clms_policy_plan b,clms_policy_duty c
		where a.ID_AHCS_POLICY_INFO =b.ID_AHCS_POLICY_INFO
		and b.ID_AHCS_POLICY_PLAN =c.ID_AHCS_POLICY_PLAN
		and a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		order by c.DUTY_SHARED_AMOUNT_MERGE

	</select>
	<select id="getPrePayDutyDetailList" resultType="com.paic.ncbs.claim.model.dto.prepayinfo.ClmsPolicyPrepayDutyDetailDTO">
		select
		c.DUTY_CODE dutyCode,
		d.DUTY_DETAIL_CODE dutyDetailCode,
		d.duty_detail_name dutyDetailName,
		d.duty_detail_type dutyDetailType ,
		d.DUTY_AMOUNT dutyDetailAmount
		from clms_policy_info a,clms_policy_plan b,clms_policy_duty c,clms_policy_duty_detail d
		where a.ID_AHCS_POLICY_INFO =b.ID_AHCS_POLICY_INFO
		and b.ID_AHCS_POLICY_PLAN =c.ID_AHCS_POLICY_PLAN
		and c.ID_AHCS_POLICY_DUTY=d.ID_AHCS_POLICY_DUTY
		and a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		order by c.DUTY_SHARED_AMOUNT_MERGE

	</select>

	<select id="getHistoryPrePayApprove" resultType="com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO">
		select  t.ID_ahcs_prepay_info prePayInfoId,
		t.REPORT_NO          reportNo,
		t.CASE_TIMES         caseTimes,
		t.PREPAY_AMOUNT      prePayAmount,
		t.PREPAY_CAUSE       prePayCause,
		t.STATUS             status,
		t.APPLY_BY           applyBy,
		t.APPLY_BY_NAME      applyName,
		t.APPROVER_BY        approverBy,
		t.APPROVER_BY_NAME   approverName,
		t.APPLY_DATE         applyDate,
		t.APPROVER_DATE      approverDate,
		t.APPROVER_REMARK    approverRemark,
		t.VERIFY_OPTIONS     verifyOptions,
		t.APPLY_AMOUNT       applyAmount,
		t.SUB_TIMES          subTimes
		from CLMS_PREPAY_INFO t
		where t.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND T.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND T.STATUS = '2'
		order by t.Created_Date
	</select>

	<select id="getPrePayWaitApprove" resultType="com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO">
		select
		t.ID_ahcs_prepay_info prePayInfoId,
		t.REPORT_NO          reportNo,
		t.CASE_TIMES         caseTimes,
		t.PREPAY_AMOUNT      prePayAmount,
		t.PREPAY_CAUSE       prePayCause,
		t.APPLY_REMARK       applyRemark,
		t.STATUS             status,
		t.APPLY_BY           applyBy,
		t.APPLY_BY_NAME      applyName,
		t.APPLY_DATE         applyDate,
		t.VERIFY_OPTIONS     verifyOptions,
		t.PREPAY_BIG_TYPE    prepayBigType,
		t.TASK_DEFINITION_BPM_KEY taskDefinitionBpmKey,
		t.SUB_TIMES          subTimes
		from CLMS_PREPAY_INFO t
		where t.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND T.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND T.STATUS = '1'
	</select>

	<update id="updatePrePayApprove" parameterType="com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO">
		update CLMS_prepay_info
		SET
		updated_by = #{updatedBy,jdbcType=VARCHAR},
		updated_date = NOW(),
		approver_date = NOW(),
		status = '2',
		approver_remark = #{approverRemark,jdbcType=VARCHAR},
		verify_options = #{verifyOptions,jdbcType=VARCHAR},
		approver_by = #{approverBy,jdbcType=VARCHAR},
		approver_by_name = #{approverName,jdbcType=VARCHAR}
		WHERE id_ahcs_prepay_info = #{prePayInfoId,jdbcType=VARCHAR}
	</update>

	<select id="getPrePayHistory" resultType="java.lang.Integer">
		SELECT count(1) FROM CLMS_PREPAY_INFO t
		WHERE t.report_No = #{reportNo,jdbcType=VARCHAR}
		AND t.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND t.VERIFY_OPTIONS != '2'
		limit 1
	</select>

	<select id="getPrePayCount" resultType="java.lang.Integer">
		SELECT count(1) FROM CLMS_PREPAY_INFO t
		WHERE t.report_No = #{reportNo,jdbcType=VARCHAR}
		AND t.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND (t.STATUS = '1' or VERIFY_OPTIONS = '1')
	</select>

	<select id="getPrePayApplyDutyList" resultType="com.paic.ncbs.claim.model.vo.ahcs.PreDutyVO">
		select POLICY_NO    policyNo,
			CASE_NO         caseNo,
			PLAN_CODE       planCode,
			DUTY_CODE       dutyCode,
			DUTY_PAY_AMOUNT dutyPreAmount
		from clms_duty_prepay_info
		where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		and CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		and SUB_TIMES = #{subTimes,jdbcType=NUMERIC}
	</select>

	<delete id="deleteDutyPrepayInfoByReportNo" >
		delete from clms_duty_prepay_info
		WHERE REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		and CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		and SUB_TIMES = #{subTimes,jdbcType=NUMERIC}
	</delete>

	<select id="getPreBigType" resultType="java.lang.String" parameterType="java.lang.String">
		select prepay_big_type
		from clms_prepay_info
		where id_ahcs_prepay_info = #{prePayInfoId,jdbcType=VARCHAR}
	</select>

	<select id="getPrePayApplyCount" resultType="java.lang.Integer">
		SELECT count(1) FROM CLMS_PREPAY_INFO t
		WHERE t.report_No = #{reportNo,jdbcType=VARCHAR}
		AND t.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
	</select>

	<select id="getDutyPrepaySum" resultType="com.paic.ncbs.claim.model.vo.ahcs.PreDutyVO">
		select t.policy_no  policyNo,
		t.plan_code       	planCode,
		t.duty_code      	dutyCode,
		sum(t.duty_pay_amount) dutyPreAmount
		FROM CLMS_duty_prepay_info t, CLMS_prepay_info t2
		WHERE t2.id_ahcs_prepay_info = t.id_ahcs_prepay_info
		AND t2.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND t2.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		AND t2.VERIFY_OPTIONS = '1'
		group by POLICY_NO ,CASE_NO ,PLAN_CODE ,DUTY_CODE
	</select>

	<select id="getFeePrepaySum" resultType="com.paic.ncbs.claim.model.vo.ahcs.PrePolicyVO">
		select POLICY_NO    policyNo,
		sum(PAYMENT_AMOUNT) policyFeeAmount
		from clm_payment_item
		where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		and CASE_TIMES  = #{caseTimes,jdbcType=NUMERIC}
		and CLAIM_TYPE ='2'
		and PAYMENT_TYPE ='11J'
		and PAYMENT_ITEM_STATUS not in ('90','20')
		group by POLICY_NO
	</select>
	<select id="getProcessStatus" resultType="java.lang.String">
		select PROCESS_STATUS from clms_case_process
		where report_no=#{reportNo} and CASE_TIMES  = #{caseTimes}
	</select>
	<select id="getApprovalPrePayDutyDetailList"  resultType="com.paic.ncbs.claim.model.dto.prepayinfo.ClmsPolicyPrepayDutyDetailDTO">
		select duty_code dutyCode,
		duty_detail_code dutyDetailCode,
		duty_detail_name dutyDetailName,
		duty_detail_amount dutyDetailAmount,
		prepay_amount  prepayAmount
		from clms_policy_prepay_duty_detail
		where report_no=#{reportNo}
		and case_times=#{caseTimes}
		and sub_times=#{subTimes}
	</select>

	<select id="getPrePayTotals" resultType="java.lang.Integer">
		SELECT count(1) FROM CLMS_PREPAY_INFO t
		WHERE t.report_No = #{reportNo}
		AND t.CASE_TIMES = #{caseTimes}
		AND t.STATUS in('1','2')
	</select>
	<select id="getPreEndDutyList" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.vo.ahcs.PreDutyVO">
		select sum(a.DUTY_PAY_AMOUNT) dutyPreAmount, DUTY_CODE dutyCode  from clms_duty_prepay_info a ,clms_prepay_info b
		where a.ID_AHCS_PREPAY_INFO=b.ID_AHCS_PREPAY_INFO
		and b.VERIFY_OPTIONS='1' and b.STATUS='2'
		and a.policy_no in(select policy_no from clms_policy_info where report_no=#{reportNo})
		GROUP BY a.DUTY_CODE
	</select>

	<!-- 查询报案号下所有预赔申请的金额信息数据-->
	<select id="getAllApprovalPrePayDutyDetailList"  resultType="com.paic.ncbs.claim.model.dto.prepayinfo.ClmsPolicyPrepayDutyDetailDTO">
		select DISTINCT a.POLICY_NO policyNo,b.PLAN_CODE planCode,b.plan_name planName,b.duty_code dutyCode,b.duty_detail_code dutyDetailCode,b.duty_detail_name dutyDetailName,
		b.duty_detail_amount dutyDetailAmount ,b.prepay_amount prepayAmount, b.sub_times subTimes from clms_duty_prepay_info a ,clms_policy_prepay_duty_detail b
		where  a.report_no=b.report_no and a.case_no=b.case_no and a.duty_code=b.duty_code
		and a.report_no=#{reportNo}
		and a.case_times=#{caseTimes}
	</select>
	<select id="getDutyIsShareAmount" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.vo.ahcs.PreDutyVO">
		select DISTINCT a.DUTY_CODE dutyCode ,a.IS_DUTY_SHARED_AMOUNT isDutySharedAmount ,a.DUTY_SHARED_AMOUNT_MERGE shareDutyGroup from clms_policy_duty a,clms_policy_plan b ,clms_policy_info c
		where a.ID_AHCS_POLICY_PLAN=b.ID_AHCS_POLICY_PLAN and
		b.ID_AHCS_POLICY_INFO=c.ID_AHCS_POLICY_INFO and
		c.REPORT_NO=#{reportNo}
	</select>
</mapper>