<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.BaseDataMapper">

    <select id="getProductLineCodeByProductCode"
            parameterType="java.lang.String" resultType="java.lang.String">
        SELECT P.PRODUCT_LINE_CODE as productLineCode
        FROM CLMP_PRODUCT_INFO_PV P
        WHERE P.PRODUCT_CODE = #{productCode,jdbcType=VARCHAR}
        AND P.PRODUCT_LINE_CODE != (CASE WHEN P.PRODUCT_CODE='A56' THEN '1'
        WHEN P.PRODUCT_CODE='**********' THEN '1'
        ELSE 'CP06' END )
    </select>

    <select id="queryReportFaildListByBatchId" resultType="java.util.HashMap"
            parameterType="java.lang.String">
        (SELECT O.PARTNER_POLICY_NO AS COPEPOLICYFIELD,
        date_format( O.ACCIDENT_DATE, '%Y-%m-%d') AS ACCIDENTDATE,
        O.ACCIDENT_REASON AS ACCIDENTCAUSE,
        S.SUGGESTION_AMOUNT AS LOSSPAYAMOUNT,
        S.CLIENT_BANK_ACCOUNT AS CLIENTBANKACCOUNT,
        S.SUGGESTION_TEXT AS SUGGESTIONTEXT,
        O.REPORT_BATCH_NO AS REPORTBATCHNO
        FROM CLMP_TRADE_ORDER O, CLMP_TRADE_SETTLEMENT S
        WHERE S.SUGGESTION = '1'
        AND O.ID_CLMP_TRADE_ORDER = S.ID_CLMP_TRADE_ORDER
        AND O.REPORT_BATCH_NO = #{batchId}
        )
        union all
        (select t.policy_no as COPEPOLICYFIELD,
        date_format( t.accident_date, '%Y-%m-%d') AS ACCIDENTDATE,
        t.accident_cause as ACCIDENTCAUSE,
        t.suggestion_amount as LOSSPAYAMOUNT,
        t.bank_account as CLIENTBANKACCOUNT,
        (case when t.reason is null then '手工撤销' else t.reason end) as SUGGESTIONTEXT,
        t.report_batch_no as REPORTBATCHNO
        from clmp_batch_report_temp t,CLMP_BATCH_REPORT t1 where t.report_status = '2' and t.report_batch_no =
        t1.report_batch_no
        and t.report_batch_no = #{batchId}
        )
    </select>

    <select id="getProvinceList" resultType="com.paic.ncbs.claim.model.dto.other.ProvinceDTO">
        select c.value_code provinceCode,
        c.value_chinese_name provinceName
        from clm_common_parameter c
        where c.collection_code = 'SFDM00'
        and c.invalidate_date is null
        and c.value_code like '%0000'
    </select>

    <select id="getCityList" resultType="com.paic.ncbs.claim.model.dto.other.CityDefineDTO"
            parameterType="java.lang.String">
        SELECT
        CITY_CODE cityCode,
        CITY_CHINESE_NAME cityChineseName,
        PROVINCE_CODE provinceCode,
        POSTCODE postCode,
        CITY_ENGLISH_NAME cityEnglishName,
        AREA_CODE areaCode,
        VEHICLE_LICENCE_PREFIX vehicleLicencePrefix,
        CITY_SHORT_NAME cityShortName,
        CITY_SPELL_NAME citySpellName,
        CITY_INITIAL_NAME cityInitialName
        FROM city_define
        where PROVINCE_CODE=#{value}
    </select>

    <select id="queryAllCertificateType" resultType="com.paic.ncbs.claim.model.dto.other.ReasonCodeDTO">
        select t.value_code reasonCode,
        t.value_chinese_name reasonName
        from clm_common_parameter t
        where t.collection_code='ZJLX00'
    </select>

    <select id="getAccidentNameByCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT
        t.CITY_CHINESE_NAME as accidentName
        FROM city_define t
        where t.CITY_CODE=#{accidentCode}
    </select>

    <select id="queryPlanClassCode" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT DISTINCT P.PRODUCT_CLASS_CODE AS PLANCLASSCODE FROM
        CLMP_PRODUCT_INFO_PV P
        WHERE P.PRODUCT_LINE_CODE != (CASE WHEN
        P.PRODUCT_CODE='A56' THEN '1' WHEN P.PRODUCT_CODE='**********' THEN
        '1' ELSE 'CP06' END )
        AND P.PRODUCT_CODE = #{planCode,jdbcType=VARCHAR}
    </select>

    <select id="getBaseDataListByCollectionCode" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.model.dto.other.KeyValDTO">
        SELECT T.VALUE_CODE AS key, T.VALUE_CHINESE_NAME AS VALUE
        FROM clm_common_parameter t
        WHERE T.COLLECTION_CODE = #{collectionCode}
    </select>

    <select id="queryProvinceNameByCodeForReport" parameterType="java.lang.String"
            resultType="java.lang.String">
        select c.value_chinese_name provinceName
        from
        clm_common_parameter c
        where c.collection_code = 'SFDM00'
        and
        c.invalidate_date is null
        and c.value_code =
        #{provinceCode,jdbcType=VARCHAR}
    </select>

    <select id="queryCityNameByCodeForReport" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT
        CITY_CHINESE_NAME cityChineseName
        FROM city_define d
        where d.city_code =
        #{cityCode,jdbcType=VARCHAR}
    </select>

    <select id="getContinentChineseNameByCode" resultType="java.lang.String"
            parameterType="java.lang.String">
        select distinct CONTINENT_NAME from GLOBAL_AREA_DEFINE
        where
        CONTINENT_CODE=#{continentCode}
    </select>

    <select id="getNationChineseNameByCode" resultType="java.lang.String"
            parameterType="java.lang.String">
        select distinct COUNTRY_NAME from GLOBAL_AREA_DEFINE
        where
        COUNTRY_CODE=#{countryCode}
    </select>

    <select id="getValueChineseName" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT a.value_chinese_name AS typeName
        FROM
        clm_common_parameter a
        WHERE A.DEPARTMENT_CODE = '2'
        AND
        A.VALUE_CODE=#{valueCode,jdbcType=VARCHAR}
        AND A.COLLECTION_CODE in
        ('PPTCD1', 'PPTCD2', 'PPTCD3')
    </select>

    <select id="queryDamageOneList" resultType="com.paic.ncbs.claim.model.dto.other.DamageDTO"
            parameterType="java.lang.String">
        select r.OBJECT_PIECE_LEVEL1 typeCode,
        (select c.value_chinese_name
        from clm_common_parameter c where
        c.collection_code in
        ('BDSP1','GCBDSP1',
        'ZRBDSP1','GCSZSP1','HYBDSP1', 'CTBDSP1',
        'CCBDSP1') and c.value_code
        = r.OBJECT_PIECE_LEVEL1) typeName
        from
        (select distinct (loss.OBJECT_PIECE_LEVEL1)
        from clmp_object_piece
        loss,CLMP_PRODUCT_INFO_PV P
        where loss.plan_class_code =
        P.PRODUCT_CLASS_CODE
        and P.PRODUCT_LINE_CODE != (CASE WHEN
        P.PRODUCT_CODE='A56' THEN '1' WHEN
        P.PRODUCT_CODE='**********' THEN '1'
        ELSE 'CP06' END )
        and p.product_code = #{planCode,jdbcType = VARCHAR}
        and (LOSS.PLAN_CODE IS NULL OR LOSS.PLAN_CODE = (case WHEN
        loss.Plan_Code
        IS NOT NULL
        and loss.plan_class_code IN ('SB', 'SF',
        'SS', 'SE') THEN
        #{planCode,jdbcType = VARCHAR} ELSE LOSS.PLAN_CODE
        END))
        <if test="threeIndustry != null and threeIndustry != ''">
            and loss.industry_type = #{threeIndustry,jdbcType=VARCHAR}
        </if>
        <if test="threeIndustry == null or threeIndustry ==''">
            and loss.industry_type is null
        </if>
        ) r
    </select>

    <select id="queryDamageTwoList" resultType="com.paic.ncbs.claim.model.dto.other.DamageDTO"
            parameterType="java.util.Map">
        select r.OBJECT_PIECE_LEVEL2 typeCode,
        (select c.value_chinese_name
        from clm_common_parameter c where
        c.collection_code in ('BDSP2',
        'GCBDSP2', 'ZRBDSP2',
        'HYBDSP2','CTBDSP2', 'CCBDSP2', 'GCSZSP2') and
        c.value_code =
        r.OBJECT_PIECE_LEVEL2) typeName
        from (select distinct
        (loss.OBJECT_PIECE_LEVEL2)
        from clmp_object_piece
        loss,
        CLMP_PRODUCT_INFO_PV P
        where loss.plan_class_code =
        P.PRODUCT_CLASS_CODE
        and loss.object_piece_level1 = #{typeCode,jdbcType
		= VARCHAR}
        and p.product_line_code != (CASE WHEN P.PRODUCT_CODE = 'A56'
        THEN '1'
        WHEN P.PRODUCT_CODE = '**********' THEN '1' ELSE 'CP06' END)
        and p.product_code = #{planCode,jdbcType = VARCHAR} AND
        (LOSS.PLAN_CODE
        IS NULL OR LOSS.PLAN_CODE = (case WHEN loss.Plan_Code
        IS NOT NULL
        and loss.plan_class_code IN ('SB', 'SF', 'SS', 'SE') THEN
        #{planCode,
        jdbcType = VARCHAR} ELSE LOSS.PLAN_CODE END))
        <if test="threeIndustry != null and threeIndustry != ''">
            and loss.industry_type = #{threeIndustry,jdbcType=VARCHAR}
        </if>
        <if test="threeIndustry == null or threeIndustry ==''">
            and loss.industry_type is null
        </if>
        ) r
    </select>

    <select id="queryDamageThreeList" resultType="com.paic.ncbs.claim.model.dto.other.DamageDTO"
            parameterType="java.lang.String">
        SELECT distinct
        COP.OBJECT_PIECE_LEVEL3 AS typeCode,
        concat(c1.value_chinese_name , ':' ,
        c2.value_chinese_name , ':' ,
        c3.value_chinese_name) AS typeName
        FROM
        clmp_object_piece cop,
        clm_common_parameter c1,
        clm_common_parameter c2,
        clm_common_parameter
        c3
        where C1.COLLECTION_CODE IN ('HYBDSP1',
        'CTBDSP1')
        AND
        C2.COLLECTION_CODE IN ('HYBDSP2', 'CTBDSP2')
        AND
        C3.COLLECTION_CODE IN
        ('HYBDSP3', 'CTBDSP3')
        AND
        COP.OBJECT_PIECE_LEVEL1 = C1.VALUE_CODE
        AND
        COP.OBJECT_PIECE_LEVEL2 =
        C2.VALUE_CODE
        AND COP.OBJECT_PIECE_LEVEL3 =
        C3.VALUE_CODE
        AND
        COP.OBJECT_PIECE_LEVEL2 = #{typeCode,jdbcType=VARCHAR}
        AND
        COP.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
    </select>

    <select id="getProvinceChineseNameByCode" parameterType="java.lang.String"
            resultType="java.lang.String">
        select t.value_chinese_name
        from clm_common_parameter t
        where t.value_code = #{typeCode,jdbcType=VARCHAR}
        and t.collection_code
        = 'SFDM00'
    </select>

    <select id="getCityOrRegionChineseNameByCode" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT
        t.CITY_CHINESE_NAME as accidentName
        FROM city_define t
        where t.CITY_CODE=#{accidentCode,jdbcType=VARCHAR}
    </select>

    <select id="queryLatestExchangeRate" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.dao.entity.other.ExchangeRateEntity">
        select currency1Code, exchangeRate
        from (select distinct CURRENCY1_CODE
        currency1Code,
        date_format(EXCHANGE_RATE, 'FM0.999999') exchangeRate,
        EFFECTIVE_DATE
        from exchange_rate
        where <![CDATA[
          (CASE WHEN str_to_date(#{date}, '%Y-%m-%d %H:%i:%s') is null THEN now() ELSE str_to_date(#{date}, '%Y-%m-%d %H:%i:%s') END ) >= EFFECTIVE_DATE
                   and (INVALIDATE_DATE is null or
                    (CASE WHEN str_to_date(#{date}, '%Y-%m-%d %H:%i:%s') is null THEN now() ELSE str_to_date(#{date}, '%Y-%m-%d %H:%i:%s') END ) < INVALIDATE_DATE
                        ) ]]>
        and currency1_code = #{currency1Code,jdbcType=VARCHAR}
        and
        currency2_code =
        #{currency2Code}
        order by EFFECTIVE_DATE)
        limit 1
    </select>

    <select id="queryPlanClassName" parameterType="java.lang.String"
            resultType="java.lang.String">
        select product_class_name planClassName from
        base_product_class where product_class_alias_code =
        #{planClassCode,jdbcType=VARCHAR}
    </select>

    <select id="getProductClassCode" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT DISTINCT P.PRODUCT_CLASS_CODE AS PLANCLASSCODE FROM
        CLMP_PRODUCT_INFO_PV P
        WHERE P.PRODUCT_LINE_CODE != (CASE WHEN
        P.PRODUCT_CODE='A56' THEN '1' WHEN P.PRODUCT_CODE='**********' THEN
        '1' ELSE 'CP06' END )
        AND P.PRODUCT_CODE = #{planCode,jdbcType=VARCHAR}
    </select>

<!--    <select id="queryExchangeRate" parameterType="java.lang.String"-->
<!--            resultType="ExchangeRateEntity">-->
<!--        <![CDATA[-->
<!--		select -->
<!--				CURRENCY1_CODE currency1Code,-->
<!--				date_format(EXCHANGE_RATE,'FM0.099999') exchangeRate,-->
<!--				CURRENCY2_CODE currency2Code-->
<!--		  from exchange_rate-->
<!--		 where DECODE(str_to_date(#{date}, '%Y-%m-%d %H:%i:%s'), NULL,now(),str_to_date(#{date}, '%Y-%m-%d %H:%i:%s'))  >= EFFECTIVE_DATE-->
<!--		   and (INVALIDATE_DATE is null or DECODE(str_to_date(#{date}, '%Y-%m-%d %H:%i:%s'), NULL,now(),str_to_date(#{date}, '%Y-%m-%d %H:%i:%s'))  < INVALIDATE_DATE)-->
<!--		 ]]>-->
<!--        <if test="currency2Code!=null and currency2Code!=''">-->
<!--            and currency2_code = #{currency2Code,jdbcType=VARCHAR}-->
<!--        </if>-->
<!--    </select>-->

    <select id="queryCauseList"
            resultType="com.paic.ncbs.claim.model.dto.accident.AccidentCauseDTO">
        select distinct y.type_level1 as typeLevel1Code,
        y.type_level2 as typeLevel2Code,
        y.plan_code as productCode,
        t.value_chinese_name as typeLevel2Name
        from clm_common_parameter t,
        clmp_plan_cause y
        where t.collection_code = 'PPTCD2'
        and t.value_code = y.type_level2
    </select>

    <select id="queryAccidentCause" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.model.dto.accident.AccidentCauseDTO">
        select y.type_level1 as typeLevel1Code,
        y.type_level2 as typeLevel2Code,
        y.plan_code as productCode,
        t.value_chinese_name as typeLevel2Name
        from clm_common_parameter t,
        clmp_plan_cause y
        where t.collection_code = 'PPTCD2'
        and t.value_code = y.type_level2
        and t.value_chinese_name=#{accidentCauseL2Name, jdbcType=VARCHAR}
        and y.plan_code=#{productCode, jdbcType=VARCHAR}
    </select>

    <select id="queryParamKey1ByTradeCode" parameterType="java.lang.String"
            resultType="java.util.HashMap">
        SELECT DISTINCT c.param_key1 as key1
        FROM
        clmp_trade_common_data c
        WHERE c.trade_code =
        #{tradeCode,jdbcType=VARCHAR}
    </select>

    <select id="queryParamKey2ByTradeCode" parameterType="java.lang.String"
            resultType="java.util.HashMap">
        SELECT DISTINCT c.param_key2 as key2
        FROM
        clmp_trade_common_data c
        WHERE c.trade_code =
        #{tradeCode,jdbcType=VARCHAR}
    </select>

    <select id="queryParamValue" resultType="java.lang.String">
        SELECT c.param_value
        FROM clmp_trade_common_data c
        WHERE c.trade_code = #{tradeCode}
        AND
        c.param_key1 = #{paramKey1,jdbcType=VARCHAR}
        AND c.param_key2 =
        #{paramKey2,jdbcType=VARCHAR}
    </select>

    <select id="queryDeptByAreaCode" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT DISTINCT DR.PARENT_DEPARTMENT_CODE
        FROM
        clmp_department_relation_pv DR, clmp_department_define_pv DEPT
        WHERE
        DEPT.DEPARTMENT_CODE = DR.PARENT_DEPARTMENT_CODE
        AND
        DEPT.DEPARTMENT_LEVEL = '2'
        AND DR.CHILD_DEPARTMENT_CODE IN
        (SELECT
        DEP.DEPARTMENT_CODE
        FROM clmp_department_define_pv DEP
        WHERE
        DEP.DISTRICT_CODE IN
        (SELECT DD.DISTRICT_CODE
        FROM DISTRICT_DEFINE DD
        WHERE DD.DISTRICT_REAL_CODE = #{accidentCityCode}))
    </select>

    <select id="queryDeptByAreaCode2" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT DISTINCT DR.PARENT_DEPARTMENT_CODE
        FROM clmp_department_relation_pv DR, clmp_department_define_pv DEPT,district_define DD
        WHERE DR.PARENT_DEPARTMENT_CODE = DEPT.DEPARTMENT_CODE
        AND DEPT.district_code = DD.District_Code
        AND DD.District_Real_Code = #{accidentCityCode,jdbcType=VARCHAR}
        AND DEPT.DEPARTMENT_LEVEL = '2'
    </select>

    <select id="queryDeptByDepartmentCode" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT DISTINCT DR.PARENT_DEPARTMENT_CODE
        FROM
        clmp_department_relation_pv DR, clmp_department_define_pv DEPT
        WHERE
        DEPT.DEPARTMENT_CODE = DR.PARENT_DEPARTMENT_CODE
        AND
        DEPT.DEPARTMENT_LEVEL = '2'
        AND DR.CHILD_DEPARTMENT_CODE =
        #{departmentCode,jdbcType=VARCHAR}
    </select>

    <select id="getClaimProductClass" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT DISTINCT P.product_line_code AS PLANCLASSCODE FROM
        CLMP_PRODUCT_INFO_PV P
        WHERE P.PRODUCT_LINE_CODE != (CASE WHEN
        P.PRODUCT_CODE='A56' THEN '1' WHEN P.PRODUCT_CODE='**********' THEN
        '1' ELSE 'CP06' END )
        AND P.PRODUCT_CODE = #{planCode,jdbcType=VARCHAR}
    </select>

    <select id="getClaimMadeProductAmount" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT COUNT(1) AMOUNT
        FROM CLM_COMMON_PARAMETER
        where
        collection_CODE = 'CLAIM_MADE_FORM_PALN'
        AND VALUE_CODE =
        #{productNo,jdbcType=VARCHAR}
    </select>

    <select id="getBdspName" parameterType="java.util.Map"
            resultType="java.lang.String">
        select t.value_chinese_name
        from clm_common_parameter t
        where t.value_code = #{typeCode,jdbcType=VARCHAR}
        and t.collection_code
        = 'SFDM00'
    </select>

    <select id="queryDepartmentShortName" parameterType="java.lang.String"
            resultType="java.lang.String">
        select T.DEPARTMENT_ABBR_NAME SHORTNAME from
        clmp_department_define_pv T where
        t.department_code=#{departmentCode,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="queryPlanName" parameterType="java.lang.String"
            resultType="java.lang.String">
        select distinct t.product_name planName from
        clmp_product_info_pv t where
        t.product_code=#{planCode,jdbcType=VARCHAR}
    </select>

    <select id="getPlanClassCodeList" resultType="com.paic.ncbs.claim.model.dto.settle.PlanCodeDTO">
        select
        n.product_class_alias_code as planClassCode,
        n.product_class_name as
        planClassChineseName,
        n.product_class_name as planClassName,
        n.business_product_line_code as businessProductLineCode
        from
        base_product_class n
        where n.business_product_line_code in ('CP01',
        'CP02', 'CP03', 'CP04',
        'CP05', 'CP06')
        order by
        n.product_class_alias_code
    </select>

    <select id="queryAccidentCauseIsExistByProductCode"
            parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM clmp_plan_cause WHERE plan_code= #{productCode,jdbcType=VARCHAR}
    </select>

    <select id="queryCauseOneList" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.model.dto.accident.AccidentCauseDTO">
        select r.type_level1 typeCode,
        (select c.value_chinese_name
        from clm_common_parameter c
        where c.collection_code = 'PPTCD1'
        and
        c.value_code = r.type_level1) typeName
        from (select distinct
        (rel.type_level1)
        from CLMP_PLAN_CAUSE rel
        where rel.plan_code =
        #{planCode,jdbcType=VARCHAR}) r
    </select>

    <select id="getProductLineAndClassCode" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.model.dto.endcase.CaseCommonDTO">
        SELECT P.PRODUCT_CODE as "productCode",
        P.PRODUCT_CLASS_CODE as "productClassCode",
        P.PRODUCT_LINE_CODE as "productLineCode"
        FROM CLMP_PRODUCT_INFO_PV P
        where
        P.PRODUCT_LINE_CODE != (CASE WHEN P.PRODUCT_CODE='A56' THEN '1'
        WHEN P.PRODUCT_CODE='**********' THEN '1'
        ELSE 'CP06' END )
        AND P.PRODUCT_CODE=#{productCode}
    </select>

    <select id="queryCauseTwoList" parameterType="com.paic.ncbs.claim.model.dto.settle.PlanCauseDTO"
            resultType="com.paic.ncbs.claim.model.dto.accident.AccidentCauseDTO">
        select distinct( rel.type_level2 ) typeCode,
        (select
        c.value_chinese_name
        from clm_common_parameter c
        where c.collection_code
        = 'PPTCD2'
        and c.value_code = rel.type_level2) typeName
        from
        CLMP_PLAN_CAUSE rel
        where rel.plan_code = #{planCode}
        <if test="typeLevel1!=null and typeLevel1!=''">
            and rel.type_level1 = #{typeLevel1}
        </if>
    </select>

    <select id="queryCauseThreeList" parameterType="com.paic.ncbs.claim.model.dto.settle.PlanCauseDTO"
            resultType="com.paic.ncbs.claim.model.dto.accident.AccidentCauseDTO">
        select distinct( rel.type_level3 ) typeCode,
        (select c.value_chinese_name
        from clm_common_parameter c
        where c.collection_code = 'PPTCD3'
        and c.value_code = rel.type_level3) typeName
        from CLMP_PLAN_CAUSE rel
        where rel.plan_code = #{planCode}
        and rel.type_level2=#{typeLevel2}
    </select>

    <select id="queryPeopleInjuryList" resultType="com.paic.ncbs.claim.model.dto.other.DamageDTO">
        select
        p.value_code typeCode,p.value_chinese_name typeName
        from
        clm_common_parameter p
        where p.collection_code = 'PPTRS1'
    </select>

    <select id="getContinentList"
            resultType="com.paic.ncbs.claim.model.dto.other.GlobalAreaDefinedDTO">
        SELECT DISTINCT CONTINENT_CODE AS
        continentCode,CONTINENT_NAME AS continentName FROM GLOBAL_AREA_DEFINE
    </select>

    <select id="getAllNationList"
            resultType="com.paic.ncbs.claim.model.dto.other.GlobalAreaDefinedDTO">
        SELECT CONTINENT_CODE AS continentCode,
        CONTINENT_NAME AS continentName,
        COUNTRY_CODE AS countryCode,
        COUNTRY_NAME AS countryName
        FROM GLOBAL_AREA_DEFINE
        order by COUNTRY_NAME
    </select>

    <select id="getNationList"
            resultType="com.paic.ncbs.claim.model.dto.other.GlobalAreaDefinedDTO"
            parameterType="java.lang.String">
        SELECT CONTINENT_CODE AS continentCode,
        CONTINENT_NAME AS continentName,
        COUNTRY_CODE AS countryCode,
        COUNTRY_NAME AS countryName
        FROM GLOBAL_AREA_DEFINE
        where CONTINENT_CODE =
        (CASE WHEN #{continentCode}='' THEN CONTINENT_CODE
        ELSE #{continentCode} END )
    </select>

    <select id="queryAllDomesticAddressList"
            resultType="com.paic.ncbs.claim.model.dto.other.DomesticAddressDTO">
        SELECT province.provinceCode,
        province.provinceName,
        city.cityCode,
        city.cityName,
        county.countyCode,
        county.countyName
        FROM (select c.value_code provinceCode, c.value_chinese_name provinceName
        from clm_common_parameter c
        where c.collection_code = 'SFDM00'
        and c.invalidate_date is null
        and c.value_code like '%0000') province,
        (SELECT CITY_CODE cityCode,
        CITY_CHINESE_NAME cityName,
        PROVINCE_CODE provinceCode,
        is_gb_code as isGbCode
        FROM city_define
        where substr(city_code, -2) = '00') city,
        (SELECT CITY_CODE countyCode,
        CITY_CHINESE_NAME countyName,
        PROVINCE_CODE provinceCode,
        is_gb_code as isGbCode
        FROM city_define
        where substr(city_code, -2) != '00') county
        where province.provincecode = city.provinceCode
        and city.provinceCode = county.provinceCode
        and substr(city.cityCode, 3, 2) = substr(county.countyCode, 3, 2)
        and county.countyName not like '%(旧)'
        and city.cityName not like '%(旧)'
    </select>

    <select id="getParamConfig" parameterType="java.lang.String"
            resultType="java.lang.String">
        select t.content as content
        from clmp_param_config t
        where
        t.item = #{item}
    </select>

    <select id="getPlanCodeList"
            parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.settle.PlanCodeDTO">
        SELECT P.product_code as planCode,
        P.product_name as planChineseName,
        concat(P.product_code,'-',P.product_name) as planCodeAndName
        FROM clmp_product_info_pv P
        where P.product_class_code = #{planCode}
        ORDER BY planCode
    </select>

    <select id="getProductLineCodeList" resultType="com.paic.ncbs.claim.model.dto.other.ProductLineDefineDTO">
        SELECT
        t.PRODUCT_LINE_CODE as productLineCode,
        t.PRODUCT_LINE_NAME as productLineName
        FROM base_product_line t
        where t.product_line_code in ('CP01', 'CP02', 'CP03', 'CP04', 'CP05', 'CP06')
        and now() BETWEEN t.effective_date AND ifnull(t.invalidate_date, now())
    </select>

    <select id="getPlanClassCodeListBypld" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.model.dto.settle.PlanCodeDTO">
        SELECT
        T.PRODUCT_CLASS_ALIAS_CODE as planClassCode,
        T.PRODUCT_CLASS_NAME as planClassName
        FROM BASE_PRODUCT_CLASS T
        WHERE T.BUSINESS_PRODUCT_LINE_CODE=#{productLineCode}
        AND now() BETWEEN t.effective_date AND ifnull(t.invalidate_date, now())
    </select>

    <select id="getPlanClassCode" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.model.dto.settle.PlanCodeDTO">
        SELECT P.Product_Code planCode,
        PD.PRODUCT_CLASS_CODE as planClassCode,
        PD.product_type as productType
        FROM CLMP_POLICY P,
        CLMP_PRODUCT_INFO_PV PD
        WHERE PD.product_Code(+) = P.Product_Code
        and PD.product_line_code != (CASE WHEN pd.PRODUCT_CODE='A56' THEN '1'
        WHEN pd.PRODUCT_CODE='**********' THEN '1'
        ELSE 'CP06' END )
        AND P.POLICY_NO = #{policyNo}
        <if test="reportNo != null and reportNo != ''">
            AND P.REPORT_NO =#{reportNo}
        </if>
        limit 1
    </select>

<!--    <select id="getPolicyReportInfo" parameterType="java.lang.String" resultType="java.util.HashMap">-->
<!--        select sum(reportNum) "reportNum", policyAttribute "policyAttribute", policyNo "policyNo", migrateFrom-->
<!--        "migrateFrom"-->
<!--        from (select count(t1.report_no) reportNum,-->
<!--        t1.policy_attribute policyAttribute,-->
<!--        t1.policy_no policyNo,-->
<!--        t1.migrate_from migrateFrom-->
<!--        from clm_policy_info t1-->
<!--        where t1.policy_no in-->
<!--        (select t.policy_no-->
<!--        from clm_policy_info t-->
<!--        where t.report_no = #reportNo#)-->
<!--        and t1.migrate_from in ('n','c')-->
<!--        group by t1.policy_attribute, t1.policy_no, t1.migrate_from-->
<!--        union all-->
<!--        select count(t2.report_no) reportNum,-->
<!--        decode(substr((select m.lower_value_code-->
<!--        from clm_common_param_mapping m-->
<!--        where m.upper_value_code = t2.plan_code-->
<!--        and m.collection_code = 'POLICY_CLAIM_MAPPING'), 0, 2),-->
<!--        'C0', '0', 'C5', '1') policyAttribute,-->
<!--        t2.policy_no policyNo,-->
<!--        '' migrateFrom-->
<!--        from t_clm_intf_case_info t2-->
<!--        where t2.policy_no in-->
<!--        (select t.policy_no-->
<!--        from clm_policy_info t-->
<!--        where t.report_no = #reportNo#-->
<!--        union-->
<!--        select t3.policy_no-->
<!--        from t_clm_intf_case_info t3-->
<!--        where t3.report_no = #reportNo#)-->
<!--        and t2.case_times = 1-->
<!--        group by t2.plan_code, t2.policy_no-->
<!--        union all-->
<!--        select count(t1.report_no) reportNum,-->
<!--        t1.policy_attribute policyAttribute,-->
<!--        t1.policy_no policyNo,-->
<!--        t1.migrate_from migrateFrom-->
<!--        from clm_policy_info t1-->
<!--        where t1.policy_no in-->
<!--        (select t.policy_no-->
<!--        from clm_policy_info t-->
<!--        where t.report_no = #reportNo#)-->
<!--        and t1.migrate_from = 'nd'-->
<!--        group by t1.policy_attribute, t1.policy_no, t1.migrate_from-->
<!--        ) tt-->
<!--        group by tt.policyAttribute, tt.policyNo, migrateFrom-->
<!--    </select>-->

    <select id="getNationalList"
            resultType="com.paic.ncbs.claim.model.dto.other.KeyValDTO">
        SELECT t.value_code as key,
        t.value_chinese_abbr_name as value
        from
        acss_parameter t
        WHERE t.collection_CODE = 'NATION'
        ORDER BY T.value_chinese_abbr_name
    </select>

    <select id="getNationalNameByCode" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.model.dto.other.KeyValDTO">
        SELECT t.value_code as key,
        t.value_chinese_abbr_name as value
        from
        acss_parameter t
        WHERE t.collection_CODE = 'NATION'
        and t.value_code = #{code,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="queryOutMapByOldCode" parameterType="java.lang.String"
            resultType="java.lang.String">
        select t.out_map_code
        from ACSS_PARAMETER t
        where t.collection_code = 'NATION'
        and t.value_code = #{valueCode}
        limit 1
    </select>
</mapper>