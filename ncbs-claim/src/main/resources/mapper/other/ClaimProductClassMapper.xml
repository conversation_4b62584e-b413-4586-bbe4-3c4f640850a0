<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.ClaimProductClassMapper">

	<select id="getClaimProductClass" parameterType="java.lang.String"
		resultType="com.paic.ncbs.claim.dao.entity.other.ClaimProductClassEntity">
		select v.product_code productCode,
		v.product_line_code businessProductLineCode,
		v.product_class_code productClassCode
		from clmp_product_info_pv v
		where v.product_code = #{productCode,jdbcType=VARCHAR}
		and v.product_line_code !=
		(case when v.product_code = 'A56' then '1' when
		v.product_code = 'MP04000028' then '1' else 'CP06' end)
	</select>
</mapper>