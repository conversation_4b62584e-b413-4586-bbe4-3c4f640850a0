<?xml version =  "1.0" encoding =  "UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace =  "com.paic.ncbs.claim.dao.mapper.other.ParamterConfigMapper">


	<update id="saveParamterConfig" parameterType="com.paic.ncbs.claim.model.dto.other.ParamterConfigDTO">
		UPDATE CLMS_PARAMTER_CONFIG T
		   SET T.UPDATED_BY = #{updatedBy}, 
		   T.UPDATED_DATE = SYSDATE(),
		   T.CONFIG_VALUE = #{configValue}
		 WHERE T.ID_AHCS_PARAMTER_CONFIG = #{idAhcsParamterConfig}
	</update>
	

	<select id="getParamterConfigByCode" resultType="com.paic.ncbs.claim.model.dto.other.ParamterConfigDTO">
		SELECT  T.ID_AHCS_PARAMTER_CONFIG idAhcsParamterConfig,
		        T.CONFIG_CODE configCode,
		        T.CONFIG_DESC configDesc,
		        T.SHOW_TYPE showType,
		        T.CONFIG_VALUE configValue,
		        T.DISPLAY_ORDER displayOrder
		   FROM CLMS_PARAMTER_CONFIG T
		  WHERE T.CONFIG_CODE = #{configCode}
		  limit 1;
	</select>
	

	<select id="getParamterConfigById" resultType="com.paic.ncbs.claim.model.dto.other.ParamterConfigDTO">
		SELECT  T.ID_AHCS_PARAMTER_CONFIG idAhcsParamterConfig,
		        T.CONFIG_CODE configCode,
		        T.CONFIG_DESC configDesc,
		        T.SHOW_TYPE showType,
		        T.CONFIG_VALUE configValue,
		        T.DISPLAY_ORDER displayOrder
		   FROM CLMS_PARAMTER_CONFIG T
		  WHERE T.ID_AHCS_PARAMTER_CONFIG = #{idAhcsParamterConfig}
		  limit 1
	</select>
	

	<select id="getParamterConfigByCodes" resultType="com.paic.ncbs.claim.model.dto.other.ParamterConfigDTO">
			SELECT T.ID_AHCS_PARAMTER_CONFIG idAhcsParamterConfig,
			    T.CONFIG_CODE configCode,
		        T.CONFIG_DESC configDesc,
		        T.SHOW_TYPE showType,
		        T.CONFIG_VALUE configValue,
		        T.DISPLAY_ORDER displayOrder
		   FROM CLMS_PARAMTER_CONFIG T
		  <if test=" configCodes != null and configCodes.size > 0 ">
			 WHERE T.CONFIG_CODE IN
			  <foreach collection="configCodes" item="code" open="(" close=")" separator=",">
					#{code}
			  </foreach>
		  </if>
		  ORDER BY T.DISPLAY_ORDER
	</select>
	

	<select id="getParamterConfigValueByCode" resultType="String">
		SELECT T.CONFIG_VALUE configValue
		   FROM CLMS_PARAMTER_CONFIG T
		  WHERE T.CONFIG_CODE = #{configCode}
		  limit 1
	</select>

	<select id="getcheckInputBillEstimateAmountByCode" resultType="com.paic.ncbs.claim.model.vo.other.ResultAmountVO">
		SELECT T.CONFIG_VALUE estimateAmount
		   FROM CLMS_PARAMTER_CONFIG T
		  WHERE T.CONFIG_CODE = #{configCode}
		  limit 1
	</select>

	<select id="getReportNoWhenMigrateFromEqualso" resultType="String">
		select t.report_no
		from clm_whole_case_base t
		where t.report_no = #{reportNo}
		and t.migrate_from = 'o'
		limit 1
	</select>

</mapper>
