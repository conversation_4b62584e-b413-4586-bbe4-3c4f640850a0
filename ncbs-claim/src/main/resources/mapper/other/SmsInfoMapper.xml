<?xml version =  "1.0" encoding =  "UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace =  "com.paic.ncbs.claim.dao.mapper.other.SmsInfoMapper">
	<resultMap type="com.paic.ncbs.claim.model.vo.record.SmsRecordVO" id="smsRecordVO">
		<result property="idAhcsSmsInfo" column="id_ahcs_sms_info"/>
		<result property="createdDate" column="created_date"/>
		<result property="sendWay" column="send_way"/>
		<result property="smsStatus" column="sms_status"/>
		<result property="mobileNo" column="mobile_no"/>
		<result property="smsContent" column="sms_content"/>
		<result property="baseDesc" column="base_desc"/>
		<result property="baseId" column="base_id"/>
		<result property="baseStatus" column="base_status"/>
		<result property="requestId" column="request_id"/>
		<result property="smsTemplateCode" column="sms_template_code"/>
		<result property="recipient" column="recipient"/>
	</resultMap>

	<insert id="addSmsInfo" parameterType="com.paic.ncbs.claim.model.dto.message.SmsInfoDTO">
		insert into clms_sms_info (
		    created_by,
			created_date,
			updated_by,
			updated_date,
			id_ahcs_sms_info,
			sms_content,
			mobile_no,
			send_user,
			report_no,
			send_date,
			sms_status,
			send_link,
			sms_template_code,
			recipient)
		values(
			#{createdBy ,jdbcType=VARCHAR},
			now(),
			#{updatedBy ,jdbcType=VARCHAR},
			now(),
			#{idAhcsSmsInfo ,jdbcType=VARCHAR},
			#{smsContent ,jdbcType=VARCHAR},
			#{mobileNo ,jdbcType=VARCHAR},
			#{sendUser ,jdbcType=VARCHAR},
			#{reportNo ,jdbcType=VARCHAR},
			now(),
			#{smsStatus ,jdbcType=VARCHAR},
			#{sendLink ,jdbcType=VARCHAR},
		    #{smsTemplateCode ,jdbcType=VARCHAR},
		    #{recipient ,jdbcType=VARCHAR}
		)
	</insert>

	<update id="updateSmsInfo" parameterType="com.paic.ncbs.claim.model.dto.message.SmsInfoDTO">
		update clms_sms_info
		set updated_date = now(),
		<if test="baseDesc != null">
			base_desc = #{baseDesc,jdbcType=VARCHAR},
		</if>
		<if test="sendSeriesId != null">
			send_series_id = #{sendSeriesId ,jdbcType=VARCHAR},
		</if>
		sms_status = #{smsStatus ,jdbcType=VARCHAR}
		where id_ahcs_sms_info = #{idAhcsSmsInfo ,jdbcType=VARCHAR}
	</update>

	<select id="querySmsRecordByReportNo" parameterType="com.paic.ncbs.claim.model.dto.message.SmsInfoDTO"
			resultMap="smsRecordVO">
		select id_ahcs_sms_info,
		       created_date,
			   '短信' send_way,
		       (case when sms_status = '03' then '失败'
		             when sms_status = '04' then '成功'
				end) as sms_status,
		       mobile_no,
		       sms_content
		from clms_sms_info
		where report_no = #{reportNo ,jdbcType=VARCHAR}
		order by created_date desc
	</select>

	<select id="querySmsInfo" parameterType="com.paic.ncbs.claim.model.dto.message.SmsInfoDTO"
			resultMap="smsRecordVO">
		select created_by,
			   created_date,
			   updated_by,
			   updated_date,
			   id_ahcs_sms_info,
			   sms_content,
			   mobile_no,
			   send_user,
			   send_date,
			   send_link,
			   sms_status,
			   report_no,
			   base_desc,
			   send_series_id,
			   base_id,
			   base_status,
			   request_id
		from clms_sms_info
		where report_no = #{reportNo ,jdbcType=VARCHAR}
		and sms_status = #{smsStatus ,jdbcType=VARCHAR}
	</select>


	<update id="updateClmsSmsInfo" parameterType="com.paic.ncbs.claim.model.dto.message.SmsInfoDTO">
		update clms_sms_info
		set updated_date = now(),
		<if test="baseDesc != null">
		    base_desc = #{baseDesc},
		</if>
		<if test="requestId != null">
			request_id = #{requestId},
		</if>
		<if test="baseId != null">
			base_id = #{baseId},
		</if>
		<if  test="baseStatus != null">
			base_status = #{baseStatus},
		</if>
		sms_status = #{smsStatus}
		where report_no = #{reportNo}
	</update>
</mapper>
