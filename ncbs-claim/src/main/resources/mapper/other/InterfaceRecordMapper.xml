<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.InterfaceRecordMapper">

    <insert id="addInterfaceRecord" parameterType="com.paic.ncbs.claim.model.dto.other.InterfaceRecordDTO">
        insert into CLMS_INTERFACE_RECORD
        (
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        REQUEST_PARAMETER,
        RESPONSE_PARAMETER,
        LINK_SYSTEM,
        IS_SUCCESS,
        ERROR_CODE,
        ERROR_MESSAGE,
        INTERFACE_URL,
        ARCHIVE_TIME
        )values(
        #{createdBy,jdbcType=VARCHAR},
        sysdate(),
        #{updatedBy,jdbcType=VARCHAR},
        sysdate(),
        #{reportNo,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=NUMERIC},
        #{requestParameter,jdbcType=VARCHAR},
        #{responseParameter,jdbcType=VARCHAR},
        #{linkSystem, jdbcType=VARCHAR},
        #{isSuccess,jdbcType=VARCHAR},
        #{errorCode, jdbcType=VARCHAR},
        #{errorMessage,jdbcType=VARCHAR},
        #{interfaceUrl,jdbcType=VARCHAR},
        <if test="archiveTime != null ">
            #{archiveTime,jdbcType=TIMESTAMP}
        </if>
        <if test="archiveTime == null ">
           sysdate()
        </if>
        )
    </insert>
</mapper>