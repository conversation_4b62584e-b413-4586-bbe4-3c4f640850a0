<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.ClmsSmsTemplateMapper">

    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.message.ClmsSmsTemplateDTO">
            <id property="idAhcsSmsTemplate" column="ID_AHCS_SMS_TEMPLATE" />
            <result property="createdBy" column="CREATED_BY" />
            <result property="createdDate" column="CREATED_DATE" />
            <result property="updatedBy" column="UPDATED_BY" />
            <result property="updatedDate" column="UPDATED_DATE" />
            <result property="templateCode" column="TEMPLATE_CODE" />
            <result property="templateName" column="TEMPLATE_NAME" />
            <result property="templateType" column="TEMPLATE_TYPE" />
            <result property="templateClass" column="TEMPLATE_CLASS" />
            <result property="templateDesc" column="TEMPLATE_DESC" />
            <result property="status" column="STATUS" />
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_SMS_TEMPLATE,CREATED_BY,CREATED_DATE,UPDATED_BY,UPDATED_DATE,TEMPLATE_CODE,
        TEMPLATE_NAME,TEMPLATE_TYPE,TEMPLATE_CLASS,TEMPLATE_DESC,STATUS
    </sql>
    <select id="getSmsTemplate" parameterType="com.paic.ncbs.claim.model.dto.message.ClmsSmsTemplateDTO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from CLMS_SMS_TEMPLATE
        where TEMPLATE_CLASS=#{templateClass}
        and   STATUS = 'Y'
        limit 1
    </select>
    <update id="modifySmsTemplate" parameterType="com.paic.ncbs.claim.model.dto.message.ClmsSmsTemplateDTO">
        update CLMS_SMS_TEMPLATE
        set TEMPLATE_DESC=#{templateDesc}
        where TEMPLATE_CLASS=#{templateClass}
    </update>

</mapper>
