<?xml version =  "1.0" encoding =  "UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace =  "com.paic.ncbs.claim.dao.mapper.other.MailInfoMapper">

	<insert id="addMailInfo" parameterType="com.paic.ncbs.claim.model.dto.message.MailInfoDTO">
		insert into clms_mail_notice_info (
		    created_by,
			created_date,
			updated_by,
			updated_date,
			id_ahcs_mail_notice_info,
			department_code,
			template_code,
			title,
			is_send_success,
			file_path,
			file_name,
			send_time,
			send_tos,
			mail_cc,
			email_content_map,
			archive_time
		)
		values(
			#{createdBy ,jdbcType=VARCHAR},
			now(),
			#{updatedBy ,jdbcType=VARCHAR},
			now(),
			#{idAhcsMailNoticeInfo ,jdbcType=VARCHAR},
			#{departmentCode ,jdbcType=VARCHAR},
			#{templateCode ,jdbcType=VARCHAR},
			#{title ,jdbcType=VARCHAR},
			#{isSendSuccess ,jdbcType=VARCHAR},
			#{filePath ,jdbcType=VARCHAR},
			#{fileName ,jdbcType=VARCHAR},
			now(),
			#{sendTos ,jdbcType=VARCHAR},
			#{mailCc ,jdbcType=VARCHAR},
			#{emailContentMap ,jdbcType=VARCHAR},
			now()
		)
	</insert>

	<update id="updateMailInfo" parameterType="com.paic.ncbs.claim.model.dto.message.MailInfoDTO">
		update clms_mail_notice_info
		set updated_date = now(),
		<if test="fileName != null">
			file_name = #{fileName,jdbcType=VARCHAR},
		</if>
		is_send_success = #{isSendSuccess ,jdbcType=VARCHAR}
		where id_ahcs_mail_notice_info = #{idAhcsMailNoticeInfo ,jdbcType=VARCHAR}
	</update>
	
	<select id="getMailPolicy" resultType="com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO">
		select policy_no     policyNo,
			department_code  departmentCode
		from clms_policy_info t
		where t.report_no = #{reportNo,jdbcType=VARCHAR}
	</select>

</mapper>
