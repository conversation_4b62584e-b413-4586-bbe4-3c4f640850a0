<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.MultiClaimApplyMapper">
	

	<select id="getMultiClaimPriorityReason" resultType="string">
		 select (select cp.VALUE_CHINESE_NAME from CLM_COMMON_PARAMETER cp 
		          where cp.COLLECTION_CODE = 'AHCS_PRIORITY_REASO'
		                and cp.VALUE_CODE = mca.PRIORITY_REASON_CODE) as priorityReasonName
		  from CLMS_MULTI_CLAIM_APPLY mca
         where mca.REPORT_NO = #{reportNo} 
               and mca.CASE_TIMES = #{caseTimes} 
		       and (mca.verify_options='1' or (mca.DEPT_VERIFY_OPTIONS='1' and mca.verify_options is null))
		       and mca.STATUS = '2' 
		       and (mca.EOA_OPTIONS is null or mca.EOA_OPTIONS = '1') 
		       and mca.IS_PRIORITY_HANDLE = 'Y'   
		       limit 1
	</select>

	<select id="getMultiClaimIngByReportNo" resultType="Integer">
		select count(1)
		from CLMS_MULTI_CLAIM_APPLY mca
		where mca.REPORT_NO = #{reportNo}
		  and mca.CASE_TIMES = #{caseTimes}
		  and mca.STATUS in('1','3','4')
	</select>

	<select id="getApplyTimesByRnCt" resultType="int">
		select IFNULL(max(APPLY_TIMES), 0)
		from CLMS_MULTI_CLAIM_APPLY mca
		where mca.REPORT_NO = #{reportNo}
		  and mca.CASE_TIMES = #{caseTimes}
	</select>

	<select id="getCurrentMultiClaimApplyUm" resultType="string">
		select APPLY_UM
		from CLMS_MULTI_CLAIM_APPLY mca
		where mca.REPORT_NO = #{reportNo}
		  and mca.CASE_TIMES = #{caseTimes}
		  and mca.APPLY_TIMES = #{applyTimes}
		  limit 1
	</select>

</mapper>