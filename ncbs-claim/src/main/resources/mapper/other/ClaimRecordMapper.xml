<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.ClaimRecordMapper">

	<select id="getCustomerByPolicy" parameterType="com.paic.ncbs.claim.model.dto.policy.ClaimRecordDTO"
			resultType="com.paic.ncbs.claim.model.dto.report.CustomerDTO">
		select distinct t1.name clientName,
			t1.certificate_no   certificateNo,
			t1.certificate_type certificateType,
			t4.accident_date    accidentDate
		from clms_report_customer t1
		left join clm_report_accident t4 on t1.report_no = t4.report_no
		where t1.report_no in
			(select t2.report_no
			 from clm_policy_pay t2
			 where t2.policy_no = #{policyNo,jdbcType=VARCHAR}
			 and t2.policy_sum_pay > 0
			 and exists (select 1
				from clm_whole_case_base t3
				where t3.report_no = t2.report_no
				and t3.whole_case_status = '0'))
	</select>

	<select id="getRecordDutyByPolicy" parameterType="com.paic.ncbs.claim.model.dto.policy.ClaimRecordDTO"
			resultType="com.paic.ncbs.claim.model.dto.report.RecordDutyInfo">
		SELECT a.DUTY_CODE dutyCode,
		       case when IFNULL(SUM(IFNULL(a.DUTY_PAY_AMOUNT, 0)), 0) >0 then 'Y' else  'N' end isHistoryFlag
		FROM
		     CLM_PLAN_DUTY_PAY a,
		     CLMS_POLICY_CLAIM_CASE b,
		     CLMS_CASE_PROCESS c
		WHERE a.CASE_NO = b.CASE_NO
		AND b.REPORT_NO= c.REPORT_NO
		AND a.CASE_TIMES = c.CASE_TIMES
		AND a.CLAIM_TYPE = '1'
		AND c.PROCESS_STATUS = '05'
		AND b.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		group by a.DUTY_CODE
	</select>

	<select id="getUnResolvedReportNo" parameterType="com.paic.ncbs.claim.model.dto.policy.ClaimRecordDTO"
			resultType="com.paic.ncbs.claim.model.dto.report.CustomerDTO">
		select distinct t2.name clientName,
		t2.certificate_no   certificateNo,
		t2.certificate_type certificateType,
		t3.accident_date    accidentDate
		from clms_report_customer t2
		left join clm_report_accident t3 on t2.report_no = t3.report_no
		where t2.REPORT_NO in (
		select distinct  t1.report_no
		from clms_policy_info t1
		where t1.policy_no = #{policyNo,jdbcType=VARCHAR}
		and exists (select 1 from clm_whole_case_base t2 where t2.report_no=t1.report_no and t2.whole_case_status !='0'))
	</select>

	<select id="getRiskPropertyByPolicy" parameterType="com.paic.ncbs.claim.model.dto.policy.ClaimRecordDTO"
			resultType="com.paic.ncbs.claim.model.dto.report.CustomerDTO">
		select distinct t1.name clientName,
						t1.certificate_no   certificateNo,
						t1.certificate_type certificateType,
						t1.id_ply_risk_property idPlyRiskProperty,
						(select t4.accident_date
						 from clm_report_accident t4
						 where t1.report_no = t4.report_no limit 1) accidentDate
		from clms_risk_property_case t1
		where t1.is_effective ='Y'
		and   t1.task_id = 'checkDuty'
		and   t1.report_no in
			  (select t2.report_no
			   from clm_policy_pay t2
			   where t2.policy_no = #{policyNo,jdbcType=VARCHAR}
				 and t2.policy_sum_pay > 0
				 and exists (select 1
							 from clm_whole_case_base t3
							 where t3.report_no = t2.report_no
							   and t3.whole_case_status = '0'))
	</select>
	
	<select id="getUnResolvedRiskPropertyByPolicy" parameterType="com.paic.ncbs.claim.model.dto.policy.ClaimRecordDTO"
			resultType="com.paic.ncbs.claim.model.dto.report.CustomerDTO">
		select  distinct
				t1.name clientName,
				t1.certificate_no   certificateNo,
				t1.certificate_type certificateType,
				t1.id_ply_risk_property idPlyRiskProperty,
				(select t4.accident_date
				from clm_report_accident t4
				where t1.report_no = t4.report_no limit 1) accidentDate
		from clms_risk_property_case t1
		where t1.is_effective ='Y'
		and t1.task_id = (select task_id from
				(
					select t5.task_id from CLMS_case_class t5
					where t5.REPORT_NO = t1.REPORT_NO
					and t5.CASE_TIMES = t1.CASE_TIMES
					and t5.IS_EFFECTIVE = 'Y'
					order by t5.CREATED_DATE desc
				) tmp limit 1
			)
		and t1.report_no in (
				select t2.report_no
				from clms_policy_info t2
				where t2.policy_no = #{policyNo, jdbcType=VARCHAR}
				and exists (select 1 from clm_whole_case_base t3 where t3.report_no=t2.report_no and t3.whole_case_status !='0')
			)
	</select>		

</mapper>