<?xml version="1.0"  encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.risk.RiskDetailMapper">

    <insert id="addRiskDetail">
        insert into
        CLMS_RISK_DETAIL(
        ID_AHCS_RISK_DETAIL,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_RISK_INFO,
        RISK_FROM,
        OBJECT_NAME,
        OBJECT_TYPE,
        PERSON_PROPERTY,
        CERTIFICATE_TYPE,
        CERTIFICATE_NO,
        MOBILE_NO,
        FIXED_TELEPHONE,
        TYPE,
        REASON_DETAILS,
        RISK_CLASS,
        PROVINCE_CODE,
        CITY_CODE,
        COUNTY_CODE,
        UNIT_ADDRESS,
        SOCIAL_CREDIT_CODE,
        MAINTAIN_BY,
        MAINTAIN_DATE)
        values
        <foreach collection="list" item="item" index="i" separator=",">
            (
            #{item.idAhcsRiskDetail},
            #{item.createdBy,jdbcType=VARCHAR},
            SYSDATE(),
            #{item.createdBy,jdbcType=VARCHAR},
            SYSDATE(),
            #{item.idAhcsRiskInfo,jdbcType=VARCHAR},
            #{item.riskFrom,jdbcType=VARCHAR},
            #{item.objectName,jdbcType=VARCHAR},
            #{item.objectType,jdbcType=VARCHAR},
            #{item.personProperty,jdbcType=VARCHAR},
            #{item.certificateType,jdbcType=VARCHAR},
            #{item.certificateNo,jdbcType=VARCHAR},
            #{item.mobileNo,jdbcType=VARCHAR},
            #{item.fixedTelephone,jdbcType=VARCHAR},
            #{item.type,jdbcType=VARCHAR},
            #{item.reasonDetails,jdbcType=VARCHAR},
            #{item.riskClass,jdbcType=VARCHAR},
            #{item.provinceCode,jdbcType=VARCHAR},
            #{item.cityCode,jdbcType=VARCHAR},
            #{item.countyCode,jdbcType=VARCHAR},
            #{item.unitAddress,jdbcType=VARCHAR},
            #{item.socialCreditCode,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            SYSDATE()
            )
        </foreach>
    </insert>

    <select id="getRiskDetailList" resultType="com.paic.ncbs.claim.model.dto.risk.RiskDetailDTO">
        select
        RISK_FROM riskFrom,
        OBJECT_NAME objectName,
        (select t.value_chinese_name
        from CLM_COMMON_PARAMETER t
        where COLLECTION_CODE in ('AHCS_RISK_MAN_TYPE','AHCS_RISK_UNIT_TYPE') and t.value_code=t1.object_type)
        objectType,
        t1.CERTIFICATE_TYPE certificateType,
        CERTIFICATE_NO certificateNo,
        MOBILE_NO mobileNo,
        FIXED_TELEPHONE fixedTelephone,
        (select t.value_chinese_name
        from CLM_COMMON_PARAMETER t
        where COLLECTION_CODE in ('AHCS_BLK_PERSON_TYPE','AHCS_BLK_ORG_TYPE') and t.value_code=t1.TYPE) type,
        RISK_CLASS riskClass,
        SOCIAL_CREDIT_CODE socialCreditCode,
        reason_details reasonDetails
        from
        CLMS_RISK_DETAIL t1
        where ID_AHCS_RISK_INFO in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>