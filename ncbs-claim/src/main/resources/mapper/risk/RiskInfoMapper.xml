<?xml version="1.0"  encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.risk.RiskInfoMapper">
	
	<insert id="addRiskInfo">
   		 insert into 
   		 			CLMS_RISK_INFO (
   		 					CREATED_BY, 
   		 					CREATED_DATE, 
   		 					UPDATED_BY, 
   		 					UPDATED_DATE, 
   		 					ID_AHCS_RISK_INFO, 
   		 					RISK_FROM, 
   		 					INFO_FROM, 
   		 					DEPARTMENT_CODE, 
   		 					TACHE_FROM, 
   		 					MAINTAIN_BY, 
   		 					MAINTAIN_DATE)
   		 		values (
   		 				#{createdBy,jdbcType=VARCHAR},
                		now(),
                		#{createdBy,jdbcType=VARCHAR},
						now(),
               			#{idAhcsRiskInfo},
               			#{riskFrom,jdbcType=VARCHAR},
               			#{infoFrom,jdbcType=VARCHAR},
               			#{departmentCode,jdbcType=VARCHAR},
               			#{tacheFrom,jdbcType=VARCHAR},
               			#{createdBy,jdbcType=VARCHAR},
		                now()
               			)
   		</insert>

	<select id="getRiskInfo"  resultType="com.paic.ncbs.claim.model.dto.risk.RiskInfoDTO">
		select
			ID_AHCS_RISK_INFO	idAhcsRiskInfo,
			TACHE_FROM	        tacheFrom
		from
			CLMS_RISK_INFO
		where RISK_FROM = #{riskFrom}
	</select>

	<select id="getReportCount" resultType="com.paic.ncbs.claim.model.vo.risk.PolicyReportVO" >
		select t.policy_no  policyNo,
		count(t.report_no)  reportCount
		from
		clms_policy_info t
		where t.policy_no in
		(select t1.policy_no from clms_policy_info t1 where t1.report_no = #{reportNo,jdbcType=VARCHAR})
		group by t.policy_no
	</select>

	<select id="getPolicyHolder" resultType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity">
		select
		a.name name,
		a.certificate_no certificateNo,
		a.address address,
		a.telephone telephone
		from clms_policy_holder a,clms_policy_info b
		where a.id_ahcs_policy_info=b.id_ahcs_policy_info
		and b.report_no = #{reportNo,jdbcType=VARCHAR}
		and a.PERSONNEL_TYPE = '0'
	</select>



</mapper>