<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.pet.PetHospitalMapper">

    <insert id="addPetHospitalList" parameterType="java.util.List">
        INSERT INTO CLMS_PET_HOSPITAL (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            HOSPITAL_CODE,
            HOSPITAL_NAME,
            PROVINCE_CODE,
            CITY_CODE,
            COUNTRY_CODE,
            HOSPITAL_ACCOUNT,
            HOSPITAL_ADDRESS,
            TELEPHONE)
        VALUES
        <foreach collection="petHospitalList" separator="," index="index" item="item">
            (
            #{item.createdBy,jdbcType=VARCHAR},
            now(),
            #{item.createdBy,jdbcType=VARCHAR},
            now(),
            #{item.hospitalCode,jdbcType=VARCHAR},
            #{item.hospitalName,jdbcType=VARCHAR},
            #{item.provinceCode,jdbcType=TIMESTAMP},
            #{item.cityCode,jdbcType=TIMESTAMP},
            #{item.countryCode,jdbcType=DECIMAL},
            #{item.hospitalAccount,jdbcType=VARCHAR},
            #{item.hospitalAddress,jdbcType=VARCHAR},
            #{item.telephone,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="getPetHospitalList" resultType="com.paic.ncbs.claim.model.dto.pet.PetHospitalDTO" parameterType="com.paic.ncbs.claim.model.dto.pet.PetHospitalDTO">
        SELECT  ID_CLMS_PET_HOSPITAL idClmsPetHospital,
                HOSPITAL_CODE hospitalCode,
                HOSPITAL_NAME hospitalName,
                PROVINCE_CODE provinceCode,
                CITY_CODE cityCode,
                COUNTRY_CODE countryCode,
                HOSPITAL_ACCOUNT hospitalAccount,
                HOSPITAL_ADDRESS hospitalAddress,
                TELEPHONE telephone
        FROM CLMS_PET_HOSPITAL
        WHERE IS_EFFECTIVE = 'Y'

        <if test="hospitalCode != null and hospitalCode != ''">
            AND HOSPITAL_CODE = #{hospitalCode,jdbcType=VARCHAR}
        </if>
        <if test="hospitalName != null and hospitalName != ''">
            AND HOSPITAL_NAME like concat('%',#{hospitalName,jdbcType=VARCHAR},'%')
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="countryCode != null and countryCode != ''">
            AND COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
        </if>
        ORDER BY HOSPITAL_NAME
    </select>

    <update id="delPetHospital" parameterType="java.util.List">
        UPDATE CLMS_PET_HOSPITAL
        SET    UPDATED_DATE = now(),
               UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
               IS_EFFECTIVE = 'N'
        WHERE HOSPITAL_CODE IN (
            <foreach collection="petHospitalCodeList" separator="," index="index" item="item">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        AND IS_EFFECTIVE = 'Y'
    </update>

    <select id="getMaxId" resultType="java.lang.Integer">
        SELECT MAX(ID_CLMS_PET_HOSPITAL)
        FROM CLMS_PET_HOSPITAL
    </select>

</mapper>