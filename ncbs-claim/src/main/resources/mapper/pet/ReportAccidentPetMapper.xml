<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.pet.ReportAccidentPetMapper" >

  <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportAccidentPetEntity" >
    <id column="ID_AHCS_REPORT_ACCIDENT_PET" property="idAhcsReportAccidentPet" jdbcType="VARCHAR" />
    <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
    <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR" />
    <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP" />
    <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR" />
    <result column="PET_DESC" property="petDesc" jdbcType="VARCHAR" />
    <result column="MEDICAL_PROJECTS" property="medicalProjects" jdbcType="VARCHAR" />
    <result column="USER_RESERVATION_NO" property="userReservationNo" jdbcType="VARCHAR" />
    <result column="HOSPITAL_NO" property="hospitalNo" jdbcType="VARCHAR" />
    <result column="ILLNESS_DESC" property="illnessDesc" jdbcType="VARCHAR" />
    <result column="FIRST_DIAGNOSIS_RESULT" property="firstDiagnosisResult" jdbcType="VARCHAR" />
    <result column="DOCUMENT_INFO" property="documentInfo" jdbcType="CLOB" />
    <result column="MEDICAL_INFO" property="medicalInfo" jdbcType="CLOB" />
    <result column="COST_ESTIMATE" property="costEstimate" jdbcType="DECIMAL" />
    <result column="PET_INJURED_TYPE" property="petInjuredType" jdbcType="VARCHAR" />
    <result column="ACCIDENT_PET" property="accidentPet" jdbcType="VARCHAR" />
    <result column="TREAT_CONDITION" property="treatCondition" jdbcType="VARCHAR" />
    <result column="HOSPITAL_NAME" property="hospitalName" jdbcType="VARCHAR" />
    <result column="HOSPITAL_PLACE" property="hospitalPlace" jdbcType="VARCHAR" />
    <result column="DISEASE_CLASSIFICATION" property="diseaseClassification" jdbcType="VARCHAR" />
    <result column="TREATMENT" property="treatment" jdbcType="VARCHAR" />
    <result column="DIAGNOSIS_DETAIL" property="diagnosisDetail" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    ID_AHCS_REPORT_ACCIDENT_PET,CREATED_BY,CREATED_DATE,UPDATED_BY,UPDATED_DATE,
    REPORT_NO,PET_DESC,MEDICAL_PROJECTS,USER_RESERVATION_NO,HOSPITAL_NO,
    ILLNESS_DESC,FIRST_DIAGNOSIS_RESULT,DOCUMENT_INFO,MEDICAL_INFO,COST_ESTIMATE,
    PET_INJURED_TYPE,ACCIDENT_PET,TREAT_CONDITION,HOSPITAL_NAME,HOSPITAL_PLACE,
    DISEASE_CLASSIFICATION,TREATMENT,DIAGNOSIS_DETAIL
  </sql>

  <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportAccidentPetEntity" >
    insert into CLMS_REPORT_ACCIDENT_PET (
    <include refid="Base_Column_List" />
    )
    values (#{idAhcsReportAccidentPet,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
    #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{reportNo,jdbcType=VARCHAR},
    #{petDesc,jdbcType=VARCHAR},#{medicalProjects,jdbcType=VARCHAR},#{userReservationNo,jdbcType=VARCHAR},
    #{hospitalNo,jdbcType=VARCHAR},#{illnessDesc,jdbcType=VARCHAR},
    #{firstDiagnosisResult,jdbcType=VARCHAR},#{documentInfo,jdbcType=VARCHAR},#{medicalInfo,jdbcType=VARCHAR},
    #{costEstimate,jdbcType=DECIMAL},#{petInjuredType,jdbcType=VARCHAR},#{accidentPet,jdbcType=VARCHAR},#{treatCondition,jdbcType=VARCHAR},
    #{hospitalName,jdbcType=VARCHAR},#{hospitalPlace,jdbcType=VARCHAR},#{diseaseClassification,jdbcType=VARCHAR},
    #{treatment,jdbcType=VARCHAR},#{diagnosisDetail,jdbcType=VARCHAR}
    )
  </insert>

  <select id="getReportAccidentPetByReportNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CLMS_REPORT_ACCIDENT_PET
    where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
  </select>
</mapper>