<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.dao.mapper.trace.ClmsPersTraceFeeMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceFeeDTO">
		 <id column="id" property="id"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="case_times" property="caseTimes"/> 
		 <result column="injured_id" property="injuredId"/> 
		 <result column="trace_exp_id" property="traceExpId"/> 
		 <result column="risk_code" property="riskCode"/> 
		 <result column="kind_code" property="kindCode"/> 
		 <result column="kind_name" property="kindName"/> 
		 <result column="loss_item_no" property="lossItemNo"/> 
		 <result column="loss_item_name" property="lossItemName"/> 
		 <result column="family_no" property="familyNo"/> 
		 <result column="item_no" property="itemNo"/> 
		 <result column="item_name" property="itemName"/> 
		 <result column="amount" property="amount"/> 
		 <result column="currency" property="currency"/> 
		 <result column="exch_rate" property="exchRate"/> 
		 <result column="fee_type_code" property="feeTypeCode"/> 
		 <result column="fee_type_name" property="feeTypeName"/> 
		 <result column="unit_amount" property="unitAmount"/> 
		 <result column="quantity" property="quantity"/> 
		 <result column="real_fee" property="realFee"/> 
		 <result column="deduction_fee" property="deductionFee"/> 
		 <result column="sum_def_loss" property="sumDefLoss"/> 
		 <result column="own_expense" property="ownExpense"/> 
		 <result column="own_pay" property="ownPay"/> 
		 <result column="third_party" property="thirdParty"/> 
		 <result column="unreasonable" property="unreasonable"/> 
		 <result column="remark" property="remark"/> 
		 <result column="valid_flag" property="validFlag"/> 
		 <result column="flag" property="flag"/> 
		 <result column="estimate_loss" property="estimateLoss"/> 
		 <result column="disabled_grade" property="disabledGrade"/> 
		 <result column="disabled_pay_rate" property="disabledPayRate"/> 
		 <result column="create_flag" property="createFlag"/> 
		 <result column="care_fee" property="careFee"/> 
		 <result column="pay_pers_day" property="payPersDay"/> 
		 <result column="in_hosiptal_day" property="inHosiptalDay"/> 
		 <result column="outpay_day" property="outpayDay"/> 
		 <result column="nopay_day" property="nopayDay"/>
		 <result column="remit_amount" property="remitAmount"/>
		 <result column="other_pay_rate" property="otherPayRate"/> 
		 <result column="deduction_content" property="deductionContent"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, report_no, case_times, injured_id, trace_exp_id,
		 risk_code, kind_code, kind_name, loss_item_no, loss_item_name,
		 family_no, item_no, item_name, amount, currency,
		 exch_rate, fee_type_code, fee_type_name, unit_amount, quantity,
		 real_fee, deduction_fee, sum_def_loss, own_expense, own_pay,
		 third_party, unreasonable, remark, valid_flag, flag,
		 estimate_loss, disabled_grade, disabled_pay_rate, create_flag, care_fee,
		 pay_pers_day, in_hosiptal_day, outpay_day, nopay_day, remit_amount, other_pay_rate,
		 deduction_content, created_by, sys_ctime, updated_by, sys_utime
		
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="injuredId != null" >
			and injured_id = #{injuredId}
		</if>
		<if test="traceExpId != null" >
			and trace_exp_id = #{traceExpId}
		</if>
		<if test="riskCode != null" >
			and risk_code = #{riskCode}
		</if>
		<if test="kindCode != null" >
			and kind_code = #{kindCode}
		</if>
		<if test="kindName != null" >
			and kind_name = #{kindName}
		</if>
		<if test="lossItemNo != null" >
			and loss_item_no = #{lossItemNo}
		</if>
		<if test="lossItemName != null" >
			and loss_item_name = #{lossItemName}
		</if>
		<if test="familyNo != null" >
			and family_no = #{familyNo}
		</if>
		<if test="itemNo != null" >
			and item_no = #{itemNo}
		</if>
		<if test="itemName != null" >
			and item_name = #{itemName}
		</if>
		<if test="amount != null" >
			and amount = #{amount}
		</if>
		<if test="currency != null" >
			and currency = #{currency}
		</if>
		<if test="exchRate != null" >
			and exch_rate = #{exchRate}
		</if>
		<if test="feeTypeCode != null" >
			and fee_type_code = #{feeTypeCode}
		</if>
		<if test="feeTypeName != null" >
			and fee_type_name = #{feeTypeName}
		</if>
		<if test="unitAmount != null" >
			and unit_amount = #{unitAmount}
		</if>
		<if test="quantity != null" >
			and quantity = #{quantity}
		</if>
		<if test="realFee != null" >
			and real_fee = #{realFee}
		</if>
		<if test="deductionFee != null" >
			and deduction_fee = #{deductionFee}
		</if>
		<if test="sumDefLoss != null" >
			and sum_def_loss = #{sumDefLoss}
		</if>
		<if test="ownExpense != null" >
			and own_expense = #{ownExpense}
		</if>
		<if test="ownPay != null" >
			and own_pay = #{ownPay}
		</if>
		<if test="thirdParty != null" >
			and third_party = #{thirdParty}
		</if>
		<if test="unreasonable != null" >
			and unreasonable = #{unreasonable}
		</if>
		<if test="remark != null" >
			and remark = #{remark}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="flag != null" >
			and flag = #{flag}
		</if>
		<if test="estimateLoss != null" >
			and estimate_loss = #{estimateLoss}
		</if>
		<if test="disabledGrade != null" >
			and disabled_grade = #{disabledGrade}
		</if>
		<if test="disabledPayRate != null" >
			and disabled_pay_rate = #{disabledPayRate}
		</if>
		<if test="createFlag != null" >
			and create_flag = #{createFlag}
		</if>
		<if test="careFee != null" >
			and care_fee = #{careFee}
		</if>
		<if test="payPersDay != null" >
			and pay_pers_day = #{payPersDay}
		</if>
		<if test="inHosiptalDay != null" >
			and in_hosiptal_day = #{inHosiptalDay}
		</if>
		<if test="outpayDay != null" >
			and outpay_day = #{outpayDay}
		</if>
		<if test="nopayDay != null" >
			and nopay_day = #{nopayDay}
		</if>
		<if test="remitAmount != null" >
			and remit_amount = #{remitAmount}
		</if>
		<if test="otherPayRate != null" >
			and other_pay_rate = #{otherPayRate}
		</if>
		<if test="deductionContent != null" >
			and deduction_content = #{deductionContent}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_pers_trace_fee
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_trace_fee
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_trace_fee
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceFeeDTO">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_pers_trace_fee
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_pers_trace_fee
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceFeeDTO">
		insert into clms_pers_trace_fee (id, report_no, case_times, injured_id, trace_exp_id,
			risk_code, kind_code, kind_name, loss_item_no, loss_item_name,
			family_no, item_no, item_name, amount, currency,
			exch_rate, fee_type_code, fee_type_name, unit_amount, quantity,
			real_fee, deduction_fee, sum_def_loss, own_expense, own_pay,
			third_party, unreasonable, remark, valid_flag, flag,
			estimate_loss, disabled_grade, disabled_pay_rate, create_flag, care_fee,
			pay_pers_day, in_hosiptal_day, outpay_day, nopay_day, remit_amount,other_pay_rate,
			deduction_content, created_by, sys_ctime, updated_by, sys_utime
			)
		values(#{id}, #{reportNo}, #{caseTimes}, #{injuredId}, #{traceExpId}, 
			#{riskCode}, #{kindCode}, #{kindName}, #{lossItemNo}, #{lossItemName}, 
			#{familyNo}, #{itemNo}, #{itemName}, #{amount}, #{currency}, 
			#{exchRate}, #{feeTypeCode}, #{feeTypeName}, #{unitAmount}, #{quantity}, 
			#{realFee}, #{deductionFee}, #{sumDefLoss}, #{ownExpense}, #{ownPay}, 
			#{thirdParty}, #{unreasonable}, #{remark}, #{validFlag}, #{flag}, 
			#{estimateLoss}, #{disabledGrade}, #{disabledPayRate}, #{createFlag}, #{careFee}, 
			#{payPersDay}, #{inHosiptalDay}, #{outpayDay}, #{nopayDay}, #{remitAmount}, #{otherPayRate},
			#{deductionContent}, #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
			)
	</insert>
	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO clms_pers_trace_fee (
		REPORT_NO, CASE_TIMES, INJURED_ID, TRACE_EXP_ID,
		RISK_CODE, KIND_CODE, KIND_NAME, LOSS_ITEM_NO, LOSS_ITEM_NAME,
		FAMILY_NO, ITEM_NO, ITEM_NAME, AMOUNT, CURRENCY,
		EXCH_RATE, FEE_TYPE_CODE, FEE_TYPE_NAME, UNIT_AMOUNT, QUANTITY,
		REAL_FEE, DEDUCTION_FEE, SUM_DEF_LOSS, OWN_EXPENSE, OWN_PAY,
		THIRD_PARTY, UNREASONABLE, REMARK, VALID_FLAG, FLAG,
		ESTIMATE_LOSS, DISABLED_GRADE, DISABLED_PAY_RATE, CREATE_FLAG, CARE_FEE,
		PAY_PERS_DAY, IN_HOSIPTAL_DAY, OUTPAY_DAY, NOPAY_DAY, REMIT_AMOUNT, OTHER_PAY_RATE,
		DEDUCTION_CONTENT, CREATED_BY, SYS_CTIME, UPDATED_BY, SYS_UTIME
		)
		SELECT
		REPORT_NO, #{reopenCaseTimes}, INJURED_ID, TRACE_EXP_ID,
		RISK_CODE, KIND_CODE, KIND_NAME, LOSS_ITEM_NO, LOSS_ITEM_NAME,
		FAMILY_NO, ITEM_NO, ITEM_NAME, AMOUNT, CURRENCY,
		EXCH_RATE, FEE_TYPE_CODE, FEE_TYPE_NAME, UNIT_AMOUNT, QUANTITY,
		REAL_FEE, DEDUCTION_FEE, SUM_DEF_LOSS, OWN_EXPENSE, OWN_PAY,
		THIRD_PARTY, UNREASONABLE, REMARK, VALID_FLAG, FLAG,
		ESTIMATE_LOSS, DISABLED_GRADE, DISABLED_PAY_RATE, CREATE_FLAG, CARE_FEE,
		PAY_PERS_DAY, IN_HOSIPTAL_DAY, OUTPAY_DAY, NOPAY_DAY, REMIT_AMOUNT, OTHER_PAY_RATE,
		DEDUCTION_CONTENT, #{userId}, now(), #{userId}, now()
		FROM clms_pers_trace_fee
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
		AND VALID_FLAG='Y'
	</insert>
	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceFeeDTO">
		insert into clms_pers_trace_fee
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="injuredId != null" >
				injured_id,
			</if>
			<if test="traceExpId != null" >
				trace_exp_id,
			</if>
			<if test="riskCode != null" >
				risk_code,
			</if>
			<if test="kindCode != null" >
				kind_code,
			</if>
			<if test="kindName != null" >
				kind_name,
			</if>
			<if test="lossItemNo != null" >
				loss_item_no,
			</if>
			<if test="lossItemName != null" >
				loss_item_name,
			</if>
			<if test="familyNo != null" >
				family_no,
			</if>
			<if test="itemNo != null" >
				item_no,
			</if>
			<if test="itemName != null" >
				item_name,
			</if>
			<if test="amount != null" >
				amount,
			</if>
			<if test="currency != null" >
				currency,
			</if>
			<if test="exchRate != null" >
				exch_rate,
			</if>
			<if test="feeTypeCode != null" >
				fee_type_code,
			</if>
			<if test="feeTypeName != null" >
				fee_type_name,
			</if>
			<if test="unitAmount != null" >
				unit_amount,
			</if>
			<if test="quantity != null" >
				quantity,
			</if>
			<if test="realFee != null" >
				real_fee,
			</if>
			<if test="deductionFee != null" >
				deduction_fee,
			</if>
			<if test="sumDefLoss != null" >
				sum_def_loss,
			</if>
			<if test="ownExpense != null" >
				own_expense,
			</if>
			<if test="ownPay != null" >
				own_pay,
			</if>
			<if test="thirdParty != null" >
				third_party,
			</if>
			<if test="unreasonable != null" >
				unreasonable,
			</if>
			<if test="remark != null" >
				remark,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="flag != null" >
				flag,
			</if>
			<if test="estimateLoss != null" >
				estimate_loss,
			</if>
			<if test="disabledGrade != null" >
				disabled_grade,
			</if>
			<if test="disabledPayRate != null" >
				disabled_pay_rate,
			</if>
			<if test="createFlag != null" >
				create_flag,
			</if>
			<if test="careFee != null" >
				care_fee,
			</if>
			<if test="payPersDay != null" >
				pay_pers_day,
			</if>
			<if test="inHosiptalDay != null" >
				in_hosiptal_day,
			</if>
			<if test="outpayDay != null" >
				outpay_day,
			</if>
			<if test="nopayDay != null" >
				nopay_day,
			</if>
			<if test="remitAmount != null" >
				remit_amount,
			</if>
			<if test="otherPayRate != null" >
				other_pay_rate,
			</if>
			<if test="deductionContent != null" >
				deduction_content,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="injuredId != null" >
				#{injuredId},
			</if>
			<if test="traceExpId != null" >
				#{traceExpId},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="kindCode != null" >
				#{kindCode},
			</if>
			<if test="kindName != null" >
				#{kindName},
			</if>
			<if test="lossItemNo != null" >
				#{lossItemNo},
			</if>
			<if test="lossItemName != null" >
				#{lossItemName},
			</if>
			<if test="familyNo != null" >
				#{familyNo},
			</if>
			<if test="itemNo != null" >
				#{itemNo},
			</if>
			<if test="itemName != null" >
				#{itemName},
			</if>
			<if test="amount != null" >
				#{amount},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="exchRate != null" >
				#{exchRate},
			</if>
			<if test="feeTypeCode != null" >
				#{feeTypeCode},
			</if>
			<if test="feeTypeName != null" >
				#{feeTypeName},
			</if>
			<if test="unitAmount != null" >
				#{unitAmount},
			</if>
			<if test="quantity != null" >
				#{quantity},
			</if>
			<if test="realFee != null" >
				#{realFee},
			</if>
			<if test="deductionFee != null" >
				#{deductionFee},
			</if>
			<if test="sumDefLoss != null" >
				#{sumDefLoss},
			</if>
			<if test="ownExpense != null" >
				#{ownExpense},
			</if>
			<if test="ownPay != null" >
				#{ownPay},
			</if>
			<if test="thirdParty != null" >
				#{thirdParty},
			</if>
			<if test="unreasonable != null" >
				#{unreasonable},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="estimateLoss != null" >
				#{estimateLoss},
			</if>
			<if test="disabledGrade != null" >
				#{disabledGrade},
			</if>
			<if test="disabledPayRate != null" >
				#{disabledPayRate},
			</if>
			<if test="createFlag != null" >
				#{createFlag},
			</if>
			<if test="careFee != null" >
				#{careFee},
			</if>
			<if test="payPersDay != null" >
				#{payPersDay},
			</if>
			<if test="inHosiptalDay != null" >
				#{inHosiptalDay},
			</if>
			<if test="outpayDay != null" >
				#{outpayDay},
			</if>
			<if test="nopayDay != null" >
				#{nopayDay},
			</if>
			<if test="remitAmount != null" >
				#{remitAmount},
			</if>
			<if test="otherPayRate != null" >
				#{otherPayRate},
			</if>
			<if test="deductionContent != null" >
				#{deductionContent},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceFeeDTO">
		update clms_pers_trace_fee
		<set>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="caseTimes != null" >
				case_times=#{caseTimes},
			</if>
			<if test="injuredId != null" >
				injured_id=#{injuredId},
			</if>
			<if test="traceExpId != null" >
				trace_exp_id=#{traceExpId},
			</if>
			<if test="riskCode != null" >
				risk_code=#{riskCode},
			</if>
			<if test="kindCode != null" >
				kind_code=#{kindCode},
			</if>
			<if test="kindName != null" >
				kind_name=#{kindName},
			</if>
			<if test="lossItemNo != null" >
				loss_item_no=#{lossItemNo},
			</if>
			<if test="lossItemName != null" >
				loss_item_name=#{lossItemName},
			</if>
			<if test="familyNo != null" >
				family_no=#{familyNo},
			</if>
			<if test="itemNo != null" >
				item_no=#{itemNo},
			</if>
			<if test="itemName != null" >
				item_name=#{itemName},
			</if>
			<if test="amount != null" >
				amount=#{amount},
			</if>
			<if test="currency != null" >
				currency=#{currency},
			</if>
			<if test="exchRate != null" >
				exch_rate=#{exchRate},
			</if>
			<if test="feeTypeCode != null" >
				fee_type_code=#{feeTypeCode},
			</if>
			<if test="feeTypeName != null" >
				fee_type_name=#{feeTypeName},
			</if>
			<if test="unitAmount != null" >
				unit_amount=#{unitAmount},
			</if>
			<if test="quantity != null" >
				quantity=#{quantity},
			</if>
			<if test="realFee != null" >
				real_fee=#{realFee},
			</if>
			<if test="deductionFee != null" >
				deduction_fee=#{deductionFee},
			</if>
			<if test="sumDefLoss != null" >
				sum_def_loss=#{sumDefLoss},
			</if>
			<if test="ownExpense != null" >
				own_expense=#{ownExpense},
			</if>
			<if test="ownPay != null" >
				own_pay=#{ownPay},
			</if>
			<if test="thirdParty != null" >
				third_party=#{thirdParty},
			</if>
			<if test="unreasonable != null" >
				unreasonable=#{unreasonable},
			</if>
			<if test="remark != null" >
				remark=#{remark},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="flag != null" >
				flag=#{flag},
			</if>
			<if test="estimateLoss != null" >
				estimate_loss=#{estimateLoss},
			</if>
			<if test="disabledGrade != null" >
				disabled_grade=#{disabledGrade},
			</if>
			<if test="disabledPayRate != null" >
				disabled_pay_rate=#{disabledPayRate},
			</if>
			<if test="createFlag != null" >
				create_flag=#{createFlag},
			</if>
			<if test="careFee != null" >
				care_fee=#{careFee},
			</if>
			<if test="payPersDay != null" >
				pay_pers_day=#{payPersDay},
			</if>
			<if test="inHosiptalDay != null" >
				in_hosiptal_day=#{inHosiptalDay},
			</if>
			<if test="outpayDay != null" >
				outpay_day=#{outpayDay},
			</if>
			<if test="nopayDay != null" >
				nopay_day=#{nopayDay},
			</if>
			<if test="remitAmount != null" >
				remit_amount=#{remitAmount},
			</if>
			<if test="otherPayRate != null" >
				other_pay_rate=#{otherPayRate},
			</if>
			<if test="deductionContent != null" >
				deduction_content=#{deductionContent},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceFeeDTO">
		update clms_pers_trace_fee
		set report_no=#{reportNo},
			case_times=#{caseTimes},
			injured_id=#{injuredId},
			trace_exp_id=#{traceExpId},
			risk_code=#{riskCode},
			kind_code=#{kindCode},
			kind_name=#{kindName},
			loss_item_no=#{lossItemNo},
			loss_item_name=#{lossItemName},
			family_no=#{familyNo},
			item_no=#{itemNo},
			item_name=#{itemName},
			amount=#{amount},
			currency=#{currency},
			exch_rate=#{exchRate},
			fee_type_code=#{feeTypeCode},
			fee_type_name=#{feeTypeName},
			unit_amount=#{unitAmount},
			quantity=#{quantity},
			real_fee=#{realFee},
			deduction_fee=#{deductionFee},
			sum_def_loss=#{sumDefLoss},
			own_expense=#{ownExpense},
			own_pay=#{ownPay},
			third_party=#{thirdParty},
			unreasonable=#{unreasonable},
			remark=#{remark},
			valid_flag=#{validFlag},
			flag=#{flag},
			estimate_loss=#{estimateLoss},
			disabled_grade=#{disabledGrade},
			disabled_pay_rate=#{disabledPayRate},
			create_flag=#{createFlag},
			care_fee=#{careFee},
			pay_pers_day=#{payPersDay},
			in_hosiptal_day=#{inHosiptalDay},
			outpay_day=#{outpayDay},
			nopay_day=#{nopayDay},
			remit_amount=#{remitAmount},
			other_pay_rate=#{otherPayRate},
			deduction_content=#{deductionContent},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>
	<!-- 根据报案号，赔付次数查询数据 -->
	<select id="selectClmsPersTraceFee" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsPersTraceFeeVO" parameterType="com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo">
		select
		<include refid="Base_Column_List" />
		from clms_pers_trace_fee
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
		<if test="traceExpId != null" >
			and trace_exp_id=#{traceExpId}
		</if>
		    and valid_flag = 'Y'
	</select>

	<delete id="deleteClmsPersTraceFee" parameterType="map">
		delete from clms_pers_trace_fee
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
	</delete>
</mapper>