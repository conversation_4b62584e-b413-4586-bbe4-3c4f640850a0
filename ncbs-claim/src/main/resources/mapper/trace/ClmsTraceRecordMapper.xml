<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.dao.mapper.trace.ClmsTraceRecordMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.trace.ClmsTraceRecordDTO">
		 <id column="id" property="id"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="injured_id" property="injuredId"/> 
		 <result column="case_times" property="caseTimes"/> 
		 <result column="tracepers_context" property="tracepersContext"/> 
		 <result column="tracepers_object" property="tracepersObject"/> 
		 <result column="phone_number" property="phoneNumber"/> 
		 <result column="trace_date" property="traceDate"/> 
		 <result column="tracepers_code" property="tracepersCode"/> 
		 <result column="tracepers_name" property="tracepersName"/> 
		 <result column="task_info_id" property="taskInfoId"/>
		 <result column="valid_flag" property="validFlag"/>
		 <result column="remark" property="remark"/>
		 <result column="flag" property="flag"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, report_no, injured_id, case_times, tracepers_context,
		 tracepers_object, phone_number, trace_date, tracepers_code, tracepers_name,
		 valid_flag,task_info_id, remark, flag, created_by, sys_ctime,
		 updated_by, sys_utime
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="injuredId != null" >
			and injured_id = #{injuredId}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="tracepersContext != null" >
			and tracepers_context = #{tracepersContext}
		</if>
		<if test="tracepersObject != null" >
			and tracepers_object = #{tracepersObject}
		</if>
		<if test="phoneNumber != null" >
			and phone_number = #{phoneNumber}
		</if>
		<if test="traceDate != null" >
			and trace_date = #{traceDate}
		</if>
		<if test="tracepersCode != null" >
			and tracepers_code = #{tracepersCode}
		</if>
		<if test="tracepersName != null" >
			and tracepers_name = #{tracepersName}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="remark != null" >
			and remark = #{remark}
		</if>
		<if test="flag != null" >
			and flag = #{flag}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
		<if test="taskInfoId != null" >
			and task_info_id = #{taskInfoId}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_trace_record
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_trace_record
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_trace_record
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsTraceRecordDTO">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_trace_record
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_trace_record
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsTraceRecordDTO">
		insert into clms_trace_record (id, report_no, injured_id, case_times, tracepers_context,
			tracepers_object, phone_number, trace_date, tracepers_code, tracepers_name,
			valid_flag,task_info_id, remark, flag, created_by, sys_ctime,
			updated_by, sys_utime)
		values(#{id}, #{reportNo}, #{injuredId}, #{caseTimes}, #{tracepersContext}, 
			#{tracepersObject}, #{phoneNumber}, #{traceDate}, #{tracepersCode}, #{tracepersName}, 
			#{validFlag},#{taskInfoId}, #{remark}, #{flag}, #{createdBy}, #{sysCtime},
			#{updatedBy}, #{sysUtime})
	</insert>
	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO clms_trace_record (
		REPORT_NO, INJURED_ID, CASE_TIMES, TRACEPERS_CONTEXT,
		TRACEPERS_OBJECT, PHONE_NUMBER, TRACE_DATE, TRACEPERS_CODE, TRACEPERS_NAME,
		VALID_FLAG,TASK_INFO_ID, REMARK, FLAG, CREATED_BY, SYS_CTIME,
		UPDATED_BY, SYS_UTIME
		)
		SELECT
		REPORT_NO,INJURED_ID, #{reopenCaseTimes},TRACEPERS_CONTEXT,
		TRACEPERS_OBJECT, PHONE_NUMBER, TRACE_DATE, TRACEPERS_CODE, TRACEPERS_NAME,
		VALID_FLAG,TASK_INFO_ID, REMARK, FLAG, #{userId}, now(),
		#{userId}, now()
		FROM clms_trace_record
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
		AND VALID_FLAG='Y'
	</insert>
	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsTraceRecordDTO">
		insert into clms_trace_record
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="injuredId != null" >
				injured_id,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="tracepersContext != null" >
				tracepers_context,
			</if>
			<if test="tracepersObject != null" >
				tracepers_object,
			</if>
			<if test="phoneNumber != null" >
				phone_number,
			</if>
			<if test="traceDate != null" >
				trace_date,
			</if>
			<if test="tracepersCode != null" >
				tracepers_code,
			</if>
			<if test="tracepersName != null" >
				tracepers_name,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="taskInfoId != null" >
				task_info_id,
			</if>
			<if test="remark != null" >
				remark,
			</if>
			<if test="flag != null" >
				flag,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="injuredId != null" >
				#{injuredId},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="tracepersContext != null" >
				#{tracepersContext},
			</if>
			<if test="tracepersObject != null" >
				#{tracepersObject},
			</if>
			<if test="phoneNumber != null" >
				#{phoneNumber},
			</if>
			<if test="traceDate != null" >
				#{traceDate},
			</if>
			<if test="tracepersCode != null" >
				#{tracepersCode},
			</if>
			<if test="tracepersName != null" >
				#{tracepersName},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="taskInfoId != null" >
				#{taskInfoId},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsTraceRecordDTO">
		update clms_trace_record
		<set>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="injuredId != null" >
				injured_id=#{injuredId},
			</if>
			<if test="caseTimes != null" >
				case_times=#{caseTimes},
			</if>
			<if test="tracepersContext != null" >
				tracepers_context=#{tracepersContext},
			</if>
			<if test="tracepersObject != null" >
				tracepers_object=#{tracepersObject},
			</if>
			<if test="phoneNumber != null" >
				phone_number=#{phoneNumber},
			</if>
			<if test="traceDate != null" >
				trace_date=#{traceDate},
			</if>
			<if test="tracepersCode != null" >
				tracepers_code=#{tracepersCode},
			</if>
			<if test="tracepersName != null" >
				tracepers_name=#{tracepersName},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="taskInfoId != null" >
				task_info_id=#{taskInfoId},
			</if>
			<if test="remark != null" >
				remark=#{remark},
			</if>
			<if test="flag != null" >
				flag=#{flag},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsTraceRecordDTO">
		update clms_trace_record
		set report_no=#{reportNo},
			injured_id=#{injuredId},
			case_times=#{caseTimes},
			tracepers_context=#{tracepersContext},
			tracepers_object=#{tracepersObject},
			phone_number=#{phoneNumber},
			trace_date=#{traceDate},
			tracepers_code=#{tracepersCode},
			tracepers_name=#{tracepersName},
			valid_flag=#{validFlag},
		    task_info_id=#{taskInfoId},
			remark=#{remark},
			flag=#{flag},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>
	<!-- 根据报案号，赔付次数查询数据 -->
	<select id="selectClmsTraceRecord" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsTraceRecordVO" parameterType="com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo">
		select
		<include refid="Base_Column_List" />
		from clms_trace_record
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
		<if test="flag != null and flag!=''" >
			and flag=#{flag}
		</if>
		    and valid_flag = 'Y'
	</select>

	<!-- 查询跟踪人员并进行人员的拼接 -->
	<!--<select id="selectTracePersons" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsTraceRecordVO" parameterType="String">
		select t.report_no,t.task_info_id,t.flag,t.sys_ctime, GROUP_CONCAT(t.tracepers_name) as tracepers_name from
		(
		select distinct ctr.task_info_id,ctr.report_no,ctr.tracepers_name,ctr.flag,ctr.sys_ctime from clms_trace_record
		ctr
		) t where 1=1
		<if test="reportNo != null and reportNo!=''">
			and t.report_no=#{reportNo}
		</if>
		and t.flag = '1'
		group by t.report_no,t.task_info_id
		order by t.sys_ctime
	</select>-->

	<!-- 查询跟踪人员并进行人员的拼接 -->
	<select id="selectTracePersons" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsTraceRecordVO" parameterType="String">
		SELECT
		ctr.report_no as reportNo,ctr.tracepers_name as tracepersName,
		cti.created_date as createdDate,cti.COMPLETE_TIME as completeTime,
		cti.ID_AHCS_TASK_INFO,
		GROUP_CONCAT(DISTINCT ctr.tracepers_name ORDER BY ctr.tracepers_name SEPARATOR ',') AS tracepers_name
		FROM
		clms_trace_record  ctr
		JOIN
		clms_task_info cti ON  ctr.task_info_id = cti.ID_AHCS_TASK_INFO
		<if test="reportNo != null and reportNo!=''" >
			and ctr.report_no=#{reportNo}
		</if>
		    and ctr.tracepers_name is not null
		GROUP BY
		cti.ID_AHCS_TASK_INFO,   cti.created_date,cti.COMPLETE_TIME
		order by cti.created_date
	</select>

	<!-- 查询跟踪人员并进行人员的拼接 -->
	<select id="selectTaskInfoList" resultType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO" parameterType="String">
		select * from clms_task_info cti where cti.ASSIGNEE_NAME  in(
		select ctr.tracepers_name from clms_trace_record ctr,clms_task_info t where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and  ctr.report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and  ctr.case_times=#{caseTimes}
		</if>
		   and t.ID_AHCS_TASK_INFO = ctr.task_info_id
		)
		limit 1
	</select>

	<delete id="deleteClmsTraceRecord" parameterType="map">
		delete from clms_trace_record
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
	</delete>
</mapper>