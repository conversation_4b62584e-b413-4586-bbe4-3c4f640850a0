<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.dao.mapper.trace.ClmsPersTraceMainMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceMainDTO">
		 <id column="id" property="id"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="case_times" property="caseTimes"/> 
		 <result column="task_id" property="taskId"/> 
		 <result column="policy_no" property="policyNo"/> 
		 <result column="class_code" property="classCode"/> 
		 <result column="risk_code" property="riskCode"/> 
		 <result column="insured_name" property="insuredName"/> 
		 <result column="trace_person_code" property="tracePersonCode"/> 
		 <result column="trace_person" property="tracePerson"/> 
		 <result column="nearly_trace_time" property="nearlyTraceTime"/> 
		 <result column="underwrite_date" property="underwriteDate"/> 
		 <result column="underwrite_flag" property="underwriteFlag"/> 
		 <result column="underwrite_code" property="underwriteCode"/> 
		 <result column="underwrite_name" property="underwriteName"/> 
		 <result column="underwrite_end_date" property="underwriteEndDate"/> 
		 <result column="valid_flag" property="validFlag"/> 
		 <result column="is_survey" property="isSurvey"/> 
		 <result column="survey_info" property="surveyInfo"/> 
		 <result column="launch_survey" property="launchSurvey"/> 
		 <result column="first_submit_time" property="firstSubmitTime"/> 
		 <result column="last_trace_date" property="lastTraceDate"/> 
		 <result column="trace_status" property="traceStatus"/> 
		 <result column="isno_endtrace" property="isnoEndtrace"/> 
		 <result column="trace_advise" property="traceAdvise"/> 
		 <result column="interval_day" property="intervalDay"/> 
		 <result column="remark" property="remark"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="sys_endctime" property="sysEndctime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, report_no, case_times, task_id, policy_no,
		 class_code, risk_code, insured_name, trace_person_code, trace_person,
		 nearly_trace_time, underwrite_date, underwrite_flag, underwrite_code, underwrite_name,
		 underwrite_end_date, valid_flag, is_survey, survey_info, launch_survey,
		 first_submit_time, last_trace_date, trace_status, isno_endtrace, trace_advise,
		 interval_day, remark, created_by, sys_ctime, sys_endctime,
		 updated_by, sys_utime
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="taskId != null" >
			and task_id = #{taskId}
		</if>
		<if test="policyNo != null" >
			and policy_no = #{policyNo}
		</if>
		<if test="classCode != null" >
			and class_code = #{classCode}
		</if>
		<if test="riskCode != null" >
			and risk_code = #{riskCode}
		</if>
		<if test="insuredName != null" >
			and insured_name = #{insuredName}
		</if>
		<if test="tracePersonCode != null" >
			and trace_person_code = #{tracePersonCode}
		</if>
		<if test="tracePerson != null" >
			and trace_person = #{tracePerson}
		</if>
		<if test="nearlyTraceTime != null" >
			and nearly_trace_time = #{nearlyTraceTime}
		</if>
		<if test="underwriteDate != null" >
			and underwrite_date = #{underwriteDate}
		</if>
		<if test="underwriteFlag != null" >
			and underwrite_flag = #{underwriteFlag}
		</if>
		<if test="underwriteCode != null" >
			and underwrite_code = #{underwriteCode}
		</if>
		<if test="underwriteName != null" >
			and underwrite_name = #{underwriteName}
		</if>
		<if test="underwriteEndDate != null" >
			and underwrite_end_date = #{underwriteEndDate}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="isSurvey != null" >
			and is_survey = #{isSurvey}
		</if>
		<if test="surveyInfo != null" >
			and survey_info = #{surveyInfo}
		</if>
		<if test="launchSurvey != null" >
			and launch_survey = #{launchSurvey}
		</if>
		<if test="firstSubmitTime != null" >
			and first_submit_time = #{firstSubmitTime}
		</if>
		<if test="lastTraceDate != null" >
			and last_trace_date = #{lastTraceDate}
		</if>
		<if test="traceStatus != null" >
			and trace_status = #{traceStatus}
		</if>
		<if test="isnoEndtrace != null" >
			and isno_endtrace = #{isnoEndtrace}
		</if>
		<if test="traceAdvise != null" >
			and trace_advise = #{traceAdvise}
		</if>
		<if test="intervalDay != null" >
			and interval_day = #{intervalDay}
		</if>
		<if test="remark != null" >
			and remark = #{remark}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="sysEndctime != null" >
			and sys_endctime = #{sysEndctime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_pers_trace_main
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_trace_main
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_trace_main
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceMainDTO">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_pers_trace_main
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_pers_trace_main
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceMainDTO">
		insert into clms_pers_trace_main (id, report_no, case_times, task_id, policy_no, 
			class_code, risk_code, insured_name, trace_person_code, trace_person, 
			nearly_trace_time, underwrite_date, underwrite_flag, underwrite_code, underwrite_name, 
			underwrite_end_date, valid_flag, is_survey, survey_info, launch_survey, 
			first_submit_time, last_trace_date, trace_status, isno_endtrace, trace_advise, 
			interval_day, remark, created_by, sys_ctime, sys_endctime, 
			updated_by, sys_utime)
		values(#{id}, #{reportNo}, #{caseTimes}, #{taskId}, #{policyNo}, 
			#{classCode}, #{riskCode}, #{insuredName}, #{tracePersonCode}, #{tracePerson}, 
			#{nearlyTraceTime}, #{underwriteDate}, #{underwriteFlag}, #{underwriteCode}, #{underwriteName}, 
			#{underwriteEndDate}, #{validFlag}, #{isSurvey}, #{surveyInfo}, #{launchSurvey}, 
			#{firstSubmitTime}, #{lastTraceDate}, #{traceStatus}, #{isnoEndtrace}, #{traceAdvise}, 
			#{intervalDay}, #{remark}, #{createdBy}, #{sysCtime}, #{sysEndctime}, 
			#{updatedBy}, #{sysUtime})
	</insert>
	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO clms_pers_trace_main (
		REPORT_NO, CASE_TIMES, TASK_ID, POLICY_NO,
		CLASS_CODE, RISK_CODE, INSURED_NAME, TRACE_PERSON_CODE, TRACE_PERSON,
		NEARLY_TRACE_TIME, UNDERWRITE_DATE, UNDERWRITE_FLAG, UNDERWRITE_CODE, UNDERWRITE_NAME,
		UNDERWRITE_END_DATE, VALID_FLAG, IS_SURVEY, SURVEY_INFO, LAUNCH_SURVEY,
		FIRST_SUBMIT_TIME, LAST_TRACE_DATE, TRACE_STATUS, ISNO_ENDTRACE, TRACE_ADVISE,
		INTERVAL_DAY, REMARK, CREATED_BY, SYS_CTIME, SYS_ENDCTIME,
		UPDATED_BY, SYS_UTIME
		)
		SELECT
		REPORT_NO, #{reopenCaseTimes},  TASK_ID, POLICY_NO,
		CLASS_CODE, RISK_CODE, INSURED_NAME, TRACE_PERSON_CODE, TRACE_PERSON,
		NEARLY_TRACE_TIME, UNDERWRITE_DATE, UNDERWRITE_FLAG, UNDERWRITE_CODE, UNDERWRITE_NAME,
		UNDERWRITE_END_DATE, VALID_FLAG, IS_SURVEY, SURVEY_INFO, LAUNCH_SURVEY,
		FIRST_SUBMIT_TIME, LAST_TRACE_DATE, TRACE_STATUS, ISNO_ENDTRACE, TRACE_ADVISE,
		INTERVAL_DAY, REMARK, #{userId}, now(), SYS_ENDCTIME,
		#{userId}, now()
		FROM clms_pers_trace_main
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
		AND VALID_FLAG='Y'
	</insert>
	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceMainDTO">
		insert into clms_pers_trace_main
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="taskId != null" >
				task_id,
			</if>
			<if test="policyNo != null" >
				policy_no,
			</if>
			<if test="classCode != null" >
				class_code,
			</if>
			<if test="riskCode != null" >
				risk_code,
			</if>
			<if test="insuredName != null" >
				insured_name,
			</if>
			<if test="tracePersonCode != null" >
				trace_person_code,
			</if>
			<if test="tracePerson != null" >
				trace_person,
			</if>
			<if test="nearlyTraceTime != null" >
				nearly_trace_time,
			</if>
			<if test="underwriteDate != null" >
				underwrite_date,
			</if>
			<if test="underwriteFlag != null" >
				underwrite_flag,
			</if>
			<if test="underwriteCode != null" >
				underwrite_code,
			</if>
			<if test="underwriteName != null" >
				underwrite_name,
			</if>
			<if test="underwriteEndDate != null" >
				underwrite_end_date,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="isSurvey != null" >
				is_survey,
			</if>
			<if test="surveyInfo != null" >
				survey_info,
			</if>
			<if test="launchSurvey != null" >
				launch_survey,
			</if>
			<if test="firstSubmitTime != null" >
				first_submit_time,
			</if>
			<if test="lastTraceDate != null" >
				last_trace_date,
			</if>
			<if test="traceStatus != null" >
				trace_status,
			</if>
			<if test="isnoEndtrace != null" >
				isno_endtrace,
			</if>
			<if test="traceAdvise != null" >
				trace_advise,
			</if>
			<if test="intervalDay != null" >
				interval_day,
			</if>
			<if test="remark != null" >
				remark,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="sysEndctime != null" >
				sys_endctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="taskId != null" >
				#{taskId},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="classCode != null" >
				#{classCode},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="insuredName != null" >
				#{insuredName},
			</if>
			<if test="tracePersonCode != null" >
				#{tracePersonCode},
			</if>
			<if test="tracePerson != null" >
				#{tracePerson},
			</if>
			<if test="nearlyTraceTime != null" >
				#{nearlyTraceTime},
			</if>
			<if test="underwriteDate != null" >
				#{underwriteDate},
			</if>
			<if test="underwriteFlag != null" >
				#{underwriteFlag},
			</if>
			<if test="underwriteCode != null" >
				#{underwriteCode},
			</if>
			<if test="underwriteName != null" >
				#{underwriteName},
			</if>
			<if test="underwriteEndDate != null" >
				#{underwriteEndDate},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="isSurvey != null" >
				#{isSurvey},
			</if>
			<if test="surveyInfo != null" >
				#{surveyInfo},
			</if>
			<if test="launchSurvey != null" >
				#{launchSurvey},
			</if>
			<if test="firstSubmitTime != null" >
				#{firstSubmitTime},
			</if>
			<if test="lastTraceDate != null" >
				#{lastTraceDate},
			</if>
			<if test="traceStatus != null" >
				#{traceStatus},
			</if>
			<if test="isnoEndtrace != null" >
				#{isnoEndtrace},
			</if>
			<if test="traceAdvise != null" >
				#{traceAdvise},
			</if>
			<if test="intervalDay != null" >
				#{intervalDay},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="sysEndctime != null" >
				#{sysEndctime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceMainDTO">
		update clms_pers_trace_main
		<set>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="caseTimes != null" >
				case_times=#{caseTimes},
			</if>
			<if test="taskId != null" >
				task_id=#{taskId},
			</if>
			<if test="policyNo != null" >
				policy_no=#{policyNo},
			</if>
			<if test="classCode != null" >
				class_code=#{classCode},
			</if>
			<if test="riskCode != null" >
				risk_code=#{riskCode},
			</if>
			<if test="insuredName != null" >
				insured_name=#{insuredName},
			</if>
			<if test="tracePersonCode != null" >
				trace_person_code=#{tracePersonCode},
			</if>
			<if test="tracePerson != null" >
				trace_person=#{tracePerson},
			</if>
			<if test="nearlyTraceTime != null" >
				nearly_trace_time=#{nearlyTraceTime},
			</if>
			<if test="underwriteDate != null" >
				underwrite_date=#{underwriteDate},
			</if>
			<if test="underwriteFlag != null" >
				underwrite_flag=#{underwriteFlag},
			</if>
			<if test="underwriteCode != null" >
				underwrite_code=#{underwriteCode},
			</if>
			<if test="underwriteName != null" >
				underwrite_name=#{underwriteName},
			</if>
			<if test="underwriteEndDate != null" >
				underwrite_end_date=#{underwriteEndDate},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="isSurvey != null" >
				is_survey=#{isSurvey},
			</if>
			<if test="surveyInfo != null" >
				survey_info=#{surveyInfo},
			</if>
			<if test="launchSurvey != null" >
				launch_survey=#{launchSurvey},
			</if>
			<if test="firstSubmitTime != null" >
				first_submit_time=#{firstSubmitTime},
			</if>
			<if test="lastTraceDate != null" >
				last_trace_date=#{lastTraceDate},
			</if>
			<if test="traceStatus != null" >
				trace_status=#{traceStatus},
			</if>
			<if test="isnoEndtrace != null" >
				isno_endtrace=#{isnoEndtrace},
			</if>
			<if test="traceAdvise != null" >
				trace_advise=#{traceAdvise},
			</if>
			<if test="intervalDay != null" >
				interval_day=#{intervalDay},
			</if>
			<if test="remark != null" >
				remark=#{remark},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="sysEndctime != null" >
				sys_endctime=#{sysEndctime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceMainDTO">
		update clms_pers_trace_main
		set report_no=#{reportNo},
			case_times=#{caseTimes},
			task_id=#{taskId},
			policy_no=#{policyNo},
			class_code=#{classCode},
			risk_code=#{riskCode},
			insured_name=#{insuredName},
			trace_person_code=#{tracePersonCode},
			trace_person=#{tracePerson},
			nearly_trace_time=#{nearlyTraceTime},
			underwrite_date=#{underwriteDate},
			underwrite_flag=#{underwriteFlag},
			underwrite_code=#{underwriteCode},
			underwrite_name=#{underwriteName},
			underwrite_end_date=#{underwriteEndDate},
			valid_flag=#{validFlag},
			is_survey=#{isSurvey},
			survey_info=#{surveyInfo},
			launch_survey=#{launchSurvey},
			first_submit_time=#{firstSubmitTime},
			last_trace_date=#{lastTraceDate},
			trace_status=#{traceStatus},
			isno_endtrace=#{isnoEndtrace},
			trace_advise=#{traceAdvise},
			interval_day=#{intervalDay},
			remark=#{remark},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			sys_endctime=#{sysEndctime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>
	<!-- 根据报案号，赔付次数查询数据 -->
	<select id="selectPersonTranceMain" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsPersTraceMainVO" parameterType="com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo">
		select
		<include refid="Base_Column_List" />
		from clms_pers_trace_main
		where 1=1
		<if test="taskId != null and taskId!=''" >
			and TASK_ID = #{taskId}
		</if>
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
		<if test="underwriteFlag != null and underwriteFlag!=''" >
			and underwrite_flag=#{underwriteFlag}
		</if>
	    	and valid_flag = 'Y'
	</select>

	<delete id="deletePersonTranceMain" parameterType="map">
		delete from clms_pers_trace_main
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
	</delete>

	<select id="selectNoticeTask" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsPersTraceMainVO">
		select
		distinct t.REPORT_NO as reportNo,t.ASSIGNER as assigneeName,t.case_times as caseTimes,
		m.interval_day as intervalDay,m.last_trace_date as lastTraceDate
		From clms_task_info t,clms_pers_trace_main m
		where t.REPORT_NO = m.report_no and  t.CASE_TIMES = m.case_times  and m.underwrite_flag ='0' and t.STATUS !='1'
		and t.TASK_DEFINITION_BPM_KEY ='OC_HUMAN_INJURY_TRACKING' and m.last_trace_date!=0
		and CURDATE() =  DATE_ADD(DATE(m.last_trace_date), INTERVAL m.interval_day DAY);
	</select>

	<select id="selectRiskGroupName" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsPersTraceMainVO" parameterType="String">
		SELECT  a.SCHEME_NAME as riskGroupName,pi.ID_AHCS_POLICY_INFO as policyId
		FROM CLMS_INSURED_PERSON a,clms_POLICY_INFO pi
		where a.id_ahcs_policy_info = pi.id_ahcs_policy_info
		<if test="reportNo != null and reportNo!=''" >
			and pi.report_no=#{reportNo}
		</if>
		LIMIT 1;
	</select>


</mapper>