<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.dao.mapper.trace.ClmsPersInjuredPartMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredPartDTO">
		 <id column="id" property="id"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="case_times" property="caseTimes"/> 
		 <result column="injured_id" property="injuredId"/> 
		 <result column="injured_part_code" property="injuredPartCode"/> 
		 <result column="injured_partname" property="injuredPartname"/> 
		 <result column="injured_diagnosis_code" property="injuredDiagnosisCode"/> 
		 <result column="injured_diagnosis_name" property="injuredDiagnosisName"/> 
		 <result column="treatment" property="treatment"/> 
		 <result column="treat_way" property="treatWay"/> 
		 <result column="treat_route" property="treatRoute"/> 
		 <result column="disease_diagnosis_code" property="diseaseDiagnosisCode"/> 
		 <result column="surgical_name_code" property="surgicalNameCode"/>
		 <result column="surgical_name" property="surgicalName"/>
		 <result column="confirmed_date" property="confirmedDate"/>
		 <result column="specific_diagnosis" property="specificDiagnosis"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/>
		 <result column="valid_flag" property="validFlag"/>
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, report_no, case_times, injured_id, injured_part_code,
		 injured_partname, injured_diagnosis_code, injured_diagnosis_name, treatment, treat_way,
		 treat_route, disease_diagnosis_code, surgical_name_code,surgical_name, confirmed_date, specific_diagnosis,
		 created_by, sys_ctime, updated_by, sys_utime,valid_flag
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="injuredId != null" >
			and injured_id = #{injuredId}
		</if>
		<if test="injuredPartCode != null" >
			and injured_part_code = #{injuredPartCode}
		</if>
		<if test="injuredPartname != null" >
			and injured_partname = #{injuredPartname}
		</if>
		<if test="injuredDiagnosisCode != null" >
			and injured_diagnosis_code = #{injuredDiagnosisCode}
		</if>
		<if test="injuredDiagnosisName != null" >
			and injured_diagnosis_name = #{injuredDiagnosisName}
		</if>
		<if test="treatment != null" >
			and treatment = #{treatment}
		</if>
		<if test="treatWay != null" >
			and treat_way = #{treatWay}
		</if>
		<if test="treatRoute != null" >
			and treat_route = #{treatRoute}
		</if>
		<if test="diseaseDiagnosisCode != null" >
			and disease_diagnosis_code = #{diseaseDiagnosisCode}
		</if>
		<if test="surgicalNameCode != null" >
			and surgical_name_code = #{surgicalNameCode}
		</if>
		<if test="surgicalName != null" >
			and surgical_name = #{surgicalName}
		</if>
		<if test="confirmedDate != null" >
			and confirmed_date = #{confirmedDate}
		</if>
		<if test="specificDiagnosis != null" >
			and specific_diagnosis = #{specificDiagnosis}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_pers_injured_part
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_injured_part
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_injured_part
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredPartDTO">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_pers_injured_part
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_pers_injured_part
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredPartDTO">
		insert into clms_pers_injured_part (id, report_no, case_times, injured_id, injured_part_code,
			injured_partname, injured_diagnosis_code, injured_diagnosis_name, treatment, treat_way,
			treat_route, disease_diagnosis_code, surgical_name_code, surgical_name,confirmed_date, specific_diagnosis,
			created_by, sys_ctime, updated_by, sys_utime,valid_flag)
		values(#{id}, #{reportNo}, #{caseTimes}, #{injuredId}, #{injuredPartCode}, 
			#{injuredPartname}, #{injuredDiagnosisCode}, #{injuredDiagnosisName}, #{treatment}, #{treatWay}, 
			#{treatRoute}, #{diseaseDiagnosisCode}, #{surgicalNameCode},#{surgicalName}, #{confirmedDate}, #{specificDiagnosis},
			#{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}, #{validFlag})
	</insert>
	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO clms_pers_injured_part (
		REPORT_NO, CASE_TIMES, INJURED_ID, INJURED_PART_CODE,
		INJURED_PARTNAME, INJURED_DIAGNOSIS_CODE, INJURED_DIAGNOSIS_NAME, TREATMENT, TREAT_WAY,
		TREAT_ROUTE, DISEASE_DIAGNOSIS_CODE, SURGICAL_NAME_CODE,SURGICAL_NAME, CONFIRMED_DATE, SPECIFIC_DIAGNOSIS,
		CREATED_BY, SYS_CTIME, UPDATED_BY, SYS_UTIME,VALID_FLAG
		)
		SELECT
		REPORT_NO, #{reopenCaseTimes}, INJURED_ID, INJURED_PART_CODE,
		INJURED_PARTNAME, INJURED_DIAGNOSIS_CODE, INJURED_DIAGNOSIS_NAME, TREATMENT, TREAT_WAY,
		TREAT_ROUTE, DISEASE_DIAGNOSIS_CODE, SURGICAL_NAME_CODE, SURGICAL_NAME,CONFIRMED_DATE, SPECIFIC_DIAGNOSIS,
		#{userId}, now(),  #{userId}, now(),VALID_FLAG
		FROM clms_pers_injured_part
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
		AND VALID_FLAG='Y'
	</insert>
	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredPartDTO">
		insert into clms_pers_injured_part
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="injuredId != null" >
				injured_id,
			</if>
			<if test="injuredPartCode != null" >
				injured_part_code,
			</if>
			<if test="injuredPartname != null" >
				injured_partname,
			</if>
			<if test="injuredDiagnosisCode != null" >
				injured_diagnosis_code,
			</if>
			<if test="injuredDiagnosisName != null" >
				injured_diagnosis_name,
			</if>
			<if test="treatment != null" >
				treatment,
			</if>
			<if test="treatWay != null" >
				treat_way,
			</if>
			<if test="treatRoute != null" >
				treat_route,
			</if>
			<if test="diseaseDiagnosisCode != null" >
				disease_diagnosis_code,
			</if>
			<if test="surgicalNameCode != null" >
				surgical_name_code,
			</if>
			<if test="surgicalName != null" >
				surgical_name,
			</if>
			<if test="confirmedDate != null" >
				confirmed_date,
			</if>
			<if test="specificDiagnosis != null" >
				specific_diagnosis,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="injuredId != null" >
				#{injuredId},
			</if>
			<if test="injuredPartCode != null" >
				#{injuredPartCode},
			</if>
			<if test="injuredPartname != null" >
				#{injuredPartname},
			</if>
			<if test="injuredDiagnosisCode != null" >
				#{injuredDiagnosisCode},
			</if>
			<if test="injuredDiagnosisName != null" >
				#{injuredDiagnosisName},
			</if>
			<if test="treatment != null" >
				#{treatment},
			</if>
			<if test="treatWay != null" >
				#{treatWay},
			</if>
			<if test="treatRoute != null" >
				#{treatRoute},
			</if>
			<if test="diseaseDiagnosisCode != null" >
				#{diseaseDiagnosisCode},
			</if>
			<if test="surgicalNameCode != null" >
				#{surgicalNameCode},
			</if>
			<if test="surgicalName != null" >
				#{surgicalName},
			</if>
			<if test="confirmedDate != null" >
				#{confirmedDate},
			</if>
			<if test="specificDiagnosis != null" >
				#{specificDiagnosis},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredPartDTO">
		update clms_pers_injured_part
		<set>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="caseTimes != null" >
				case_times=#{caseTimes},
			</if>
			<if test="injuredId != null" >
				injured_id=#{injuredId},
			</if>
			<if test="injuredPartCode != null" >
				injured_part_code=#{injuredPartCode},
			</if>
			<if test="injuredPartname != null" >
				injured_partname=#{injuredPartname},
			</if>
			<if test="injuredDiagnosisCode != null" >
				injured_diagnosis_code=#{injuredDiagnosisCode},
			</if>
			<if test="injuredDiagnosisName != null" >
				injured_diagnosis_name=#{injuredDiagnosisName},
			</if>
			<if test="treatment != null" >
				treatment=#{treatment},
			</if>
			<if test="treatWay != null" >
				treat_way=#{treatWay},
			</if>
			<if test="treatRoute != null" >
				treat_route=#{treatRoute},
			</if>
			<if test="diseaseDiagnosisCode != null" >
				disease_diagnosis_code=#{diseaseDiagnosisCode},
			</if>
			<if test="surgicalNameCode != null" >
				surgical_name_code=#{surgicalNameCode},
			</if>
			<if test="surgicalName != null" >
				surgical_name=#{surgicalName},
			</if>
			<if test="confirmedDate != null" >
				confirmed_date=#{confirmedDate},
			</if>
			<if test="specificDiagnosis != null" >
				specific_diagnosis=#{specificDiagnosis},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredPartDTO">
		update clms_pers_injured_part
		set report_no=#{reportNo},
			case_times=#{caseTimes},
			injured_id=#{injuredId},
			injured_part_code=#{injuredPartCode},
			injured_partname=#{injuredPartname},
			injured_diagnosis_code=#{injuredDiagnosisCode},
			injured_diagnosis_name=#{injuredDiagnosisName},
			treatment=#{treatment},
			treat_way=#{treatWay},
			treat_route=#{treatRoute},
			disease_diagnosis_code=#{diseaseDiagnosisCode},
			surgical_name_code=#{surgicalNameCode},
			surgical_name=#{surgicalName},
			confirmed_date=#{confirmedDate},
			specific_diagnosis=#{specificDiagnosis},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime},
		    valid_flag=#{validFlag}
		where id = #{id}
	</update>

	<!-- 根据报案号，赔付次数查询数据 -->
	<select id="selectClmsPersInjuredPart" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsPersInjuredPartVO" parameterType="com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo">
		select
		<include refid="Base_Column_List" />
		from clms_pers_injured_part
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
		    and valid_flag = 'Y'
	</select>
	<!-- 按主键List删除多条记录 -->
	<delete id="deleteClmsPersInjuredPart" parameterType="map">
		delete from clms_pers_injured_part
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
	</delete>

</mapper>