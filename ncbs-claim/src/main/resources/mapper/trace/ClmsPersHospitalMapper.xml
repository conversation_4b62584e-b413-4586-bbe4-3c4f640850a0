<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.dao.mapper.trace.ClmsPersHospitalMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.trace.ClmsPersHospitalDTO">
		 <id column="id" property="id"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="case_times" property="caseTimes"/> 
		 <result column="injured_id" property="injuredId"/> 
		 <result column="person_name" property="personName"/> 
		 <result column="risk_code" property="riskCode"/> 
		 <result column="inhospital_days" property="inhospitalDays"/> 
		 <result column="hospital_bed" property="hospitalBed"/> 
		 <result column="in_hospital_date" property="inHospitalDate"/> 
		 <result column="out_hospital_date" property="outHospitalDate"/> 
		 <result column="hospital_province" property="hospitalProvince"/> 
		 <result column="hospital_city" property="hospitalCity"/> 
		 <result column="hospital_code" property="hospitalCode"/> 
		 <result column="hospital_name" property="hospitalName"/> 
		 <result column="hospital_grade" property="hospitalGrade"/> 
		 <result column="agency_type" property="agencyType"/> 
		 <result column="agency_attributes" property="agencyAttributes"/> 
		 <result column="is_relative_hospital" property="isRelativeHospital"/> 
		 <result column="main_physician" property="mainPhysician"/> 
		 <result column="main_physician_phone" property="mainPhysicianPhone"/> 
		 <result column="admin_office" property="adminOffice"/> 
		 <result column="is_transfers" property="isTransfers"/> 
		 <result column="major_diagnosis" property="majorDiagnosis"/> 
		 <result column="hospital_course" property="hospitalCourse"/> 
		 <result column="remark" property="remark"/> 
		 <result column="valid_flag" property="validFlag"/> 
		 <result column="flag" property="flag"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, report_no, case_times, injured_id, person_name,
		 risk_code, inhospital_days, hospital_bed, in_hospital_date, out_hospital_date,
		 hospital_province, hospital_city, hospital_code, hospital_name, hospital_grade,
		 agency_type, agency_attributes, is_relative_hospital, main_physician, main_physician_phone,
		 admin_office, is_transfers, major_diagnosis, hospital_course, remark,
		 valid_flag, flag, created_by, sys_ctime, updated_by,
		 sys_utime
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="injuredId != null" >
			and injured_id = #{injuredId}
		</if>
		<if test="personName != null" >
			and person_name = #{personName}
		</if>
		<if test="riskCode != null" >
			and risk_code = #{riskCode}
		</if>
		<if test="inhospitalDays != null" >
			and inhospital_days = #{inhospitalDays}
		</if>
		<if test="hospitalBed != null" >
			and hospital_bed = #{hospitalBed}
		</if>
		<if test="inHospitalDate != null" >
			and in_hospital_date = #{inHospitalDate}
		</if>
		<if test="outHospitalDate != null" >
			and out_hospital_date = #{outHospitalDate}
		</if>
		<if test="hospitalProvince != null" >
			and hospital_province = #{hospitalProvince}
		</if>
		<if test="hospitalCity != null" >
			and hospital_city = #{hospitalCity}
		</if>
		<if test="hospitalCode != null" >
			and hospital_code = #{hospitalCode}
		</if>
		<if test="hospitalName != null" >
			and hospital_name = #{hospitalName}
		</if>
		<if test="hospitalGrade != null" >
			and hospital_grade = #{hospitalGrade}
		</if>
		<if test="agencyType != null" >
			and agency_type = #{agencyType}
		</if>
		<if test="agencyAttributes != null" >
			and agency_attributes = #{agencyAttributes}
		</if>
		<if test="isRelativeHospital != null" >
			and is_relative_hospital = #{isRelativeHospital}
		</if>
		<if test="mainPhysician != null" >
			and main_physician = #{mainPhysician}
		</if>
		<if test="mainPhysicianPhone != null" >
			and main_physician_phone = #{mainPhysicianPhone}
		</if>
		<if test="adminOffice != null" >
			and admin_office = #{adminOffice}
		</if>
		<if test="isTransfers != null" >
			and is_transfers = #{isTransfers}
		</if>
		<if test="majorDiagnosis != null" >
			and major_diagnosis = #{majorDiagnosis}
		</if>
		<if test="hospitalCourse != null" >
			and hospital_course = #{hospitalCourse}
		</if>
		<if test="remark != null" >
			and remark = #{remark}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="flag != null" >
			and flag = #{flag}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_pers_hospital
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_hospital
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_hospital
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersHospitalDTO">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_pers_hospital
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_pers_hospital
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersHospitalDTO">
		insert into clms_pers_hospital (id, report_no, case_times, injured_id, person_name, 
			risk_code, inhospital_days, hospital_bed, in_hospital_date, out_hospital_date, 
			hospital_province, hospital_city, hospital_code, hospital_name, hospital_grade, 
			agency_type, agency_attributes, is_relative_hospital, main_physician, main_physician_phone, 
			admin_office, is_transfers, major_diagnosis, hospital_course, remark, 
			valid_flag, flag, created_by, sys_ctime, updated_by, 
			sys_utime)
		values(#{id}, #{reportNo}, #{caseTimes}, #{injuredId}, #{personName}, 
			#{riskCode}, #{inhospitalDays}, #{hospitalBed}, #{inHospitalDate}, #{outHospitalDate}, 
			#{hospitalProvince}, #{hospitalCity}, #{hospitalCode}, #{hospitalName}, #{hospitalGrade}, 
			#{agencyType}, #{agencyAttributes}, #{isRelativeHospital}, #{mainPhysician}, #{mainPhysicianPhone}, 
			#{adminOffice}, #{isTransfers}, #{majorDiagnosis}, #{hospitalCourse}, #{remark}, 
			#{validFlag}, #{flag}, #{createdBy}, #{sysCtime}, #{updatedBy}, 
			#{sysUtime})
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersHospitalDTO">
		insert into clms_pers_hospital
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="injuredId != null" >
				injured_id,
			</if>
			<if test="personName != null" >
				person_name,
			</if>
			<if test="riskCode != null" >
				risk_code,
			</if>
			<if test="inhospitalDays != null" >
				inhospital_days,
			</if>
			<if test="hospitalBed != null" >
				hospital_bed,
			</if>
			<if test="inHospitalDate != null" >
				in_hospital_date,
			</if>
			<if test="outHospitalDate != null" >
				out_hospital_date,
			</if>
			<if test="hospitalProvince != null" >
				hospital_province,
			</if>
			<if test="hospitalCity != null" >
				hospital_city,
			</if>
			<if test="hospitalCode != null" >
				hospital_code,
			</if>
			<if test="hospitalName != null" >
				hospital_name,
			</if>
			<if test="hospitalGrade != null" >
				hospital_grade,
			</if>
			<if test="agencyType != null" >
				agency_type,
			</if>
			<if test="agencyAttributes != null" >
				agency_attributes,
			</if>
			<if test="isRelativeHospital != null" >
				is_relative_hospital,
			</if>
			<if test="mainPhysician != null" >
				main_physician,
			</if>
			<if test="mainPhysicianPhone != null" >
				main_physician_phone,
			</if>
			<if test="adminOffice != null" >
				admin_office,
			</if>
			<if test="isTransfers != null" >
				is_transfers,
			</if>
			<if test="majorDiagnosis != null" >
				major_diagnosis,
			</if>
			<if test="hospitalCourse != null" >
				hospital_course,
			</if>
			<if test="remark != null" >
				remark,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="flag != null" >
				flag,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="injuredId != null" >
				#{injuredId},
			</if>
			<if test="personName != null" >
				#{personName},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="inhospitalDays != null" >
				#{inhospitalDays},
			</if>
			<if test="hospitalBed != null" >
				#{hospitalBed},
			</if>
			<if test="inHospitalDate != null" >
				#{inHospitalDate},
			</if>
			<if test="outHospitalDate != null" >
				#{outHospitalDate},
			</if>
			<if test="hospitalProvince != null" >
				#{hospitalProvince},
			</if>
			<if test="hospitalCity != null" >
				#{hospitalCity},
			</if>
			<if test="hospitalCode != null" >
				#{hospitalCode},
			</if>
			<if test="hospitalName != null" >
				#{hospitalName},
			</if>
			<if test="hospitalGrade != null" >
				#{hospitalGrade},
			</if>
			<if test="agencyType != null" >
				#{agencyType},
			</if>
			<if test="agencyAttributes != null" >
				#{agencyAttributes},
			</if>
			<if test="isRelativeHospital != null" >
				#{isRelativeHospital},
			</if>
			<if test="mainPhysician != null" >
				#{mainPhysician},
			</if>
			<if test="mainPhysicianPhone != null" >
				#{mainPhysicianPhone},
			</if>
			<if test="adminOffice != null" >
				#{adminOffice},
			</if>
			<if test="isTransfers != null" >
				#{isTransfers},
			</if>
			<if test="majorDiagnosis != null" >
				#{majorDiagnosis},
			</if>
			<if test="hospitalCourse != null" >
				#{hospitalCourse},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO clms_pers_hospital (
		REPORT_NO, CASE_TIMES, INJURED_ID, PERSON_NAME,
		RISK_CODE, INHOSPITAL_DAYS, HOSPITAL_BED, IN_HOSPITAL_DATE, OUT_HOSPITAL_DATE,
		HOSPITAL_PROVINCE, HOSPITAL_CITY, HOSPITAL_CODE, HOSPITAL_NAME, HOSPITAL_GRADE,
		AGENCY_TYPE, AGENCY_ATTRIBUTES, IS_RELATIVE_HOSPITAL, MAIN_PHYSICIAN, MAIN_PHYSICIAN_PHONE,
		ADMIN_OFFICE, IS_TRANSFERS, MAJOR_DIAGNOSIS, HOSPITAL_COURSE, REMARK,
		VALID_FLAG, FLAG, CREATED_BY, SYS_CTIME, UPDATED_BY,
		SYS_UTIME
		)
		SELECT
		REPORT_NO, #{reopenCaseTimes}, INJURED_ID, PERSON_NAME,
		RISK_CODE, INHOSPITAL_DAYS, HOSPITAL_BED, IN_HOSPITAL_DATE, OUT_HOSPITAL_DATE,
		HOSPITAL_PROVINCE, HOSPITAL_CITY, HOSPITAL_CODE, HOSPITAL_NAME, HOSPITAL_GRADE,
		AGENCY_TYPE, AGENCY_ATTRIBUTES, IS_RELATIVE_HOSPITAL, MAIN_PHYSICIAN, MAIN_PHYSICIAN_PHONE,
		ADMIN_OFFICE, IS_TRANSFERS, MAJOR_DIAGNOSIS, HOSPITAL_COURSE, REMARK,
		VALID_FLAG, FLAG, #{userId}, now(), #{userId},
		now()
		FROM clms_pers_hospital
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
		AND VALID_FLAG='Y'
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersHospitalDTO">
		update clms_pers_hospital
		<set>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="caseTimes != null" >
				case_times=#{caseTimes},
			</if>
			<if test="injuredId != null" >
				injured_id=#{injuredId},
			</if>
			<if test="personName != null" >
				person_name=#{personName},
			</if>
			<if test="riskCode != null" >
				risk_code=#{riskCode},
			</if>
			<if test="inhospitalDays != null" >
				inhospital_days=#{inhospitalDays},
			</if>
			<if test="hospitalBed != null" >
				hospital_bed=#{hospitalBed},
			</if>
			<if test="inHospitalDate != null" >
				in_hospital_date=#{inHospitalDate},
			</if>
			<if test="outHospitalDate != null" >
				out_hospital_date=#{outHospitalDate},
			</if>
			<if test="hospitalProvince != null" >
				hospital_province=#{hospitalProvince},
			</if>
			<if test="hospitalCity != null" >
				hospital_city=#{hospitalCity},
			</if>
			<if test="hospitalCode != null" >
				hospital_code=#{hospitalCode},
			</if>
			<if test="hospitalName != null" >
				hospital_name=#{hospitalName},
			</if>
			<if test="hospitalGrade != null" >
				hospital_grade=#{hospitalGrade},
			</if>
			<if test="agencyType != null" >
				agency_type=#{agencyType},
			</if>
			<if test="agencyAttributes != null" >
				agency_attributes=#{agencyAttributes},
			</if>
			<if test="isRelativeHospital != null" >
				is_relative_hospital=#{isRelativeHospital},
			</if>
			<if test="mainPhysician != null" >
				main_physician=#{mainPhysician},
			</if>
			<if test="mainPhysicianPhone != null" >
				main_physician_phone=#{mainPhysicianPhone},
			</if>
			<if test="adminOffice != null" >
				admin_office=#{adminOffice},
			</if>
			<if test="isTransfers != null" >
				is_transfers=#{isTransfers},
			</if>
			<if test="majorDiagnosis != null" >
				major_diagnosis=#{majorDiagnosis},
			</if>
			<if test="hospitalCourse != null" >
				hospital_course=#{hospitalCourse},
			</if>
			<if test="remark != null" >
				remark=#{remark},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="flag != null" >
				flag=#{flag},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersHospitalDTO">
		update clms_pers_hospital
		set report_no=#{reportNo},
			case_times=#{caseTimes},
			injured_id=#{injuredId},
			person_name=#{personName},
			risk_code=#{riskCode},
			inhospital_days=#{inhospitalDays},
			hospital_bed=#{hospitalBed},
			in_hospital_date=#{inHospitalDate},
			out_hospital_date=#{outHospitalDate},
			hospital_province=#{hospitalProvince},
			hospital_city=#{hospitalCity},
			hospital_code=#{hospitalCode},
			hospital_name=#{hospitalName},
			hospital_grade=#{hospitalGrade},
			agency_type=#{agencyType},
			agency_attributes=#{agencyAttributes},
			is_relative_hospital=#{isRelativeHospital},
			main_physician=#{mainPhysician},
			main_physician_phone=#{mainPhysicianPhone},
			admin_office=#{adminOffice},
			is_transfers=#{isTransfers},
			major_diagnosis=#{majorDiagnosis},
			hospital_course=#{hospitalCourse},
			remark=#{remark},
			valid_flag=#{validFlag},
			flag=#{flag},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>

	<!-- 根据报案号，赔付次数查询数据 -->
	<select id="selectClmsPersHospital" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsPersHospitalVO" parameterType="com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo">
		select
		<include refid="Base_Column_List" />
		from clms_pers_hospital
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
		    and valid_flag = 'Y'
	</select>
	<select id="selectClmsPersHospitals" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsPersHospitalVO" parameterType="map">
		select
		<include refid="Base_Column_List" />
		from clms_pers_hospital
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
		and valid_flag = 'Y'
	</select>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteclmsPersHospital" parameterType="map">
		delete from clms_pers_hospital
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
	</delete>

</mapper>