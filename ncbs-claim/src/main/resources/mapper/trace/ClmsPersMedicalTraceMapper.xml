<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.dao.mapper.trace.ClmsPersMedicalTraceMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.trace.ClmsPersMedicalTraceDTO">
		 <id column="id" property="id"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="case_times" property="caseTimes"/> 
		 <result column="injured_id" property="injuredId"/> 
		 <result column="trace_date" property="traceDate"/> 
		 <result column="currency" property="currency"/> 
		 <result column="pay_way" property="payWay"/> 
		 <result column="incurred_fee" property="incurredFee"/> 
		 <result column="required_fee" property="requiredFee"/> 
		 <result column="treament_content" property="treamentContent"/> 
		 <result column="tracepers_code" property="tracepersCode"/> 
		 <result column="tracepers_name" property="tracepersName"/> 
		 <result column="tracepers_context" property="tracepersContext"/> 
		 <result column="tracepers_object" property="tracepersObject"/> 
		 <result column="phone_number" property="phoneNumber"/> 
		 <result column="pay_person_type" property="payPersonType"/> 
		 <result column="pay_person_name" property="payPersonName"/> 
		 <result column="valid_flag" property="validFlag"/> 
		 <result column="remark" property="remark"/> 
		 <result column="flag" property="flag"/> 
		 <result column="body_status" property="bodyStatus"/> 
		 <result column="unend_reason" property="unendReason"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, report_no, case_times, injured_id, trace_date,
		 currency, pay_way, incurred_fee, required_fee, treament_content,
		 tracepers_code, tracepers_name, tracepers_context, tracepers_object, phone_number,
		 pay_person_type, pay_person_name, valid_flag, remark, flag,
		 body_status, unend_reason, created_by, sys_ctime, updated_by,
		 sys_utime
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="injuredId != null" >
			and injured_id = #{injuredId}
		</if>
		<if test="traceDate != null" >
			and trace_date = #{traceDate}
		</if>
		<if test="currency != null" >
			and currency = #{currency}
		</if>
		<if test="payWay != null" >
			and pay_way = #{payWay}
		</if>
		<if test="incurredFee != null" >
			and incurred_fee = #{incurredFee}
		</if>
		<if test="requiredFee != null" >
			and required_fee = #{requiredFee}
		</if>
		<if test="treamentContent != null" >
			and treament_content = #{treamentContent}
		</if>
		<if test="tracepersCode != null" >
			and tracepers_code = #{tracepersCode}
		</if>
		<if test="tracepersName != null" >
			and tracepers_name = #{tracepersName}
		</if>
		<if test="tracepersContext != null" >
			and tracepers_context = #{tracepersContext}
		</if>
		<if test="tracepersObject != null" >
			and tracepers_object = #{tracepersObject}
		</if>
		<if test="phoneNumber != null" >
			and phone_number = #{phoneNumber}
		</if>
		<if test="payPersonType != null" >
			and pay_person_type = #{payPersonType}
		</if>
		<if test="payPersonName != null" >
			and pay_person_name = #{payPersonName}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="remark != null" >
			and remark = #{remark}
		</if>
		<if test="flag != null" >
			and flag = #{flag}
		</if>
		<if test="bodyStatus != null" >
			and body_status = #{bodyStatus}
		</if>
		<if test="unendReason != null" >
			and unend_reason = #{unendReason}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_pers_medical_trace
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_medical_trace
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_medical_trace
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersMedicalTraceDTO">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_pers_medical_trace
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_pers_medical_trace
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersMedicalTraceDTO">
		insert into clms_pers_medical_trace (id, report_no, case_times, injured_id, trace_date, 
			currency, pay_way, incurred_fee, required_fee, treament_content, 
			tracepers_code, tracepers_name, tracepers_context, tracepers_object, phone_number, 
			pay_person_type, pay_person_name, valid_flag, remark, flag, 
			body_status, unend_reason, created_by, sys_ctime, updated_by, 
			sys_utime)
		values(#{id}, #{reportNo}, #{caseTimes}, #{injuredId}, #{traceDate}, 
			#{currency}, #{payWay}, #{incurredFee}, #{requiredFee}, #{treamentContent}, 
			#{tracepersCode}, #{tracepersName}, #{tracepersContext}, #{tracepersObject}, #{phoneNumber}, 
			#{payPersonType}, #{payPersonName}, #{validFlag}, #{remark}, #{flag}, 
			#{bodyStatus}, #{unendReason}, #{createdBy}, #{sysCtime}, #{updatedBy}, 
			#{sysUtime})
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersMedicalTraceDTO">
		insert into clms_pers_medical_trace
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="injuredId != null" >
				injured_id,
			</if>
			<if test="traceDate != null" >
				trace_date,
			</if>
			<if test="currency != null" >
				currency,
			</if>
			<if test="payWay != null" >
				pay_way,
			</if>
			<if test="incurredFee != null" >
				incurred_fee,
			</if>
			<if test="requiredFee != null" >
				required_fee,
			</if>
			<if test="treamentContent != null" >
				treament_content,
			</if>
			<if test="tracepersCode != null" >
				tracepers_code,
			</if>
			<if test="tracepersName != null" >
				tracepers_name,
			</if>
			<if test="tracepersContext != null" >
				tracepers_context,
			</if>
			<if test="tracepersObject != null" >
				tracepers_object,
			</if>
			<if test="phoneNumber != null" >
				phone_number,
			</if>
			<if test="payPersonType != null" >
				pay_person_type,
			</if>
			<if test="payPersonName != null" >
				pay_person_name,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="remark != null" >
				remark,
			</if>
			<if test="flag != null" >
				flag,
			</if>
			<if test="bodyStatus != null" >
				body_status,
			</if>
			<if test="unendReason != null" >
				unend_reason,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="injuredId != null" >
				#{injuredId},
			</if>
			<if test="traceDate != null" >
				#{traceDate},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="payWay != null" >
				#{payWay},
			</if>
			<if test="incurredFee != null" >
				#{incurredFee},
			</if>
			<if test="requiredFee != null" >
				#{requiredFee},
			</if>
			<if test="treamentContent != null" >
				#{treamentContent},
			</if>
			<if test="tracepersCode != null" >
				#{tracepersCode},
			</if>
			<if test="tracepersName != null" >
				#{tracepersName},
			</if>
			<if test="tracepersContext != null" >
				#{tracepersContext},
			</if>
			<if test="tracepersObject != null" >
				#{tracepersObject},
			</if>
			<if test="phoneNumber != null" >
				#{phoneNumber},
			</if>
			<if test="payPersonType != null" >
				#{payPersonType},
			</if>
			<if test="payPersonName != null" >
				#{payPersonName},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="bodyStatus != null" >
				#{bodyStatus},
			</if>
			<if test="unendReason != null" >
				#{unendReason},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersMedicalTraceDTO">
		update clms_pers_medical_trace
		<set>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="caseTimes != null" >
				case_times=#{caseTimes},
			</if>
			<if test="injuredId != null" >
				injured_id=#{injuredId},
			</if>
			<if test="traceDate != null" >
				trace_date=#{traceDate},
			</if>
			<if test="currency != null" >
				currency=#{currency},
			</if>
			<if test="payWay != null" >
				pay_way=#{payWay},
			</if>
			<if test="incurredFee != null" >
				incurred_fee=#{incurredFee},
			</if>
			<if test="requiredFee != null" >
				required_fee=#{requiredFee},
			</if>
			<if test="treamentContent != null" >
				treament_content=#{treamentContent},
			</if>
			<if test="tracepersCode != null" >
				tracepers_code=#{tracepersCode},
			</if>
			<if test="tracepersName != null" >
				tracepers_name=#{tracepersName},
			</if>
			<if test="tracepersContext != null" >
				tracepers_context=#{tracepersContext},
			</if>
			<if test="tracepersObject != null" >
				tracepers_object=#{tracepersObject},
			</if>
			<if test="phoneNumber != null" >
				phone_number=#{phoneNumber},
			</if>
			<if test="payPersonType != null" >
				pay_person_type=#{payPersonType},
			</if>
			<if test="payPersonName != null" >
				pay_person_name=#{payPersonName},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="remark != null" >
				remark=#{remark},
			</if>
			<if test="flag != null" >
				flag=#{flag},
			</if>
			<if test="bodyStatus != null" >
				body_status=#{bodyStatus},
			</if>
			<if test="unendReason != null" >
				unend_reason=#{unendReason},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersMedicalTraceDTO">
		update clms_pers_medical_trace
		set report_no=#{reportNo},
			case_times=#{caseTimes},
			injured_id=#{injuredId},
			trace_date=#{traceDate},
			currency=#{currency},
			pay_way=#{payWay},
			incurred_fee=#{incurredFee},
			required_fee=#{requiredFee},
			treament_content=#{treamentContent},
			tracepers_code=#{tracepersCode},
			tracepers_name=#{tracepersName},
			tracepers_context=#{tracepersContext},
			tracepers_object=#{tracepersObject},
			phone_number=#{phoneNumber},
			pay_person_type=#{payPersonType},
			pay_person_name=#{payPersonName},
			valid_flag=#{validFlag},
			remark=#{remark},
			flag=#{flag},
			body_status=#{bodyStatus},
			unend_reason=#{unendReason},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>
	<!-- 根据报案号，赔付次数查询数据 -->
	<select id="selectClmsPersMedicalTrace" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsPersMedicalTraceVO" parameterType="com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo">
		select
		<include refid="Base_Column_List" />
		from clms_pers_medical_trace
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
		    and valid_flag = 'Y'
	</select>

	<delete id="deleteClmsPersMedicalTrace" parameterType="map">
		delete from clms_pers_medical_trace
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
	</delete>
</mapper>