<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.dao.mapper.trace.ClmsPersInjuredMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredDTO">
		 <id column="id" property="id"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="case_times" property="caseTimes"/> 
		 <result column="pers_trace_main_id" property="persTraceMainId"/> 
		 <result column="class_code" property="classCode"/> 
		 <result column="risk_code" property="riskCode"/> 
		 <result column="person_name" property="personName"/> 
		 <result column="certi_type" property="certiType"/> 
		 <result column="certi_code" property="certiCode"/> 
		 <result column="phone_number" property="phoneNumber"/> 
		 <result column="person_age" property="personAge"/> 
		 <result column="birthday" property="birthday"/> 
		 <result column="person_sex" property="personSex"/> 
		 <result column="domicile" property="domicile"/> 
		 <result column="domicile_place_code" property="domicilePlaceCode"/> 
		 <result column="domicile_place" property="domicilePlace"/> 
		 <result column="domicile_area" property="domicileArea"/> 
		 <result column="pamanent_address_code" property="pamanentAddressCode"/> 
		 <result column="pamanent_address" property="pamanentAddress"/> 
		 <result column="live_area" property="liveArea"/> 
		 <result column="work_unit" property="workUnit"/> 
		 <result column="work_comment" property="workComment"/> 
		 <result column="issign" property="issign"/> 
		 <result column="hire_date" property="hireDate"/> 
		 <result column="work_address" property="workAddress"/> 
		 <result column="tic_code" property="ticCode"/> 
		 <result column="tic_name" property="ticName"/> 
		 <result column="in_come" property="inCome"/> 
		 <result column="accreditation_name" property="accreditationName"/> 
		 <result column="disabled_degree" property="disabledDegree"/> 
		 <result column="reference_standard" property="referenceStandard"/> 
		 <result column="demage_code" property="demageCode"/> 
		 <result column="unexpected_name" property="unexpectedName"/> 
		 <result column="conscious" property="conscious"/> 
		 <result column="isno_disability" property="isnoDisability"/> 
		 <result column="isno_die" property="isnoDie"/> 
		 <result column="disabled_situation" property="disabledSituation"/> 
		 <result column="tracer_equired" property="tracerEquired"/> 
		 <result column="visit_type" property="visitType"/> 
		 <result column="trace_feedback" property="traceFeedback"/> 
		 <result column="report_flag" property="reportFlag"/> 
		 <result column="case_type" property="caseType"/> 
		 <result column="injury_part" property="injuryPart"/> 
		 <result column="identify_criteria" property="identifyCriteria"/> 
		 <result column="disability_name" property="disabilityName"/>
		 <result column="identify_time" property="identifyTime"/> 
		 <result column="identify_agencies" property="identifyAgencies"/> 
		 <result column="miamitem" property="miamitem"/> 
		 <result column="indentify_way" property="indentifyWay"/> 
		 <result column="sum_claim_deloss" property="sumClaimDeloss"/> 
		 <result column="sum_report_fee" property="sumReportFee"/> 
		 <result column="sum_real_fee" property="sumRealFee"/> 
		 <result column="sum_detraction_fee" property="sumDetractionFee"/> 
		 <result column="sum_def_loss" property="sumDefLoss"/> 
		 <result column="sum_veri_report_fee" property="sumVeriReportFee"/> 
		 <result column="sum_veri_real_fee" property="sumVeriRealFee"/> 
		 <result column="sum_veri_detraction_fee" property="sumVeriDetractionFee"/> 
		 <result column="sum_veri_def_loss" property="sumVeriDefLoss"/> 
		 <result column="currency" property="currency"/> 
		 <result column="undwrt_valid_flag" property="undwrtValidFlag"/> 
		 <result column="social_security" property="socialSecurity"/> 
		 <result column="profession_code" property="professionCode"/>
		 <result column="sub_profession_code" property="subProfessionCode"/>
		 <result column="profession_grade" property="professionGrade"/>
		 <result column="occupation_type" property="occupationType"/>
		 <result column="occupation_code" property="occupationCode"/>
		 <result column="occupation_grade" property="occupationGrade"/>
		 <result column="medical_type" property="medicalType"/>
		 <result column="is_high" property="isHigh"/>
		 <result column="valid_flag" property="validFlag"/>
		 <result column="remarks" property="remarks"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/>
		 <result column="accident_type" property="accidentType"/>
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, report_no, case_times, pers_trace_main_id, class_code,
		 risk_code, person_name, certi_type, certi_code, phone_number,
		 person_age, birthday, person_sex, domicile, domicile_place_code,
		 domicile_place, domicile_area, pamanent_address_code, pamanent_address, live_area,
		 work_unit, work_comment, issign, hire_date, work_address,
		 tic_code, tic_name, in_come, accreditation_name, disabled_degree,
		 reference_standard, demage_code, unexpected_name, conscious, isno_disability,
		 isno_die, disabled_situation, tracer_equired, visit_type, trace_feedback,
		 report_flag, case_type, injury_part, identify_criteria, disability_name, identify_time, identify_agencies, miamitem, indentify_way,
		 sum_claim_deloss, sum_report_fee, sum_real_fee, sum_detraction_fee, sum_def_loss,
		 sum_veri_report_fee, sum_veri_real_fee, sum_veri_detraction_fee, sum_veri_def_loss, currency,
		 undwrt_valid_flag, social_security, profession_code, sub_profession_code,profession_grade,occupation_type,occupation_code,occupation_grade, medical_type,is_high,
		 valid_flag, remarks, created_by, sys_ctime, updated_by,
		 sys_utime,accident_type
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="persTraceMainId != null" >
			and pers_trace_main_id = #{persTraceMainId}
		</if>
		<if test="classCode != null" >
			and class_code = #{classCode}
		</if>
		<if test="riskCode != null" >
			and risk_code = #{riskCode}
		</if>
		<if test="personName != null" >
			and person_name = #{personName}
		</if>
		<if test="certiType != null" >
			and certi_type = #{certiType}
		</if>
		<if test="certiCode != null" >
			and certi_code = #{certiCode}
		</if>
		<if test="phoneNumber != null" >
			and phone_number = #{phoneNumber}
		</if>
		<if test="personAge != null" >
			and person_age = #{personAge}
		</if>
		<if test="birthday != null" >
			and birthday = #{birthday}
		</if>
		<if test="personSex != null" >
			and person_sex = #{personSex}
		</if>
		<if test="domicile != null" >
			and domicile = #{domicile}
		</if>
		<if test="domicilePlaceCode != null" >
			and domicile_place_code = #{domicilePlaceCode}
		</if>
		<if test="domicilePlace != null" >
			and domicile_place = #{domicilePlace}
		</if>
		<if test="domicileArea != null" >
			and domicile_area = #{domicileArea}
		</if>
		<if test="pamanentAddressCode != null" >
			and pamanent_address_code = #{pamanentAddressCode}
		</if>
		<if test="pamanentAddress != null" >
			and pamanent_address = #{pamanentAddress}
		</if>
		<if test="liveArea != null" >
			and live_area = #{liveArea}
		</if>
		<if test="workUnit != null" >
			and work_unit = #{workUnit}
		</if>
		<if test="workComment != null" >
			and work_comment = #{workComment}
		</if>
		<if test="issign != null" >
			and issign = #{issign}
		</if>
		<if test="hireDate != null" >
			and hire_date = #{hireDate}
		</if>
		<if test="workAddress != null" >
			and work_address = #{workAddress}
		</if>
		<if test="ticCode != null" >
			and tic_code = #{ticCode}
		</if>
		<if test="ticName != null" >
			and tic_name = #{ticName}
		</if>
		<if test="inCome != null" >
			and in_come = #{inCome}
		</if>
		<if test="accreditationName != null" >
			and accreditation_name = #{accreditationName}
		</if>
		<if test="disabledDegree != null" >
			and disabled_degree = #{disabledDegree}
		</if>
		<if test="referenceStandard != null" >
			and reference_standard = #{referenceStandard}
		</if>
		<if test="demageCode != null" >
			and demage_code = #{demageCode}
		</if>
		<if test="unexpectedName != null" >
			and unexpected_name = #{unexpectedName}
		</if>
		<if test="conscious != null" >
			and conscious = #{conscious}
		</if>
		<if test="isnoDisability != null" >
			and isno_disability = #{isnoDisability}
		</if>
		<if test="isnoDie != null" >
			and isno_die = #{isnoDie}
		</if>
		<if test="disabledSituation != null" >
			and disabled_situation = #{disabledSituation}
		</if>
		<if test="tracerEquired != null" >
			and tracer_equired = #{tracerEquired}
		</if>
		<if test="visitType != null" >
			and visit_type = #{visitType}
		</if>
		<if test="traceFeedback != null" >
			and trace_feedback = #{traceFeedback}
		</if>
		<if test="reportFlag != null" >
			and report_flag = #{reportFlag}
		</if>
		<if test="caseType != null" >
			and case_type = #{caseType}
		</if>
		<if test="injuryPart != null" >
			and injury_part = #{injuryPart}
		</if>
		<if test="identifyCriteria != null" >
			and identify_criteria = #{identifyCriteria}
		</if>
		<if test="disabilityName != null" >
			and disability_name = #{disabilityName}
		</if>
		<if test="identifyTime != null" >
			and identify_time = #{identifyTime}
		</if>
		<if test="identifyAgencies != null" >
			and identify_agencies = #{identifyAgencies}
		</if>
		<if test="miamitem != null" >
			and miamitem = #{miamitem}
		</if>
		<if test="indentifyWay != null" >
			and indentify_way = #{indentifyWay}
		</if>
		<if test="sumClaimDeloss != null" >
			and sum_claim_deloss = #{sumClaimDeloss}
		</if>
		<if test="sumReportFee != null" >
			and sum_report_fee = #{sumReportFee}
		</if>
		<if test="sumRealFee != null" >
			and sum_real_fee = #{sumRealFee}
		</if>
		<if test="sumDetractionFee != null" >
			and sum_detraction_fee = #{sumDetractionFee}
		</if>
		<if test="sumDefLoss != null" >
			and sum_def_loss = #{sumDefLoss}
		</if>
		<if test="sumVeriReportFee != null" >
			and sum_veri_report_fee = #{sumVeriReportFee}
		</if>
		<if test="sumVeriRealFee != null" >
			and sum_veri_real_fee = #{sumVeriRealFee}
		</if>
		<if test="sumVeriDetractionFee != null" >
			and sum_veri_detraction_fee = #{sumVeriDetractionFee}
		</if>
		<if test="sumVeriDefLoss != null" >
			and sum_veri_def_loss = #{sumVeriDefLoss}
		</if>
		<if test="currency != null" >
			and currency = #{currency}
		</if>
		<if test="undwrtValidFlag != null" >
			and undwrt_valid_flag = #{undwrtValidFlag}
		</if>
		<if test="socialSecurity != null" >
			and social_security = #{socialSecurity}
		</if>
		<if test="professionCode != null" >
			and profession_code = #{professionCode}
		</if>
		<if test="subProfessionCode != null" >
			and sub_profession_code = #{subProfessionCode}
		</if>
		<if test="professionGrade != null" >
			and profession_grade = #{professionGrade}
		</if>
		<if test="medicalType != null" >
			and medical_type = #{medicalType}
		</if>
		<if test="isHigh != null" >
			and is_high = #{isHigh}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="remarks != null" >
			and remarks = #{remarks}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
		<if test="accidentType != null" >
			and accident_type = #{accidentType}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_pers_injured
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_injured
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_injured
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredDTO">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_pers_injured
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_pers_injured
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredDTO">
		insert into clms_pers_injured (id, report_no, case_times, pers_trace_main_id, class_code, 
			risk_code, person_name, certi_type, certi_code, phone_number, 
			person_age, birthday, person_sex, domicile, domicile_place_code, 
			domicile_place, domicile_area, pamanent_address_code, pamanent_address, live_area, 
			work_unit, work_comment, issign, hire_date, work_address, 
			tic_code, tic_name, in_come, accreditation_name, disabled_degree, 
			reference_standard, demage_code, unexpected_name, conscious, isno_disability, 
			isno_die, disabled_situation, tracer_equired, visit_type, trace_feedback, 
			report_flag, case_type, injury_part, identify_criteria, disability_name, identify_time, identify_agencies, miamitem, indentify_way,
			sum_claim_deloss, sum_report_fee, sum_real_fee, sum_detraction_fee, sum_def_loss, 
			sum_veri_report_fee, sum_veri_real_fee, sum_veri_detraction_fee, sum_veri_def_loss, currency, 
			undwrt_valid_flag, social_security, profession_code,sub_profession_code ,profession_grade,occupation_type,occupation_code,occupation_grade ,medical_type,is_high,
			valid_flag, remarks, created_by, sys_ctime, updated_by, 
			sys_utime,accident_type)
		values(#{id}, #{reportNo}, #{caseTimes}, #{persTraceMainId}, #{classCode}, 
			#{riskCode}, #{personName}, #{certiType}, #{certiCode}, #{phoneNumber}, 
			#{personAge}, #{birthday}, #{personSex}, #{domicile}, #{domicilePlaceCode}, 
			#{domicilePlace}, #{domicileArea}, #{pamanentAddressCode}, #{pamanentAddress}, #{liveArea}, 
			#{workUnit}, #{workComment}, #{issign}, #{hireDate}, #{workAddress}, 
			#{ticCode}, #{ticName}, #{inCome}, #{accreditationName}, #{disabledDegree}, 
			#{referenceStandard}, #{demageCode}, #{unexpectedName}, #{conscious}, #{isnoDisability}, 
			#{isnoDie}, #{disabledSituation}, #{tracerEquired}, #{visitType}, #{traceFeedback}, 
			#{reportFlag}, #{caseType}, #{injuryPart}, #{identifyCriteria}, #{disabilityName}, #{identifyTime}, #{identifyAgencies}, #{miamitem}, #{indentifyWay},
			#{sumClaimDeloss}, #{sumReportFee}, #{sumRealFee}, #{sumDetractionFee}, #{sumDefLoss}, 
			#{sumVeriReportFee}, #{sumVeriRealFee}, #{sumVeriDetractionFee}, #{sumVeriDefLoss}, #{currency}, 
			#{undwrtValidFlag}, #{socialSecurity}, #{professionCode}, #{subProfessionCode},#{professionGrade},
			#{occupationType}, #{occupationCode},#{occupationGrade},#{medicalType},#{isHigh},
			#{validFlag}, #{remarks}, #{createdBy}, #{sysCtime}, #{updatedBy}, 
			#{sysUtime},#{accidentType})
	</insert>
	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO clms_pers_injured (
		REPORT_NO, CASE_TIMES, PERS_TRACE_MAIN_ID, CLASS_CODE,
		RISK_CODE, PERSON_NAME, CERTI_TYPE, CERTI_CODE, PHONE_NUMBER,
		PERSON_AGE, BIRTHDAY, PERSON_SEX, DOMICILE, DOMICILE_PLACE_CODE,
		DOMICILE_PLACE, DOMICILE_AREA, PAMANENT_ADDRESS_CODE, PAMANENT_ADDRESS, LIVE_AREA,
		WORK_UNIT, WORK_COMMENT, ISSIGN, HIRE_DATE, WORK_ADDRESS,
		TIC_CODE, TIC_NAME, IN_COME, ACCREDITATION_NAME, DISABLED_DEGREE,
		REFERENCE_STANDARD, DEMAGE_CODE, UNEXPECTED_NAME, CONSCIOUS, ISNO_DISABILITY,
		ISNO_DIE, DISABLED_SITUATION, TRACER_EQUIRED, VISIT_TYPE, TRACE_FEEDBACK,
		REPORT_FLAG, CASE_TYPE, INJURY_PART, IDENTIFY_CRITERIA, DISABILITY_NAME, IDENTIFY_TIME, IDENTIFY_AGENCIES, MIAMITEM, INDENTIFY_WAY,
		SUM_CLAIM_DELOSS, SUM_REPORT_FEE, SUM_REAL_FEE, SUM_DETRACTION_FEE, SUM_DEF_LOSS,
		SUM_VERI_REPORT_FEE, SUM_VERI_REAL_FEE, SUM_VERI_DETRACTION_FEE, SUM_VERI_DEF_LOSS, CURRENCY,
		UNDWRT_VALID_FLAG, SOCIAL_SECURITY, PROFESSION_CODE,SUB_PROFESSION_CODE ,PROFESSION_GRADE, OCCUPATION_TYPE,OCCUPATION_CODE,OCCUPATION_GRADE,MEDICAL_TYPE,is_high,
		VALID_FLAG, REMARKS, CREATED_BY, SYS_CTIME, UPDATED_BY,
		SYS_UTIME,accident_type
		)
		SELECT
		REPORT_NO, #{reopenCaseTimes}, PERS_TRACE_MAIN_ID, CLASS_CODE,
		RISK_CODE, PERSON_NAME, CERTI_TYPE, CERTI_CODE, PHONE_NUMBER,
		PERSON_AGE, BIRTHDAY, PERSON_SEX, DOMICILE, DOMICILE_PLACE_CODE,
		DOMICILE_PLACE, DOMICILE_AREA, PAMANENT_ADDRESS_CODE, PAMANENT_ADDRESS, LIVE_AREA,
		WORK_UNIT, WORK_COMMENT, ISSIGN, HIRE_DATE, WORK_ADDRESS,
		TIC_CODE, TIC_NAME, IN_COME, ACCREDITATION_NAME, DISABLED_DEGREE,
		REFERENCE_STANDARD, DEMAGE_CODE, UNEXPECTED_NAME, CONSCIOUS, ISNO_DISABILITY,
		ISNO_DIE, DISABLED_SITUATION, TRACER_EQUIRED, VISIT_TYPE, TRACE_FEEDBACK,
		REPORT_FLAG, CASE_TYPE, INJURY_PART, IDENTIFY_CRITERIA, DISABILITY_NAME, IDENTIFY_TIME, IDENTIFY_AGENCIES, MIAMITEM, INDENTIFY_WAY,
		SUM_CLAIM_DELOSS, SUM_REPORT_FEE, SUM_REAL_FEE, SUM_DETRACTION_FEE, SUM_DEF_LOSS,
		SUM_VERI_REPORT_FEE, SUM_VERI_REAL_FEE, SUM_VERI_DETRACTION_FEE, SUM_VERI_DEF_LOSS, CURRENCY,
		UNDWRT_VALID_FLAG, SOCIAL_SECURITY, PROFESSION_CODE,SUB_PROFESSION_CODE ,PROFESSION_GRADE, OCCUPATION_TYPE,OCCUPATION_CODE,OCCUPATION_GRADE,MEDICAL_TYPE,is_High,
		VALID_FLAG, REMARKS,#{userId}, now(), #{userId},
		now(),accident_type
		FROM clms_pers_injured
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
		AND VALID_FLAG='Y'
	</insert>
	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredDTO">
		insert into clms_pers_injured
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="persTraceMainId != null" >
				pers_trace_main_id,
			</if>
			<if test="classCode != null" >
				class_code,
			</if>
			<if test="riskCode != null" >
				risk_code,
			</if>
			<if test="personName != null" >
				person_name,
			</if>
			<if test="certiType != null" >
				certi_type,
			</if>
			<if test="certiCode != null" >
				certi_code,
			</if>
			<if test="phoneNumber != null" >
				phone_number,
			</if>
			<if test="personAge != null" >
				person_age,
			</if>
			<if test="birthday != null" >
				birthday,
			</if>
			<if test="personSex != null" >
				person_sex,
			</if>
			<if test="domicile != null" >
				domicile,
			</if>
			<if test="domicilePlaceCode != null" >
				domicile_place_code,
			</if>
			<if test="domicilePlace != null" >
				domicile_place,
			</if>
			<if test="domicileArea != null" >
				domicile_area,
			</if>
			<if test="pamanentAddressCode != null" >
				pamanent_address_code,
			</if>
			<if test="pamanentAddress != null" >
				pamanent_address,
			</if>
			<if test="liveArea != null" >
				live_area,
			</if>
			<if test="workUnit != null" >
				work_unit,
			</if>
			<if test="workComment != null" >
				work_comment,
			</if>
			<if test="issign != null" >
				issign,
			</if>
			<if test="hireDate != null" >
				hire_date,
			</if>
			<if test="workAddress != null" >
				work_address,
			</if>
			<if test="ticCode != null" >
				tic_code,
			</if>
			<if test="ticName != null" >
				tic_name,
			</if>
			<if test="inCome != null" >
				in_come,
			</if>
			<if test="accreditationName != null" >
				accreditation_name,
			</if>
			<if test="disabledDegree != null" >
				disabled_degree,
			</if>
			<if test="referenceStandard != null" >
				reference_standard,
			</if>
			<if test="demageCode != null" >
				demage_code,
			</if>
			<if test="unexpectedName != null" >
				unexpected_name,
			</if>
			<if test="conscious != null" >
				conscious,
			</if>
			<if test="isnoDisability != null" >
				isno_disability,
			</if>
			<if test="isnoDie != null" >
				isno_die,
			</if>
			<if test="disabledSituation != null" >
				disabled_situation,
			</if>
			<if test="tracerEquired != null" >
				tracer_equired,
			</if>
			<if test="visitType != null" >
				visit_type,
			</if>
			<if test="traceFeedback != null" >
				trace_feedback,
			</if>
			<if test="reportFlag != null" >
				report_flag,
			</if>
			<if test="caseType != null" >
				case_type,
			</if>
			<if test="injuryPart != null" >
				injury_part,
			</if>
			<if test="identifyCriteria != null" >
				identify_criteria,
			</if>
			<if test="disabilityName != null" >
				disability_name,
			</if>
			<if test="identifyTime != null" >
				identify_time,
			</if>
			<if test="identifyAgencies != null" >
				identify_agencies,
			</if>
			<if test="miamitem != null" >
				miamitem,
			</if>
			<if test="indentifyWay != null" >
				indentify_way,
			</if>
			<if test="sumClaimDeloss != null" >
				sum_claim_deloss,
			</if>
			<if test="sumReportFee != null" >
				sum_report_fee,
			</if>
			<if test="sumRealFee != null" >
				sum_real_fee,
			</if>
			<if test="sumDetractionFee != null" >
				sum_detraction_fee,
			</if>
			<if test="sumDefLoss != null" >
				sum_def_loss,
			</if>
			<if test="sumVeriReportFee != null" >
				sum_veri_report_fee,
			</if>
			<if test="sumVeriRealFee != null" >
				sum_veri_real_fee,
			</if>
			<if test="sumVeriDetractionFee != null" >
				sum_veri_detraction_fee,
			</if>
			<if test="sumVeriDefLoss != null" >
				sum_veri_def_loss,
			</if>
			<if test="currency != null" >
				currency,
			</if>
			<if test="undwrtValidFlag != null" >
				undwrt_valid_flag,
			</if>
			<if test="socialSecurity != null" >
				social_security,
			</if>
			<if test="professionCode != null" >
				profession_code,
			</if>
			<if test="subProfessionCode != null" >
				sub_profession_code,
			</if>
			<if test="professionGrade != null" >
				profession_grade,
			</if>
			<if test="medicalType != null" >
				medical_type,
			</if>

			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="remarks != null" >
				remarks,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
			<if test="isHigh != null" >
				is_high,
			</if>
			<if test="accidentType != null" >
				accident_type,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="persTraceMainId != null" >
				#{persTraceMainId},
			</if>
			<if test="classCode != null" >
				#{classCode},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="personName != null" >
				#{personName},
			</if>
			<if test="certiType != null" >
				#{certiType},
			</if>
			<if test="certiCode != null" >
				#{certiCode},
			</if>
			<if test="phoneNumber != null" >
				#{phoneNumber},
			</if>
			<if test="personAge != null" >
				#{personAge},
			</if>
			<if test="birthday != null" >
				#{birthday},
			</if>
			<if test="personSex != null" >
				#{personSex},
			</if>
			<if test="domicile != null" >
				#{domicile},
			</if>
			<if test="domicilePlaceCode != null" >
				#{domicilePlaceCode},
			</if>
			<if test="domicilePlace != null" >
				#{domicilePlace},
			</if>
			<if test="domicileArea != null" >
				#{domicileArea},
			</if>
			<if test="pamanentAddressCode != null" >
				#{pamanentAddressCode},
			</if>
			<if test="pamanentAddress != null" >
				#{pamanentAddress},
			</if>
			<if test="liveArea != null" >
				#{liveArea},
			</if>
			<if test="workUnit != null" >
				#{workUnit},
			</if>
			<if test="workComment != null" >
				#{workComment},
			</if>
			<if test="issign != null" >
				#{issign},
			</if>
			<if test="hireDate != null" >
				#{hireDate},
			</if>
			<if test="workAddress != null" >
				#{workAddress},
			</if>
			<if test="ticCode != null" >
				#{ticCode},
			</if>
			<if test="ticName != null" >
				#{ticName},
			</if>
			<if test="inCome != null" >
				#{inCome},
			</if>
			<if test="accreditationName != null" >
				#{accreditationName},
			</if>
			<if test="disabledDegree != null" >
				#{disabledDegree},
			</if>
			<if test="referenceStandard != null" >
				#{referenceStandard},
			</if>
			<if test="demageCode != null" >
				#{demageCode},
			</if>
			<if test="unexpectedName != null" >
				#{unexpectedName},
			</if>
			<if test="conscious != null" >
				#{conscious},
			</if>
			<if test="isnoDisability != null" >
				#{isnoDisability},
			</if>
			<if test="isnoDie != null" >
				#{isnoDie},
			</if>
			<if test="disabledSituation != null" >
				#{disabledSituation},
			</if>
			<if test="tracerEquired != null" >
				#{tracerEquired},
			</if>
			<if test="visitType != null" >
				#{visitType},
			</if>
			<if test="traceFeedback != null" >
				#{traceFeedback},
			</if>
			<if test="reportFlag != null" >
				#{reportFlag},
			</if>
			<if test="caseType != null" >
				#{caseType},
			</if>
			<if test="injuryPart != null" >
				#{injuryPart},
			</if>
			<if test="identifyCriteria != null" >
				#{identifyCriteria},
			</if>
			<if test="disabilityName != null" >
				#{disabilityName},
			</if>
			<if test="identifyTime != null" >
				#{identifyTime},
			</if>
			<if test="identifyAgencies != null" >
				#{identifyAgencies},
			</if>
			<if test="miamitem != null" >
				#{miamitem},
			</if>
			<if test="indentifyWay != null" >
				#{indentifyWay},
			</if>
			<if test="sumClaimDeloss != null" >
				#{sumClaimDeloss},
			</if>
			<if test="sumReportFee != null" >
				#{sumReportFee},
			</if>
			<if test="sumRealFee != null" >
				#{sumRealFee},
			</if>
			<if test="sumDetractionFee != null" >
				#{sumDetractionFee},
			</if>
			<if test="sumDefLoss != null" >
				#{sumDefLoss},
			</if>
			<if test="sumVeriReportFee != null" >
				#{sumVeriReportFee},
			</if>
			<if test="sumVeriRealFee != null" >
				#{sumVeriRealFee},
			</if>
			<if test="sumVeriDetractionFee != null" >
				#{sumVeriDetractionFee},
			</if>
			<if test="sumVeriDefLoss != null" >
				#{sumVeriDefLoss},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="undwrtValidFlag != null" >
				#{undwrtValidFlag},
			</if>
			<if test="socialSecurity != null" >
				#{socialSecurity},
			</if>
			<if test="professionCode != null" >
				#{professionCode},
			</if>
			<if test="subProfessionCode != null" >
				#{subProfessionCode},
			</if>
			<if test="professionGrade != null" >
				#{professionGrade},
			</if>
			<if test="medicalType != null" >
				#{medicalType},
			</if>

			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="remarks != null" >
				#{remarks},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
			<if test="isHigh != null" >
				#{isHigh},
			</if>
			<if test="accidentType != null" >
				#{accidentType},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredDTO">
		update clms_pers_injured
		<set>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="caseTimes != null" >
				case_times=#{caseTimes},
			</if>
			<if test="persTraceMainId != null" >
				pers_trace_main_id=#{persTraceMainId},
			</if>
			<if test="classCode != null" >
				class_code=#{classCode},
			</if>
			<if test="riskCode != null" >
				risk_code=#{riskCode},
			</if>
			<if test="personName != null" >
				person_name=#{personName},
			</if>
			<if test="certiType != null" >
				certi_type=#{certiType},
			</if>
			<if test="certiCode != null" >
				certi_code=#{certiCode},
			</if>
			<if test="phoneNumber != null" >
				phone_number=#{phoneNumber},
			</if>
			<if test="personAge != null" >
				person_age=#{personAge},
			</if>
			<if test="birthday != null" >
				birthday=#{birthday},
			</if>
			<if test="personSex != null" >
				person_sex=#{personSex},
			</if>
			<if test="domicile != null" >
				domicile=#{domicile},
			</if>
			<if test="domicilePlaceCode != null" >
				domicile_place_code=#{domicilePlaceCode},
			</if>
			<if test="domicilePlace != null" >
				domicile_place=#{domicilePlace},
			</if>
			<if test="domicileArea != null" >
				domicile_area=#{domicileArea},
			</if>
			<if test="pamanentAddressCode != null" >
				pamanent_address_code=#{pamanentAddressCode},
			</if>
			<if test="pamanentAddress != null" >
				pamanent_address=#{pamanentAddress},
			</if>
			<if test="liveArea != null" >
				live_area=#{liveArea},
			</if>
			<if test="workUnit != null" >
				work_unit=#{workUnit},
			</if>
			<if test="workComment != null" >
				work_comment=#{workComment},
			</if>
			<if test="issign != null" >
				issign=#{issign},
			</if>
			<if test="hireDate != null" >
				hire_date=#{hireDate},
			</if>
			<if test="workAddress != null" >
				work_address=#{workAddress},
			</if>
			<if test="ticCode != null" >
				tic_code=#{ticCode},
			</if>
			<if test="ticName != null" >
				tic_name=#{ticName},
			</if>
			<if test="inCome != null" >
				in_come=#{inCome},
			</if>
			<if test="accreditationName != null" >
				accreditation_name=#{accreditationName},
			</if>
			<if test="disabledDegree != null" >
				disabled_degree=#{disabledDegree},
			</if>
			<if test="referenceStandard != null" >
				reference_standard=#{referenceStandard},
			</if>
			<if test="demageCode != null" >
				demage_code=#{demageCode},
			</if>
			<if test="unexpectedName != null" >
				unexpected_name=#{unexpectedName},
			</if>
			<if test="conscious != null" >
				conscious=#{conscious},
			</if>
			<if test="isnoDisability != null" >
				isno_disability=#{isnoDisability},
			</if>
			<if test="isnoDie != null" >
				isno_die=#{isnoDie},
			</if>
			<if test="disabledSituation != null" >
				disabled_situation=#{disabledSituation},
			</if>
			<if test="tracerEquired != null" >
				tracer_equired=#{tracerEquired},
			</if>
			<if test="visitType != null" >
				visit_type=#{visitType},
			</if>
			<if test="traceFeedback != null" >
				trace_feedback=#{traceFeedback},
			</if>
			<if test="reportFlag != null" >
				report_flag=#{reportFlag},
			</if>
			<if test="caseType != null" >
				case_type=#{caseType},
			</if>
			<if test="injuryPart != null" >
				injury_part=#{injuryPart},
			</if>
			<if test="identifyCriteria != null" >
				identify_criteria=#{identifyCriteria},
			</if>
			<if test="disabilityName != null" >
				disability_name=#{disabilityName},
			</if>
			<if test="identifyTime != null" >
				identify_time=#{identifyTime},
			</if>
			<if test="identifyAgencies != null" >
				identify_agencies=#{identifyAgencies},
			</if>
			<if test="miamitem != null" >
				miamitem=#{miamitem},
			</if>
			<if test="indentifyWay != null" >
				indentify_way=#{indentifyWay},
			</if>
			<if test="sumClaimDeloss != null" >
				sum_claim_deloss=#{sumClaimDeloss},
			</if>
			<if test="sumReportFee != null" >
				sum_report_fee=#{sumReportFee},
			</if>
			<if test="sumRealFee != null" >
				sum_real_fee=#{sumRealFee},
			</if>
			<if test="sumDetractionFee != null" >
				sum_detraction_fee=#{sumDetractionFee},
			</if>
			<if test="sumDefLoss != null" >
				sum_def_loss=#{sumDefLoss},
			</if>
			<if test="sumVeriReportFee != null" >
				sum_veri_report_fee=#{sumVeriReportFee},
			</if>
			<if test="sumVeriRealFee != null" >
				sum_veri_real_fee=#{sumVeriRealFee},
			</if>
			<if test="sumVeriDetractionFee != null" >
				sum_veri_detraction_fee=#{sumVeriDetractionFee},
			</if>
			<if test="sumVeriDefLoss != null" >
				sum_veri_def_loss=#{sumVeriDefLoss},
			</if>
			<if test="currency != null" >
				currency=#{currency},
			</if>
			<if test="undwrtValidFlag != null" >
				undwrt_valid_flag=#{undwrtValidFlag},
			</if>
			<if test="socialSecurity != null" >
				social_security=#{socialSecurity},
			</if>
			<if test="professionCode != null" >
				profession_code=#{professionCode},
			</if>
			<if test="subProfessionCode != null" >
				sub_profession_code=#{subProfessionCode},
			</if>
			<if test="professionGrade != null" >
				profession_grade=#{professionGrade},
			</if>

			<if test="occupationCode != null" >
				occupation_code=#{occupationCode},
			</if>
			<if test="occupationType != null" >
				occupation_type=#{occupationType},
			</if>
			<if test="occupationGrade != null" >
				occupation_grade=#{occupationGrade},
			</if>

			<if test="medicalType != null" >
				medical_type=#{medicalType},
			</if>
			<if test="isHigh != null" >
				 is_high = #{isHigh},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="remarks != null" >
				remarks=#{remarks},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
			<if test="accidentType != null" >
				accident_type=#{accidentType},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredDTO">
		update clms_pers_injured
		set report_no=#{reportNo},
			case_times=#{caseTimes},
			pers_trace_main_id=#{persTraceMainId},
			class_code=#{classCode},
			risk_code=#{riskCode},
			person_name=#{personName},
			certi_type=#{certiType},
			certi_code=#{certiCode},
			phone_number=#{phoneNumber},
			person_age=#{personAge},
			birthday=#{birthday},
			person_sex=#{personSex},
			domicile=#{domicile},
			domicile_place_code=#{domicilePlaceCode},
			domicile_place=#{domicilePlace},
			domicile_area=#{domicileArea},
			pamanent_address_code=#{pamanentAddressCode},
			pamanent_address=#{pamanentAddress},
			live_area=#{liveArea},
			work_unit=#{workUnit},
			work_comment=#{workComment},
			issign=#{issign},
			hire_date=#{hireDate},
			work_address=#{workAddress},
			tic_code=#{ticCode},
			tic_name=#{ticName},
			in_come=#{inCome},
			accreditation_name=#{accreditationName},
			disabled_degree=#{disabledDegree},
			reference_standard=#{referenceStandard},
			demage_code=#{demageCode},
			unexpected_name=#{unexpectedName},
			conscious=#{conscious},
			isno_disability=#{isnoDisability},
			isno_die=#{isnoDie},
			disabled_situation=#{disabledSituation},
			tracer_equired=#{tracerEquired},
			visit_type=#{visitType},
			trace_feedback=#{traceFeedback},
			report_flag=#{reportFlag},
			case_type=#{caseType},
			injury_part=#{injuryPart},
			identify_criteria=#{identifyCriteria},
			disability_name=#{disabilityName},
			identify_time=#{identifyTime},
			identify_agencies=#{identifyAgencies},
			miamitem=#{miamitem},
			indentify_way=#{indentifyWay},
			sum_claim_deloss=#{sumClaimDeloss},
			sum_report_fee=#{sumReportFee},
			sum_real_fee=#{sumRealFee},
			sum_detraction_fee=#{sumDetractionFee},
			sum_def_loss=#{sumDefLoss},
			sum_veri_report_fee=#{sumVeriReportFee},
			sum_veri_real_fee=#{sumVeriRealFee},
			sum_veri_detraction_fee=#{sumVeriDetractionFee},
			sum_veri_def_loss=#{sumVeriDefLoss},
			currency=#{currency},
			undwrt_valid_flag=#{undwrtValidFlag},
			social_security=#{socialSecurity},
			profession_code=#{professionCode},
			sub_profession_code=#{subProfessionCode},
			profession_grade=#{professionGrade},
			occupation_type = #{occupationType},
			occupation_code = #{occupationCode},
		    occupation_grade = #{occupationGrade},
			medical_type=#{medicalType},
		    is_high = #{isHigh},
			valid_flag=#{validFlag},
			remarks=#{remarks},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime},
		    accident_type=#{accidentType}
		where id = #{id}
	</update>
	<!-- 根据报案号，赔付次数查询数据 -->
	<select id="selectClmsPersInjured" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsPersInjuredVO" parameterType="com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo">
		select
		<include refid="Base_Column_List" />
		from clms_pers_injured
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
		and valid_flag = 'Y'
	</select>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteClmsPersInjured" parameterType="map">
		delete from clms_pers_injured
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
	</delete>
</mapper>