<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.dao.mapper.trace.ClmsPersTraceExpMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceExpDTO">
		 <id column="id" property="id"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="case_times" property="caseTimes"/> 
		 <result column="injured_id" property="injuredId"/> 
		 <result column="kind_code" property="kindCode"/> 
		 <result column="kind_name" property="kindName"/> 
		 <result column="loss_item_no" property="lossItemNo"/> 
		 <result column="loss_item_name" property="lossItemName"/> 
		 <result column="family_no" property="familyNo"/> 
		 <result column="item_no" property="itemNo"/> 
		 <result column="item_name" property="itemName"/> 
		 <result column="amount" property="amount"/> 
		 <result column="currency" property="currency"/> 
		 <result column="exch_rate" property="exchRate"/> 
		 <result column="sum_def_loss" property="sumDefLoss"/> 
		 <result column="claim_amount" property="claimAmount"/> 
		 <result column="currency1" property="currency1"/> 
		 <result column="exch_rate1" property="exchRate1"/> 
		 <result column="currency2" property="currency2"/> 
		 <result column="sum_def_loss_cny" property="sumDefLossCny"/> 
		 <result column="unit_amount" property="unitAmount"/> 
		 <result column="quantity" property="quantity"/> 
		 <result column="disabled_grade" property="disabledGrade"/> 
		 <result column="disabled_pay_rate" property="disabledPayRate"/> 
		 <result column="estimate_loss" property="estimateLoss"/> 
		 <result column="valid_flag" property="validFlag"/> 
		 <result column="remark" property="remark"/> 
		 <result column="care_fee" property="careFee"/> 
		 <result column="pay_pers_day" property="payPersDay"/> 
		 <result column="in_hosiptal_day" property="inHosiptalDay"/> 
		 <result column="outpay_day" property="outpayDay"/> 
		 <result column="deductible" property="deductible"/> 
		 <result column="deductible_rate" property="deductibleRate"/> 
		 <result column="insured_rate" property="insuredRate"/>
		 <result column="term_code" property="termCode"/>
		 <result column="term_name" property="termName"/>
		 <result column="tax_fee" property="taxFee"/>
		 <result column="related_flag" property="relatedFlag"/>
		 <result column="created_by" property="createdBy"/>
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, report_no, case_times, injured_id, kind_code,
		 kind_name, loss_item_no, loss_item_name, family_no, item_no,
		 item_name, amount, currency, exch_rate, sum_def_loss,
		 claim_amount, currency1, exch_rate1, currency2, sum_def_loss_cny,
		 unit_amount, quantity, disabled_grade, disabled_pay_rate, estimate_loss,
		 valid_flag, remark, care_fee, pay_pers_day, in_hosiptal_day,
		 outpay_day, deductible, deductible_rate, insured_rate,term_code,term_name,tax_fee,related_flag,
		 created_by, sys_ctime, updated_by, sys_utime
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="injuredId != null" >
			and injured_id = #{injuredId}
		</if>
		<if test="kindCode != null" >
			and kind_code = #{kindCode}
		</if>
		<if test="kindName != null" >
			and kind_name = #{kindName}
		</if>
		<if test="lossItemNo != null" >
			and loss_item_no = #{lossItemNo}
		</if>
		<if test="lossItemName != null" >
			and loss_item_name = #{lossItemName}
		</if>
		<if test="familyNo != null" >
			and family_no = #{familyNo}
		</if>
		<if test="itemNo != null" >
			and item_no = #{itemNo}
		</if>
		<if test="itemName != null" >
			and item_name = #{itemName}
		</if>
		<if test="amount != null" >
			and amount = #{amount}
		</if>
		<if test="currency != null" >
			and currency = #{currency}
		</if>
		<if test="exchRate != null" >
			and exch_rate = #{exchRate}
		</if>
		<if test="sumDefLoss != null" >
			and sum_def_loss = #{sumDefLoss}
		</if>
		<if test="claimAmount != null" >
			and claim_amount = #{claimAmount}
		</if>
		<if test="currency1 != null" >
			and currency1 = #{currency1}
		</if>
		<if test="exchRate1 != null" >
			and exch_rate1 = #{exchRate1}
		</if>
		<if test="currency2 != null" >
			and currency2 = #{currency2}
		</if>
		<if test="sumDefLossCny != null" >
			and sum_def_loss_cny = #{sumDefLossCny}
		</if>
		<if test="unitAmount != null" >
			and unit_amount = #{unitAmount}
		</if>
		<if test="quantity != null" >
			and quantity = #{quantity}
		</if>
		<if test="disabledGrade != null" >
			and disabled_grade = #{disabledGrade}
		</if>
		<if test="disabledPayRate != null" >
			and disabled_pay_rate = #{disabledPayRate}
		</if>
		<if test="estimateLoss != null" >
			and estimate_loss = #{estimateLoss}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="remark != null" >
			and remark = #{remark}
		</if>
		<if test="careFee != null" >
			and care_fee = #{careFee}
		</if>
		<if test="payPersDay != null" >
			and pay_pers_day = #{payPersDay}
		</if>
		<if test="inHosiptalDay != null" >
			and in_hosiptal_day = #{inHosiptalDay}
		</if>
		<if test="outpayDay != null" >
			and outpay_day = #{outpayDay}
		</if>
		<if test="deductible != null" >
			and deductible = #{deductible}
		</if>
		<if test="deductibleRate != null" >
			and deductible_rate = #{deductibleRate}
		</if>
		<if test="insuredRate != null" >
			and insured_rate = #{insuredRate}
		</if>
		<if test="termCode != null" >
			and term_code = #{termCode}
		</if>
		<if test="termName != null" >
			and term_name = #{termName}
		</if>
		<if test="taxFee != null" >
			and tax_fee = #{taxFee}
		</if>
		<if test="relatedFlag != null" >
			and related_flag = #{relatedFlag},
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_pers_trace_exp
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_trace_exp
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_pers_trace_exp
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceExpDTO">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_pers_trace_exp
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_pers_trace_exp
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceExpDTO">
		insert into clms_pers_trace_exp (id, report_no, case_times, injured_id, kind_code, 
			kind_name, loss_item_no, loss_item_name, family_no, item_no, 
			item_name, amount, currency, exch_rate, sum_def_loss, 
			claim_amount, currency1, exch_rate1, currency2, sum_def_loss_cny, 
			unit_amount, quantity, disabled_grade, disabled_pay_rate, estimate_loss, 
			valid_flag, remark, care_fee, pay_pers_day, in_hosiptal_day, 
			outpay_day, deductible, deductible_rate, insured_rate,term_code,term_name,tax_fee,related_flag,
			created_by, sys_ctime, updated_by, sys_utime)
		values(#{id}, #{reportNo}, #{caseTimes}, #{injuredId}, #{kindCode}, 
			#{kindName}, #{lossItemNo}, #{lossItemName}, #{familyNo}, #{itemNo}, 
			#{itemName}, #{amount}, #{currency}, #{exchRate}, #{sumDefLoss}, 
			#{claimAmount}, #{currency1}, #{exchRate1}, #{currency2}, #{sumDefLossCny}, 
			#{unitAmount}, #{quantity}, #{disabledGrade}, #{disabledPayRate}, #{estimateLoss}, 
			#{validFlag}, #{remark}, #{careFee}, #{payPersDay}, #{inHosiptalDay}, 
			#{outpayDay}, #{deductible}, #{deductibleRate}, #{insuredRate},#{termCode},#{termName}, #{taxFee}, #{relatedFlag},
			#{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime})
	</insert>
	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO clms_pers_trace_exp (
		REPORT_NO, CASE_TIMES, INJURED_ID, KIND_CODE,
		KIND_NAME, LOSS_ITEM_NO, LOSS_ITEM_NAME, FAMILY_NO, ITEM_NO,
		ITEM_NAME, AMOUNT, CURRENCY, EXCH_RATE, SUM_DEF_LOSS,
		CLAIM_AMOUNT, CURRENCY1, EXCH_RATE1, CURRENCY2, SUM_DEF_LOSS_CNY,
		UNIT_AMOUNT, QUANTITY, DISABLED_GRADE, DISABLED_PAY_RATE, ESTIMATE_LOSS,
		VALID_FLAG, REMARK, CARE_FEE, PAY_PERS_DAY, IN_HOSIPTAL_DAY,
		OUTPAY_DAY, DEDUCTIBLE, DEDUCTIBLE_RATE, INSURED_RATE,term_code,term_name, TAX_FEE,related_flag,
		CREATED_BY, SYS_CTIME, UPDATED_BY, SYS_UTIME
		)
		SELECT
		REPORT_NO, #{reopenCaseTimes}, INJURED_ID, KIND_CODE,
		KIND_NAME, LOSS_ITEM_NO, LOSS_ITEM_NAME, FAMILY_NO, ITEM_NO,
		ITEM_NAME, AMOUNT, CURRENCY, EXCH_RATE, SUM_DEF_LOSS,
		CLAIM_AMOUNT, CURRENCY1, EXCH_RATE1, CURRENCY2, SUM_DEF_LOSS_CNY,
		UNIT_AMOUNT, QUANTITY, DISABLED_GRADE, DISABLED_PAY_RATE, ESTIMATE_LOSS,
		VALID_FLAG, REMARK, CARE_FEE, PAY_PERS_DAY, IN_HOSIPTAL_DAY,
		OUTPAY_DAY, DEDUCTIBLE, DEDUCTIBLE_RATE, INSURED_RATE,term_code,term_name, TAX_FEE,related_flag,
		#{userId}, now(),  #{userId}, now()
		FROM clms_pers_trace_exp
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
		AND VALID_FLAG='Y'
	</insert>
	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="savePersTraceExp" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceExpDTO">
		<selectKey keyProperty="id" resultType="int">
			select LAST_INSERT_ID()
		</selectKey>
		insert into clms_pers_trace_exp
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="injuredId != null" >
				injured_id,
			</if>
			<if test="kindCode != null" >
				kind_code,
			</if>
			<if test="kindName != null" >
				kind_name,
			</if>
			<if test="lossItemNo != null" >
				loss_item_no,
			</if>
			<if test="lossItemName != null" >
				loss_item_name,
			</if>
			<if test="familyNo != null" >
				family_no,
			</if>
			<if test="itemNo != null" >
				item_no,
			</if>
			<if test="itemName != null" >
				item_name,
			</if>
			<if test="amount != null" >
				amount,
			</if>
			<if test="currency != null" >
				currency,
			</if>
			<if test="exchRate != null" >
				exch_rate,
			</if>
			<if test="sumDefLoss != null" >
				sum_def_loss,
			</if>
			<if test="claimAmount != null" >
				claim_amount,
			</if>
			<if test="currency1 != null" >
				currency1,
			</if>
			<if test="exchRate1 != null" >
				exch_rate1,
			</if>
			<if test="currency2 != null" >
				currency2,
			</if>
			<if test="sumDefLossCny != null" >
				sum_def_loss_cny,
			</if>
			<if test="unitAmount != null" >
				unit_amount,
			</if>
			<if test="quantity != null" >
				quantity,
			</if>
			<if test="disabledGrade != null" >
				disabled_grade,
			</if>
			<if test="disabledPayRate != null" >
				disabled_pay_rate,
			</if>
			<if test="estimateLoss != null" >
				estimate_loss,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="remark != null" >
				remark,
			</if>
			<if test="careFee != null" >
				care_fee,
			</if>
			<if test="payPersDay != null" >
				pay_pers_day,
			</if>
			<if test="inHosiptalDay != null" >
				in_hosiptal_day,
			</if>
			<if test="outpayDay != null" >
				outpay_day,
			</if>
			<if test="deductible != null" >
				deductible,
			</if>
			<if test="deductibleRate != null" >
				deductible_rate,
			</if>
			<if test="insuredRate != null" >
				insured_rate,
			</if>
			<if test="termCode != null" >
				term_code,
			</if>
			<if test="termName != null" >
				term_name,
			</if>
			<if test="taxFee != null" >
				tax_fee,
			</if>
			<if test="relatedFlag != null" >
				related_flag,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="injuredId != null" >
				#{injuredId},
			</if>
			<if test="kindCode != null" >
				#{kindCode},
			</if>
			<if test="kindName != null" >
				#{kindName},
			</if>
			<if test="lossItemNo != null" >
				#{lossItemNo},
			</if>
			<if test="lossItemName != null" >
				#{lossItemName},
			</if>
			<if test="familyNo != null" >
				#{familyNo},
			</if>
			<if test="itemNo != null" >
				#{itemNo},
			</if>
			<if test="itemName != null" >
				#{itemName},
			</if>
			<if test="amount != null" >
				#{amount},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="exchRate != null" >
				#{exchRate},
			</if>
			<if test="sumDefLoss != null" >
				#{sumDefLoss},
			</if>
			<if test="claimAmount != null" >
				#{claimAmount},
			</if>
			<if test="currency1 != null" >
				#{currency1},
			</if>
			<if test="exchRate1 != null" >
				#{exchRate1},
			</if>
			<if test="currency2 != null" >
				#{currency2},
			</if>
			<if test="sumDefLossCny != null" >
				#{sumDefLossCny},
			</if>
			<if test="unitAmount != null" >
				#{unitAmount},
			</if>
			<if test="quantity != null" >
				#{quantity},
			</if>
			<if test="disabledGrade != null" >
				#{disabledGrade},
			</if>
			<if test="disabledPayRate != null" >
				#{disabledPayRate},
			</if>
			<if test="estimateLoss != null" >
				#{estimateLoss},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="careFee != null" >
				#{careFee},
			</if>
			<if test="payPersDay != null" >
				#{payPersDay},
			</if>
			<if test="inHosiptalDay != null" >
				#{inHosiptalDay},
			</if>
			<if test="outpayDay != null" >
				#{outpayDay},
			</if>
			<if test="deductible != null" >
				#{deductible},
			</if>
			<if test="deductibleRate != null" >
				#{deductibleRate},
			</if>
			<if test="insuredRate != null" >
				#{insuredRate},
			</if>
			<if test="termCode != null" >
				#{termCode},
			</if>
			<if test="termName != null" >
				#{termName},
			</if>
			<if test="taxFee != null" >
				#{taxFee},
			</if>
			<if test="relatedFlag != null" >
				#{relatedFlag},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceExpDTO">
		update clms_pers_trace_exp
		<set>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="caseTimes != null" >
				case_times=#{caseTimes},
			</if>
			<if test="injuredId != null" >
				injured_id=#{injuredId},
			</if>
			<if test="kindCode != null" >
				kind_code=#{kindCode},
			</if>
			<if test="kindName != null" >
				kind_name=#{kindName},
			</if>
			<if test="lossItemNo != null" >
				loss_item_no=#{lossItemNo},
			</if>
			<if test="lossItemName != null" >
				loss_item_name=#{lossItemName},
			</if>
			<if test="familyNo != null" >
				family_no=#{familyNo},
			</if>
			<if test="itemNo != null" >
				item_no=#{itemNo},
			</if>
			<if test="itemName != null" >
				item_name=#{itemName},
			</if>
			<if test="amount != null" >
				amount=#{amount},
			</if>
			<if test="currency != null" >
				currency=#{currency},
			</if>
			<if test="exchRate != null" >
				exch_rate=#{exchRate},
			</if>
			<if test="sumDefLoss != null" >
				sum_def_loss=#{sumDefLoss},
			</if>
			<if test="claimAmount != null" >
				claim_amount=#{claimAmount},
			</if>
			<if test="currency1 != null" >
				currency1=#{currency1},
			</if>
			<if test="exchRate1 != null" >
				exch_rate1=#{exchRate1},
			</if>
			<if test="currency2 != null" >
				currency2=#{currency2},
			</if>
			<if test="sumDefLossCny != null" >
				sum_def_loss_cny=#{sumDefLossCny},
			</if>
			<if test="unitAmount != null" >
				unit_amount=#{unitAmount},
			</if>
			<if test="quantity != null" >
				quantity=#{quantity},
			</if>
			<if test="disabledGrade != null" >
				disabled_grade=#{disabledGrade},
			</if>
			<if test="disabledPayRate != null" >
				disabled_pay_rate=#{disabledPayRate},
			</if>
			<if test="estimateLoss != null" >
				estimate_loss=#{estimateLoss},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="remark != null" >
				remark=#{remark},
			</if>
			<if test="careFee != null" >
				care_fee=#{careFee},
			</if>
			<if test="payPersDay != null" >
				pay_pers_day=#{payPersDay},
			</if>
			<if test="inHosiptalDay != null" >
				in_hosiptal_day=#{inHosiptalDay},
			</if>
			<if test="outpayDay != null" >
				outpay_day=#{outpayDay},
			</if>
			<if test="deductible != null" >
				deductible=#{deductible},
			</if>
			<if test="deductibleRate != null" >
				deductible_rate=#{deductibleRate},
			</if>
			<if test="insuredRate != null" >
				insured_rate=#{insuredRate},
			</if>
			<if test="termCode != null" >
				term_code=#{termCode},
			</if>
			<if test="termName != null" >
				term_name=#{termName},
			</if>
			<if test="taxFee != null" >
				tax_fee=#{taxFee},
			</if>
			<if test="relatedFlag != null" >
				related_flag = #{relatedFlag},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceExpDTO">
		update clms_pers_trace_exp
		set report_no=#{reportNo},
			case_times=#{caseTimes},
			injured_id=#{injuredId},
			kind_code=#{kindCode},
			kind_name=#{kindName},
			loss_item_no=#{lossItemNo},
			loss_item_name=#{lossItemName},
			family_no=#{familyNo},
			item_no=#{itemNo},
			item_name=#{itemName},
			amount=#{amount},
			currency=#{currency},
			exch_rate=#{exchRate},
			sum_def_loss=#{sumDefLoss},
			claim_amount=#{claimAmount},
			currency1=#{currency1},
			exch_rate1=#{exchRate1},
			currency2=#{currency2},
			sum_def_loss_cny=#{sumDefLossCny},
			unit_amount=#{unitAmount},
			quantity=#{quantity},
			disabled_grade=#{disabledGrade},
			disabled_pay_rate=#{disabledPayRate},
			estimate_loss=#{estimateLoss},
			valid_flag=#{validFlag},
			remark=#{remark},
			care_fee=#{careFee},
			pay_pers_day=#{payPersDay},
			in_hosiptal_day=#{inHosiptalDay},
			outpay_day=#{outpayDay},
			deductible=#{deductible},
			deductible_rate=#{deductibleRate},
			insured_rate=#{insuredRate},
			term_code=#{termCode},
			term_name=#{termName},
			tax_fee=#{taxFee},
			related_flag = #{relatedFlag},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>
	<!-- 根据报案号，赔付次数查询数据 -->
	<select id="selectClmsPersTraceExp" resultType="com.paic.ncbs.claim.model.vo.trace.ClmsPersTraceExpVO" parameterType="com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo">
		select
		<include refid="Base_Column_List" />
		from clms_pers_trace_exp
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
		    and valid_flag = 'Y'
	</select>

	<delete id="deleteClmsPersTraceExp" parameterType="map">
		delete from clms_pers_trace_exp
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and case_times=#{caseTimes}
		</if>
	</delete>
</mapper>