<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.user.DepartmentInfoMapper" >

	 <select id="queryDepartmentInfoByDeptCode"  parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.user.DepartmentDTO">
        select
         d.DEPARTMENT_CODE              AS "departmentCode",
         d.DEPARTMENT_CHINESE_NAME      AS "departmentChineseName",
         d.DEPARTMENT_ABBR_NAME         AS "departmentName",
         d.DEPARTMENT_LEVEL             AS "departmentLevel",
         d.CHINESE_ADDRESS              AS "chineseAddress",
         d.POSTCODE                     AS "postcode",
         d.DEPARTMENT_ENGLISH_NAME      AS "departmentEnglishName",
         d.<PERSON><PERSON>L<PERSON>H_ADDRESS              AS "englishAddress",
         d.<PERSON><PERSON>ON<PERSON>                    AS "telephone",
         d.<PERSON>X                          AS "fax",
         d.UPPER_DEPARTMENT_CODE        AS "upperDepartmentCode",
         d.LINK_MAN_CODE                AS "linkManCode",
         d.INVALIDATE_DATE              AS "invalidateDate",
         d.DEPARTMENT_MARK              AS "departmentMark",
         d.ALARM_MARK                   AS "alarmMark",
         d.DEFAULT_LISCENSE_CODE        AS "defaultLiscenseCode",
         d.DEFAULT_LISCENSE             AS "defaultLiscense",
         d.EMAIL                        AS "email",
         d.VEHICLE_LISCENCE_CODE_PREFIX AS "vehicleLiscenceCodePrefix",
         d.TELEPHONE_AREA_CODE          AS "telephoneAreaCode",
         d.IS_VIP                       AS "isVip",
         d.DISTRICT_CODE                AS "districtCode",
         d.IS_DEPARTMENT_NODE           AS "isDepartmentNode",
         d.IS_NEW_DEPARTMENT_NODE       AS "isNewDepartmentNode",
         d.DEPARTMENT_NODE_TYPE         AS "departmentNodeType",
         d.DEPARTMENT_NODE_STATUS       AS "departmentNodeStatus",
         d.DEPARTMENT_NODE_TYPE_CLASS   AS "departmentNodeTypeClass",
         d.DEPARTMENT_NODE_NAME         AS "departmentNodeName",
         d.TAX_REGISTER_NO              AS "taxRegisterNo",
         d.CHANNEL_ATTRIBUTE            AS "channelAttribute",
         d.IS_TOWN_DEPARTMENT           AS "isTownDepartment",
         d.TOWN_DEPARTMENT_TYPE         AS "townDepartmentType",
         d.VOLUME_LASTYEAR              AS "volumeLastyear",
         d.FIRST_YEAR_COMMISSION        AS "firstYearCommission",
         d.FIRST_PLAN                   AS "firstPlan",
         d.SECOND_PLAN                  AS "secondPlan",
         d.THIRD_PLAN                   AS "thirdPlan",
         d.INSURED_PLACE                AS "insuredPlace",
         d.TAX_REGISTER_NAME            AS "taxRegisterName"
      from clmp_department_define_pv d
       where d.department_code = #{deptCode,jdbcType=VARCHAR}
       <![CDATA[and (d.invalidate_date is null or d.invalidate_date > sysdate())]]>
    </select>

</mapper>