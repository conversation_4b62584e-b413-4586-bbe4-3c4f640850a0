<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.user.DepartmentRelationMapper">

    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.user.DepartmentRelationEntity">
        <id column="CHILD_DEPARTMENT_CODE" property="childDepartmentCode" jdbcType="VARCHAR"/>
        <id column="PARENT_DEPARTMENT_CODE" property="parentDepartmentCode" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="DEPARTMENT_LEVEL" property="departmentLevel" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        CHILD_DEPARTMENT_CODE, PARENT_DEPARTMENT_CODE, CREATED_BY, CREATED_DATE, UPDATED_BY,
        UPDATED_DATE, DEPARTMENT_LEVEL
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap"
            parameterType="com.paic.ncbs.claim.dao.entity.user.DepartmentRelationKeyEntity">
        select
        <include refid="Base_Column_List"/>
        from DEPARTMENT_RELATION
        where CHILD_DEPARTMENT_CODE = #{childDepartmentCode,jdbcType=VARCHAR}
        and PARENT_DEPARTMENT_CODE = #{parentDepartmentCode,jdbcType=VARCHAR}
    </select>

    <select id="getSecondDepartmentRelation" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from DEPARTMENT_RELATION
        where CHILD_DEPARTMENT_CODE = #{childDepartmentCode,jdbcType=VARCHAR}
        and DEPARTMENT_LEVEL =2
    </select>

    <select id="getDepartmentRelation" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from department_relation
        where child_department_code = #{childDepartmentCode,jdbcType=VARCHAR}
        order by department_level desc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.user.DepartmentRelationKeyEntity">
        delete from DEPARTMENT_RELATION
        where CHILD_DEPARTMENT_CODE = #{childDepartmentCode,jdbcType=VARCHAR}
        and PARENT_DEPARTMENT_CODE = #{parentDepartmentCode,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.user.DepartmentRelationEntity">
        insert into DEPARTMENT_RELATION (CHILD_DEPARTMENT_CODE, PARENT_DEPARTMENT_CODE,
        CREATED_BY, CREATED_DATE, UPDATED_BY,
        UPDATED_DATE, DEPARTMENT_LEVEL)
        values (#{childDepartmentCode,jdbcType=VARCHAR}, #{parentDepartmentCode,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR},
        #{updatedDate,jdbcType=TIMESTAMP}, #{departmentLevel,jdbcType=DECIMAL})
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.user.DepartmentRelationEntity">
        insert into DEPARTMENT_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="childDepartmentCode != null">
                CHILD_DEPARTMENT_CODE,
            </if>
            <if test="parentDepartmentCode != null">
                PARENT_DEPARTMENT_CODE,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="departmentLevel != null">
                DEPARTMENT_LEVEL,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="childDepartmentCode != null">
                #{childDepartmentCode,jdbcType=VARCHAR},
            </if>
            <if test="parentDepartmentCode != null">
                #{parentDepartmentCode,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="departmentLevel != null">
                #{departmentLevel,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.user.DepartmentRelationEntity">
        update DEPARTMENT_RELATION
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="departmentLevel != null">
                DEPARTMENT_LEVEL = #{departmentLevel,jdbcType=DECIMAL},
            </if>
        </set>
        where CHILD_DEPARTMENT_CODE = #{childDepartmentCode,jdbcType=VARCHAR}
        and PARENT_DEPARTMENT_CODE = #{parentDepartmentCode,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.user.DepartmentRelationEntity">
        update DEPARTMENT_RELATION
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        DEPARTMENT_LEVEL = #{departmentLevel,jdbcType=DECIMAL}
        where CHILD_DEPARTMENT_CODE = #{childDepartmentCode,jdbcType=VARCHAR}
        and PARENT_DEPARTMENT_CODE = #{parentDepartmentCode,jdbcType=VARCHAR}
    </update>
</mapper>