<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.user.ReceiverConfigMapper">

	<select id="getReceiverList" parameterType="com.paic.ncbs.claim.model.dto.user.ReceiverConfigDTO"
			resultType="com.paic.ncbs.claim.model.dto.user.ReceiverConfigDTO">
		select
			id_clms_receiver_config idClmsReceiverConfig,
			dept_code      deptCode,
			node           node,
		    dept_name      deptName,
			receiver_id    receiverId,
			receiver_name  receiverName,
			receiver_email receiverEmail,
			copy_id        copyId,
			copy_name      copyName,
			copy_email     copyEmail,
			amount_scope   amountScope
		from clms_receiver_config
		where is_effective = 'Y'
		<if test="deptCode != null">
			and dept_code = #{deptCode ,jdbcType=VARCHAR}
		</if>
		<if test="node != null">
			and node =  #{node ,jdbcType=VARCHAR}
		</if>
		<if test="remark != null">
			and amount_scope = #{amountScope ,jdbcType=VARCHAR}
		</if>
	</select>

	<update id="updateReceiverList" parameterType="java.util.List">
		<foreach collection="list" item="item"  open="" separator=";" close="">
			update clms_receiver_config
			set updated_by = #{item.updatedBy,jdbcType=VARCHAR},
			updated_date = now(),
			receiver_id = #{item.receiverId,jdbcType=VARCHAR},
			receiver_name = #{item.receiverName,jdbcType=VARCHAR},
			receiver_email = #{item.receiverEmail,jdbcType=VARCHAR},
			copy_id = #{item.copyId,jdbcType=VARCHAR},
			copy_name = #{item.copyName,jdbcType=VARCHAR},
			copy_email = #{item.copyEmail,jdbcType=VARCHAR}
			where id_clms_receiver_config = #{item.idClmsReceiverConfig,jdbcType=VARCHAR}
			and   is_effective  = 'Y'
		</foreach>
	</update>

	<update id="updateEmail" parameterType="com.paic.ncbs.claim.model.dto.user.ReceiverConfigDTO">
		update clms_receiver_config
		set updated_by = #{updatedBy,jdbcType=VARCHAR},

		<if test="receiverEmail != null">
			receiver_email = #{receiverEmail,jdbcType=VARCHAR},
		</if>
		<if test="copyEmail != null">
			copy_email = #{copyEmail,jdbcType=VARCHAR},
		</if>
		updated_date = now()
		where id_clms_receiver_config = #{idClmsReceiverConfig,jdbcType=VARCHAR}
		and   is_effective  = 'Y'

	</update>

</mapper>