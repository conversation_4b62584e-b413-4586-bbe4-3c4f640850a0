<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.user.PermissionMapper">

	<insert id="addPermissionList" parameterType="java.util.List">
		insert into clms_permission (
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_CLMS_PERMISSION,
		DEPT_CODE,
		TYPE_CODE,
		GRADE,
		MIN_AMOUNT,
		MAX_AMOUNT)
		values
		<foreach collection="list" separator="," index="index" item="item">
			(#{item.createdBy,jdbcType=VARCHAR},
			now(),
			#{item.createdBy,jdbcType=VARCHAR},
			now(),
			#{item.idClmsPermission,jdbcType=VARCHAR},
			#{item.deptCode,jdbcType=VARCHAR},
			#{item.typeCode,jdbcType=VARCHAR},
			#{item.grade,jdbcType=INTEGER},
			#{item.minAmount,jdbcType=DECIMAL},
			#{item.maxAmount,jdbcType=DECIMAL})
		</foreach>
	</insert>

	<update id="updatePermission" parameterType="com.paic.ncbs.claim.model.dto.user.PermissionDTO">
		update clms_permission
		set UPDATED_DATE = now(),
		UPDATED_BY = #{createdBy,jdbcType=VARCHAR},
		MIN_AMOUNT = #{minAmount,jdbcType=DECIMAL},
		MAX_AMOUNT = #{maxAmount,jdbcType=DECIMAL}
		where ID_CLMS_PERMISSION = #{idClmsPermission,jdbcType=VARCHAR}
	</update>

	<select id="getPermissionList" parameterType="com.paic.ncbs.claim.model.dto.user.PermissionDTO"
			resultType="com.paic.ncbs.claim.model.dto.user.PermissionDTO">
		select
		ID_CLMS_PERMISSION idClmsPermission,
		DEPT_CODE deptCode,
		TYPE_CODE typeCode,
		GRADE grade,
		MIN_AMOUNT minAmount,
		MAX_AMOUNT maxAmount
		from clms_permission
		where DEPT_CODE = #{deptCode,jdbcType=VARCHAR}
		and TYPE_CODE   = #{typeCode,jdbcType=VARCHAR}
		order by GRADE
	</select>

	<delete id="delPermission" parameterType="java.lang.String">
		delete from clms_permission
		where DEPT_CODE = #{deptCode,jdbcType=VARCHAR}
		and TYPE_CODE   = #{typeCode,jdbcType=VARCHAR}
	</delete>

	<select id="getPermissionGrade"  resultType="java.lang.Integer">
		select MAX(GRADE)
		from clms_permission
		where DEPT_CODE = #{deptCode,jdbcType=VARCHAR}
		and TYPE_CODE   = #{typeCode,jdbcType=VARCHAR}
	    and MIN_AMOUNT <![CDATA[ < ]]> #{amount,jdbcType=DECIMAL}
	    and MAX_AMOUNT <![CDATA[ >= ]]> #{amount,jdbcType=DECIMAL}
	</select>

</mapper>