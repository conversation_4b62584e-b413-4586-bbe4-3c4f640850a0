<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.duty.DutyRejectDetailMapper">

    <!-- 保存核责拒赔详情 -->
    <insert id="addDutyRejectDetailList" parameterType="java.util.List">
        INSERT INTO CLMS_DUTY_REJECT_DETAIL (
        CREATED_BY ,
        CREATED_DATE,
        UPDATED_BY ,
        UPDATED_DATE,
        REPORT_NO ,
        CASE_TIMES ,
        POLICY_NO,
        POLICY_CER_NO,
        REJECT_AMOUNT ,
        STATUS ,
        LOSS_OBJECT_NO,
        ARCHIVE_TIME,
        ID_AHCS_DUTY_REJECT_DETAIL
        ) values
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item.createdBy, jdbcType=VARCHAR},
            sysdate(),
            #{item.updatedBy, jdbcType=VARCHAR},
            sysdate(),
            #{item.reportNo, jdbcType=VARCHAR},
            #{item.caseTimes, jdbcType=NUMERIC},
            #{item.policyNo, jdbcType=VARCHAR},
            #{item.policyCerNo, jdbcType=VARCHAR},
            #{item.rejectAmount, jdbcType=NUMERIC},
            #{item.status, jdbcType=VARCHAR},
            #{item.lossObjectNo, jdbcType=VARCHAR},
            <if test="item.archiveTime != null ">
                #{item.archiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="item.archiveTime == null ">
                sysdate()
            </if>,
            REPLACE(UUID(),'-','')
        </foreach>
    </insert>

    <select id="getDutyRejectDetailList" resultType="com.paic.ncbs.claim.model.vo.duty.DutyRejectDetailVO">
        SELECT REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        POLICY_NO policyNo,
        POLICY_CER_NO policyCerNo,
        REJECT_AMOUNT rejectAmount,
        STATUS status,
        LOSS_OBJECT_NO lossObjectNo
        FROM CLMS_DUTY_REJECT_DETAIL T
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            and status = #{status}
        </if>
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            and loss_object_no = #{lossObjectNo}
        </if>
    </select>

    <delete id="removeDutyRejectDetailList">
        DELETE FROM CLMS_DUTY_REJECT_DETAIL T WHERE T.REPORT_NO=#{reportNo} AND T.CASE_TIMES = #{caseTimes}
    </delete>

    <select id="getEstimatePolicyByReportNo" resultType="com.paic.ncbs.claim.model.vo.duty.DutyRejectDetailVO">
        select
        po.POLICY_NO policyNo,
        po.policy_cer_no policyCerNo
        from CLMS_estimate_policy po
        where po.report_no=#{reportNo,jdbcType=VARCHAR}
        and po.case_times=#{caseTimes,jdbcType=INTEGER}
        order by po.estimate_amount desc
    </select>

    <select id="getSettlePolicyByReportNo" resultType="com.paic.ncbs.claim.model.vo.duty.DutyRejectDetailVO">
        select
        api.policy_no policyNo,
        api.policy_cer_no policyCerNo
        from CLMS_policy_info api
        where api.report_no=#{reportNo,jdbcType=VARCHAR}
        and exists
        ( select 1 from CLMS_duty_detail_pay dp where
        (dp.settle_amount is not null or dp.auto_settle_amount is not null)
        and dp.case_no= api.case_no
        and dp.case_times=#{caseTimes,jdbcType=INTEGER}
        AND dp.IS_EFFECTIVE = 'Y'
        ) as temp limit 1
    </select>

    <select id="getRejectAmountByReportNo" resultType="java.math.BigDecimal">
        select sum(t.reject_amount) from CLMS_duty_reject_detail t where t.report_no=#{reportNo} and
        t.case_times=#{caseTimes} and t.status='1'
    </select>
</mapper>