<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.duty.LossItemDefineMapper">
	<resultMap type="com.paic.ncbs.claim.model.vo.duty.LossItemDefineVO" id="result1">
		<result column="LOSS_ITEM_CODE" property="lossItemCode"/>
		<result column="LOSS_ITEM_NAME" property="lossItemName"/>
		<collection property="subLossItemList" ofType="com.paic.ncbs.claim.model.vo.duty.LossItemDefineVO" column="LOSS_ITEM_CODE" select="getSubLossItems">
		</collection>
	</resultMap>
	
	<select id="getLossItems" resultMap="result1">
		select LOSS_ITEM_CODE,LOSS_ITEM_NAME from CLMS_LOSS_ITEM_DEFINE where PARENT_CODE = '0'
	</select>
	
	<select id="getSubLossItems" parameterType="string" resultMap="result1">
		select LOSS_ITEM_CODE,LOSS_ITEM_NAME from CLMS_LOSS_ITEM_DEFINE t1 where PARENT_CODE = #{parentCode}
	</select>

</mapper>