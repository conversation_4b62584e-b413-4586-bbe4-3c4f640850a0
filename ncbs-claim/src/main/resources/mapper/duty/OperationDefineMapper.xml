<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.duty.OperationDefineMapper">

	<select id="getOperationDefine" resultType="com.paic.ncbs.claim.model.dto.duty.OperationDefineDTO">
		SELECT T.DEPT_CODE deptCode,
			   T.DEPT_NAME deptName,
			   T.PART_CODE partCode,
			   T.PART_NAME partName,
			   T.OPERATION_CODE operationCode,
			   T.OPERATION_NAME operationName,
			   T.OPERATION_LEVEL operationLevel		   
		  FROM CLMS_OPERATION_DEFINE T
		  WHERE 1=1
		  <if test="deptName != null">
		  	AND T.DEPT_NAME LIKE CONCAT('%',#{deptName},'%')
		  </if> 
	</select>

	<select id="getTherapyOperation" resultType="com.paic.ncbs.claim.model.vo.duty.TherapyOperationVO" parameterType="com.paic.ncbs.claim.model.vo.duty.TherapyOperationVO">
		select t.operation_code operationCode,
			   t.operation_name  operationName
		from
			clms_therapy_operation t
		where t.operation_name like CONCAT('%',#{operationName},'%')
		<if test="orgType != null and orgType != '' ">
			AND T.org_type = #{orgType}
		</if>
		limit 1000
	</select>

	<select id="getTherapyOperationByCode" resultType="java.lang.String" parameterType="java.lang.String">
		select t.operation_name  operationName
		from
			clms_therapy_operation t
		where t.operation_code = #{operationCode,jdbcType=VARCHAR}
		<if test="orgType != null and orgType != '' ">
			AND T.org_type = #{orgType}
		</if>
		limit 1
	</select>

	
</mapper>