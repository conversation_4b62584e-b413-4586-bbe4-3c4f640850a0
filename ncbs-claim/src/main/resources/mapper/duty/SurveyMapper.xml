<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.SurveyMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.duty.SurveyDTO" id="surveyMap">
		<id column="ID_AHCS_SURVEY" property="idAhcsSurvey"/>
	  	<result column="CREATED_BY" property="createdBy"/>
	  	<result column="CREATED_DATE" property="createdDate"/>
	  	<result column="UPDATED_BY" property="updatedBy"/>
	  	<result column="UPDATED_DATE" property="updatedDate"/>
	  	<result column="REPORT_NO" property="reportNo"/>
	  	<result column="CASE_TIMES" property="caseTimes"/>
	  	<result column="SURVEY_UM" property="surveyUm"/>
	  	<result column="SURVEY_DATE" property="surveyDate"/>
	  	<result column="PHONE_SURVEY_DETAIL" property="phoneSurveyDetail"/>
	  	<result column="TASK_ID " property="taskId" />
	  	<result column="PARTNER_CODE" property="partnerCode" />
	</resultMap>

	<insert id="addSurvey" parameterType="com.paic.ncbs.claim.model.dto.duty.SurveyDTO">
	  insert into
	  		CLMS_SURVEY
	  		(CREATED_BY,
	  		CREATED_DATE,
	  		UPDATED_BY,
	  		UPDATED_DATE,
	  		ID_AHCS_SURVEY,
	  		REPORT_NO,
	  		CASE_TIMES,
	  		SURVEY_UM,
	  		SURVEY_DATE,
	  		PHONE_SURVEY_DETAIL,
	  		TASK_ID,
	  		STATUS,
	  		ARCHIVE_TIME
	  		)
	  	values
		  	(#{createdBy},
			NOW(),
		  	#{updatedBy},
			NOW(),
			#{idAhcsSurvey},
		  	#{reportNo},
		  	#{caseTimes},
		  	#{surveyUm,jdbcType=VARCHAR},
			NOW(),
		  	#{phoneSurveyDetail,jdbcType=VARCHAR},
		  	#{taskId,jdbcType=VARCHAR},
		  	#{status,jdbcType=VARCHAR},
			 sysdate()
		  	)
	</insert>

	<update id="modifySurvey" parameterType="com.paic.ncbs.claim.model.dto.duty.SurveyDTO">
	  update
	  		CLMS_SURVEY
	  	set
		  	UPDATED_BY=#{updatedBy},
		  	UPDATED_DATE=NOW(),
		  	SURVEY_UM=#{surveyUm},
		  	SURVEY_DATE=NOW(),
		  	PHONE_SURVEY_DETAIL=#{phoneSurveyDetail,jdbcType=VARCHAR} ,
		  	status=#{status,jdbcType=VARCHAR}
		 where REPORT_NO=#{reportNo} and CASE_TIMES=#{caseTimes}
		 		<if test="taskId != null and taskId != '' ">
					and TASK_ID = #{taskId}
				 </if>
	</update>

	<select id="getSurvey" resultMap="surveyMap" parameterType="com.paic.ncbs.claim.model.dto.duty.SurveyDTO">
	  select CREATED_BY,
	  		CREATED_DATE,
	  		UPDATED_BY,
	  		UPDATED_DATE,
	  		ID_AHCS_SURVEY,
	  		REPORT_NO,
	  		CASE_TIMES,
	  		SURVEY_UM,
	  		SURVEY_DATE,
	  		PHONE_SURVEY_DETAIL,
	  		TASK_ID,
	  		STATUS
	   from
	  		CLMS_SURVEY
	   where REPORT_NO=#{reportNo} and CASE_TIMES=#{caseTimes}
	   		 <if test="taskId != null and taskId != '' ">
				and TASK_ID = #{taskId}
		 	 </if>
		 	 <if test="status != null and status != '' ">
				and status = #{status}
		 	 </if>
            limit 1
	</select>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO CLMS_SURVEY (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_SURVEY,
			REPORT_NO,
			CASE_TIMES,
			SURVEY_UM,
			SURVEY_DATE,
			PHONE_SURVEY_DETAIL,
			TASK_ID,
			STATUS,
			ARCHIVE_TIME
		)
		SELECT
			#{userId},
			NOW(),
			#{userId},
			NOW(),
			MD5(UUID()),
			REPORT_NO,
			#{reopenCaseTimes},
			SURVEY_UM,
			SURVEY_DATE,
			PHONE_SURVEY_DETAIL,
			TASK_ID,
			STATUS,
			NOW()
		FROM CLMS_SURVEY
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
	</insert>

	<select id="getsureyDeatalInfo" resultType="com.paic.ncbs.claim.model.vo.duty.SurveyVO">
		select PHONE_SURVEY_DETAIL from CLMS_SURVEY where REPORT_NO =#{reportNo}
		and task_id=#{taskId} and `STATUS`=#{status}
	</select>
</mapper>
