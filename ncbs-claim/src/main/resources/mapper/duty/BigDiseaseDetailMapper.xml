<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.duty.BigDiseaseDetailMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDetailDTO" id="result">
		<result column="BIG_DISEASE_DETAIL_CODE" property="bigDiseaseDetailCode" />
		<result column="ID_AHCS_BIG_DISEASE" property="bigDiseaseId" />
		<result column="STATUS" property="status" />
		<result column="ARCHIVE_TIME"  property="archiveTime"/>
	</resultMap>

	<insert id="saveBigDiseaseDetail" parameterType="com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDetailDTO">
		insert into CLMS_big_disease_detail
		(CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_AHCS_BIG_DISEASE,
		BIG_DISEASE_DETAIL_CODE,
		STATUS,
		archive_time)
		values
		(#{createdBy},
		now(),
		#{updatedBy},
		now(),
		#{bigDiseaseId,jdbcType=VARCHAR},
		#{bigDiseaseDetailCode,jdbcType=VARCHAR},
		#{status,jdbcType=VARCHAR},
		<if test="archiveTime != null ">
			#{archiveTime,jdbcType=TIMESTAMP}
		</if>
		<if test="archiveTime == null ">
			now()
		</if>
		)
	</insert>

	<delete id="removeBigDiseaseDetail" >
		delete from CLMS_big_disease_detail where ID_AHCS_BIG_DISEASE=#{bigDiseaseId}
	</delete>

	<select id="getBigDiseaseDetailList" parameterType="string" resultType="string">
		select t.BIG_DISEASE_DETAIL_CODE
		from CLMS_big_disease_detail t
		where t.id_ahcs_big_disease = #{bigDiseaseId}
	</select>

	<select id="getBigDiseaseDetailDTOList" parameterType="string" resultMap="result">
		select bdd.BIG_DISEASE_DETAIL_CODE,
		bdd.ID_AHCS_BIG_DISEASE,
		bdd.STATUS,
		bdd.ARCHIVE_TIME
		from CLMS_big_disease_detail bdd
		where bdd.id_ahcs_big_disease = #{bigDiseaseId}
	</select>

	<!-- 新增多条 重大疾病详细-->
	<insert id="addBigDiseaseDetailList">
		insert into CLMS_big_disease_detail
		(CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_AHCS_BIG_DISEASE,
		BIG_DISEASE_DETAIL_CODE,
		STATUS,
		archive_time)
		<foreach collection="bigDiseaseDetailList" index="index" item="item" open="(" close=")" separator="union all">
			select #{userId},
			now(),
			#{userId},
			now(),
			#{idBigDisease,jdbcType=VARCHAR},
			#{item.bigDiseaseDetailCode,jdbcType=VARCHAR},
			#{item.status,jdbcType=VARCHAR} ,
			<if test="item.archiveTime != null ">
				#{item.archiveTime,jdbcType=TIMESTAMP}
			</if>
			<if test="item.archiveTime == null ">
				now()
			</if>
		</foreach>
	</insert>

</mapper>