<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.duty.AirportInfoMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.AirportInfoDTO" id="ResultAirportInfoMap">
		<result column="ID_AHCS_AIRPORT_INFO" property="idAhcsAirportInfo"/>
		<result column="AIRPORT_CODE" property="airportCode"/>
		<result column="AIRPORT_NAME" property="airportName"/>
		<result column="CITY_CODE" property="cityCode"/>
		<result column="CITY_NAME" property="cityName"/>
		<result column="PROVINCE_CODE" property="provinceCode"/>
		<result column="PROVINCE_NAME" property="provinceName"/>
		<result column="AIRPORT_TYPE" property="airportType"/>
		<result column="CREATED_BY" property="createdBy"/>
		<result column="CREATED_DATE" property="createdDate"/>
		<result column="UPDATED_BY" property="updatedBy"/>
		<result column="UPDATED_DATE" property="updatedDate"/>
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.vo.duty.AirportInfoVO" id="ResultAirportInfoParamMap">
		<result column="ID_AHCS_AIRPORT_INFO" property="idAhcsAirportInfo"/>
		<result column="AIRPORT_CODE" property="airportCode"/>
		<result column="AIRPORT_NAME" property="airportName"/>
		<result column="CITY_CODE" property="cityCode"/>
		<result column="CITY_NAME" property="cityName"/>
		<result column="PROVINCE_CODE" property="provinceCode"/>
		<result column="PROVINCE_NAME" property="provinceName"/>
		<result column="AIRPORT_TYPE" property="airportType"/>
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.vo.duty.ProvinceVO" id="ProvinceMap">
		<result column="PROVINCE_CODE" property="provinceCode"/>
		<result column="PROVINCE_NAME" property="provinceName"/>
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.vo.duty.CityVO" id="CityMap">
		<result column="CITY_CODE" property="cityCode"/>
		<result column="CITY_NAME" property="cityName"/>
	</resultMap>
	
	<insert id="saveAirportInfo" parameterType="com.paic.ncbs.claim.model.dto.duty.AirportInfoDTO">
		INSERT INTO CLMS_AIRPORT_INFO (
		 	CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			AIRPORT_CODE,
			AIRPORT_NAME,
			CITY_CODE,
			CITY_NAME,
			PROVINCE_CODE,
			PROVINCE_NAME,
			AIRPORT_TYPE
		) values(
			#{createdBy,jdbcType=VARCHAR},
			SYSDATE,
			#{updatedBy,jdbcType=VARCHAR},
			SYSDATE,
			#{airportCode,jdbcType=VARCHAR},
			#{airportName,jdbcType=VARCHAR},
			#{cityCode,jdbcType=VARCHAR},
			#{cityName,jdbcType=VARCHAR},
			#{provinceCode,jdbcType=VARCHAR},
			#{provinceName,jdbcType=VARCHAR},
			#{airportType,jdbcType=VARCHAR}
		)
	</insert>

	<select id="getAirportInfoAll" resultMap="ResultAirportInfoMap">		
		SELECT T.id_ahcs_airport_info, 
		       T.airport_code, 
		       T.airport_name, 
		       T.city_code, 
		       T.city_name, 
		       T.province_code, 
		       T.province_name, 
		       T.airport_type 
		FROM   CLMS_AIRPORT_INFO T 
    </select>	
    
	<select id="getAirporCodeByName" resultType="string">		
		SELECT T.airport_code FROM CLMS_AIRPORT_INFO T where t.airport_name like '%'||#{airporName}||'%'  
    </select>	
	
	<select id="getAirportInfoByType" resultMap="ResultAirportInfoMap">		
		SELECT T.id_ahcs_airport_info, 
		       T.airport_code, 
		       T.airport_name, 
		       T.city_code, 
		       T.city_name, 
		       T.province_code, 
		       T.province_name, 
		       T.airport_type 
		FROM   CLMS_AIRPORT_INFO T 
		WHERE 1=1
		<if test = " airportType != null and airportType != '' ">
			AND T.airport_type = #{airportType,jdbcType=VARCHAR}
		</if>
    </select>	

	<select id="getAirportInfoListByAirportCode" resultMap="ResultAirportInfoMap">		
		SELECT T.id_ahcs_airport_info, 
		       T.airport_code, 
		       T.airport_name, 
		       T.city_code, 
		       T.city_name, 
		       T.province_code, 
		       T.province_name, 
		       T.airport_type 
		FROM   CLMS_AIRPORT_INFO T 
		WHERE T.airport_code IN 
          	  <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
				      #{item,jdbcType=VARCHAR}
			  </foreach>
			  AND T.airport_type = '0'
    </select>

	<select id="getAirportInfoByAirportCode" resultMap="ResultAirportInfoMap">		
		SELECT T.id_ahcs_airport_info, 
		       T.airport_code, 
		       T.airport_name, 
		       T.city_code, 
		       T.city_name, 
		       T.province_code, 
		       T.province_name, 
		       T.airport_type 
		FROM   CLMS_AIRPORT_INFO T 
		WHERE T.airport_code = #{airportCode,jdbcType=VARCHAR}
		<if test=" airportType != null and airportType != '' ">
			 AND T.AIRPORT_TYPE=#{airportType,jdbcType=VARCHAR}
		</if>
    </select>
    
	<select id="checkAirportInfoExist" resultMap="ResultAirportInfoMap">		
		SELECT T.id_ahcs_airport_info, 
		       T.airport_code, 
		       T.airport_name, 
		       T.city_code, 
		       T.city_name, 
		       T.province_code, 
		       T.province_name, 
		       T.airport_type 
		FROM   CLMS_AIRPORT_INFO T 
		WHERE T.AIRPORT_CODE = #{airportCode,jdbcType=VARCHAR}
		<if test=" idAhcsAirportInfo != null and idAhcsAirportInfo != '' ">
			  AND T.ID_AHCS_AIRPORT_INFO NOT IN (#{idAhcsAirportInfo,jdbcType=VARCHAR})
		</if>
    </select>
	
	<select id="getAirportInfoListByCode"  resultMap="ResultAirportInfoMap">			
		SELECT T.id_ahcs_airport_info, 
		       T.airport_code, 
		       T.airport_name, 
		       T.city_code, 
		       T.city_name, 
		       T.province_code, 
		       T.province_name, 
		       T.airport_type 
		FROM   CLMS_AIRPORT_INFO T 
		WHERE T.province_code = #{provinceCode,jdbcType=VARCHAR}
		AND T.city_code =  #{cityCode,jdbcType=VARCHAR}
		and t.AIRPORT_TYPE=#{airportType,jdbcType=VARCHAR}
    </select>
    
    <select id="getAirportInfoByAirportName" resultMap="ResultAirportInfoMap">		
		SELECT T.id_ahcs_airport_info, 
		       T.airport_code, 
		       T.airport_name, 
		       T.city_code, 
		       T.city_name, 
		       T.province_code, 
		       T.province_name, 
		       T.airport_type 
		FROM   CLMS_AIRPORT_INFO T 
		WHERE T.airport_name  like '%' || #{airportName} || '%'
		and t.AIRPORT_TYPE=#{airportType,jdbcType=VARCHAR}
    </select>
	
	<select id="getProvinceVO" resultMap="ProvinceMap">		
	     SELECT T.PROVINCE_CODE, T.PROVINCE_NAME
	     FROM   CLMS_AIRPORT_INFO T
	     WHERE  T.AIRPORT_TYPE = #{airportType,jdbcType=VARCHAR}
	     GROUP  BY T.PROVINCE_CODE, T.PROVINCE_NAME
	     ORDER  BY T.PROVINCE_CODE ASC
    </select>
    
   	<select id="getCity" resultMap="CityMap">		
	    SELECT T.CITY_CODE, T.CITY_NAME
	    FROM   CLMS_AIRPORT_INFO T
	    WHERE  T.PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR}
	    GROUP  BY T.CITY_CODE, T.CITY_NAME
	    ORDER  BY T.CITY_CODE
    </select>

    <select id="getAirportInfoListByParma" resultMap="ResultAirportInfoParamMap">		
		SELECT T.ID_AHCS_AIRPORT_INFO,
		       T.AIRPORT_CODE,
		       T.AIRPORT_NAME,
		       T.CITY_CODE,
		       T.CITY_NAME,
		       T.PROVINCE_CODE,
		       T.PROVINCE_NAME,
			   T.AIRPORT_TYPE
		FROM   CLMS_AIRPORT_INFO T
		WHERE 1=1
		<if test="queryParam != '' and queryParam !=null">
			AND    T.AIRPORT_NAME  LIKE '%' || #{queryParam,jdbcType=VARCHAR} || '%'
			OR     T.AIRPORT_CODE  LIKE UPPER('%' || #{queryParam,jdbcType=VARCHAR} || '%')
			OR     T.PROVINCE_NAME LIKE '%' || #{queryParam,jdbcType=VARCHAR} || '%'
			OR     T.CITY_NAME LIKE '%' || #{queryParam,jdbcType=VARCHAR} || '%'
		</if>
		ORDER BY T.UPDATED_DATE DESC
    </select>
    
    <delete id="deleteAirportInfo">
    
    	delete from CLMS_AIRPORT_INFO T WHERE T.ID_AHCS_AIRPORT_INFO = #{idAhcsAirportInfo,jdbcType=VARCHAR}
    	
    </delete>

    <select id="getAirportInfoById" resultMap="ResultAirportInfoMap">	 	
	    SELECT T.ID_AHCS_AIRPORT_INFO,
	           T.AIRPORT_CODE,
	           T.AIRPORT_NAME,
	           T.CITY_CODE,
	           T.CITY_NAME,
	           T.PROVINCE_CODE,
	           T.PROVINCE_NAME,
	           T.AIRPORT_TYPE,
		       T.UPDATED_BY,
		       T.UPDATED_DATE,
		       T.CREATED_BY,
		       T.CREATED_DATE
	    FROM   CLMS_AIRPORT_INFO T
		WHERE T.ID_AHCS_AIRPORT_INFO = #{idAhcsAirportInfo,jdbcType=VARCHAR}
    </select>

	<select id="getAirportTypeByCode" resultType="java.lang.String">
		SELECT AIRPORT_TYPE FROM CLMS_AIRPORT_INFO WHERE  AIRPORT_CODE= #{airportCode}
	</select>
    
    <update id="updateAirportInfoById">
	    UPDATE CLMS_AIRPORT_INFO T
			SET T.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
			<if test="airportCode != '' and airportCode !=null">
				T.AIRPORT_CODE  = #{airportCode,jdbcType=VARCHAR},
			</if>
			<if test="airportName != '' and airportName !=null">
				  T.AIRPORT_NAME  = #{airportName,jdbcType=VARCHAR},
			</if>
			<if test="cityCode != '' and cityCode !=null">
				 T.CITY_CODE     = #{cityCode,jdbcType=VARCHAR},
			</if>
			<if test="cityName != '' and cityName !=null">
				 T.CITY_NAME     = #{cityName,jdbcType=VARCHAR},
			</if>
			<if test="provinceCode != '' and provinceCode !=null">
				T.PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR},
			</if>
			<if test="provinceName != '' and provinceName !=null">
				T.PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
			</if>
			<if test="airportType != '' and airportType !=null">
				T.AIRPORT_TYPE  = #{airportType,jdbcType=VARCHAR},
			</if>
			T.UPDATED_DATE  = sysdate    
		WHERE  T.ID_AHCS_AIRPORT_INFO = #{idAhcsAirportInfo,jdbcType=VARCHAR}
	 </update>
	
	<select id="getAirportAddrByCode" resultMap="ResultAirportInfoMap">
		SELECT AIRPORT_TYPE,
		       PROVINCE_CODE 
		  FROM CLMS_AIRPORT_INFO 
		 WHERE AIRPORT_CODE= #{airportCode}
	</select>
	
</mapper>