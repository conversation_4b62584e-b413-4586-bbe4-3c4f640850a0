<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO" id="result">
        <id property="idAhcsDutyDetailPay" column="ID_AHCS_DUTY_DETAIL_PAY"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="idAhcsDutyPay" column="ID_AHCS_DUTY_PAY"/>
        <result property="dutyDetailCode" column="DUTY_DETAIL_CODE"/>
        <result property="dutyDetailName" column="DUTY_DETAIL_NAME"/>
        <result property="dutyDetailType" column="DUTY_DETAIL_TYPE"/>
        <result property="settleAmount" column="SETTLE_AMOUNT"/>
        <result property="autoSettleAmount" column="AUTO_SETTLE_AMOUNT"/>
        <result property="remitAmount" column="REMIT_AMOUNT"/>
        <result property="payProportion" column="PAY_PROPORTION"/>
        <result property="thirdPartyPayment" column="PREPAID_AMOUNT"/>
        <result property="reasonableAmount" column="REASONABLE_AMOUNT"/>
        <result property="reasonableAmountType" column="REASONABLE_AMOUNT_TYPE"/>
        <result property="baseAmountPay" column="BASE_AMOUNT_PAY"/>
        <result property="remitDays" column="REMIT_DAYS"/>
        <result property="allowanceAmount" column="ALLOWANCE_AMOUNT"/>
        <result property="allowanceDays" column="ALLOWANCE_DAYS"/>
        <result property="disabilityRate" column="DISABILITY_RATE"/>
        <result property="detailLimitAmount" column="DETAIL_LIMIT_AMOUNT"/>
        <result property="claimType" column="CLAIM_TYPE"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="idAhcsBatch" column="ID_AHCS_BATCH"/>
        <result property="accommodationAmount" column="ACCOMMODATION_AMOUNT"/>
        <result property="protocolAmount" column="PROTOCOL_AMOUNT"/>
        <result property="indemnityMode" column="INDEMNITY_MODE"/>
        <result property="autoAmountRecord" column="AUTO_AMOUNT_RECORD"/>
        <result property="idPlyRiskProperty" column="id_ply_risk_property"/>

    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.vo.duty.DutyDetailConfigVO" id="DutyDetailBashConfigMap">
        <id property="idAhcsDutyDetailConfig" column="ID_AHCS_DUTY_DETAIL_CONFIG"/>
        <result property="insuredApplyStatus" column="INSURED_APPLY_STATUS"/>
        <result property="accidentType" column="ACCIDENT_TYPE"/>
        <result property="therapyType" column="THERAPY_TYPE"/>
        <result property="trafficAccidentType" column="TRAFFIC_ACCIDENT_TYPE"/>
        <result column="detail_element1_code" property="detailElement1Code"/>
        <result column="formula_group_code" property="formulaGroupCode"/>
        <result property="planElementCode" column="PLAN_ELEMENT_CODE"/>
    </resultMap>
    <select id="getDetailBaseConfigByCode" resultMap="DutyDetailBashConfigMap">
        select
        ID_AHCS_DUTY_DETAIL_CONFIG,
        INSURED_APPLY_STATUS,
        ACCIDENT_TYPE,
        THERAPY_TYPE,
        TRAFFIC_ACCIDENT_TYPE,
        DETAIL_ELEMENT1_CODE
        from CLMS_DUTY_DETAIL_CONFIG g
        where PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        and DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        and DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR}
    </select>

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO" id="DutyDetail">
        <id property="idAhcsDutyDetailPay" column="ID_AHCS_DUTY_DETAIL_PAY"/>
        <result property="idAhcsDutyPay" column="ID_AHCS_DUTY_PAY"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyDetailCode" column="DUTY_DETAIL_CODE"/>
        <result property="dutyDetailName" column="DUTY_DETAIL_NAME"/>
        <result property="dutyDetailType" column="DUTY_DETAIL_TYPE"/>
        <result property="detailLimitAmount" column="DETAIL_LIMIT_AMOUNT"/>
        <result property="settleAmount" column="SETTLE_AMOUNT"/>
        <result property="autoSettleAmount" column="AUTO_SETTLE_AMOUNT"/>
        <result property="baseAmountPay" column="BASE_AMOUNT_PAY"/>
        <result property="allowanceAmount" column="ALLOWANCE_AMOUNT"/>
        <result property="allowanceDays" column="ALLOWANCE_DAYS"/>
        <result property="disabilityRate" column="DISABILITY_RATE"/>
        <result property="payProportion" column="PAY_PROPORTION"/>
        <result property="remitAmount" column="REMIT_AMOUNT"/>
        <result property="remitDays" column="REMIT_DAYS"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="autoAmountRecord" column="AUTO_AMOUNT_RECORD"/>
        <result property="fixedLimit" column="FIXED_LIMIT"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO" id="detailGroupAmount">
        <result property="settleAmount" column="SETTLE_AMOUNT"/>
        <result property="dutyDetailCode" column="DUTY_DETAIL_CODE"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="dutyCode" column="DUTY_CODE"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.HistoryPayInfoDTO" id="dutyDetailHistoryPayInfo">
        <result property="policyNo" column="POLICY_NO"/>
        <result property="policyCerNo" column="POLICY_CER_NO"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="orgPlanCode" column="ORG_PLAN_CODE"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="orgDutyCode" column="ORG_DUTY_CODE"/>
        <result property="dutyDetailCode" column="DUTY_DETAIL_CODE"/>
        <result property="orgDutyDetailCode" column="ORG_DUTY_DETAIL_CODE"/>
        <result property="dutyBaseAmount" column="DUTY_AMOUNT"/>
        <result property="dutyHistoryPay" column="duty_history_pay"/>
        <result property="dutyDetailBaseAmount" column="DUTY_DETAIL_AMOUNT"/>
        <result property="dutyDetailHistoryPay" column="detail_history_pay"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO" id="dutyDetailResult">
        <result property="dutyDetailCode" column="DUTY_DETAIL_CODE"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertDutyDetailPayList" parameterType="java.util.List">
        insert into CLMS_DUTY_DETAIL_PAY (
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_DUTY_DETAIL_PAY,
        ID_AHCS_DUTY_PAY,
        DUTY_DETAIL_CODE,
        DUTY_DETAIL_NAME,
        DUTY_DETAIL_TYPE,
        BASE_AMOUNT_PAY,
        SETTLE_AMOUNT,
        AUTO_SETTLE_AMOUNT,
        REMIT_AMOUNT,
        REMIT_DAYS,
        PAY_PROPORTION,
        PREPAID_AMOUNT,
        REASONABLE_AMOUNT,
        REASONABLE_AMOUNT_TYPE,
        ALLOWANCE_AMOUNT,
        ALLOWANCE_DAYS,
        DISABILITY_RATE,
        DETAIL_LIMIT_AMOUNT,
        CLAIM_TYPE,
        POLICY_NO,
        PLAN_CODE,
        ID_AHCS_BATCH,
        CASE_NO,
        CASE_TIMES,
        DUTY_CODE,
        ACCOMMODATION_AMOUNT,
        PROTOCOL_AMOUNT,
        INDEMNITY_MODE,
        AUTO_AMOUNT_RECORD,
        ARCHIVE_TIME,
        IS_EFFECTIVE,
        FIXED_LIMIT,
        id_ply_risk_property
        )
        <foreach collection="list" item="item" index="index"
                 separator=" union all ">
            SELECT
            #{item.createdBy,jdbcType=VARCHAR},
            sysdate(),
            #{item.updatedBy,jdbcType=VARCHAR},
            sysdate(),
            left(hex(uuid()),32),
            #{item.idAhcsDutyPay,jdbcType=VARCHAR},
            #{item.dutyDetailCode,jdbcType=VARCHAR},
            #{item.dutyDetailName,jdbcType=VARCHAR},
            #{item.dutyDetailType,jdbcType=VARCHAR},
            #{item.baseAmountPay,jdbcType=NUMERIC},
            #{item.settleAmount,jdbcType=NUMERIC},
            #{item.autoSettleAmount,jdbcType=NUMERIC},
            #{item.remitAmount,jdbcType=NUMERIC},
            #{item.remitDays,jdbcType=NUMERIC},
            #{item.payProportion,jdbcType=NUMERIC},
            #{item.thirdPartyPayment,jdbcType=NUMERIC},
            #{item.reasonableAmount,jdbcType=NUMERIC},
            #{item.reasonableAmountType,jdbcType=VARCHAR},
            #{item.allowanceAmount,jdbcType=NUMERIC},
            #{item.allowanceDays,jdbcType=NUMERIC},
            #{item.disabilityRate,jdbcType=NUMERIC},
            #{item.detailLimitAmount,jdbcType=NUMERIC},
            #{item.claimType,jdbcType=VARCHAR},
            #{item.policyNo,jdbcType=VARCHAR},
            #{item.planCode,jdbcType=VARCHAR},
            #{item.idAhcsBatch,jdbcType=VARCHAR},
            #{item.caseNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=INTEGER},
            #{item.dutyCode,jdbcType=VARCHAR},
            #{item.accommodationAmount,jdbcType=NUMERIC},
            #{item.protocolAmount,jdbcType=NUMERIC},
            #{item.indemnityMode,jdbcType=VARCHAR},
            #{item.autoAmountRecord,jdbcType=NUMERIC},
            sysdate() ,
            'Y',
            #{item.fixedLimit,jdbcType=NUMERIC},
            #{item.idPlyRiskProperty,jdbcType=VARCHAR}
            FROM DUAL
        </foreach>
    </insert>

    <!-- 根据责任id查询 -->
    <select id="selectByAhcsDutyPayId" parameterType="java.util.Map" resultMap="result">
        select
        ddp.CREATED_BY,
        ddp.CREATED_DATE,
        ddp.UPDATED_BY,
        ddp.UPDATED_DATE,
        ddp.ID_AHCS_DUTY_DETAIL_PAY,
        ddp.ID_AHCS_DUTY_PAY,
        ddp.DUTY_DETAIL_CODE,
        ddp.DUTY_DETAIL_NAME,
        ddp.DUTY_DETAIL_TYPE ,
        ddp.SETTLE_AMOUNT,
        ddp.AUTO_SETTLE_AMOUNT,
        ddp.REMIT_AMOUNT,
        ddp.REMIT_DAYS,
        ddp.PAY_PROPORTION,
        ddp.REASONABLE_AMOUNT,
        ddp.REASONABLE_AMOUNT_TYPE,
        ddp.BILL_AMOUNT,
        ddp.PARTIAL_DEDUCTIBLE,
        ddp.DEDUCTIBLE_AMOUNT,
        ddp.IMMODERATE_AMOUNT,
        ddp.PREPAID_AMOUNT,
        ddp.TREATMENT_DAYS,
        ddp.ALLOWANCE_AMOUNT,
        ddp.ALLOWANCE_DAYS,
        ddp.DISABILITY_RATE,
        ddp.DETAIL_LIMIT_AMOUNT,
        ddp.CLAIM_TYPE,
        ddp.POLICY_NO,
        ddp.PLAN_CODE,
        ddp.ID_AHCS_BATCH,
        ddp.CASE_NO,
        ddp.CASE_TIMES,
        ddp.DUTY_CODE,
        ddp.BASE_AMOUNT_PAY,
        ddp.ACCOMMODATION_AMOUNT,
        ddp.PROTOCOL_AMOUNT,
        ddp.INDEMNITY_MODE,
        ddp.AUTO_AMOUNT_RECORD,
        ddp.id_ply_risk_property
        from CLMS_DUTY_DETAIL_PAY ddp
        where
        ID_AHCS_DUTY_PAY=#{idAhcsDutyPay}
        AND ddp.IS_EFFECTIVE = 'Y'
        ORDER BY DUTY_DETAIL_CODE

    </select>

    <select id="getDutyDetailListByIdDuty" parameterType="string" resultMap="dutyDetailResult">
        select t.ORG_DUTY_DETAIL_CODE,
        t.DUTY_DETAIL_CODE
        from CLMS_POLICY_DUTY_DETAIL t
        where t.ID_AHCS_POLICY_DUTY=#{idAhcsDutyPay,jdbcType = VARCHAR}
    </select>

    <!-- 根据责任id查询 抄单查询 -->
    <select id="selectDutyDetail" resultMap="DutyDetail">
        select
        CREATED_BY,
        UPDATED_BY,
        ID_AHCS_POLICY_DUTY_DETAIL ID_AHCS_DUTY_DETAIL_PAY,
        ID_AHCS_POLICY_DUTY ID_AHCS_DUTY_PAY,
        DUTY_DETAIL_CODE,
        DUTY_DETAIL_NAME,
        DUTY_DETAIL_TYPE,
        DETAIL_LIMIT_AMOUNT,
        DUTY_AMOUNT BASE_AMOUNT_PAY,
        NOCLAIM_AMOUNT REMIT_AMOUNT,
        NOCLAIM_DAYS REMIT_DAYS,
        ALLOWANCE_EVERYDAY ALLOWANCE_AMOUNT,
        CLAIM_PROPORTION PAY_PROPORTION,
        OBSERVED_DAYS,
        FIXED_LIMIT
        from CLMS_POLICY_DUTY_DETAIL
        where ID_AHCS_POLICY_DUTY=#{idAhcsDutyPay}
    </select>

    <!-- 根据id查询 -->
    <select id="getById" parameterType="java.lang.String" resultMap="result">
        select CREATED_BY ,
        CREATED_DATE ,
        UPDATED_BY ,
        UPDATED_DATE ,
        ID_AHCS_DUTY_DETAIL_PAY,
        ID_AHCS_DUTY_PAY ,
        POLICY_NO ,
        CASE_NO ,
        CASE_TIMES ,
        PLAN_CODE ,
        DUTY_CODE ,
        BASE_AMOUNT_PAY ,
        CLAIM_TYPE ,
        DUTY_DETAIL_CODE ,
        DUTY_DETAIL_NAME ,
        SETTLE_AMOUNT,
        AUTO_SETTLE_AMOUNT ,
        REMIT_AMOUNT ,
        PAY_PROPORTION ,
        REASONABLE_AMOUNT ,
        REASONABLE_AMOUNT_TYPE,
        TREATMENT_DAYS ,
        REMIT_DAYS ,
        ALLOWANCE_AMOUNT,
        SUB_TIMES ,
        ID_AHCS_BATCH ,
        PROTOCOL_AMOUNT ,
        ACCOMMODATION_AMOUNT ,
        INDEMNITY_MODE
        from CLMS_DUTY_DETAIL_PAY where ID_AHCS_DUTY_DETAIL_PAY = #{id} AND IS_EFFECTIVE = 'Y'
    </select>

    <!-- 批量更新 -->
    <update id="updateDutyDetailPayList" parameterType="java.util.List">

        update CLMS_DUTY_DETAIL_PAY
        set
        <if test="updatedBy != null and updatedBy != '' ">
            UPDATED_BY = #{updatedBy},
        </if>
        <if test="dutyDetailCode != null and dutyDetailCode != '' ">
            DUTY_DETAIL_CODE = #{dutyDetailCode},
        </if>
        <if test="dutyDetailName != null and dutyDetailName != '' ">
            DUTY_DETAIL_NAME = #{dutyDetailName},
        </if>
        <if test="remitAmount != null ">
            REMIT_AMOUNT = #{remitAmount},
        </if>
        <if test="remitDays != null ">
            REMIT_DAYS = #{remitDays},
        </if>
        <if test="payProportion != null ">
            PAY_PROPORTION = #{payProportion},
        </if>
        <if test="reasonableAmount != null ">
            REASONABLE_AMOUNT = #{reasonableAmount},
        </if>
        <if test="reasonableAmountType != null and reasonableAmountType != '' ">
            REASONABLE_AMOUNT_TYPE = #{reasonableAmountType},
        </if>
        <if test="allowanceAmount != null ">
            ALLOWANCE_AMOUNT = #{allowanceAmount},
        </if>
        <if test="allowanceDays != null ">
            ALLOWANCE_DAYS = #{allowanceDays},
        </if>
        <if test="thirdPartyPayment != null ">
            PREPAID_AMOUNT = #{thirdPartyPayment},
        </if>
        <if test="claimType != null ">
            CLAIM_TYPE = #{claimType},
        </if>
        <if test="policyNo != null ">
            POLICY_NO = #{policyNo},
        </if>
        <if test="planCode != null ">
            PLAN_CODE = #{planCode},
        </if>
        <if test="idAhcsBatch != null ">
            ID_AHCS_BATCH = #{idAhcsBatch},
        </if>
        <if test="dutyCode != null ">
            DUTY_CODE = #{dutyCode},
        </if>
        <if test="caseTimes != null ">
            CASE_TIMES = #{caseTimes},
        </if>
        <if test="caseNo != null ">
            CASE_NO = #{caseNo},
        </if>
        <if test="disabilityRate != null ">
            DISABILITY_RATE = #{disabilityRate},
        </if>
        <if test="disabilityRate != null ">
            DETAIL_LIMIT_AMOUNT = #{detailLimitAmount},
        </if>
        SETTLE_AMOUNT = #{settleAmount,jdbcType=NUMERIC},
        AUTO_SETTLE_AMOUNT = #{autoSettleAmount,jdbcType=NUMERIC},
        ACCOMMODATION_AMOUNT = #{accommodationAmount,jdbcType=NUMERIC},
        PROTOCOL_AMOUNT = #{protocolAmount,jdbcType=NUMERIC},
        AUTO_AMOUNT_RECORD = #{autoAmountRecord,jdbcType=NUMERIC},
        INDEMNITY_MODE =  #{indemnityMode,jdbcType=VARCHAR},
        UPDATED_DATE = NOW()
        WHERE ID_AHCS_DUTY_DETAIL_PAY=#{idAhcsDutyDetailPay} AND IS_EFFECTIVE = 'Y'
    </update>

    <select id="getDutyDetailHistoryPay" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam"
            resultType="java.math.BigDecimal">
        SELECT
        ifnull(SUM(IF(A.SETTLE_AMOUNT &gt; 0,A.SETTLE_AMOUNT,A.AUTO_SETTLE_AMOUNT)),0)
        FROM CLMS_DUTY_DETAIL_PAY A,
        CLM_CASE_BASE C,
        CLMS_POLICY_CLAIM_CASE B
        WHERE A.CASE_TIMES=C.CASE_TIMES
        AND A.CASE_NO=C.CASE_NO
        AND A.CASE_NO = B.CASE_NO
        AND A.DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR}
        AND A.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        AND A.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        AND A.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        <if test="insuredCode != null ">
            AND B.INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
        </if>
        <if test="subpolicyNo != null ">
            AND B.SUBPOLICY_NO = #{subpolicyNo,jdbcType=VARCHAR}
        </if>
        AND (C.CASE_STATUS = '0' or C.CASE_STATUS = '5')
        AND A.CLAIM_TYPE = '1'
        AND A.IS_EFFECTIVE = 'Y'
    </select>

    <select id="getDutyDetailBaseAmount" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam"
            resultType="java.math.BigDecimal">
        SELECT PDD.DUTY_AMOUNT
        FROM CLMS_POLICY_DUTY_DETAIL PDD,
        CLMS_POLICY_DUTY PD,
        CLMS_POLICY_PLAN PP,
        CLMS_POLICY_INFO PI
        WHERE PI.ID_AHCS_POLICY_INFO = PP.ID_AHCS_POLICY_INFO
        AND PD.ID_AHCS_POLICY_PLAN = PP.ID_AHCS_POLICY_PLAN
        AND PD.ID_AHCS_POLICY_DUTY = PDD.ID_AHCS_POLICY_DUTY
        AND PDD.DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR}
        AND PD.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        AND PP.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        AND PI.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        AND PI.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <!-- 批量删除-->
    <delete id="deleteByBatchId" parameterType="java.lang.String">
        delete from CLMS_DUTY_DETAIL_PAY where ID_AHCS_BATCH = #{idAhcsBatch,jdbcType=VARCHAR} and
        CLAIM_TYPE=#{claimType,jdbcType=VARCHAR}
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO">
        UPDATE
        CLMS_DUTY_DETAIL_PAY
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = SYSDATE(),
        IS_EFFECTIVE = 'N'
        WHERE
        ID_AHCS_BATCH = #{idAhcsBatch}
        AND CLAIM_TYPE = #{claimType}
        AND IS_EFFECTIVE = 'Y'
    </update>

    <!-- 查询事故者现状 -->
    <select id="getInsuredStatus" parameterType="java.lang.String" resultType="java.lang.String">
        select DISTINCT INSURED_APPLY_STATUS from CLMS_DUTY_DETAIL_CONFIG
        where DUTY_DETAIL_CODE in
        (select DUTY_DETAIL_CODE FROM CLMS_DUTY_DETAIL_PAY WHERE DUTY_CODE = #{dutyCode} AND IS_EFFECTIVE = 'Y')
    </select>

    <!--查詢重开前所有责任明細赔款金额总和并且按险种,责任,责任明细分组 -->
    <select id="getDetailGroupAmount" resultMap="detailGroupAmount">
        select plan_code,duty_code,duty_detail_code,ifnull(sum(ifnull(ddp.auto_settle_amount, 0) +
        ifnull(ddp.settle_amount,0)),0) settle_amount
        from CLMS_duty_detail_pay ddp
        where ddp.case_no in (select pp.case_no
        from clm_policy_pay pp
        where pp.report_no = #{reportNo,jdbcType=VARCHAR}
        and pp.case_times <![CDATA[<]]>#{caseTimes,jdbcType=NUMERIC}) and ddp.case_times <![CDATA[<]]>
        #{caseTimes,jdbcType=NUMERIC} AND ddp.IS_EFFECTIVE = 'Y'
        group by ddp.plan_code ,ddp.duty_code, ddp.duty_detail_code
    </select>

    <select id="getDutyDetailTypeCode" resultType="java.lang.String">
        SELECT DUTY_DETAIL_TYPE_CODE
        FROM CLMS_DUTY_DETAIL_CONFIG DDT
        WHERE PLAN_CODE = #{planCode, jdbcType = VARCHAR}
        AND DUTY_CODE = #{dutyCode, jdbcType = VARCHAR}
        AND DUTY_DETAIL_CODE = #{dutyDetailCode, jdbcType = VARCHAR}
    </select>

    <!--查询本责任明细责历史已赔付案件的金额,且赔付结论为赔付-->
    <select id="getHistoryCaseDetails" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam"
            resultMap="DutyDetail">
        SELECT
        (select dsa.settle_attr_json from
        CLMS_detail_settle_attr dsa where
        dsa.case_no=A.case_no and
        dsa.case_times=A.case_times and dsa.policy_no=A.policy_no and
        dsa.plan_code=A.plan_code and dsa.duty_code=A.duty_code and
        dsa.duty_detail_code=A.duty_detail_code) SETTLE_ATTR_JSON,
        A.CASE_NO,A.CASE_TIMES,A.POLICY_NO,A.PLAN_CODE,A.DUTY_CODE,A.DUTY_DETAIL_CODE,A.SETTLE_AMOUNT,A.AUTO_SETTLE_AMOUNT
        FROM CLMS_DUTY_DETAIL_PAY A,
        CLM_CASE_BASE C,
        CLMS_POLICY_CLAIM_CASE B
        WHERE A.CASE_TIMES=C.CASE_TIMES
        AND A.CASE_NO=C.CASE_NO
        AND A.CASE_NO = B.CASE_NO
        AND A.DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR}
        AND A.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        AND A.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        AND A.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        <if test="partyNo != null and partyNo != '' ">
            AND B.PARTY_NO = #{partyNo,jdbcType=VARCHAR}
        </if>
        AND C.CASE_STATUS = '0'
        AND A.CLAIM_TYPE = '1'
        AND A.IS_EFFECTIVE = 'Y'
        AND EXISTS (SELECT 1
        FROM CLMS_CASE_PROCESS P
        WHERE P.REPORT_NO = B.REPORT_NO
        AND P.CASE_TIMES = A.CASE_TIMES
        AND P.PROCESS_STATUS IN ( '06','07'))
        ORDER BY A.CREATED_DATE DESC
    </select>

    <select id="getDutyHistoryPayAmount" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(IFNULL(a.DUTY_PAY_AMOUNT, 0)), 0)
        FROM CLM_PLAN_DUTY_PAY a
            JOIN CLMS_POLICY_CLAIM_CASE b ON a.CASE_NO = b.CASE_NO
            JOIN CLMS_CASE_PROCESS c ON b.REPORT_NO= c.REPORT_NO AND a.CASE_TIMES = c.CASE_TIMES
        WHERE
        (a.CASE_TIMES,a.CASE_NO) IN
            (SELECT MAX(a.CASE_TIMES) CASE_TIMES, a.CASE_NO
             FROM CLM_PLAN_DUTY_PAY a
              JOIN CLMS_POLICY_CLAIM_CASE b ON a.CASE_NO = b.CASE_NO
              JOIN CLMS_CASE_PROCESS c ON b.REPORT_NO = c.REPORT_NO AND a.CASE_TIMES = c.CASE_TIMES
            WHERE a.CLAIM_TYPE = '1'
              AND c.PROCESS_STATUS = '05'
            <if test="insuredCode != null">
                AND b.INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
            </if>
            <if test="dutyCode != null">
                AND a.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
            </if>
            <if test="dutyCodeList != null and dutyCodeList.size() > 0">
                AND a.DUTY_CODE IN
                <foreach collection="dutyCodeList" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="planCode != null and planCode != ''">
                AND a.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
            </if>
            AND b.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
            <if test="caseNo != null and caseNo != ''">
                AND a.CASE_NO = #{caseNo,jdbcType=VARCHAR}
            </if>
            <if test="caseTimes != null">
                AND a.CASE_TIMES = #{caseTimes,jdbcType=VARCHAR}
            </if>
            GROUP BY a.CASE_NO)
        AND a.CLAIM_TYPE = '1'
        AND c.PROCESS_STATUS = '05'
        <if test="dutyCode != null">
            AND a.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        </if>
        <if test="dutyCodeList != null and dutyCodeList.size() > 0">
            AND a.DUTY_CODE IN
            <foreach collection="dutyCodeList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="insuredCode != null">
            AND b.INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
        </if>
        <if test="planCode != null and planCode != ''">
            AND a.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        </if>
        AND b.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        <if test="selectScene == null">
            AND b.REPORT_NO != #{reportNo,jdbcType=VARCHAR}
        </if>
        <if test="caseNo != null and caseNo != ''">
            AND a.CASE_NO = #{caseNo,jdbcType=VARCHAR}
        </if>
        <if test="caseTimes != null">
            AND a.CASE_TIMES = #{caseTimes,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getDutyHistoryPrePayAmount" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(IFNULL(a.DUTY_PAY_AMOUNT, 0)), 0)
        FROM CLM_PLAN_DUTY_PAY a
            JOIN CLMS_POLICY_CLAIM_CASE b ON a.CASE_NO = b.CASE_NO
            JOIN clm_whole_case_base d ON b.REPORT_NO= d.REPORT_NO
        WHERE
        (a.CASE_TIMES,a.CASE_NO) IN
            ( SELECT MAX(a.CASE_TIMES) CASE_TIMES, a.CASE_NO
              FROM CLM_PLAN_DUTY_PAY a
                JOIN CLMS_POLICY_CLAIM_CASE b ON a.CASE_NO = b.CASE_NO
            WHERE a.CLAIM_TYPE = '2'
            <if test="insuredCode != null">
                AND b.INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
            </if>
            <if test="dutyCode != null">
                AND a.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
            </if>
            <if test="dutyCodeList != null and dutyCodeList.size() > 0">
                and a.DUTY_CODE in
                <foreach collection="dutyCodeList" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="planCode != null and planCode != ''">
                AND a.PLAN_CODE = #{planCode, jdbcType = VARCHAR}
            </if>
            AND b.POLICY_NO = #{policyNo, jdbcType = VARCHAR}
            GROUP BY a.CASE_NO)
        AND d.WHOLE_CASE_STATUS !=0
        AND a.CLAIM_TYPE = '2'
        <if test="insuredCode != null">
            AND b.INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
        </if>
        <if test="dutyCode != null">
            AND a.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        </if>
        <if test="dutyCodeList != null and dutyCodeList.size() > 0">
            and a.DUTY_CODE in
            <foreach collection="dutyCodeList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="planCode != null and planCode != ''">
            AND a.PLAN_CODE = #{planCode, jdbcType = VARCHAR}
        </if>
        AND b.POLICY_NO = #{policyNo, jdbcType = VARCHAR}
        <if test="selectScene == null">
            AND b.REPORT_NO != #{reportNo,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getDutyDetailHistoryPayAmount" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(IFNULL(IF(a.SETTLE_AMOUNT &gt; 0,a.SETTLE_AMOUNT,a.AUTO_SETTLE_AMOUNT), 0)),0)
        FROM CLMS_DUTY_DETAIL_PAY a, CLMS_POLICY_CLAIM_CASE b, CLMS_CASE_PROCESS c,
        (
            SELECT MAX(a.CASE_TIMES) CASE_TIMES, a.CASE_NO
            FROM CLMS_DUTY_DETAIL_PAY a, CLMS_POLICY_CLAIM_CASE b, CLMS_CASE_PROCESS c
            WHERE a.CASE_NO = b.CASE_NO
            AND b.REPORT_NO = c.REPORT_NO
            AND a.CASE_TIMES = c.CASE_TIMES
            AND a.CLAIM_TYPE = '1'
            AND c.PROCESS_STATUS = '05'
            AND a.IS_EFFECTIVE = 'Y'
            AND b.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
            AND a.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
            AND a.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
            AND a.DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR}
            <if test="insuredCode != null">
                AND b.INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
            </if>
            GROUP BY a.CASE_NO
        ) d
        WHERE a.CASE_NO = b.CASE_NO
        AND b.REPORT_NO = c.REPORT_NO
        AND a.CASE_TIMES = c.CASE_TIMES
        AND a.CASE_TIMES = d.CASE_TIMES
        AND a.CASE_NO = d.CASE_NO
        AND a.CLAIM_TYPE = '1'
        AND c.PROCESS_STATUS = '05'
        AND a.IS_EFFECTIVE = 'Y'
        AND b.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        AND a.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        AND a.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        AND a.DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR}
        <if test="insuredCode != null">
            AND b.INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
        </if>
        <if test="selectScene == null">
            AND b.REPORT_NO != #{reportNo,jdbcType=VARCHAR}
        </if>
    </select>

    <!--查询当前被保险人的历史赔付信息  赔付+预赔-->
    <select id="getDutyDetailHistoryPayInfo" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam"
            resultMap="dutyDetailHistoryPayInfo">
        SELECT PI.POLICY_NO,
        PP.PLAN_CODE,
        PD.DUTY_CODE,
        PDD.DUTY_DETAIL_CODE,
        PD.DUTY_AMOUNT,
        PDD.DUTY_AMOUNT DUTY_DETAIL_AMOUNT
        FROM CLMS_POLICY_DUTY PD,
        CLMS_POLICY_PLAN PP,
        CLMS_POLICY_INFO PI,
        CLMS_POLICY_DUTY_DETAIL PDD
        WHERE PI.ID_AHCS_POLICY_INFO = PP.ID_AHCS_POLICY_INFO
        AND PD.ID_AHCS_POLICY_PLAN = PP.ID_AHCS_POLICY_PLAN
        AND PD.ID_AHCS_POLICY_DUTY = PDD.ID_AHCS_POLICY_DUTY
        AND PI.POLICY_NO= #{policyNo, jdbcType = VARCHAR}
    </select>

    <!--获取E生保合计免赔额-->
    <select id="selectTotalDeductible" resultType="java.math.BigDecimal">
        select ifnull(SUM(ifnull(A.REMIT_AMOUNT, 0)), 0)
        from CLMS_duty_detail_pay A
        where a.policy_no = #{policyNo, jdbcType = VARCHAR} and A.IS_EFFECTIVE = 'Y'
        and exists
        (select 1
        from CLMS_POLICY_CLAIM_CASE B
        where a.case_no = b.case_no
        and a.policy_no = b.policy_no
        and b.insured_code = #{insuredCode,jdbcType=VARCHAR}
        AND EXISTS (select 1
        from CLMS_case_process c
        where b.report_no = c.report_no
        and c.case_times = a.case_times
        and c.process_status in ('05', '06'))
        AND EXISTS (SELECT 1
        FROM CLM_WHOLE_CASE_BASE D
        WHERE A.CASE_TIMES = D.CASE_TIMES
        AND D.REPORT_NO = B.REPORT_NO
        AND D.INDEMNITY_CONCLUSION = '1'))
    </select>


    <!--通过赔案号查询理算信息 -->
    <select id="getSettleResultByCaseNo" resultMap="result">
        select
        POLICY_NO, CASE_NO, CASE_TIMES, ID_AHCS_BATCH, SUB_TIMES, PLAN_CODE, DUTY_CODE, BASE_AMOUNT_PAY,
        CLAIM_TYPE, DUTY_DETAIL_CODE, DUTY_DETAIL_NAME, SETTLE_AMOUNT, AUTO_SETTLE_AMOUNT,
        REMIT_AMOUNT, PAY_PROPORTION, REASONABLE_AMOUNT, REASONABLE_AMOUNT_TYPE, TREATMENT_DAYS,
        REMIT_DAYS, ALLOWANCE_AMOUNT, PROTOCOL_AMOUNT, ACCOMMODATION_AMOUNT, AUTO_AMOUNT_RECORD
        from CLMS_DUTY_DETAIL_PAY
        where
        CASE_NO=#{caseNo,jdbcType=VARCHAR} and CASE_TIMES=#{caseTimes,jdbcType=NUMERIC}
    </select>

    <!--责任明细历史预赔金额 -->
    <select id="getPrePayDutyDetailHistoryPayAmount" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(IFNULL(IF(a.SETTLE_AMOUNT &gt; 0,a.SETTLE_AMOUNT,a.AUTO_SETTLE_AMOUNT), 0)),0)
        FROM CLMS_DUTY_DETAIL_PAY a, CLMS_POLICY_CLAIM_CASE b, CLMS_CASE_PROCESS c,
        (
        SELECT MAX(a.CASE_TIMES) CASE_TIMES, a.CASE_NO
        FROM CLMS_DUTY_DETAIL_PAY a, CLMS_POLICY_CLAIM_CASE b, CLMS_CASE_PROCESS c
        WHERE a.CASE_NO = b.CASE_NO
        AND b.REPORT_NO = c.REPORT_NO
        AND a.CASE_TIMES = c.CASE_TIMES
        AND a.CLAIM_TYPE = '2'
        AND c.PROCESS_STATUS !='05'
        AND a.IS_EFFECTIVE = 'Y'
        AND b.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        AND a.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        AND a.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        AND a.DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR}
        GROUP BY a.CASE_NO
        ) d
        WHERE a.CASE_NO = b.CASE_NO
        AND b.REPORT_NO = c.REPORT_NO
        AND a.CASE_TIMES = c.CASE_TIMES
        AND a.CASE_TIMES = d.CASE_TIMES
        AND a.CASE_NO = d.CASE_NO
        AND a.CLAIM_TYPE = '2'
        AND c.PROCESS_STATUS != '05'
        AND a.IS_EFFECTIVE = 'Y'
        AND b.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        AND a.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        AND a.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        AND a.DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR}
        <if test="selectScene == null">
            AND b.REPORT_NO != #{reportNo,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getSumDetailCode" resultType="java.math.BigDecimal">
        select sum(c.SETTLE_AMOUNT) from clm_whole_case_base a ,clms_policy_info b ,clms_duty_detail_pay c
        where a.REPORT_NO=b.REPORT_NO  and a.WHOLE_CASE_STATUS='0'
        and b.POLICY_NO=c.POLICY_NO and b.CASE_NO=c.CASE_NO
        and c.POLICY_NO=#{policyNo}
        and c.DUTY_CODE=#{dutyCode}
        and c.DUTY_DETAIL_CODE=#{dutyDetailCode}
    </select>
    <select id="getIndmenityInfo" resultType="java.lang.Integer">
        select count(*) from CLMS_DUTY_DETAIL_PAY c where
        c.duty_code=#{dutyCode}
        and c.INDEMNITY_MODE='1'
        and c.case_no in
        <foreach collection="list"  open="("  close=")" item="caseNo" separator=",">
            #{caseNo}
        </foreach>
    </select>
    <update id="updateDetailAmountByDetailInfo">
        update CLMS_DUTY_DETAIL_PAY set SETTLE_AMOUNT=#{settleAmount},UPDATED_DATE=now()
        where case_no=#{caseNo}
        and CASE_TIMES=#{caseTimes}
        and POLICY_NO=#{policyNo}
        and PLAN_CODE=#{planCode}
        and DUTY_CODE=#{dutyCode}
        and DUTY_DETAIL_CODE=#{dutyDetailCode}
    </update>
    <select id="getDutyDetailCodeAmount" parameterType="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO" resultType="java.math.BigDecimal">
        select nvl(AUTO_SETTLE_AMOUNT,0) from clms_duty_detail_pay where
        case_no=#{caseNo}
        and POLICY_NO=#{policyNo}
        and PLAN_CODE=#{planCode}
        and DUTY_CODE=#{dutyCode}
        and DUTY_DETAIL_CODE=#{dutyDetailCode}
        and CASE_TIMES=#{caseTimes}
    </select>
</mapper>