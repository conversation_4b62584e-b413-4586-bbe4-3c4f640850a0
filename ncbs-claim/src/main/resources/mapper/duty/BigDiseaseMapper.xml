<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.duty.BigDiseaseMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDTO" id="result">
        <id column="ID_AHCS_BIG_DISEASE" property="bigDiseaseId"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="BIG_DISEASE_CODE" property="bigDiseaseCode"/>
        <result column="BIG_DISEASE_OTHER" property="bigDiseaseOther"/>
        <result column="DISASE_DIAGNOSIS_DATE" property="disaseDiagnosisDate"/>
        <result column="IS_INFORM_HEALTH" property="isInformHealth"/>
        <result column="IS_SITUATION_CONSISTENT" property="isSituationConsistent"/>
        <result column="HISTORY_DISEASE" property="historyDisease"/>
        <result column="PRIMARY_SITE" property="primarySite"/>
        <result column="PRIMARY_SITE_DESC" property="primarySiteDesc"/>
        <result column="PRIMARY_SITE_UNKNOWN" property="primarySiteUnknown"/>
        <result column="PLACE_CANCERS" property="placeCancers"/>
        <result column="TASK_ID" property="taskId"/>
        <result column="STATUS" property="status"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.vo.settle.SettleBigDiseaseVO" id="result1">
        <id column="id_ahcs_big_disease" property="bigDiseaseId"/>
        <result column="is_situation_consistent" property="isInformInsurant"/>
        <result column="disase_diagnosis_date" property="disaseDate"/>
        <result column="big_disease_name" property="bigDiseaseType"/>
        <result column="BIG_DISEASE_OTHER" property="bigDiseaseOther"/>
        <result column="primary_site" property="primarySite"/>
        <result column="primary_site_desc" property="primarySiteDesc"/>
        <result column="primary_site_unknown" property="primarySiteUnknown"/>
        <collection property="bigDiseaseDetail" ofType="string" javaType="list" column="id_ahcs_big_disease"
                    select="getDiseaseDetailRemark">
            <result column="diseaseDetailRemark"/>
        </collection>
    </resultMap>

    <insert id="saveBigDisease" parameterType="com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDTO">
        insert into CLMS_big_disease
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_BIG_DISEASE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        BIG_DISEASE_CODE,
        BIG_DISEASE_OTHER,
        DISASE_DIAGNOSIS_DATE,
        IS_INFORM_HEALTH,
        IS_SITUATION_CONSISTENT,
        HISTORY_DISEASE,
        PRIMARY_SITE,
        PRIMARY_SITE_DESC,
        PRIMARY_SITE_UNKNOWN,
        PLACE_CANCERS,
        TASK_ID,
        STATUS,
        archive_time,
        IS_EFFECTIVE)
        values
        (#{createdBy},
        now(),
        #{createdBy},
        now(),
        #{bigDiseaseId},
        #{reportNo},
        #{caseTimes},
        #{idAhcsChannelProcess,jdbcType=VARCHAR},
        #{bigDiseaseCode,jdbcType=VARCHAR},
        #{bigDiseaseOther,jdbcType=VARCHAR},
        #{disaseDiagnosisDate,jdbcType = TIMESTAMP},
        #{isInformHealth,jdbcType=VARCHAR},
        #{isSituationConsistent,jdbcType=VARCHAR},
        #{historyDisease,jdbcType=VARCHAR},
        #{primarySite,jdbcType=VARCHAR},
        #{primarySiteDesc,jdbcType=VARCHAR},
        #{primarySiteUnknown,jdbcType=VARCHAR},
        #{placeCancers,jdbcType=VARCHAR},
        #{taskId,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        <if test="archiveTime != null ">
            #{archiveTime,jdbcType=TIMESTAMP},
        </if>
        <if test="archiveTime == null ">
            sysdate(),
        </if>
        'Y'
        )
    </insert>

    <delete id="removeBigDisease">
        delete from CLMS_big_disease where ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDTO">
        UPDATE
        CLMS_BIG_DISEASE
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = now(),
        IS_EFFECTIVE = 'N'
        WHERE
        ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>

    <!-- 根据通道号、环节号获取最新环节的重大疾病 -->
    <select id="getBigDisease" resultMap="result">
        select t.ID_AHCS_BIG_DISEASE,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.BIG_DISEASE_CODE,
        t.BIG_DISEASE_OTHER,
        t.DISASE_DIAGNOSIS_DATE,
        t.IS_INFORM_HEALTH,
        t.IS_SITUATION_CONSISTENT,
        t.HISTORY_DISEASE,
        t.PRIMARY_SITE,
        t.PRIMARY_SITE_DESC,
        t.PRIMARY_SITE_UNKNOWN,
        t.PLACE_CANCERS,
        t.TASK_ID,
        t.STATUS
        from CLMS_big_disease t
        where t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_big_disease t1 where
        t1.id_ahcs_channel_process = #{idAhcsChannelProcess}
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        AND t1.IS_EFFECTIVE = 'Y'
        order by t1.created_date desc) as temp limit 1
        )
    </select>

    <select id="getSettleBigDisease" resultMap="result1">
        select t1.id_ahcs_big_disease,
        case t1.is_situation_consistent when
        'N' then
        '投保告知：不相符' else
        '投保告知：相符' end as is_situation_consistent,
        t2.big_disease_name,
        t1.BIG_DISEASE_OTHER,
        t1.disase_diagnosis_date,
        ( select t.value_chinese_name from clm_common_parameter t where t.collection_CODE='AHCS_BODY_PARTS' and
        t.value_code=t1.primary_site) primary_site,
        t1.primary_site_desc,
        t1. primary_site_unknown
        from CLMS_big_disease t1 left join CLMS_big_disease_define t2 on t1.big_disease_code = t2.big_disease_code
        where t1.id_ahcs_channel_process = #{idAhcsChannelProcess}
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        AND t1.IS_EFFECTIVE = 'Y'
        and t1.task_id =
        (select * from
        (select t.task_id from CLMS_big_disease t where
        t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.created_date desc) as temp limit 1
        )
    </select>

    <select id="getDiseaseDetailRemark" resultType="string">
        select t3.big_disease_detail_remark diseaseDetailRemark
        from CLMS_big_disease t1,
        CLMS_big_disease_detail t2,
        CLMS_big_disease_detail_def t3
        where t1.id_ahcs_big_disease = t2.id_ahcs_big_disease
        and t2.big_disease_detail_code = t3.big_disease_detail_code
        and t1.id_ahcs_big_disease = #{bigDiseaseId}
        AND t1.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 根据通道号、环节号获取重大疾病 -->
    <select id="getBigDiseaseDTO" resultMap="result">
        select t.ID_AHCS_BIG_DISEASE,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.BIG_DISEASE_CODE,
        t.BIG_DISEASE_OTHER,
        t.DISASE_DIAGNOSIS_DATE,
        t.IS_INFORM_HEALTH,
        t.IS_SITUATION_CONSISTENT,
        t.HISTORY_DISEASE,
        t.PRIMARY_SITE,
        t.PRIMARY_SITE_DESC,
        t.PRIMARY_SITE_UNKNOWN,
        t.PLACE_CANCERS,
        t.TASK_ID,
        t.STATUS,
        t.ARCHIVE_TIME
        from CLMS_big_disease t
        where t.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        and t.STATUS = '1'
        and t.TASK_ID = #{taskId}
        AND t.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 根据通道号、环节号获取重大疾病 -->
    <select id="getBigDiseaseByReportNo" resultMap="result">
        select t.ID_AHCS_BIG_DISEASE,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.BIG_DISEASE_CODE,
        t.BIG_DISEASE_OTHER,
        t.DISASE_DIAGNOSIS_DATE,
        t.IS_INFORM_HEALTH,
        t.IS_SITUATION_CONSISTENT,
        t.HISTORY_DISEASE,
        t.PRIMARY_SITE,
        t.PRIMARY_SITE_DESC,
        t.PRIMARY_SITE_UNKNOWN,
        t.PLACE_CANCERS,
        t.TASK_ID,
        t.STATUS
        from CLMS_big_disease t
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        limit 1
        order by t.UPDATED_DATE
    </select>

    <!-- 根据报案号赔付次数获取重大疾病代码 -->
    <select id="getCriticalIllnessNo" resultType="string">
        select t.BIG_DISEASE_CODE
        from CLMS_big_disease t
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.STATUS = '1'
        and t.task_id = #{taskId}
        AND t.IS_EFFECTIVE = 'Y'
        limit 1
    </select>

    <!-- 获取重大疾病时间 -->
    <select id="getBigDisaseDateByReportNo" resultType="string">
        SELECT date_format(ABD.DISASE_DIAGNOSIS_DATE,'%Y%m%d') DISASE_DIAGNOSIS_DATE
        FROM CLMS_BIG_DISEASE ABD
        WHERE ABD.REPORT_NO = #{reportNo}
        AND ABD.CASE_TIMES = #{caseTimes}
        AND ABD.DISASE_DIAGNOSIS_DATE is not null
        AND ABD.IS_EFFECTIVE = 'Y'
        AND ABD.TASK_ID =
        (select * from
        (select t1.TASK_ID from CLMS_BIG_DISEASE t1
        where t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        and t1.DISASE_DIAGNOSIS_DATE is not null
        AND t1.IS_EFFECTIVE = 'Y'
        order by t1.CREATED_DATE desc)
        as temp limit 1
        )
        limit 1
    </select>

    <select id="getBigDiseaseDTOByReportNo" resultMap="result">
        select t.ID_AHCS_BIG_DISEASE,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.BIG_DISEASE_CODE,
        t.BIG_DISEASE_OTHER,
        t.DISASE_DIAGNOSIS_DATE,
        t.IS_INFORM_HEALTH,
        t.IS_SITUATION_CONSISTENT,
        t.HISTORY_DISEASE,
        t.PRIMARY_SITE,
        t.PRIMARY_SITE_DESC,
        t.PRIMARY_SITE_UNKNOWN,
        t.PLACE_CANCERS,
        t.TASK_ID,
        t.STATUS
        from CLMS_big_disease t
        where t.REPORT_NO = #{reportNo} AND t.CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_BIG_DISEASE (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_BIG_DISEASE,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_CHANNEL_PROCESS,
            BIG_DISEASE_CODE,
            BIG_DISEASE_OTHER,
            DISASE_DIAGNOSIS_DATE,
            IS_INFORM_HEALTH,
            IS_SITUATION_CONSISTENT,
            HISTORY_DISEASE,
            PRIMARY_SITE,
            PRIMARY_SITE_DESC,
            PRIMARY_SITE_UNKNOWN,
            PLACE_CANCERS,
            TASK_ID,
            STATUS,
            ARCHIVE_TIME,
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            MD5(UUID()),
            REPORT_NO,
            #{reopenCaseTimes},
            #{idClmChannelProcess},
            BIG_DISEASE_CODE,
            BIG_DISEASE_OTHER,
            DISASE_DIAGNOSIS_DATE,
            IS_INFORM_HEALTH,
            IS_SITUATION_CONSISTENT,
            HISTORY_DISEASE,
            PRIMARY_SITE,
            PRIMARY_SITE_DESC,
            PRIMARY_SITE_UNKNOWN,
            PLACE_CANCERS,
            TASK_ID,
            STATUS,
            NOW(),
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY
        FROM CLMS_BIG_DISEASE
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
        AND IS_EFFECTIVE = 'Y'
    </insert>
</mapper>