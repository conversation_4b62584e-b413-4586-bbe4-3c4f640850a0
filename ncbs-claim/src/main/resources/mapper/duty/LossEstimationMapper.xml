<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.duty.LossEstimationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.duty.LossEstimationDTO">
        <id column="id" property="id" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="loss_type" property="lossType" />
        <result column="loss_name" property="lossName" />
        <result column="certificate_type" property="certificateType" />
        <result column="certificate_no" property="certificateNo" />
        <result column="medical_fee" property="medicalFee" />
        <result column="Loss_working_fee" property="LossWorkingFee" />
        <result column="damages_amount" property="damagesAmount" />
        <result column="loss_basis" property="lossBasis" />
        <result column="task_id" property="taskId"/>
    </resultMap>

    <sql id="Base_Column_List">
        created_by,sys_ctime,updated_by,sys_utime,report_no,case_times,loss_type,loss_name
        ,certificate_type,certificate_no,medical_fee,Loss_working_fee,damages_amount,loss_basis
    </sql>
    <delete id="removeLossEstimation">
        delete from clms_loss_estimation
        where report_no = #{reportNo} and case_times = #{caseTimes} and task_id = #{taskId}
    </delete>
    <select id="getLossEstimationVOs"
            resultType="com.paic.ncbs.claim.model.vo.duty.LossEstimationVO">
       select
        report_no as  reportNo,
        case_times as caseTimes,
        loss_type as lossType,
        loss_name as lossName,
        certificate_type as certificateType,
        certificate_no as certificateNo,
        medical_fee as medicalFee,
        Loss_working_fee as  LossWorkingFee,
        damages_amount as damagesAmount,
        loss_basis as lossBasis
        FROM clms_loss_estimation cle
        WHERE cle.REPORT_NO = TRIM(#{reportNo}) and cle.CASE_TIMES = #{caseTimes} and cle.TASK_ID = #{taskId}
        ORDER BY cle.sys_utime DESC
    </select>
    <update id="updateLossEstimationTaskId">
        update clms_loss_estimation cle
        set cle.task_id = #{idFlagHistoryChange}
        where cle.REPORT_NO = TRIM(#{reportNo}) and cle.CASE_TIMES = #{caseTimes} and cle.TASK_ID = #{taskId}
    </update>
    <select id="getLastLossEstimationVOs"
            resultType="com.paic.ncbs.claim.model.vo.duty.LossEstimationVO">
        select
        report_no as  reportNo,
        case_times as caseTimes,
        loss_type as lossType,
        loss_name as lossName,
        certificate_type as certificateType,
        certificate_no as certificateNo,
        medical_fee as medicalFee,
        Loss_working_fee as  LossWorkingFee,
        damages_amount as damagesAmount,
        loss_basis as lossBasis
        FROM clms_loss_estimation cle
        WHERE cle.REPORT_NO = TRIM(#{reportNo}) and cle.TASK_ID = (select task_id from clms_loss_estimation cle where cle.REPORT_NO = TRIM(#{reportNo}) order by cle.sys_utime desc limit 1)
        ORDER BY cle.sys_utime DESC
    </select>
    <select id="getLastLossEstimationVOByCaseTimes"
            resultType="com.paic.ncbs.claim.model.dto.duty.LossEstimationDTO">
        select<include refid="Base_Column_List"/>
        FROM clms_loss_estimation cle
        WHERE cle.REPORT_NO = TRIM(#{reportNo}) and cle.TASK_ID = (select task_id from clms_loss_estimation cle where cle.REPORT_NO = TRIM(#{reportNo}) and cle.CASE_TIMES = #{caseTimes} order by cle.sys_utime desc limit 1)
        ORDER BY cle.sys_utime DESC
    </select>
</mapper>