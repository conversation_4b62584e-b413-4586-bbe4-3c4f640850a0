<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper">
    
    <resultMap type="com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO" id="result">
        <id property="idEntrust" column="id_entrust" />
        <result property="reportNo" column="report_no" />
        <result property="caseTimes" column="case_times" />
        <result property="thirdPartyType" column="third_party_type" />
        <result property="insuredStatus" column="insured_status" />
        <result property="accidentCode" column="accident_code" />
        <result property="other" column="other" />
        <result property="entrustDptCode" column="entrust_dpt_code" />
        <result property="entrustDptName" column="entrust_dpt_name" />
        <result property="contactName" column="contact_name" />
        <result property="contactPhone" column="contact_phone" />
        <result property="entrustDes" column="entrust_des" />
        <result property="entrustName" column="entrust_name" />
        <result property="litigationStrategy" column="litigation_strategy" />
        <result property="feeStandard" column="fee_standard" />
        <result property="auditorCode" column="auditor_code" />
        <result property="entrustStatus" column="entrust_status" />
        <result property="fileId" column="file_id" />
        <result property="printStatus" column="print_status" />
        <result property="createdBy" column="created_by" />
        <result property="sysCtime" column="sys_ctime" />
        <result property="updatedBy" column="updated_by" />
        <result property="sysUtime" column="sys_utime" />
        <result property="validFlag" column="valid_flag" />
    </resultMap>

    <!-- 插入委托信息 -->
    <insert id="insertEntrustment" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO">
        INSERT INTO clms_entrust_main (
            id_entrust, report_no, case_times, third_party_type,
            insured_status, accident_code, other,
            entrust_dpt_code, entrust_dpt_name, contact_name, contact_phone,
            entrust_des, entrust_name, litigation_strategy, fee_standard,
            auditor_code, entrust_status, file_id, print_status, valid_flag,
            created_by, sys_ctime, updated_by, sys_utime
        ) VALUES (
            #{idEntrust}, #{reportNo}, #{caseTimes}, #{thirdPartyType},
            #{insuredStatus}, #{accidentCode}, #{other},
            #{entrustDptCode}, #{entrustDptName}, #{contactName}, #{contactPhone},
            #{entrustDes}, #{entrustName}, #{litigationStrategy}, #{feeStandard},
            #{auditorCode}, #{entrustStatus}, #{fileId}, #{printStatus}, #{validFlag},
            #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        )
    </insert>

    <!-- 更新委托信息 -->
    <update id="updateEntrustment" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO">
        UPDATE clms_entrust_main
        <set>
            <if test="reportNo != null">report_no = #{reportNo},</if>
            <if test="caseTimes != null">case_times = #{caseTimes},</if>
            <if test="thirdPartyType != null">third_party_type = #{thirdPartyType},</if>
            <if test="insuredStatus != null">insured_status = #{insuredStatus},</if>
            <if test="accidentCode != null">accident_code = #{accidentCode},</if>
            <if test="other != null">other = #{other},</if>
            <if test="entrustDptCode != null">entrust_dpt_code = #{entrustDptCode},</if>
            <if test="entrustDptName != null">entrust_dpt_name = #{entrustDptName},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="entrustDes != null">entrust_des = #{entrustDes},</if>
            <if test="entrustName != null">entrust_name = #{entrustName},</if>
            <if test="litigationStrategy != null">litigation_strategy = #{litigationStrategy},</if>
            <if test="feeStandard != null">fee_standard = #{feeStandard},</if>
            <if test="auditorCode != null">auditor_code = #{auditorCode},</if>
            <if test="entrustStatus != null">entrust_status = #{entrustStatus},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="printStatus != null">print_status = #{printStatus},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime},</if>
        </set>
        WHERE id_entrust = #{idEntrust}
    </update>

    <!-- 删除草稿任务 -->
    <delete id="deleteEntrustmentNoOperate">
        DELETE FROM clms_entrust_main
        WHERE report_no = #{reportNo}
          AND case_times = #{caseTimes}
          AND id_entrust = #{idEntrust}
          AND entrust_status = '0'
    </delete>

    <!-- 通用条件查询委托记录 -->
    <select id="selectByConditions" resultMap="result">
        SELECT * FROM clms_entrust_main
        WHERE report_no = #{reportNo}
        <if test="caseTimes != null">
            AND case_times = #{caseTimes}
        </if>
        <if test="idEntrust != null and idEntrust != ''">
            AND id_entrust = #{idEntrust}
        </if>
        <if test="entrustStatus != null and entrustStatus != ''">
            AND entrust_status IN
            <foreach collection="entrustStatus.split(',')" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        AND valid_flag = 'Y'
        <choose>
            <when test="orderBy == 'ASC'">
                ORDER BY sys_ctime ASC
            </when>
            <otherwise>
                ORDER BY sys_ctime DESC
            </otherwise>
        </choose>
        <if test="limitOne != null and limitOne == true">
            LIMIT 1
        </if>
    </select>

    <!-- 根据报案号查询历史记录 -->
    <select id="selectHistoryByReportNo" resultMap="result">
        SELECT * FROM clms_entrust_main
        WHERE report_no = #{reportNo}
        ORDER BY sys_ctime asc
    </select>

    <!-- 根据主键查询 -->
    <select id="selectById" resultMap="result">
        SELECT * FROM clms_entrust_main
        WHERE id_entrust = #{idEntrust}
    </select>

    <!-- 查询用于打印的委托信息（移除固定条件到Java代码中处理） -->
    <select id="selectForPrint" resultMap="result">
        SELECT * FROM clms_entrust_main
        WHERE report_no = #{reportNo}
        AND valid_flag = 'Y'
        ORDER BY sys_ctime DESC
    </select>

    <!-- 根据报案号查询保单号和被保险人姓名 -->
    <select id="getPolicyInfoByReportNo" resultType="java.util.HashMap">
        SELECT
            pi.POLICY_NO as policyNo,
            ip.NAME as insuredName
        FROM CLMS_POLICY_INFO pi
        LEFT JOIN CLMS_INSURED_PERSON ip ON pi.ID_AHCS_POLICY_INFO = ip.ID_AHCS_POLICY_INFO
        WHERE pi.REPORT_NO = #{reportNo}
        LIMIT 1
    </select>

    <!-- 根据报案号查询案件机构代码 -->
    <select id="getDepartmentCodeByReportNo" resultType="java.lang.String">
        SELECT P.DEPARTMENT_CODE
        FROM clm_case_base P
        WHERE P.REPORT_NO = TRIM(#{reportNo}) and P.CASE_TIMES = #{caseTimes}
        LIMIT 1
    </select>

</mapper>