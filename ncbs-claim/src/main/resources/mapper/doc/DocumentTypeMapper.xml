<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.doc.DocumentTypeMapper">
    <resultMap id="result" type="com.paic.ncbs.claim.model.dto.doc.DocumentTypeDTO" >
        <id column="ID_AHCS_DOCUMENT_TYPE" property="documentTypeId" />
        <result column="CREATED_BY" property="createdBy" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="UPDATED_BY" property="updatedBy" />
        <result column="UPDATED_DATE" property="updatedDate" />
        <result column="BIG_TYPE_CODE" property="bigCode" />
        <result column="BIG_TYPE_NAME" property="bigName" />
        <result column="SMALL_TYPE_CODE" property="smallCode" />
        <result column="SMALL_TYPE_NAME" property="smallName" />
        <result column="TYPE_EFFECT" property="effect" />
        <result column="SMALL_ORDER" property="smallOrder" />
    </resultMap>

    <resultMap id="selfDoc" type="com.paic.ncbs.claim.model.dto.fileupload.SelfDocTypeDTO" >
        <id column="ID_AHCS_SELF_DOC_TYPE" property="idAhcsSelfDocType" />
        <result column="CREATED_BY" property="createdBy" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="UPDATED_BY" property="updatedBy" />
        <result column="UPDATED_DATE" property="updatedDate" />
        <result column="SMALL_TYPE_CODE" property="smallTypeCode" />
        <result column="SELF_BIG_CODE" property="selfBigCode" />
        <result column="SELF_BIG_NAME" property="selfBigName" />
        <result column="SELF_SMALL_CODE" property="selfSmallCode" />
        <result column="SELF_SMALL_NAME" property="selfSmallName" />
        <result column="IS_EFFECT" property="isEffect" />
        <result column="BIG_ORDER" property="bigOrder" />
        <result column="SMALL_ORDER" property="smallOrder" />
    </resultMap>

    <select id="getBigCodeBySmallCode" resultType="java.lang.String">
        SELECT
            BIG_TYPE_CODE
        FROM  CLMS_DOCUMENT_TYPE
        WHERE MAP_TYPE='0'
          AND TYPE_EFFECT='Y'
          AND SMALL_TYPE_CODE=#{smallCode}
    </select>

    <select id="getBigNameBySmallCode" resultType="java.lang.String">
        SELECT
            BIG_TYPE_NAME
        FROM  CLMS_DOCUMENT_TYPE
        WHERE MAP_TYPE='0'
          AND TYPE_EFFECT='Y'
          AND SMALL_TYPE_CODE=#{smallCode}
    </select>

    <select id="getSmallNameBySmallCode" resultType="java.lang.String">
        SELECT
            SMALL_TYPE_NAME
        FROM  CLMS_DOCUMENT_TYPE
        WHERE MAP_TYPE='0'
          AND TYPE_EFFECT='Y'
          AND SMALL_TYPE_CODE=#{smallCode}
    </select>

    <select id="getDocumentSmallTypeList" resultMap="result">
		SELECT CREATED_BY,
		       CREATED_DATE,
		       UPDATED_BY,
		       UPDATED_DATE,
		       ID_AHCS_DOCUMENT_TYPE,
		       BIG_TYPE_CODE,
		       BIG_TYPE_NAME,
		       SMALL_TYPE_CODE,
		       SMALL_TYPE_NAME,
		       TYPE_EFFECT,
		       SMALL_ORDER
		FROM CLMS_DOCUMENT_TYPE ADT
		WHERE TYPE_EFFECT='Y' AND MAP_TYPE='0'
	</select>


    <select id="getDocumentBigTypeList" resultMap="result">
		SELECT 
		       MAX(BIG_TYPE_CODE) BIG_TYPE_CODE,
		       BIG_TYPE_NAME
		FROM CLMS_DOCUMENT_TYPE ADT WHERE TYPE_EFFECT='Y' AND MAP_TYPE='0'
		GROUP BY BIG_TYPE_CODE,BIG_TYPE_NAME
		ORDER BY BIG_TYPE_CODE
	</select>

    <select id = "getSelfTypeByScene" resultMap="selfDoc">
	     SELECT DISTINCT SMALL_TYPE_CODE,SELF_BIG_CODE,SELF_BIG_NAME,SELF_SMALL_CODE, SELF_SMALL_NAME,BIG_ORDER,SMALL_ORDER 
	     FROM CLMS_SELF_DOC_TYPE WHERE IS_EFFECT='Y' AND SCENE=#{scene} ORDER BY  BIG_ORDER
	</select>

    <select id="getDocumentListInfo" parameterType="com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO" resultType="com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO">
        select d.DOCUMENT_NAME fileName ,d.UPLOAD_PATH  fileId,d.UPLOAD_DATE uploadDate,DOCUMENT_FORMAT fileFormat  from document d ,CLMS_FILE_INFO b where b.DOCUMENT_GROUP_ID =d.DOCUMENT_GROUP_ID
        and b.REPORT_NO =#{reportNo}
        and b.CASE_TIMES =#{caseTimes}
        <if test="fileType != null and fileType!=''" >
            and b.FILE_TYPE =#{fileType}
        </if>
    </select>

</mapper>