<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.sync.ClmFileSyncMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.sync.ClmFileSyncEntity" id="result">
        <id property="id" column="id"/>
        <result property="reportNo" column="report_no"/>
        <result property="syncStatus" column="sync_status"/>
        <result property="syncTime" column="sync_time"/>
        <result property="sysCtime" column="sys_ctime"/>
        <result property="sysUtime" column="sys_utime"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
    </resultMap>

    <select id="getCaseList" resultMap="result">
        select id,
            report_no,
            sync_status,
            sync_time,
            sys_ctime,
            sys_utime,
            created_by,
            updated_by
        from clm_file_sync
        where sync_status = '0'
        order by id desc
        limit #{number ,jdbcType=NUMERIC}
    </select>

    <update id="updateClmFileSync" parameterType="com.paic.ncbs.claim.dao.entity.sync.ClmFileSyncEntity">
        update clm_file_sync
        set sys_utime = now(),
        sync_status = #{syncStatus ,jdbcType=VARCHAR},
        UPDATED_BY = #{updatedBy ,jdbcType=VARCHAR},
        sync_time = #{syncTime ,jdbcType=TIMESTAMP}
        where id = #{id ,jdbcType=NUMERIC}
    </update>


</mapper>