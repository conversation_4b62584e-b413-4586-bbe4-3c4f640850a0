<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.chase.ChaseMapper">
   
   <resultMap type="com.paic.ncbs.claim.model.dto.chase.ChaseApplyDTO" id="chaseApply">
			<id property="idAhcsChaseApply" column="ID_AHCS_CHASE_APPLY" />
			<result property="reportNo" column="REPORT_NO" />
			<result property="caseTimes" column="CASE_TIMES" />
			<result property="applyTimes" column="APPLY_TIMES" />
			<result property="applyReason" column="APPLY_REASON" />
			<result property="applyRemark" column="APPLY_REMARK" />
			<result property="applyUm" column="APPLY_UM" />
			<result property="applyName" column="APPLY_NAME" />
			<result property="verifyUm" column="VERIFY_UM" />
			<result property="verifyName" column="VERIFY_NAME" />
			<result property="verifyOptions" column="VERIFY_OPTIONS" />
			<result property="verifyRemark" column="VERIFY_REMARK" />
			<result property="status" column="STATUS" />
			<result property="departmentCode" column="DEPARTMENT_CODE" />
			<result property="applyDate" column="APPLY_DATE" />
			<result property="verifyDate" column="VERIFY_DATE" />
			<result property="chaseAmountSum" column="CHASE_AMOUNT_SUM" />
	   		<result property="applyChaseSubType" column="APPLY_CHASE_SUB_TYPE" />
	        <result property="verifyChaseSubType" column="VERIFY_CHASE_SUB_TYPE" />
	</resultMap>
   
   	<resultMap type="com.paic.ncbs.claim.model.vo.chase.ChaseTaskInfoVO" id="chaseTaskInfoVO">
		<result property="idAhcsChaseApply" column="ID_AHCS_CHASE_APPLY"/>
		<result property="applyUM" column="APPLY_UM"/>
		<result property="applyName" column="APPLY_Name"/>
		<result property="applyDate" column="APPLY_DATE"/>
		<result property="verifyUM" column="VERIFY_UM"/>
		<result property="verifyName" column="VERIFY_Name"/>
		<result property="verifyDate" column="VERIFY_DATE"/>
	</resultMap>
   
	<update id="mergeChaseApply">
		MERGE INTO CLMS_CHASE_APPLY CA
		USING
		(
			SELECT
			#{chaseApply.createdBy,jdbcType=VARCHAR} created_by,
			sysdate created_date,
			#{chaseApply.updatedBy,jdbcType=VARCHAR} updated_by,
			sysdate updated_date,
			#{chaseApply.idAhcsChaseApply,jdbcType=VARCHAR} ID_AHCS_CHASE_APPLY,
			#{chaseApply.reportNo,jdbcType=VARCHAR} REPORT_NO,
			#{chaseApply.caseTimes,jdbcType=NUMERIC} CASE_TIMES,
			#{chaseApply.applyTimes,jdbcType=NUMERIC} APPLY_TIMES,
			#{chaseApply.applyReason,jdbcType=VARCHAR} APPLY_REASON,
			#{chaseApply.applyRemark,jdbcType=VARCHAR} APPLY_REMARK,
			#{chaseApply.applyUm,jdbcType=VARCHAR} APPLY_UM,
			#{chaseApply.applyName,jdbcType=VARCHAR} APPLY_NAME,
			#{chaseApply.applyDate,jdbcType=TIMESTAMP} APPLY_DATE,
			#{chaseApply.verifyUm,jdbcType=VARCHAR} VERIFY_UM,
			#{chaseApply.verifyName,jdbcType=VARCHAR} VERIFY_NAME,
			#{chaseApply.verifyDate,jdbcType=TIMESTAMP} VERIFY_DATE,
			#{chaseApply.verifyOptions,jdbcType=VARCHAR} VERIFY_OPTIONS,
			#{chaseApply.verifyRemark,jdbcType=VARCHAR} VERIFY_REMARK,
			#{chaseApply.status,jdbcType=VARCHAR} STATUS,
			#{chaseApply.departmentCode,jdbcType=VARCHAR} DEPARTMENT_CODE,
			#{chaseApply.applyChaseSubType,jdbcType=VARCHAR} APPLY_CHASE_SUB_TYPE,
			#{chaseApply.verifyChaseSubType,jdbcType=VARCHAR} VERIFY_CHASE_SUB_TYPE
			FROM DUAL
		) CA1
		ON (CA.REPORT_NO = CA1.REPORT_NO and CA.CASE_TIMES = CA1.CASE_TIMES
		and CA.APPLY_TIMES = CA1.APPLY_TIMES) 
		WHEN MATCHED THEN
		UPDATE SET CA.APPLY_REASON = CA1.APPLY_REASON,
		CA.APPLY_REMARK = CA1.APPLY_REMARK,
		CA.APPLY_UM = CA1.APPLY_UM,
		CA.APPLY_NAME = CA1.APPLY_NAME,
		CA.APPLY_DATE = CA1.APPLY_DATE,
		CA.VERIFY_UM = CA1.VERIFY_UM,
		CA.VERIFY_NAME = CA1.VERIFY_NAME,
		CA.VERIFY_DATE = CA1.VERIFY_DATE,
		CA.VERIFY_OPTIONS = CA1.VERIFY_OPTIONS,
		CA.VERIFY_REMARK = CA1.VERIFY_REMARK,
		CA.STATUS = CA1.STATUS,
		CA.DEPARTMENT_CODE = CA1.DEPARTMENT_CODE,
		CA.APPLY_CHASE_SUB_TYPE = CA1.APPLY_CHASE_SUB_TYPE,
		CA.VERIFY_CHASE_SUB_TYPE = CA1.VERIFY_CHASE_SUB_TYPE,
		CA.updated_by = CA1.updated_by,
		CA.updated_date = CA1.updated_date
		WHEN NOT MATCHED THEN
		INSERT
		(CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_CHASE_APPLY,
		REPORT_NO, CASE_TIMES, APPLY_TIMES,APPLY_REASON,APPLY_REMARK,APPLY_UM,APPLY_NAME,APPLY_DATE,
		VERIFY_UM,VERIFY_NAME,VERIFY_DATE,VERIFY_OPTIONS,VERIFY_REMARK,STATUS,DEPARTMENT_CODE,APPLY_CHASE_SUB_TYPE,VERIFY_CHASE_SUB_TYPE)
		VALUES
		(CA1.CREATED_BY, CA1.CREATED_DATE, CA1.UPDATED_BY, CA1.UPDATED_DATE, CA1.ID_AHCS_CHASE_APPLY,
		CA1.REPORT_NO, CA1.CASE_TIMES, CA1.APPLY_TIMES,CA1.APPLY_REASON,CA1.APPLY_REMARK,CA1.APPLY_UM,
		CA1.APPLY_NAME,CA1.APPLY_DATE,CA1.VERIFY_UM,CA1.VERIFY_NAME,CA1.VERIFY_DATE,CA1.VERIFY_OPTIONS,
		CA1.VERIFY_REMARK,CA1.STATUS,CA1.DEPARTMENT_CODE,CA1.APPLY_CHASE_SUB_TYPE,CA1.VERIFY_CHASE_SUB_TYPE)
	</update>
	
	<select id="listChaseApply" resultMap="chaseApply">
		SELECT
		CA.ID_AHCS_CHASE_APPLY,
		CA.REPORT_NO,
		CA.CASE_TIMES,
		CA.APPLY_TIMES,
		CA.APPLY_REASON,
		CA.APPLY_REMARK,
		CA.APPLY_UM,
		CA.APPLY_NAME,
		CA.APPLY_DATE,
		CA.VERIFY_UM,
		CA.VERIFY_NAME,
		CA.VERIFY_DATE,
		CA.VERIFY_OPTIONS,
		CA.VERIFY_REMARK,
		CA.STATUS,
		CA.DEPARTMENT_CODE,
		(SELECT SUM(CAS.CLAIM_AMOUNT) FROM CLMS_CHASE_APPLY_SEQ CAS where CAS.ID_AHCS_CHASE_APPLY=CA.ID_AHCS_CHASE_APPLY) CHASE_AMOUNT_SUM
		FROM CLMS_CHASE_APPLY CA
		WHERE CA.REPORT_NO = #{reportNo}
		AND CA.CASE_TIMES = #{caseTimes}
		ORDER BY APPLY_TIMES
	</select>
	
	
	
	<select id="getChaseApply" resultMap="chaseApply">
		SELECT
		CA.ID_AHCS_CHASE_APPLY,
		CA.REPORT_NO,
		CA.CASE_TIMES,
		CA.APPLY_TIMES,
		CA.APPLY_REASON,
		CA.APPLY_REMARK,
		CA.APPLY_UM,
		CA.APPLY_NAME,
		CA.APPLY_DATE,
		CA.VERIFY_UM,
		CA.VERIFY_NAME,
		CA.VERIFY_DATE,
		CA.VERIFY_OPTIONS,
		CA.VERIFY_REMARK,
		CA.STATUS,
		CA.DEPARTMENT_CODE
		FROM CLMS_CHASE_APPLY CA
		WHERE CA.REPORT_NO = #{reportNo}
		AND CA.CASE_TIMES = #{caseTimes}
		AND CA.APPLY_TIMES = #{applyTimes}
	</select>
	
	<select id="getChaseApplyId" resultType="java.lang.String">
		SELECT
		CA.ID_AHCS_CHASE_APPLY
		FROM CLMS_CHASE_APPLY CA
		WHERE CA.REPORT_NO = #{reportNo}
		AND CA.CASE_TIMES = #{caseTimes}
		AND CA.APPLY_TIMES = #{applyTimes}
	</select>
	
	<select id="getApplyTimes" resultType="java.lang.Integer">
		SELECT
		ifnull(max(APPLY_TIMES),0)+1
		FROM CLMS_CHASE_APPLY CA
		WHERE CA.REPORT_NO = #{reportNo}
		AND CA.CASE_TIMES = #{caseTimes}
		and CA.STATUS='2'	
	</select>	
	
	<select id="getApplyTaskCounts" resultType="java.lang.Integer">
		SELECT
		IFNULL(count(1),0)
		FROM CLMS_CHASE_APPLY CA
		WHERE CA.REPORT_NO = #{reportNo}
		AND CA.CASE_TIMES > #{caseTimes}
		and CA.STATUS='1'	
	</select>	
	
	<select id="getChaseReportCount" resultType="java.lang.Integer">
		SELECT
		IFNULL(count(1),0)
		FROM CLMS_CHASE_APPLY CA
		WHERE CA.REPORT_NO = #{reportNo, jdbcType=VARCHAR}
		AND CA.CASE_TIMES = #{caseTimes, jdbcType=NUMERIC}
		and CA.STATUS='2'
		and CA.VERIFY_OPTIONS ='1'
	</select>
	
	<select id="getPassChaseApply" resultMap="chaseApply">
		SELECT
		CA.ID_AHCS_CHASE_APPLY,
		CA.REPORT_NO,
		CA.CASE_TIMES,
		CA.APPLY_TIMES,
		CA.APPLY_REASON,
		CA.APPLY_REMARK,
		CA.APPLY_UM,
		CA.APPLY_NAME,
		CA.APPLY_DATE,
		CA.VERIFY_UM,
		CA.VERIFY_NAME,
		CA.VERIFY_DATE,
		CA.VERIFY_OPTIONS,
		CA.VERIFY_REMARK,
		CA.STATUS,
		CA.DEPARTMENT_CODE
		FROM CLMS_CHASE_APPLY CA
		WHERE CA.REPORT_NO = #{reportNo}
		AND CA.CASE_TIMES = #{caseTimes}
		AND CA.STATUS='2'
		AND CA.VERIFY_OPTIONS ='1'
	</select>
	

	<select id="getChaseApplyReasonByReport" resultType="string">
	   SELECT
		      CA.APPLY_REASON
		 FROM CLMS_CHASE_APPLY CA
		WHERE CA.REPORT_NO = #{reportNo}
		      AND CA.CASE_TIMES = #{caseTimes}
			  AND CA.STATUS='2'
			  AND CA.VERIFY_OPTIONS ='1'
	</select>
	

	<select id="getChaseTaskInfoVO" resultMap="chaseTaskInfoVO">
		select a.id_ahcs_chase_apply,
			   a.apply_um,
		       (select u.user_name
		          from CLMS_user_info u
		         where u.user_id = a.apply_um) apply_name,
		       a.apply_date,
		       a.verify_um,
		       (select u.user_name
		          from CLMS_user_info u
		         where u.user_id = a.verify_um) verify_name,
		       a.verify_date
		  from CLMS_chase_apply a
		 where a.report_no = #{reportNo, jdbcType=VARCHAR}
		   and a.case_times = #{caseTimes, jdbcType=INTEGER}
		   order by a.apply_date
	</select>

</mapper>