<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.rule.AutoRuleMainInfoMapper">
  <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.rule.AutoRuleMainInfoEntity">
    <!--@mbg.generated-->
    <!--@Table clms_auto_rule_main_info-->
    <id column="id_auto_rule_main" property="idAutoRuleMain" />
    <result column="report_no" property="reportNo" />
    <result column="case_times" property="caseTimes" />
    <result column="rule_type" property="ruleType" />
    <result column="is_rule_pass" property="isRulePass" />
    <result column="is_auto_pass" property="isAutoPass" />
    <result column="auto_pass_date" property="autoPassDate" />
    <result column="is_effective" property="isEffective" />
    <result column="created_by" property="createdBy" />
    <result column="created_date" property="createdDate" />
    <result column="updated_by" property="updatedBy" />
    <result column="updated_date" property="updatedDate" />

    <!-- 关联明细-->
    <collection property="autoRuleDetailInfoEntities"
                ofType="com.paic.ncbs.claim.dao.entity.rule.AutoRuleDetailInfoEntity"
                select="com.paic.ncbs.claim.dao.mapper.rule.AutoRuleDetailInfoMapper.selectByMainId"
                column="id_auto_rule_main">
    </collection>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id_auto_rule_main, report_no, case_times, rule_type, is_rule_pass, is_auto_pass, 
    auto_pass_date, is_effective, created_by, created_date, updated_by, updated_date
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from clms_auto_rule_main_info
    where id_auto_rule_main = #{idAutoRuleMain}
  </select>
  <select id="selectByCondition" parameterType="com.paic.ncbs.claim.dao.entity.rule.AutoRuleMainInfoEntity"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from clms_auto_rule_main_info
    where is_effective = 'Y'
    <if test="idAutoRuleMain != null and idAutoRuleMain != ''">
      and id_auto_rule_main = #{idAutoRuleMain}
    </if>
    <if test="reportNo != null and reportNo != ''">
      and report_no = #{reportNo}
    </if>
    <if test="caseTimes != null and caseTimes != ''">
      and case_times = #{caseTimes}
    </if>
    <if test="ruleType != null and ruleType != ''">
      and rule_type = #{ruleType}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from clms_auto_rule_main_info
    where id_auto_rule_main = #{idAutoRuleMain}
  </delete>
  <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.rule.AutoRuleMainInfoEntity">
    <!--@mbg.generated-->
    insert into clms_auto_rule_main_info (id_auto_rule_main, report_no, case_times, rule_type, is_rule_pass, 
      is_auto_pass, auto_pass_date, is_effective, created_by, created_date, updated_by, 
      updated_date)
    values (#{idAutoRuleMain}, #{reportNo}, #{caseTimes}, #{ruleType}, #{isRulePass}, 
      #{isAutoPass}, #{autoPassDate}, #{isEffective}, #{createdBy}, #{createdDate}, #{updatedBy}, 
      #{updatedDate})
  </insert>
  <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.rule.AutoRuleMainInfoEntity">
    <!--@mbg.generated-->
    insert into clms_auto_rule_main_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="idAutoRuleMain != null">
        id_auto_rule_main,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="caseTimes != null">
        case_times,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="isRulePass != null">
        is_rule_pass,
      </if>
      <if test="isAutoPass != null">
        is_auto_pass,
      </if>
      <if test="autoPassDate != null">
        auto_pass_date,
      </if>
      <if test="isEffective != null">
        is_effective,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdDate != null">
        created_date,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedDate != null">
        updated_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="idAutoRuleMain != null">
        #{idAutoRuleMain},
      </if>
      <if test="reportNo != null">
        #{reportNo},
      </if>
      <if test="caseTimes != null">
        #{caseTimes},
      </if>
      <if test="ruleType != null">
        #{ruleType},
      </if>
      <if test="isRulePass != null">
        #{isRulePass},
      </if>
      <if test="isAutoPass != null">
        #{isAutoPass},
      </if>
      <if test="autoPassDate != null">
        #{autoPassDate},
      </if>
      <if test="isEffective != null">
        #{isEffective},
      </if>
      <if test="createdBy != null">
        #{createdBy},
      </if>
      <if test="createdDate != null">
        #{createdDate},
      </if>
      <if test="updatedBy != null">
        #{updatedBy},
      </if>
      <if test="updatedDate != null">
        #{updatedDate},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.rule.AutoRuleMainInfoEntity">
    <!--@mbg.generated-->
    update clms_auto_rule_main_info
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo},
      </if>
      <if test="caseTimes != null">
        case_times = #{caseTimes},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType},
      </if>
      <if test="isRulePass != null">
        is_rule_pass = #{isRulePass},
      </if>
      <if test="isAutoPass != null">
        is_auto_pass = #{isAutoPass},
      </if>
      <if test="autoPassDate != null">
        auto_pass_date = #{autoPassDate},
      </if>
      <if test="isEffective != null">
        is_effective = #{isEffective},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy},
      </if>
      <if test="createdDate != null">
        created_date = #{createdDate},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy},
      </if>
      <if test="updatedDate != null">
        updated_date = #{updatedDate},
      </if>
    </set>
    where id_auto_rule_main = #{idAutoRuleMain}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.rule.AutoRuleMainInfoEntity">
    <!--@mbg.generated-->
    update clms_auto_rule_main_info
    set report_no = #{reportNo},
      case_times = #{caseTimes},
      rule_type = #{ruleType},
      is_rule_pass = #{isRulePass},
      is_auto_pass = #{isAutoPass},
      auto_pass_date = #{autoPassDate},
      is_effective = #{isEffective},
      created_by = #{createdBy},
      created_date = #{createdDate},
      updated_by = #{updatedBy},
      updated_date = #{updatedDate}
    where id_auto_rule_main = #{idAutoRuleMain}
  </update>
</mapper>