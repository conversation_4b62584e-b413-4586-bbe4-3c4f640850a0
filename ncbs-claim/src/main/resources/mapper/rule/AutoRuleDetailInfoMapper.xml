<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.rule.AutoRuleDetailInfoMapper">
  <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.rule.AutoRuleDetailInfoEntity">
    <!--@mbg.generated-->
    <!--@Table clms_auto_rule_detail_info-->
    <id column="id_auto_rule_info" property="idAutoRuleInfo" />
    <result column="report_no" property="reportNo" />
    <result column="case_times" property="caseTimes" />
    <result column="serial_no" property="serialNo" />
    <result column="id_auto_rule_main" property="idAutoRuleMain" />
    <result column="bill_no" property="billNo" />
    <result column="batch_no" property="batchNo" />
    <result column="rule_code" property="ruleCode" />
    <result column="rule_message" property="ruleMessage" />
    <result column="created_by" property="createdBy" />
    <result column="created_date" property="createdDate" />
    <result column="updated_by" property="updatedBy" />
    <result column="updated_date" property="updatedDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id_auto_rule_info, report_no, case_times, serial_no, id_auto_rule_main, bill_no, 
    batch_no, rule_code, rule_message, created_by, created_date, updated_by, updated_date
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from clms_auto_rule_detail_info
    where id_auto_rule_info = #{idAutoRuleInfo}
  </select>

  <select id="selectByMainId" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from clms_auto_rule_detail_info
    where id_auto_rule_main = #{idAutoRuleMain}
    order by serial_no
  </select>

  <select id="getCountByReportNo" resultType="Integer">
    <!--@mbg.generated-->
    select
      count(1)
    from clms_auto_rule_detail_info
           join clms_auto_rule_main_info
                on clms_auto_rule_detail_info.id_auto_rule_main = clms_auto_rule_main_info.id_auto_rule_main
    where clms_auto_rule_main_info.is_effective = 'Y'
      and clms_auto_rule_detail_info.report_no = #{reportNo}
      and clms_auto_rule_detail_info.case_times = #{caseTimes}
      and clms_auto_rule_main_info.rule_type = #{ruleType}
    <if test="code != null and code != ''">
      and clms_auto_rule_detail_info.rule_code = #{code}
    </if>
    order by serial_no
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from clms_auto_rule_detail_info
    where id_auto_rule_info = #{idAutoRuleInfo}
  </delete>
  <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.rule.AutoRuleDetailInfoEntity">
    <!--@mbg.generated-->
    insert into clms_auto_rule_detail_info (id_auto_rule_info, report_no, case_times, serial_no, id_auto_rule_main, 
      bill_no, batch_no, rule_code, rule_message, created_by, created_date, 
      updated_by, updated_date)
    values (#{idAutoRuleInfo}, #{reportNo}, #{caseTimes}, #{serialNo}, #{idAutoRuleMain}, 
      #{billNo}, #{batchNo}, #{ruleCode}, #{ruleMessage}, #{createdBy}, #{createdDate}, 
      #{updatedBy}, #{updatedDate})
  </insert>
  <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.rule.AutoRuleDetailInfoEntity">
    <!--@mbg.generated-->
    insert into clms_auto_rule_detail_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="idAutoRuleInfo != null">
        id_auto_rule_info,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="caseTimes != null">
        case_times,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="idAutoRuleMain != null">
        id_auto_rule_main,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="ruleCode != null">
        rule_code,
      </if>
      <if test="ruleMessage != null">
        rule_message,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdDate != null">
        created_date,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedDate != null">
        updated_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="idAutoRuleInfo != null">
        #{idAutoRuleInfo},
      </if>
      <if test="reportNo != null">
        #{reportNo},
      </if>
      <if test="caseTimes != null">
        #{caseTimes},
      </if>
      <if test="serialNo != null">
        #{serialNo},
      </if>
      <if test="idAutoRuleMain != null">
        #{idAutoRuleMain},
      </if>
      <if test="billNo != null">
        #{billNo},
      </if>
      <if test="batchNo != null">
        #{batchNo},
      </if>
      <if test="ruleCode != null">
        #{ruleCode},
      </if>
      <if test="ruleMessage != null">
        #{ruleMessage},
      </if>
      <if test="createdBy != null">
        #{createdBy},
      </if>
      <if test="createdDate != null">
        #{createdDate},
      </if>
      <if test="updatedBy != null">
        #{updatedBy},
      </if>
      <if test="updatedDate != null">
        #{updatedDate},
      </if>
    </trim>
  </insert>
  <insert id="insertList" parameterType="java.util.List">
    <!--@mbg.generated-->
    insert into clms_auto_rule_detail_info (id_auto_rule_info, report_no, case_times, serial_no, id_auto_rule_main,
                                            bill_no, batch_no, rule_code, rule_message, created_by, created_date,
                                            updated_by, updated_date)
    <foreach collection="list" item="item" index="index" separator="union all">
      select #{item.idAutoRuleInfo},
             #{item.reportNo},
             #{item.caseTimes},
             #{item.serialNo},
             #{item.idAutoRuleMain},
             #{item.billNo},
             #{item.batchNo},
             #{item.ruleCode},
             #{item.ruleMessage},
             #{item.createdBy},
             #{item.createdDate},
             #{item.updatedBy},
             #{item.updatedDate}
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.rule.AutoRuleDetailInfoEntity">
    <!--@mbg.generated-->
    update clms_auto_rule_detail_info
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo},
      </if>
      <if test="caseTimes != null">
        case_times = #{caseTimes},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo},
      </if>
      <if test="idAutoRuleMain != null">
        id_auto_rule_main = #{idAutoRuleMain},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo},
      </if>
      <if test="ruleCode != null">
        rule_code = #{ruleCode},
      </if>
      <if test="ruleMessage != null">
        rule_message = #{ruleMessage},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy},
      </if>
      <if test="createdDate != null">
        created_date = #{createdDate},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy},
      </if>
      <if test="updatedDate != null">
        updated_date = #{updatedDate},
      </if>
    </set>
    where id_auto_rule_info = #{idAutoRuleInfo}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.rule.AutoRuleDetailInfoEntity">
    <!--@mbg.generated-->
    update clms_auto_rule_detail_info
    set report_no = #{reportNo},
      case_times = #{caseTimes},
      serial_no = #{serialNo},
      id_auto_rule_main = #{idAutoRuleMain},
      bill_no = #{billNo},
      batch_no = #{batchNo},
      rule_code = #{ruleCode},
      rule_message = #{ruleMessage},
      created_by = #{createdBy},
      created_date = #{createdDate},
      updated_by = #{updatedBy},
      updated_date = #{updatedDate}
    where id_auto_rule_info = #{idAutoRuleInfo}
  </update>

  <select id="getSerialNo" resultType="java.lang.Integer" parameterType="java.lang.String">
    select IFNULL(max(serial_no), 0)
    from clms_auto_rule_detail_info
    where id_auto_rule_main = #{idAutoRuleMain}
  </select>
</mapper>