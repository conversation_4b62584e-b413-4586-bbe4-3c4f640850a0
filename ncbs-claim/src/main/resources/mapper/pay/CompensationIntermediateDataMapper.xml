<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.pay.CompensationIntermediateDataMapper">


    <insert id="insert"  parameterType="com.paic.ncbs.claim.model.dto.pay.CompensationIntermediateData">
        insert into clms_compensation_intermediate_data (id,policy_no, report_no, case_no,
        case_times, plan_code, duty_code ,client_name, client_certificate_no, pay_flag,pay_amount,pay_amount_change,pay_date,
        created_by, created_date, updated_by, updated_date)
        values (#{id},#{policyNo}, #{reportNo}, #{caseNo},
        #{caseTimes}, #{planCode}, #{dutyCode}, #{payFlag},#{payAmount},#{payAmountChange},#{payDate},
        #{createdBy}, now(), #{updatedBy},now())
    </insert>

    <insert id="batchInsert">
        insert into clms_compensation_intermediate_data (id,policy_no, report_no, case_no,
        case_times, plan_code, duty_code ,client_name, client_certificate_no, pay_flag,pay_amount,pay_amount_change,pay_date,
        created_by, created_date, updated_by, updated_date)
        values
        <foreach collection="compensationIntermediateDataList" item="data" separator=",">
            (#{data.id},#{data.policyNo}, #{data.reportNo}, #{data.caseNo},
            #{data.caseTimes}, #{data.planCode}, #{data.dutyCode},
            #{data.clientName},#{data.clientCertificateNo},#{data.payFlag},#{data.payAmount},#{data.payAmountChange},#{data.payDate},
            #{data.createdBy}, now(), #{data.updatedBy}, now())
        </foreach>
    </insert>

    <select id="queryListByCondition"
            resultType="com.paic.ncbs.claim.model.dto.pay.CompensationIntermediateData">
        select policy_no,
        report_no,
        case_no,
        case_times,
        plan_code,
        duty_code ,
        pay_flag,
        pay_amount,
        updated_date
        from clms_compensation_intermediate_data
        where report_no = #{reportNo}
    </select>
</mapper>