# Docker image for springboot file run
# VERSION 0.0.1
# Author: zzc

# dockerfile 的命令摘要
# FROM- 镜像从那里来
# MAINTAINER- 镜像维护者信息
# WORKDIR- 切换当前执行的工作目录
# EXPOSE- 暴露端口
# ARG- 变量属性值，但不在容器内部起作用
# ENV- 变量属性值，容器内部也会起作用
# ADD- 添加文件，如果是压缩文件也解压
# COPY- 添加文件，以复制的形式
# RUN- 构建镜像执行的命令，每一次RUN都会构建一层
# VOLUME- 定义数据卷，如果没有定义则使用默认
# USER- 指定后续执行的用户组和用户
# HEALTHCHECHECK- 健康检测指令
# CMD- 容器启动的命令，如果有多个则以最后一个为准，与ENTRYPOINT同时存在时，CMD中的内容会被用做ENTRYPOINT的参数
# ENTRYPOINT- 容器进入时执行的命令

# 基础镜像信息:使用java:8做为基础镜像
FROM java:8
# 维护者信息
MAINTAINER zouzhicheng926 <<EMAIL>>
# 将jar包添加到容器
ADD ../../../target/ncbs-claim.jar ncbs-claim.jar
# 更新jar包时间
RUN bash -c 'touch /ncbs-claim.jar'
# 容器进入时执行的命令
# exec方式： ["executable","param1","param2"]（JSON数组，所以一定要是双引号）
ENTRYPOINT ["java","-jar","/ncbs-claim.jar"]