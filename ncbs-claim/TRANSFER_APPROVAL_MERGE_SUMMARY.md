# 移交审批功能合并总结

## 需求描述
将 `InvestigateTaskAuditController` 中的 `transferApproval` 方法合并到 `InvestigateController` 的 `addInvestigateAudit` 中。当 `auditOpinion` 传入"移交审批"时，将任务流 `CLMS_TASK_INFO` 中的 `ASSIGNER` 修改为表 `CLMS_INVESTIGATE` 中的"审核人"（`AUDITOR_UM` 字段）。

## 实现方案

### 1. 修改 InvestigateController.addInvestigateAudit 方法
- **文件**: `ncbs-claim/src/main/java/com/paic/ncbs/claim/controller/who/investigate/InvestigateController.java`
- **修改内容**: 在原有逻辑前添加移交审批判断逻辑
- **关键代码**:
```java
// 处理移交审批逻辑
if ("移交审批".equals(investigateAudit.getAuditOpinion())) {
    LogUtil.audit("#调查·移交审批#开始处理，调查ID：{}", investigateAudit.getIdAhcsInvestigate());
    investigateAuditService.handleTransferApproval(investigateAudit, u.getUserCode());
    LogUtil.audit("#调查·移交审批#处理完成，调查ID：{}", investigateAudit.getIdAhcsInvestigate());
    return ResponseResult.success();
}
```

### 2. 新增 InvestigateAuditService.handleTransferApproval 方法
- **接口文件**: `ncbs-claim/src/main/java/com/paic/ncbs/claim/service/investigate/InvestigateAuditService.java`
- **实现文件**: `ncbs-claim/src/main/java/com/paic/ncbs/claim/service/investigate/impl/InvestigateAuditServiceImpl.java`
- **功能**: 
  1. 根据调查ID获取调查信息
  2. 获取调查信息中的审核人（`AUDITOR_UM`）
  3. 更新任务流中的 `ASSIGNER` 为审核人

### 3. 核心业务逻辑
```java
@Override
@Transactional(rollbackFor = Exception.class)
public void handleTransferApproval(InvestigateAuditDTO investigateAudit, String userId) throws GlobalBusinessException {
    // 获取调查信息
    InvestigateVO investigateVo = investigateDao.getInvestigateById(investigateAudit.getIdAhcsInvestigate());
    if (investigateVo == null) {
        throw new GlobalBusinessException("调查信息不存在");
    }

    // 获取调查信息中的审核人
    String auditorUm = investigateVo.getAuditorUm();
    if (StringUtils.isEmptyStr(auditorUm)) {
        throw new GlobalBusinessException("调查信息中审核人不能为空");
    }

    // 更新任务流中的ASSIGNER为调查表中的审核人
    TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
    taskInfoDTO.setReportNo(investigateVo.getReportNo());
    taskInfoDTO.setCaseTimes(investigateVo.getCaseTimes());
    taskInfoDTO.setAssigner(auditorUm);
    taskInfoDTO.setUpdatedBy(userId);
    taskInfoDTO.setUpdatedDate(new Date());
    taskInfoDTO.setStatus("0"); // 待处理状态
    taskInfoDTO.setTaskDefinitionBpmKey(BpmConstants.OC_INVESTIGATE_APPROVAL);
    taskInfoDTO.setAssigneeName(auditorUm);

    // 使用 TaskInfoMapper 直接更新任务分配人
    taskInfoMapper.updateTaskAssigner(taskInfoDTO);
}
```

### 4. 删除原有的 transferApproval 相关代码
- 删除 `InvestigateTaskAuditController.transferApproval` 方法
- 删除 `InvestigateTaskAuditService.transferApproval` 方法声明
- 删除 `InvestigateTaskAuditServiceImpl.transferApproval` 方法实现
- 删除 `InvestigateTaskAuditMapper.transferApproval` 方法声明
- 删除 `InvestigateTaskAuditMapper.xml` 中的 `transferApproval` SQL

## 数据库表结构说明

### CLMS_INVESTIGATE 表
- `AUDITOR_UM`: 审核人字段，存储审核人的用户编码

### CLMS_TASK_INFO 表
- `ASSIGNER`: 任务分配人字段，需要更新为审核人
- `ASSIGNEE_NAME`: 分配人姓名
- `REPORT_NO`: 报案号
- `CASE_TIMES`: 案件次数
- `TASK_DEFINITION_BMP_KEY`: 任务定义键，使用 `OC_INVESTIGATE_APPROVAL`
- `STATUS`: 任务状态，设置为 "0"（待处理）

## 使用的 SQL 更新方法
使用 `TaskInfoMapper.updateTaskAssigner` 方法，对应的 SQL：
```xml
<update id="updateTaskAssigner" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
    update clms_task_info
    set ASSIGNER=#{assigner},
        ASSIGNEE_NAME=#{assigneeName},
        UPDATED_BY=#{updatedBy},
        UPDATED_DATE=#{updatedDate},
        ES_UPDATED_DATE = NOW(3)
    where REPORT_NO=#{reportNo}
    and case_times=#{caseTimes}
    and status=#{status}
    and TASK_DEFINITION_BPM_KEY=#{taskDefinitionBpmKey}
</update>
```

## 测试
创建了单元测试文件 `InvestigateAuditServiceTransferApprovalTest.java` 来验证功能的正确性，包括：
- 正常移交审批流程测试
- 调查信息不存在的异常测试
- 审核人为空的异常测试

## 调用方式
前端调用 `InvestigateController.addInvestigateAudit` 接口时，将 `auditOpinion` 参数设置为"移交审批"即可触发移交审批逻辑。

## 注意事项
1. 移交审批操作会直接返回成功，不会继续执行原有的审批逻辑
2. 需要确保 `CLMS_INVESTIGATE` 表中的 `AUDITOR_UM` 字段有值
3. 任务状态会被设置为待处理状态（"0"）
4. 使用了事务注解确保数据一致性
