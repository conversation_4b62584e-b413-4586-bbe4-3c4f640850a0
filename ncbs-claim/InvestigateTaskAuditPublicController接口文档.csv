接口名称,接口地址,接口类型,请求参数字段名,字段类型,字段大小,字段含义,是否必传,返回参数字段名,返回字段类型,返回字段含义,样例报文
查询调查任务列表,/public/app/investigateTaskAuditAction/getInvestigateTaskList,POST,businessNo,String,50,报案号、被保险人名称、保单号,否,,,,"{"businessNo":"TEST001","departmentCode":"001"}"
,,,reportNo,String,20,报案号,否,code,String,成功状态码,
,,,departmentCode,String,20,案件所属机构编码,否,msg,String,返回消息,
,,,policyNo,String,30,保单号,否,data,Map<String,List<WorkBenchTaskVO>>,任务列表数据,
,,,insuredName,String,50,被保险人,否,reportNo,String,报案号,
,,,assignStartTime,Date,-,派工时间-开始时间,否,taskDefinitionBpmKey,String,任务分类,
,,,assignEndTime,Date,-,派工时间-结束时间,否,insuredName,String,被保险人,
,,,,,,,policyNo,String,保单号,
,,,,,,,assigneeTime,Date,派工时间,
,,,,,,,estimateAmount,String,未决金额,

完成调查任务审核,/public/app/investigateTaskAuditAction/finishTaskAudit,POST,taskId,String,50,任务ID,是,,,,"{"taskId":"123456","auditResult":"PASS","auditOpinion":"审核通过","reportNo":"TEST001","caseTimes":1}"
,,,auditResult,String,10,审核结果,是,code,String,成功状态码,
,,,auditOpinion,String,500,审核意见,否,msg,String,返回消息,
,,,reportNo,String,20,报案号,是,data,Object,返回数据,
,,,caseTimes,Integer,-,赔付次数,是,,,

历史案件列表,/public/app/investigateTaskAuditAction/getHistoryCaseListNew,GET,reportNo,String,20,报案号,是,,,GET /public/app/investigateTaskAuditAction/getHistoryCaseListNew?reportNo=TEST001&caseTimes=1&pageIndex=1&pageRows=10
,,,caseTimes,Integer,-,赔付次数,是,code,String,成功状态码,
,,,pageIndex,Integer,-,页码,否,msg,String,返回消息,
,,,pageRows,Integer,-,每页条数,否,data,List<ClaimInfoToESVO>,历史案件列表,
,,,,,,,reportNo,String,报案号,
,,,,,,,policyNo,String,保单号,
,,,,,,,insuredName,String,被保险人,
,,,,,,,caseStatus,String,案件状态,
,,,,,,,paidAmount,String,赔付金额,

查看单证,/public/app/investigateTaskAuditAction/getDocumentList,POST,reportNo,String,20,报案号,是,,,,"{"reportNo":"TEST001","caseTimes":1}"
,,,caseTimes,Integer,-,赔付次数,是,code,String,成功状态码,
,,,,,,,msg,String,返回消息,
,,,,,,,data,List<FileInfoVO>,单证文件列表,
,,,,,,,bigCode,String,单证大类代码,
,,,,,,,bigName,String,单证大类名称,
,,,,,,,smallTypeList,List,单证细类列表,
,,,,,,,documentList,List,单证文件列表,

获取单证地址,/public/app/investigateTaskAuditAction/getIntranetIOBSDownloadUrl,GET,fileId,String,50,文件ID,是,,,GET /public/app/investigateTaskAuditAction/getIntranetIOBSDownloadUrl?fileId=123456&fileName=test.pdf
,,,fileName,String,200,文件名,是,code,String,成功状态码,
,,,,,,,msg,String,返回消息,
,,,,,,,data,Object,包含下载地址的对象,
,,,,,,,iobsFileDownloadUrl,String,文件下载地址,

单证大类列表,/public/app/investigateTaskAuditAction/getAllDocumentTypeList,POST,reportNo,String,20,报案号,否,,,,"{"reportNo":"TEST001","caseTimes":1}"
,,,caseTimes,Integer,-,赔付次数,否,code,String,成功状态码,
,,,,,,,msg,String,返回消息,
,,,,,,,data,List<DocumentTypeDTO>,单证类型列表,
,,,,,,,bigCode,String,单证大类代码,
,,,,,,,bigName,String,单证大类名称,
,,,,,,,smallTypeList,List,单证细类列表,
,,,,,,,smallCode,String,单证细类代码,
,,,,,,,smallName,String,单证细类名称,
