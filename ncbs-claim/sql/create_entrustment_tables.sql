-- 第三方委托相关表建表sql

-- 1. 第三方委托主表
create table clms_entrust_main (
    id_entrust varchar(32) primary key comment '委托表主键',
    report_no varchar(50) comment '报案号',
    case_times int comment '赔付次数',
    third_party_type varchar(2) comment '第三方类型：01-公估，02-律师，03-其他',
    insured_status varchar(100) comment '事故者现状',
    accident_code varchar(200) comment '事故场景编号，取自数据字典。如有多个，用英文逗号分隔',
    other varchar(500) comment '其他',
    entrust_dpt_code varchar(100) comment '第三方公估公司代码',
    entrust_dpt_name varchar(100) comment '第三方公估公司名称',
    contact_name varchar(100) comment '联系人姓名',
    contact_phone varchar(100) comment '联系方式',
    entrust_des varchar(500) comment '委托说明',
    entrust_name varchar(200) comment '委托对象',
    litigation_strategy varchar(500) comment '诉讼策略',
    fee_standard varchar(500) comment '收费标准',
    auditor_code varchar(50) comment '审批人代码',
    entrust_status varchar(2) comment '委托状态 0-草稿（暂存时为草稿）、1-待审批、2-不同意、3-同意',
    file_id varchar(300) comment '打印文件id',
    print_status varchar(2) comment '委托打印状态 1-已完成 2-未完成',
    created_by varchar(50)  not null comment '创建人',
    sys_ctime datetime not null default current_timestamp comment '创建时间',
    updated_by varchar(50)  default null comment '修改人员',
    sys_utime datetime not null default current_timestamp comment '修改时间',
    valid_flag varchar(1) comment '有效标志 Y-有效 N-无效',
    key idx_sys_ctime (sys_ctime),
    key idx_sys_utime (sys_utime),
    key idx_report_no (report_no, case_times)
)engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='第三方委托信息表';

-- 2. 委托审批表
create table clms_entrust_audit (
    id_entrust_audit varchar(32) primary key comment '委托审核表主键',
    id_entrust_main varchar(32) comment '委托表主键',
    report_no varchar(50) comment '报案号',
    case_times int comment '赔付次数',
    policy_no varchar(30) comment '保单号',
    insured_name varchar(30) comment '被保险人名称',
    third_party_type varchar(2) comment '第三方类型：01-公估，02-律师，03-其他',
    entrust_dpt_code varchar(100) comment '第三方公估公司代码',
    entrust_dpt_name varchar(100) comment '第三方公估公司名称',
    submit_code varchar(50) comment '发起人',
    submit_name varchar(50) comment '发起人姓名',
    auditor_code varchar(50) comment '审批人',
    auditor_name varchar(50) comment '审批人姓名',
    auditor_dpt_code varchar(50) comment '审批人机构代码',
    auditor_dpt_name varchar(50) comment '审批人机构名称',
    audit_opinion varchar(2) comment '审批意见 1-待审核 2-不同意、3-同意',
    audit_time datetime comment '审核时间',
    valid_flag varchar(1) comment '有效标志 Y-有效 N-无效',
    remark varchar(2000) comment '备注',
    created_by varchar(50)  not null comment '创建人',
    sys_ctime datetime not null default current_timestamp comment '创建时间',
    updated_by varchar(50)  default null comment '修改人员',
    sys_utime datetime not null default current_timestamp comment '修改时间',
    key idx_entrust (id_entrust_main),
    key idx_sys_ctime (sys_ctime),
    key idx_sys_utime (sys_utime),
    key idx_report_no (report_no, case_times)
)engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='第三方委托审批信息表';

-- 配置脚本 >>本案存在未审批完成的第三方委托任务 ，不允许提交理算，零注申请，提交拒赔;
DELETE FROM clms_task_conflict WHERE conflict_task_key = 'OC_ENTRUSTMENT_APPROVAL' AND conflict_task_status = '0' AND constraint_type = '1';
INSERT INTO clms_task_conflict (created_by, sys_ctime, updated_by, sys_utime, id_clms_task_conflict, task_definition_bpm_key, plan_operation, conflict_task_key, conflict_task_status, constraint_type, conflict_reason)
VALUES('system', now(), 'system', now(),  REPLACE(UUID(),'-',''), 'OC_MANUAL_SETTLE', '1', 'OC_ENTRUSTMENT_APPROVAL', '0', '1', '当前案件存在处理中的第三方委托，不允许理算提交。');
INSERT INTO clms_task_conflict (created_by, sys_ctime, updated_by, sys_utime, id_clms_task_conflict, task_definition_bpm_key, plan_operation, conflict_task_key, conflict_task_status, constraint_type, conflict_reason)
VALUES('system', now(), 'system', now(),  REPLACE(UUID(),'-',''), 'OC_ZERO_CANCEL_DEPT_AUDIT', '0', 'OC_ENTRUSTMENT_APPROVAL', '0', '1', '当前案件存在处理中的第三方委托，不允许发起零注申请。');
INSERT INTO clms_task_conflict (created_by, sys_ctime, updated_by, sys_utime, id_clms_task_conflict, task_definition_bpm_key, plan_operation, conflict_task_key, conflict_task_status, constraint_type, conflict_reason)
VALUES('system', now(), 'system', now(),  REPLACE(UUID(),'-',''), 'OC_REJECT_REVIEW', '1', 'OC_ENTRUSTMENT_APPROVAL', '0', '1', '当前案件存在处理中的第三方委托，不允许拒赔。');

-- 删除测试代码 InvestigateServiceImpl 类的方法 getServerInfoList
-- 删除测试代码 PrintCoreServiceImpl 类的 updateInvestigateFileInfo
-- 删除测试代码 PrintServiceImpl 类的 findEntrustFileId
-- 删除测试代码 EntrustmentServiceImpl 类的 getApprovalUsers
-- 删除测试代码 EntrustmentServiceImpl 类的 submitentrustAudit



